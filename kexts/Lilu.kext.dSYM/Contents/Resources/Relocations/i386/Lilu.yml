---
triple:          'i386-apple-darwin'
binary-path:     '/Users/<USER>/work/Lilu/Lilu/build/Release/Lilu.kext/Contents/MacOS/Lilu'
relocations:
  - { offsetInCU: 0x35, offset: 0x35, size: 0x4, addend: 0x0, symName: _kmod_info, symObjAddr: 0x0, symBinAddr: 0x437B4, symSize: 0x0 }
  - { offsetInCU: 0x206, offset: 0x206, size: 0x4, addend: 0x0, symName: __realmain, symObjAddr: 0xA8, symBinAddr: 0x4385C, symSize: 0x0 }
  - { offsetInCU: 0x218, offset: 0x218, size: 0x4, addend: 0x0, symName: __antimain, symObjAddr: 0xAC, symBinAddr: 0x43860, symSize: 0x0 }
  - { offsetInCU: 0x22A, offset: 0x22A, size: 0x4, addend: 0x0, symName: __kext_apple_cc, symObjAddr: 0xB0, symBinAddr: 0x43864, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x256, size: 0x4, addend: 0x0, symName: _decodeInstruction, symObjAddr: 0x0, symBinAddr: 0x0, symSize: 0x1BD0 }
  - { offsetInCU: 0x3D, offset: 0x26C, size: 0x4, addend: 0x0, symName: _x86DisassemblerContexts, symObjAddr: 0x351C, symBinAddr: 0x220F0, symSize: 0x0 }
  - { offsetInCU: 0x85, offset: 0x2B4, size: 0x4, addend: 0x0, symName: _x86DisassemblerOneByteOpcodes, symObjAddr: 0x751C, symBinAddr: 0x260F0, symSize: 0x0 }
  - { offsetInCU: 0x10F, offset: 0x33E, size: 0x4, addend: 0x0, symName: _index_x86DisassemblerOneByteOpcodes, symObjAddr: 0xB11C, symBinAddr: 0x29CF0, symSize: 0x0 }
  - { offsetInCU: 0x12D, offset: 0x35C, size: 0x4, addend: 0x0, symName: _x86DisassemblerTwoByteOpcodes, symObjAddr: 0xB1D0, symBinAddr: 0x29DA4, symSize: 0x0 }
  - { offsetInCU: 0x14D, offset: 0x37C, size: 0x4, addend: 0x0, symName: _index_x86DisassemblerTwoByteOpcodes, symObjAddr: 0xF5D0, symBinAddr: 0x2E1A4, symSize: 0x0 }
  - { offsetInCU: 0x161, offset: 0x390, size: 0x4, addend: 0x0, symName: _x86DisassemblerThreeByte38Opcodes, symObjAddr: 0xF684, symBinAddr: 0x2E258, symSize: 0x0 }
  - { offsetInCU: 0x181, offset: 0x3B0, size: 0x4, addend: 0x0, symName: _index_x86DisassemblerThreeByte38Opcodes, symObjAddr: 0x15284, symBinAddr: 0x33E58, symSize: 0x0 }
  - { offsetInCU: 0x195, offset: 0x3C4, size: 0x4, addend: 0x0, symName: _x86DisassemblerThreeByte3AOpcodes, symObjAddr: 0x15338, symBinAddr: 0x33F0C, symSize: 0x0 }
  - { offsetInCU: 0x1B5, offset: 0x3E4, size: 0x4, addend: 0x0, symName: _index_x86DisassemblerThreeByte3AOpcodes, symObjAddr: 0x15F38, symBinAddr: 0x34B0C, symSize: 0x0 }
  - { offsetInCU: 0x1C6, offset: 0x3F5, size: 0x4, addend: 0x0, symName: _emptyTable, symObjAddr: 0x15FEC, symBinAddr: 0x34BC0, symSize: 0x0 }
  - { offsetInCU: 0x1D8, offset: 0x407, size: 0x4, addend: 0x0, symName: _modRMTable, symObjAddr: 0x163EC, symBinAddr: 0x34FC0, symSize: 0x0 }
  - { offsetInCU: 0x208, offset: 0x437, size: 0x4, addend: 0x0, symName: _x86DisassemblerInstrSpecifiers, symObjAddr: 0x17732, symBinAddr: 0x36306, symSize: 0x0 }
  - { offsetInCU: 0x245, offset: 0x474, size: 0x4, addend: 0x0, symName: _x86_16_bit_eq_lookup, symObjAddr: 0x18E50, symBinAddr: 0x37A24, symSize: 0x0 }
  - { offsetInCU: 0x269, offset: 0x498, size: 0x4, addend: 0x0, symName: _x86_16_bit_eq_tbl, symObjAddr: 0x18440, symBinAddr: 0x37014, symSize: 0x0 }
  - { offsetInCU: 0x2C1, offset: 0x4F0, size: 0x4, addend: 0x0, symName: _x86OperandSets, symObjAddr: 0x2D6C, symBinAddr: 0x21940, symSize: 0x0 }
  - { offsetInCU: 0x2562, offset: 0x2791, size: 0x4, addend: 0x0, symName: _decodeInstruction, symObjAddr: 0x0, symBinAddr: 0x0, symSize: 0x1BD0 }
  - { offsetInCU: 0x2F85, offset: 0x31B4, size: 0x4, addend: 0x0, symName: _getIDWithAttrMask, symObjAddr: 0x1BD0, symBinAddr: 0x1BD0, symSize: 0x2F0 }
  - { offsetInCU: 0x30A3, offset: 0x32D2, size: 0x4, addend: 0x0, symName: _readModRM, symObjAddr: 0x1EC0, symBinAddr: 0x1EC0, symSize: 0x300 }
  - { offsetInCU: 0x3242, offset: 0x3471, size: 0x4, addend: 0x0, symName: _readDisplacement, symObjAddr: 0x21C0, symBinAddr: 0x21C0, symSize: 0x210 }
  - { offsetInCU: 0x3246, offset: 0x3475, size: 0x4, addend: 0x0, symName: _readSIB, symObjAddr: 0x23D0, symBinAddr: 0x23D0, symSize: 0x190 }
  - { offsetInCU: 0x3365, offset: 0x3594, size: 0x4, addend: 0x0, symName: _readSIB, symObjAddr: 0x23D0, symBinAddr: 0x23D0, symSize: 0x190 }
  - { offsetInCU: 0x3439, offset: 0x3668, size: 0x4, addend: 0x0, symName: _fixupReg, symObjAddr: 0x2560, symBinAddr: 0x2560, symSize: 0x2A0 }
  - { offsetInCU: 0x356A, offset: 0x3799, size: 0x4, addend: 0x0, symName: _readImmediate, symObjAddr: 0x2800, symBinAddr: 0x2800, symSize: 0x3F0 }
  - { offsetInCU: 0x36A9, offset: 0x38D8, size: 0x4, addend: 0x0, symName: _fixupRegValue, symObjAddr: 0x2BF0, symBinAddr: 0x2BF0, symSize: 0x17B }
  - { offsetInCU: 0x27, offset: 0x3956, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher8getErrorEv, symObjAddr: 0x0, symBinAddr: 0x2D70, symSize: 0x10 }
  - { offsetInCU: 0x2B, offset: 0x395A, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher20tempExecutableMemoryE, symObjAddr: 0x299B, symBinAddr: 0x570B, symSize: 0x1000 }
  - { offsetInCU: 0x36, offset: 0x3965, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher15kernelWriteLockE, symObjAddr: 0x1AC44, symBinAddr: 0x43B28, symSize: 0x0 }
  - { offsetInCU: 0x5D72, offset: 0x96A1, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher23eraseCoverageInstPrefixEymx, symObjAddr: 0x710, symBinAddr: 0x3480, symSize: 0x1C0 }
  - { offsetInCU: 0x5D93, offset: 0x96C2, size: 0x4, addend: 0x0, symName: __ZZN13KernelPatcher23eraseCoverageInstPrefixEymxE13IncInstPrefix, symObjAddr: 0x41BA, symBinAddr: 0x38732, symSize: 0x0 }
  - { offsetInCU: 0x5E59, offset: 0x9788, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher20tempExecutableMemoryE, symObjAddr: 0x299B, symBinAddr: 0x570B, symSize: 0x1000 }
  - { offsetInCU: 0x5E6E, offset: 0x979D, size: 0x4, addend: 0x0, symName: __ZL4that, symObjAddr: 0x1AC48, symBinAddr: 0x46F28, symSize: 0x0 }
  - { offsetInCU: 0x5EA7, offset: 0x97D6, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher8getErrorEv, symObjAddr: 0x0, symBinAddr: 0x2D70, symSize: 0x10 }
  - { offsetInCU: 0x5EC5, offset: 0x97F4, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher10clearErrorEv, symObjAddr: 0x10, symBinAddr: 0x2D80, symSize: 0x10 }
  - { offsetInCU: 0x5EFC, offset: 0x982B, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher4initEv, symObjAddr: 0x20, symBinAddr: 0x2D90, symSize: 0xD0 }
  - { offsetInCU: 0x6077, offset: 0x99A6, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher9loadKinfoEPKcPKS1_mbbb, symObjAddr: 0xF0, symBinAddr: 0x2E60, symSize: 0x290 }
  - { offsetInCU: 0x62EA, offset: 0x9C19, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher6deinitEv, symObjAddr: 0x380, symBinAddr: 0x30F0, symSize: 0x1F0 }
  - { offsetInCU: 0x64C6, offset: 0x9DF5, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher9loadKinfoEPNS_8KextInfoE, symObjAddr: 0x570, symBinAddr: 0x32E0, symSize: 0x80 }
  - { offsetInCU: 0x6523, offset: 0x9E52, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher17updateRunningInfoEmymb, symObjAddr: 0x5F0, symBinAddr: 0x3360, symSize: 0xB0 }
  - { offsetInCU: 0x65A1, offset: 0x9ED0, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher16compatibleKernelEjj, symObjAddr: 0x6A0, symBinAddr: 0x3410, symSize: 0x30 }
  - { offsetInCU: 0x65D2, offset: 0x9F01, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher23eraseCoverageInstPrefixEym, symObjAddr: 0x6D0, symBinAddr: 0x3440, symSize: 0x40 }
  - { offsetInCU: 0x6615, offset: 0x9F44, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher11solveSymbolEmPKc, symObjAddr: 0x8D0, symBinAddr: 0x3640, symSize: 0x60 }
  - { offsetInCU: 0x68D4, offset: 0xA203, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher18setupKextListeningEv, symObjAddr: 0x930, symBinAddr: 0x36A0, symSize: 0x1B0 }
  - { offsetInCU: 0x6B0B, offset: 0xA43A, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher14onOSKextUnloadEPv, symObjAddr: 0xAE0, symBinAddr: 0x3850, symSize: 0x40 }
  - { offsetInCU: 0x6B69, offset: 0xA498, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher31onOSKextSaveLoadedKextPanicListEv, symObjAddr: 0xB20, symBinAddr: 0x3890, symSize: 0x90 }
  - { offsetInCU: 0x6B6D, offset: 0xA49C, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher13routeMultipleEmPNS_12RouteRequestEmymbb, symObjAddr: 0xBB0, symBinAddr: 0x3920, symSize: 0x40 }
  - { offsetInCU: 0x6BC3, offset: 0xA4F2, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher13routeMultipleEmPNS_12RouteRequestEmymbb, symObjAddr: 0xBB0, symBinAddr: 0x3920, symSize: 0x40 }
  - { offsetInCU: 0x6C1A, offset: 0xA549, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher20onKmodCreateInternalEP9kmod_infoPi, symObjAddr: 0xBF0, symBinAddr: 0x3960, symSize: 0x90 }
  - { offsetInCU: 0x6D01, offset: 0xA630, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher10waitOnKextEPNS_11KextHandlerE, symObjAddr: 0xC80, symBinAddr: 0x39F0, symSize: 0xC0 }
  - { offsetInCU: 0x6D85, offset: 0xA6B4, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher25updateKextHandlerFeaturesEPNS_8KextInfoE, symObjAddr: 0xD40, symBinAddr: 0x3AB0, symSize: 0x60 }
  - { offsetInCU: 0x6DD6, offset: 0xA705, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher16applyLookupPatchEPKNS_11LookupPatchE, symObjAddr: 0xDA0, symBinAddr: 0x3B10, symSize: 0x20 }
  - { offsetInCU: 0x6E0C, offset: 0xA73B, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher16applyLookupPatchEPKNS_11LookupPatchEPhm, symObjAddr: 0xDC0, symBinAddr: 0x3B30, symSize: 0x1C0 }
  - { offsetInCU: 0x6EFA, offset: 0xA829, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher23freeFileBufferResourcesEv, symObjAddr: 0xF80, symBinAddr: 0x3CF0, symSize: 0x30 }
  - { offsetInCU: 0x6F20, offset: 0xA84F, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher8activateEv, symObjAddr: 0xFB0, symBinAddr: 0x3D20, symSize: 0x50 }
  - { offsetInCU: 0x6F74, offset: 0xA8A3, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher25processAlreadyLoadedKextsEv, symObjAddr: 0x1000, symBinAddr: 0x3D70, symSize: 0x40 }
  - { offsetInCU: 0x6FA7, offset: 0xA8D6, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher13routeFunctionEyybbb, symObjAddr: 0x1040, symBinAddr: 0x3DB0, symSize: 0x40 }
  - { offsetInCU: 0x74F6, offset: 0xAE25, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher21routeFunctionInternalEyybbbNS_8JumpTypeEP8MachInfoPy, symObjAddr: 0x1080, symBinAddr: 0x3DF0, symSize: 0x990 }
  - { offsetInCU: 0x74FA, offset: 0xAE29, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher17routeFunctionLongEyybbb, symObjAddr: 0x1A10, symBinAddr: 0x4780, symSize: 0x40 }
  - { offsetInCU: 0x8182, offset: 0xBAB1, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher17routeFunctionLongEyybbb, symObjAddr: 0x1A10, symBinAddr: 0x4780, symSize: 0x40 }
  - { offsetInCU: 0x81EF, offset: 0xBB1E, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher18routeFunctionShortEyybbb, symObjAddr: 0x1A50, symBinAddr: 0x47C0, symSize: 0x40 }
  - { offsetInCU: 0x825B, offset: 0xBB8A, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher9readChainEyRNS_8JumpTypeE, symObjAddr: 0x1A90, symBinAddr: 0x4800, symSize: 0x60 }
  - { offsetInCU: 0x829D, offset: 0xBBCC, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher16createTrampolineEymPKhm, symObjAddr: 0x1AF0, symBinAddr: 0x4860, symSize: 0x1A0 }
  - { offsetInCU: 0x8342, offset: 0xBC71, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher10routeBlockEyPKhmbb, symObjAddr: 0x1C90, symBinAddr: 0x4A00, symSize: 0x120 }
  - { offsetInCU: 0x842B, offset: 0xBD5A, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher21routeMultipleInternalEmPNS_12RouteRequestEmymbbNS_8JumpTypeE, symObjAddr: 0x1DB0, symBinAddr: 0x4B20, symSize: 0x510 }
  - { offsetInCU: 0x8608, offset: 0xBF37, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher17routeMultipleLongEmPNS_12RouteRequestEmymbb, symObjAddr: 0x22C0, symBinAddr: 0x5030, symSize: 0x40 }
  - { offsetInCU: 0x8698, offset: 0xBFC7, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher18routeMultipleShortEmPNS_12RouteRequestEmymbb, symObjAddr: 0x2300, symBinAddr: 0x5070, symSize: 0x40 }
  - { offsetInCU: 0x8728, offset: 0xC057, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher11findPatternEPKvS1_mS1_mPm, symObjAddr: 0x2340, symBinAddr: 0x50B0, symSize: 0xD0 }
  - { offsetInCU: 0x8878, offset: 0xC1A7, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher22findAndReplaceWithMaskEPvmPKvmS2_mS2_mS2_mmm, symObjAddr: 0x2410, symBinAddr: 0x5180, symSize: 0x3C0 }
  - { offsetInCU: 0x8A99, offset: 0xC3C8, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher11processKextEP9kmod_infob, symObjAddr: 0x27D0, symBinAddr: 0x5540, symSize: 0x1CB }
  - { offsetInCU: 0x8A9D, offset: 0xC3CC, size: 0x4, addend: 0x0, symName: __ZN13KernelPatcher20tempExecutableMemoryE, symObjAddr: 0x299B, symBinAddr: 0x570B, symSize: 0x1000 }
  - { offsetInCU: 0x27, offset: 0xC4C9, size: 0x4, addend: 0x0, symName: __ZN10RTCStorage4initEb, symObjAddr: 0x0, symBinAddr: 0x6710, symSize: 0xC0 }
  - { offsetInCU: 0xA9CE, offset: 0x16E70, size: 0x4, addend: 0x0, symName: __ZN10RTCStorage4initEb, symObjAddr: 0x0, symBinAddr: 0x6710, symSize: 0xC0 }
  - { offsetInCU: 0xAA29, offset: 0x16ECB, size: 0x4, addend: 0x0, symName: __ZN10RTCStorage6deinitEv, symObjAddr: 0xC0, symBinAddr: 0x67D0, symSize: 0x30 }
  - { offsetInCU: 0xAA4E, offset: 0x16EF0, size: 0x4, addend: 0x0, symName: __ZN10RTCStorage19checkExtendedMemoryEv, symObjAddr: 0xF0, symBinAddr: 0x6800, symSize: 0x30 }
  - { offsetInCU: 0xAA99, offset: 0x16F3B, size: 0x4, addend: 0x0, symName: __ZN10RTCStorage4readEyjPh, symObjAddr: 0x120, symBinAddr: 0x6830, symSize: 0x150 }
  - { offsetInCU: 0xAB32, offset: 0x16FD4, size: 0x4, addend: 0x0, symName: __ZN10RTCStorage5writeEyjPh, symObjAddr: 0x270, symBinAddr: 0x6980, symSize: 0x150 }
  - { offsetInCU: 0xABCB, offset: 0x1706D, size: 0x4, addend: 0x0, symName: __ZN10RTCStorage8readByteEP20IOACPIPlatformDeviceh, symObjAddr: 0x3C0, symBinAddr: 0x6AD0, symSize: 0x50 }
  - { offsetInCU: 0xABFA, offset: 0x1709C, size: 0x4, addend: 0x0, symName: __ZN10RTCStorage9writeByteEP20IOACPIPlatformDevicehh, symObjAddr: 0x410, symBinAddr: 0x6B20, symSize: 0x60 }
  - { offsetInCU: 0xAC6C, offset: 0x1710E, size: 0x4, addend: 0x0, symName: __ZN10RTCStorage10readDirectEP20IOACPIPlatformDevicehtPhb, symObjAddr: 0x470, symBinAddr: 0x6B80, symSize: 0xC0 }
  - { offsetInCU: 0xAD5F, offset: 0x17201, size: 0x4, addend: 0x0, symName: __ZN10RTCStorage11writeDirectEP20IOACPIPlatformDevicehtPhbb, symObjAddr: 0x530, symBinAddr: 0x6C40, symSize: 0x219 }
  - { offsetInCU: 0x27, offset: 0x17413, size: 0x4, addend: 0x0, symName: _X86_init, symObjAddr: 0x0, symBinAddr: 0x6E60, symSize: 0x40 }
  - { offsetInCU: 0x3D, offset: 0x17429, size: 0x4, addend: 0x0, symName: _X86RegDesc, symObjAddr: 0x307C, symBinAddr: 0x38738, symSize: 0x0 }
  - { offsetInCU: 0xDE, offset: 0x174CA, size: 0x4, addend: 0x0, symName: _X86MCRegisterClasses, symObjAddr: 0x42C4, symBinAddr: 0x39980, symSize: 0x0 }
  - { offsetInCU: 0x22F, offset: 0x1761B, size: 0x4, addend: 0x0, symName: _GR8, symObjAddr: 0x4B7E, symBinAddr: 0x3A23A, symSize: 0x0 }
  - { offsetInCU: 0x24D, offset: 0x17639, size: 0x4, addend: 0x0, symName: _GR8Bits, symObjAddr: 0x4BA6, symBinAddr: 0x3A262, symSize: 0x0 }
  - { offsetInCU: 0x26B, offset: 0x17657, size: 0x4, addend: 0x0, symName: _GR8_NOREX, symObjAddr: 0x4BC2, symBinAddr: 0x3A27E, symSize: 0x0 }
  - { offsetInCU: 0x289, offset: 0x17675, size: 0x4, addend: 0x0, symName: _GR8_NOREXBits, symObjAddr: 0x4BD2, symBinAddr: 0x3A28E, symSize: 0x0 }
  - { offsetInCU: 0x2A7, offset: 0x17693, size: 0x4, addend: 0x0, symName: _GR8_ABCD_H, symObjAddr: 0x4BD6, symBinAddr: 0x3A292, symSize: 0x0 }
  - { offsetInCU: 0x2C5, offset: 0x176B1, size: 0x4, addend: 0x0, symName: _GR8_ABCD_HBits, symObjAddr: 0x4BDE, symBinAddr: 0x3A29A, symSize: 0x0 }
  - { offsetInCU: 0x2E3, offset: 0x176CF, size: 0x4, addend: 0x0, symName: _GR8_ABCD_L, symObjAddr: 0x4BE0, symBinAddr: 0x3A29C, symSize: 0x0 }
  - { offsetInCU: 0x2F5, offset: 0x176E1, size: 0x4, addend: 0x0, symName: _GR8_ABCD_LBits, symObjAddr: 0x4BE8, symBinAddr: 0x3A2A4, symSize: 0x0 }
  - { offsetInCU: 0x307, offset: 0x176F3, size: 0x4, addend: 0x0, symName: _GR16, symObjAddr: 0x4BEC, symBinAddr: 0x3A2A8, symSize: 0x0 }
  - { offsetInCU: 0x325, offset: 0x17711, size: 0x4, addend: 0x0, symName: _GR16Bits, symObjAddr: 0x4C0C, symBinAddr: 0x3A2C8, symSize: 0x0 }
  - { offsetInCU: 0x343, offset: 0x1772F, size: 0x4, addend: 0x0, symName: _GR16_NOREX, symObjAddr: 0x4C2A, symBinAddr: 0x3A2E6, symSize: 0x0 }
  - { offsetInCU: 0x355, offset: 0x17741, size: 0x4, addend: 0x0, symName: _GR16_NOREXBits, symObjAddr: 0x4C3A, symBinAddr: 0x3A2F6, symSize: 0x0 }
  - { offsetInCU: 0x373, offset: 0x1775F, size: 0x4, addend: 0x0, symName: _VK1, symObjAddr: 0x4C40, symBinAddr: 0x3A2FC, symSize: 0x0 }
  - { offsetInCU: 0x385, offset: 0x17771, size: 0x4, addend: 0x0, symName: _VK1Bits, symObjAddr: 0x4C50, symBinAddr: 0x3A30C, symSize: 0x0 }
  - { offsetInCU: 0x3A3, offset: 0x1778F, size: 0x4, addend: 0x0, symName: _VK16, symObjAddr: 0x4C5C, symBinAddr: 0x3A318, symSize: 0x0 }
  - { offsetInCU: 0x3B5, offset: 0x177A1, size: 0x4, addend: 0x0, symName: _VK16Bits, symObjAddr: 0x4C6C, symBinAddr: 0x3A328, symSize: 0x0 }
  - { offsetInCU: 0x3C7, offset: 0x177B3, size: 0x4, addend: 0x0, symName: _VK2, symObjAddr: 0x4C78, symBinAddr: 0x3A334, symSize: 0x0 }
  - { offsetInCU: 0x3D9, offset: 0x177C5, size: 0x4, addend: 0x0, symName: _VK2Bits, symObjAddr: 0x4C88, symBinAddr: 0x3A344, symSize: 0x0 }
  - { offsetInCU: 0x3EB, offset: 0x177D7, size: 0x4, addend: 0x0, symName: _VK4, symObjAddr: 0x4C94, symBinAddr: 0x3A350, symSize: 0x0 }
  - { offsetInCU: 0x3FD, offset: 0x177E9, size: 0x4, addend: 0x0, symName: _VK4Bits, symObjAddr: 0x4CA4, symBinAddr: 0x3A360, symSize: 0x0 }
  - { offsetInCU: 0x40F, offset: 0x177FB, size: 0x4, addend: 0x0, symName: _VK8, symObjAddr: 0x4CB0, symBinAddr: 0x3A36C, symSize: 0x0 }
  - { offsetInCU: 0x421, offset: 0x1780D, size: 0x4, addend: 0x0, symName: _VK8Bits, symObjAddr: 0x4CC0, symBinAddr: 0x3A37C, symSize: 0x0 }
  - { offsetInCU: 0x433, offset: 0x1781F, size: 0x4, addend: 0x0, symName: _VK16WM, symObjAddr: 0x4CCC, symBinAddr: 0x3A388, symSize: 0x0 }
  - { offsetInCU: 0x451, offset: 0x1783D, size: 0x4, addend: 0x0, symName: _VK16WMBits, symObjAddr: 0x4CDA, symBinAddr: 0x3A396, symSize: 0x0 }
  - { offsetInCU: 0x463, offset: 0x1784F, size: 0x4, addend: 0x0, symName: _VK1WM, symObjAddr: 0x4CE6, symBinAddr: 0x3A3A2, symSize: 0x0 }
  - { offsetInCU: 0x475, offset: 0x17861, size: 0x4, addend: 0x0, symName: _VK1WMBits, symObjAddr: 0x4CF4, symBinAddr: 0x3A3B0, symSize: 0x0 }
  - { offsetInCU: 0x487, offset: 0x17873, size: 0x4, addend: 0x0, symName: _VK2WM, symObjAddr: 0x4D00, symBinAddr: 0x3A3BC, symSize: 0x0 }
  - { offsetInCU: 0x499, offset: 0x17885, size: 0x4, addend: 0x0, symName: _VK2WMBits, symObjAddr: 0x4D0E, symBinAddr: 0x3A3CA, symSize: 0x0 }
  - { offsetInCU: 0x4AB, offset: 0x17897, size: 0x4, addend: 0x0, symName: _VK4WM, symObjAddr: 0x4D1A, symBinAddr: 0x3A3D6, symSize: 0x0 }
  - { offsetInCU: 0x4BD, offset: 0x178A9, size: 0x4, addend: 0x0, symName: _VK4WMBits, symObjAddr: 0x4D28, symBinAddr: 0x3A3E4, symSize: 0x0 }
  - { offsetInCU: 0x4CF, offset: 0x178BB, size: 0x4, addend: 0x0, symName: _VK8WM, symObjAddr: 0x4D34, symBinAddr: 0x3A3F0, symSize: 0x0 }
  - { offsetInCU: 0x4E1, offset: 0x178CD, size: 0x4, addend: 0x0, symName: _VK8WMBits, symObjAddr: 0x4D42, symBinAddr: 0x3A3FE, symSize: 0x0 }
  - { offsetInCU: 0x4F3, offset: 0x178DF, size: 0x4, addend: 0x0, symName: _SEGMENT_REG, symObjAddr: 0x4D4E, symBinAddr: 0x3A40A, symSize: 0x0 }
  - { offsetInCU: 0x511, offset: 0x178FD, size: 0x4, addend: 0x0, symName: _SEGMENT_REGBits, symObjAddr: 0x4D5A, symBinAddr: 0x3A416, symSize: 0x0 }
  - { offsetInCU: 0x52F, offset: 0x1791B, size: 0x4, addend: 0x0, symName: _GR16_ABCD, symObjAddr: 0x4D62, symBinAddr: 0x3A41E, symSize: 0x0 }
  - { offsetInCU: 0x541, offset: 0x1792D, size: 0x4, addend: 0x0, symName: _GR16_ABCDBits, symObjAddr: 0x4D6A, symBinAddr: 0x3A426, symSize: 0x0 }
  - { offsetInCU: 0x553, offset: 0x1793F, size: 0x4, addend: 0x0, symName: _FPCCR, symObjAddr: 0x4D6E, symBinAddr: 0x3A42A, symSize: 0x0 }
  - { offsetInCU: 0x571, offset: 0x1795D, size: 0x4, addend: 0x0, symName: _FPCCRBits, symObjAddr: 0x4D70, symBinAddr: 0x3A42C, symSize: 0x0 }
  - { offsetInCU: 0x58F, offset: 0x1797B, size: 0x4, addend: 0x0, symName: _FR32X, symObjAddr: 0x4D74, symBinAddr: 0x3A430, symSize: 0x0 }
  - { offsetInCU: 0x5AD, offset: 0x17999, size: 0x4, addend: 0x0, symName: _FR32XBits, symObjAddr: 0x4DB4, symBinAddr: 0x3A470, symSize: 0x0 }
  - { offsetInCU: 0x5CB, offset: 0x179B7, size: 0x4, addend: 0x0, symName: _FR32, symObjAddr: 0x4DC8, symBinAddr: 0x3A484, symSize: 0x0 }
  - { offsetInCU: 0x5DD, offset: 0x179C9, size: 0x4, addend: 0x0, symName: _FR32Bits, symObjAddr: 0x4DE8, symBinAddr: 0x3A4A4, symSize: 0x0 }
  - { offsetInCU: 0x5FB, offset: 0x179E7, size: 0x4, addend: 0x0, symName: _GR32, symObjAddr: 0x4DFA, symBinAddr: 0x3A4B6, symSize: 0x0 }
  - { offsetInCU: 0x60D, offset: 0x179F9, size: 0x4, addend: 0x0, symName: _GR32Bits, symObjAddr: 0x4E1A, symBinAddr: 0x3A4D6, symSize: 0x0 }
  - { offsetInCU: 0x62B, offset: 0x17A17, size: 0x4, addend: 0x0, symName: _GR32_NOAX, symObjAddr: 0x4E38, symBinAddr: 0x3A4F4, symSize: 0x0 }
  - { offsetInCU: 0x649, offset: 0x17A35, size: 0x4, addend: 0x0, symName: _GR32_NOAXBits, symObjAddr: 0x4E56, symBinAddr: 0x3A512, symSize: 0x0 }
  - { offsetInCU: 0x65B, offset: 0x17A47, size: 0x4, addend: 0x0, symName: _GR32_NOSP, symObjAddr: 0x4E74, symBinAddr: 0x3A530, symSize: 0x0 }
  - { offsetInCU: 0x66D, offset: 0x17A59, size: 0x4, addend: 0x0, symName: _GR32_NOSPBits, symObjAddr: 0x4E92, symBinAddr: 0x3A54E, symSize: 0x0 }
  - { offsetInCU: 0x67F, offset: 0x17A6B, size: 0x4, addend: 0x0, symName: _GR32_NOAX_and_GR32_NOSP, symObjAddr: 0x4EB0, symBinAddr: 0x3A56C, symSize: 0x0 }
  - { offsetInCU: 0x69D, offset: 0x17A89, size: 0x4, addend: 0x0, symName: _GR32_NOAX_and_GR32_NOSPBits, symObjAddr: 0x4ECC, symBinAddr: 0x3A588, symSize: 0x0 }
  - { offsetInCU: 0x6AF, offset: 0x17A9B, size: 0x4, addend: 0x0, symName: _DEBUG_REG, symObjAddr: 0x4EEA, symBinAddr: 0x3A5A6, symSize: 0x0 }
  - { offsetInCU: 0x6C1, offset: 0x17AAD, size: 0x4, addend: 0x0, symName: _DEBUG_REGBits, symObjAddr: 0x4EFA, symBinAddr: 0x3A5B6, symSize: 0x0 }
  - { offsetInCU: 0x6DF, offset: 0x17ACB, size: 0x4, addend: 0x0, symName: _GR32_NOREX, symObjAddr: 0x4F04, symBinAddr: 0x3A5C0, symSize: 0x0 }
  - { offsetInCU: 0x6F1, offset: 0x17ADD, size: 0x4, addend: 0x0, symName: _GR32_NOREXBits, symObjAddr: 0x4F14, symBinAddr: 0x3A5D0, symSize: 0x0 }
  - { offsetInCU: 0x703, offset: 0x17AEF, size: 0x4, addend: 0x0, symName: _VK32, symObjAddr: 0x4F18, symBinAddr: 0x3A5D4, symSize: 0x0 }
  - { offsetInCU: 0x715, offset: 0x17B01, size: 0x4, addend: 0x0, symName: _VK32Bits, symObjAddr: 0x4F28, symBinAddr: 0x3A5E4, symSize: 0x0 }
  - { offsetInCU: 0x727, offset: 0x17B13, size: 0x4, addend: 0x0, symName: _GR32_NOAX_and_GR32_NOREX, symObjAddr: 0x4F34, symBinAddr: 0x3A5F0, symSize: 0x0 }
  - { offsetInCU: 0x739, offset: 0x17B25, size: 0x4, addend: 0x0, symName: _GR32_NOAX_and_GR32_NOREXBits, symObjAddr: 0x4F42, symBinAddr: 0x3A5FE, symSize: 0x0 }
  - { offsetInCU: 0x74B, offset: 0x17B37, size: 0x4, addend: 0x0, symName: _GR32_NOREX_NOSP, symObjAddr: 0x4F46, symBinAddr: 0x3A602, symSize: 0x0 }
  - { offsetInCU: 0x75D, offset: 0x17B49, size: 0x4, addend: 0x0, symName: _GR32_NOREX_NOSPBits, symObjAddr: 0x4F54, symBinAddr: 0x3A610, symSize: 0x0 }
  - { offsetInCU: 0x76F, offset: 0x17B5B, size: 0x4, addend: 0x0, symName: _RFP32, symObjAddr: 0x4F58, symBinAddr: 0x3A614, symSize: 0x0 }
  - { offsetInCU: 0x781, offset: 0x17B6D, size: 0x4, addend: 0x0, symName: _RFP32Bits, symObjAddr: 0x4F66, symBinAddr: 0x3A622, symSize: 0x0 }
  - { offsetInCU: 0x79F, offset: 0x17B8B, size: 0x4, addend: 0x0, symName: _VK32WM, symObjAddr: 0x4F72, symBinAddr: 0x3A62E, symSize: 0x0 }
  - { offsetInCU: 0x7B1, offset: 0x17B9D, size: 0x4, addend: 0x0, symName: _VK32WMBits, symObjAddr: 0x4F80, symBinAddr: 0x3A63C, symSize: 0x0 }
  - { offsetInCU: 0x7C3, offset: 0x17BAF, size: 0x4, addend: 0x0, symName: _GR32_NOAX_and_GR32_NOREX_NOSP, symObjAddr: 0x4F8C, symBinAddr: 0x3A648, symSize: 0x0 }
  - { offsetInCU: 0x7D5, offset: 0x17BC1, size: 0x4, addend: 0x0, symName: _GR32_NOAX_and_GR32_NOREX_NOSPBits, symObjAddr: 0x4F98, symBinAddr: 0x3A654, symSize: 0x0 }
  - { offsetInCU: 0x7E7, offset: 0x17BD3, size: 0x4, addend: 0x0, symName: _GR32_ABCD, symObjAddr: 0x4F9C, symBinAddr: 0x3A658, symSize: 0x0 }
  - { offsetInCU: 0x7F9, offset: 0x17BE5, size: 0x4, addend: 0x0, symName: _GR32_ABCDBits, symObjAddr: 0x4FA4, symBinAddr: 0x3A660, symSize: 0x0 }
  - { offsetInCU: 0x80B, offset: 0x17BF7, size: 0x4, addend: 0x0, symName: _GR32_ABCD_and_GR32_NOAX, symObjAddr: 0x4FA8, symBinAddr: 0x3A664, symSize: 0x0 }
  - { offsetInCU: 0x829, offset: 0x17C15, size: 0x4, addend: 0x0, symName: _GR32_ABCD_and_GR32_NOAXBits, symObjAddr: 0x4FAE, symBinAddr: 0x3A66A, symSize: 0x0 }
  - { offsetInCU: 0x83B, offset: 0x17C27, size: 0x4, addend: 0x0, symName: _GR32_TC, symObjAddr: 0x4FB2, symBinAddr: 0x3A66E, symSize: 0x0 }
  - { offsetInCU: 0x84D, offset: 0x17C39, size: 0x4, addend: 0x0, symName: _GR32_TCBits, symObjAddr: 0x4FB8, symBinAddr: 0x3A674, symSize: 0x0 }
  - { offsetInCU: 0x85F, offset: 0x17C4B, size: 0x4, addend: 0x0, symName: _GR32_AD, symObjAddr: 0x4FBC, symBinAddr: 0x3A678, symSize: 0x0 }
  - { offsetInCU: 0x87D, offset: 0x17C69, size: 0x4, addend: 0x0, symName: _GR32_ADBits, symObjAddr: 0x4FC0, symBinAddr: 0x3A67C, symSize: 0x0 }
  - { offsetInCU: 0x88F, offset: 0x17C7B, size: 0x4, addend: 0x0, symName: _GR32_NOAX_and_GR32_TC, symObjAddr: 0x4FC4, symBinAddr: 0x3A680, symSize: 0x0 }
  - { offsetInCU: 0x8A1, offset: 0x17C8D, size: 0x4, addend: 0x0, symName: _GR32_NOAX_and_GR32_TCBits, symObjAddr: 0x4FC8, symBinAddr: 0x3A684, symSize: 0x0 }
  - { offsetInCU: 0x8B3, offset: 0x17C9F, size: 0x4, addend: 0x0, symName: _CCR, symObjAddr: 0x4FCC, symBinAddr: 0x3A688, symSize: 0x0 }
  - { offsetInCU: 0x8C5, offset: 0x17CB1, size: 0x4, addend: 0x0, symName: _CCRBits, symObjAddr: 0x4FCE, symBinAddr: 0x3A68A, symSize: 0x0 }
  - { offsetInCU: 0x8D7, offset: 0x17CC3, size: 0x4, addend: 0x0, symName: _GR32_AD_and_GR32_NOAX, symObjAddr: 0x4FD2, symBinAddr: 0x3A68E, symSize: 0x0 }
  - { offsetInCU: 0x8E9, offset: 0x17CD5, size: 0x4, addend: 0x0, symName: _GR32_AD_and_GR32_NOAXBits, symObjAddr: 0x4FD4, symBinAddr: 0x3A690, symSize: 0x0 }
  - { offsetInCU: 0x8FB, offset: 0x17CE7, size: 0x4, addend: 0x0, symName: _RFP64, symObjAddr: 0x4FD8, symBinAddr: 0x3A694, symSize: 0x0 }
  - { offsetInCU: 0x90D, offset: 0x17CF9, size: 0x4, addend: 0x0, symName: _RFP64Bits, symObjAddr: 0x4FE6, symBinAddr: 0x3A6A2, symSize: 0x0 }
  - { offsetInCU: 0x91F, offset: 0x17D0B, size: 0x4, addend: 0x0, symName: _FR64X, symObjAddr: 0x4FF2, symBinAddr: 0x3A6AE, symSize: 0x0 }
  - { offsetInCU: 0x931, offset: 0x17D1D, size: 0x4, addend: 0x0, symName: _FR64XBits, symObjAddr: 0x5032, symBinAddr: 0x3A6EE, symSize: 0x0 }
  - { offsetInCU: 0x943, offset: 0x17D2F, size: 0x4, addend: 0x0, symName: _GR64, symObjAddr: 0x5046, symBinAddr: 0x3A702, symSize: 0x0 }
  - { offsetInCU: 0x961, offset: 0x17D4D, size: 0x4, addend: 0x0, symName: _GR64Bits, symObjAddr: 0x5068, symBinAddr: 0x3A724, symSize: 0x0 }
  - { offsetInCU: 0x97F, offset: 0x17D6B, size: 0x4, addend: 0x0, symName: _CONTROL_REG, symObjAddr: 0x5076, symBinAddr: 0x3A732, symSize: 0x0 }
  - { offsetInCU: 0x991, offset: 0x17D7D, size: 0x4, addend: 0x0, symName: _CONTROL_REGBits, symObjAddr: 0x5096, symBinAddr: 0x3A752, symSize: 0x0 }
  - { offsetInCU: 0x9AF, offset: 0x17D9B, size: 0x4, addend: 0x0, symName: _FR64, symObjAddr: 0x50A0, symBinAddr: 0x3A75C, symSize: 0x0 }
  - { offsetInCU: 0x9C1, offset: 0x17DAD, size: 0x4, addend: 0x0, symName: _FR64Bits, symObjAddr: 0x50C0, symBinAddr: 0x3A77C, symSize: 0x0 }
  - { offsetInCU: 0x9D3, offset: 0x17DBF, size: 0x4, addend: 0x0, symName: _GR64_with_sub_8bit, symObjAddr: 0x50D2, symBinAddr: 0x3A78E, symSize: 0x0 }
  - { offsetInCU: 0x9E5, offset: 0x17DD1, size: 0x4, addend: 0x0, symName: _GR64_with_sub_8bitBits, symObjAddr: 0x50F2, symBinAddr: 0x3A7AE, symSize: 0x0 }
  - { offsetInCU: 0x9F7, offset: 0x17DE3, size: 0x4, addend: 0x0, symName: _GR64_NOSP, symObjAddr: 0x5100, symBinAddr: 0x3A7BC, symSize: 0x0 }
  - { offsetInCU: 0xA09, offset: 0x17DF5, size: 0x4, addend: 0x0, symName: _GR64_NOSPBits, symObjAddr: 0x511E, symBinAddr: 0x3A7DA, symSize: 0x0 }
  - { offsetInCU: 0xA1B, offset: 0x17E07, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_NOAX, symObjAddr: 0x512C, symBinAddr: 0x3A7E8, symSize: 0x0 }
  - { offsetInCU: 0xA2D, offset: 0x17E19, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_NOAXBits, symObjAddr: 0x514A, symBinAddr: 0x3A806, symSize: 0x0 }
  - { offsetInCU: 0xA3F, offset: 0x17E2B, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_NOAX_and_GR32_NOSP, symObjAddr: 0x5158, symBinAddr: 0x3A814, symSize: 0x0 }
  - { offsetInCU: 0xA51, offset: 0x17E3D, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_NOAX_and_GR32_NOSPBits, symObjAddr: 0x5174, symBinAddr: 0x3A830, symSize: 0x0 }
  - { offsetInCU: 0xA63, offset: 0x17E4F, size: 0x4, addend: 0x0, symName: _GR64_NOREX, symObjAddr: 0x5182, symBinAddr: 0x3A83E, symSize: 0x0 }
  - { offsetInCU: 0xA81, offset: 0x17E6D, size: 0x4, addend: 0x0, symName: _GR64_NOREXBits, symObjAddr: 0x5194, symBinAddr: 0x3A850, symSize: 0x0 }
  - { offsetInCU: 0xA93, offset: 0x17E7F, size: 0x4, addend: 0x0, symName: _GR64_TC, symObjAddr: 0x519A, symBinAddr: 0x3A856, symSize: 0x0 }
  - { offsetInCU: 0xAA5, offset: 0x17E91, size: 0x4, addend: 0x0, symName: _GR64_TCBits, symObjAddr: 0x51AC, symBinAddr: 0x3A868, symSize: 0x0 }
  - { offsetInCU: 0xAC3, offset: 0x17EAF, size: 0x4, addend: 0x0, symName: _GR64_NOSP_and_GR64_TC, symObjAddr: 0x51BA, symBinAddr: 0x3A876, symSize: 0x0 }
  - { offsetInCU: 0xAD5, offset: 0x17EC1, size: 0x4, addend: 0x0, symName: _GR64_NOSP_and_GR64_TCBits, symObjAddr: 0x51CA, symBinAddr: 0x3A886, symSize: 0x0 }
  - { offsetInCU: 0xAE7, offset: 0x17ED3, size: 0x4, addend: 0x0, symName: _GR64_with_sub_16bit_in_GR16_NOREX, symObjAddr: 0x51D8, symBinAddr: 0x3A894, symSize: 0x0 }
  - { offsetInCU: 0xAF9, offset: 0x17EE5, size: 0x4, addend: 0x0, symName: _GR64_with_sub_16bit_in_GR16_NOREXBits, symObjAddr: 0x51E8, symBinAddr: 0x3A8A4, symSize: 0x0 }
  - { offsetInCU: 0xB0B, offset: 0x17EF7, size: 0x4, addend: 0x0, symName: _VK64, symObjAddr: 0x51EE, symBinAddr: 0x3A8AA, symSize: 0x0 }
  - { offsetInCU: 0xB1D, offset: 0x17F09, size: 0x4, addend: 0x0, symName: _VK64Bits, symObjAddr: 0x51FE, symBinAddr: 0x3A8BA, symSize: 0x0 }
  - { offsetInCU: 0xB2F, offset: 0x17F1B, size: 0x4, addend: 0x0, symName: _VR64, symObjAddr: 0x520A, symBinAddr: 0x3A8C6, symSize: 0x0 }
  - { offsetInCU: 0xB41, offset: 0x17F2D, size: 0x4, addend: 0x0, symName: _VR64Bits, symObjAddr: 0x521A, symBinAddr: 0x3A8D6, symSize: 0x0 }
  - { offsetInCU: 0xB53, offset: 0x17F3F, size: 0x4, addend: 0x0, symName: _GR64_NOREX_NOSP, symObjAddr: 0x5228, symBinAddr: 0x3A8E4, symSize: 0x0 }
  - { offsetInCU: 0xB65, offset: 0x17F51, size: 0x4, addend: 0x0, symName: _GR64_NOREX_NOSPBits, symObjAddr: 0x5236, symBinAddr: 0x3A8F2, symSize: 0x0 }
  - { offsetInCU: 0xB77, offset: 0x17F63, size: 0x4, addend: 0x0, symName: _GR64_TC_and_GR64_with_sub_32bit_in_GR32_NOAX, symObjAddr: 0x523C, symBinAddr: 0x3A8F8, symSize: 0x0 }
  - { offsetInCU: 0xB89, offset: 0x17F75, size: 0x4, addend: 0x0, symName: _GR64_TC_and_GR64_with_sub_32bit_in_GR32_NOAXBits, symObjAddr: 0x524A, symBinAddr: 0x3A906, symSize: 0x0 }
  - { offsetInCU: 0xB9B, offset: 0x17F87, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_NOAX_and_GR32_NOREX, symObjAddr: 0x5258, symBinAddr: 0x3A914, symSize: 0x0 }
  - { offsetInCU: 0xBAD, offset: 0x17F99, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_NOAX_and_GR32_NOREXBits, symObjAddr: 0x5266, symBinAddr: 0x3A922, symSize: 0x0 }
  - { offsetInCU: 0xBBF, offset: 0x17FAB, size: 0x4, addend: 0x0, symName: _VK64WM, symObjAddr: 0x526C, symBinAddr: 0x3A928, symSize: 0x0 }
  - { offsetInCU: 0xBD1, offset: 0x17FBD, size: 0x4, addend: 0x0, symName: _VK64WMBits, symObjAddr: 0x527A, symBinAddr: 0x3A936, symSize: 0x0 }
  - { offsetInCU: 0xBE3, offset: 0x17FCF, size: 0x4, addend: 0x0, symName: _GR64_NOREX_and_GR64_TC, symObjAddr: 0x5286, symBinAddr: 0x3A942, symSize: 0x0 }
  - { offsetInCU: 0xBF5, offset: 0x17FE1, size: 0x4, addend: 0x0, symName: _GR64_NOREX_and_GR64_TCBits, symObjAddr: 0x5292, symBinAddr: 0x3A94E, symSize: 0x0 }
  - { offsetInCU: 0xC07, offset: 0x17FF3, size: 0x4, addend: 0x0, symName: _GR64_TCW64, symObjAddr: 0x5298, symBinAddr: 0x3A954, symSize: 0x0 }
  - { offsetInCU: 0xC19, offset: 0x18005, size: 0x4, addend: 0x0, symName: _GR64_TCW64Bits, symObjAddr: 0x52A4, symBinAddr: 0x3A960, symSize: 0x0 }
  - { offsetInCU: 0xC2B, offset: 0x18017, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_NOAX_and_GR32_NOREX_NOSP, symObjAddr: 0x52B2, symBinAddr: 0x3A96E, symSize: 0x0 }
  - { offsetInCU: 0xC3D, offset: 0x18029, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_NOAX_and_GR32_NOREX_NOSPBits, symObjAddr: 0x52BE, symBinAddr: 0x3A97A, symSize: 0x0 }
  - { offsetInCU: 0xC4F, offset: 0x1803B, size: 0x4, addend: 0x0, symName: _GR64_NOREX_NOSP_and_GR64_TC, symObjAddr: 0x52C4, symBinAddr: 0x3A980, symSize: 0x0 }
  - { offsetInCU: 0xC6D, offset: 0x18059, size: 0x4, addend: 0x0, symName: _GR64_NOREX_NOSP_and_GR64_TCBits, symObjAddr: 0x52CE, symBinAddr: 0x3A98A, symSize: 0x0 }
  - { offsetInCU: 0xC7F, offset: 0x1806B, size: 0x4, addend: 0x0, symName: _GR64_TCW64_and_GR64_with_sub_32bit_in_GR32_NOAX, symObjAddr: 0x52D4, symBinAddr: 0x3A990, symSize: 0x0 }
  - { offsetInCU: 0xC91, offset: 0x1807D, size: 0x4, addend: 0x0, symName: _GR64_TCW64_and_GR64_with_sub_32bit_in_GR32_NOAXBits, symObjAddr: 0x52DE, symBinAddr: 0x3A99A, symSize: 0x0 }
  - { offsetInCU: 0xCA3, offset: 0x1808F, size: 0x4, addend: 0x0, symName: _GR64_ABCD, symObjAddr: 0x52EC, symBinAddr: 0x3A9A8, symSize: 0x0 }
  - { offsetInCU: 0xCB5, offset: 0x180A1, size: 0x4, addend: 0x0, symName: _GR64_ABCDBits, symObjAddr: 0x52F4, symBinAddr: 0x3A9B0, symSize: 0x0 }
  - { offsetInCU: 0xCC7, offset: 0x180B3, size: 0x4, addend: 0x0, symName: _GR64_TC_and_GR64_with_sub_32bit_in_GR32_NOAX_and_GR32_NOREX, symObjAddr: 0x52FA, symBinAddr: 0x3A9B6, symSize: 0x0 }
  - { offsetInCU: 0xCD9, offset: 0x180C5, size: 0x4, addend: 0x0, symName: _GR64_TC_and_GR64_with_sub_32bit_in_GR32_NOAX_and_GR32_NOREXBits, symObjAddr: 0x5302, symBinAddr: 0x3A9BE, symSize: 0x0 }
  - { offsetInCU: 0xCEB, offset: 0x180D7, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_NOAX, symObjAddr: 0x5308, symBinAddr: 0x3A9C4, symSize: 0x0 }
  - { offsetInCU: 0xCFD, offset: 0x180E9, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_NOAXBits, symObjAddr: 0x530E, symBinAddr: 0x3A9CA, symSize: 0x0 }
  - { offsetInCU: 0xD0F, offset: 0x180FB, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_TC, symObjAddr: 0x5314, symBinAddr: 0x3A9D0, symSize: 0x0 }
  - { offsetInCU: 0xD21, offset: 0x1810D, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_TCBits, symObjAddr: 0x531A, symBinAddr: 0x3A9D6, symSize: 0x0 }
  - { offsetInCU: 0xD33, offset: 0x1811F, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_AD, symObjAddr: 0x5320, symBinAddr: 0x3A9DC, symSize: 0x0 }
  - { offsetInCU: 0xD45, offset: 0x18131, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_ADBits, symObjAddr: 0x5324, symBinAddr: 0x3A9E0, symSize: 0x0 }
  - { offsetInCU: 0xD57, offset: 0x18143, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_NOAX_and_GR32_TC, symObjAddr: 0x532A, symBinAddr: 0x3A9E6, symSize: 0x0 }
  - { offsetInCU: 0xD69, offset: 0x18155, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_NOAX_and_GR32_TCBits, symObjAddr: 0x532E, symBinAddr: 0x3A9EA, symSize: 0x0 }
  - { offsetInCU: 0xD7B, offset: 0x18167, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_AD_and_GR32_NOAX, symObjAddr: 0x5334, symBinAddr: 0x3A9F0, symSize: 0x0 }
  - { offsetInCU: 0xD8D, offset: 0x18179, size: 0x4, addend: 0x0, symName: _GR64_with_sub_32bit_in_GR32_AD_and_GR32_NOAXBits, symObjAddr: 0x5336, symBinAddr: 0x3A9F2, symSize: 0x0 }
  - { offsetInCU: 0xD9F, offset: 0x1818B, size: 0x4, addend: 0x0, symName: _RST, symObjAddr: 0x533C, symBinAddr: 0x3A9F8, symSize: 0x0 }
  - { offsetInCU: 0xDB1, offset: 0x1819D, size: 0x4, addend: 0x0, symName: _RSTBits, symObjAddr: 0x534C, symBinAddr: 0x3AA08, symSize: 0x0 }
  - { offsetInCU: 0xDCF, offset: 0x181BB, size: 0x4, addend: 0x0, symName: _RFP80, symObjAddr: 0x535C, symBinAddr: 0x3AA18, symSize: 0x0 }
  - { offsetInCU: 0xDE1, offset: 0x181CD, size: 0x4, addend: 0x0, symName: _RFP80Bits, symObjAddr: 0x536A, symBinAddr: 0x3AA26, symSize: 0x0 }
  - { offsetInCU: 0xDF3, offset: 0x181DF, size: 0x4, addend: 0x0, symName: _VR128X, symObjAddr: 0x5376, symBinAddr: 0x3AA32, symSize: 0x0 }
  - { offsetInCU: 0xE05, offset: 0x181F1, size: 0x4, addend: 0x0, symName: _VR128XBits, symObjAddr: 0x53B6, symBinAddr: 0x3AA72, symSize: 0x0 }
  - { offsetInCU: 0xE17, offset: 0x18203, size: 0x4, addend: 0x0, symName: _VR128, symObjAddr: 0x53CA, symBinAddr: 0x3AA86, symSize: 0x0 }
  - { offsetInCU: 0xE29, offset: 0x18215, size: 0x4, addend: 0x0, symName: _VR128Bits, symObjAddr: 0x53EA, symBinAddr: 0x3AAA6, symSize: 0x0 }
  - { offsetInCU: 0xE3B, offset: 0x18227, size: 0x4, addend: 0x0, symName: _VR256X, symObjAddr: 0x53FC, symBinAddr: 0x3AAB8, symSize: 0x0 }
  - { offsetInCU: 0xE4D, offset: 0x18239, size: 0x4, addend: 0x0, symName: _VR256XBits, symObjAddr: 0x543C, symBinAddr: 0x3AAF8, symSize: 0x0 }
  - { offsetInCU: 0xE6B, offset: 0x18257, size: 0x4, addend: 0x0, symName: _VR256, symObjAddr: 0x5454, symBinAddr: 0x3AB10, symSize: 0x0 }
  - { offsetInCU: 0xE7D, offset: 0x18269, size: 0x4, addend: 0x0, symName: _VR256Bits, symObjAddr: 0x5474, symBinAddr: 0x3AB30, symSize: 0x0 }
  - { offsetInCU: 0xE9B, offset: 0x18287, size: 0x4, addend: 0x0, symName: _VR512, symObjAddr: 0x548A, symBinAddr: 0x3AB46, symSize: 0x0 }
  - { offsetInCU: 0xEAD, offset: 0x18299, size: 0x4, addend: 0x0, symName: _VR512Bits, symObjAddr: 0x54CA, symBinAddr: 0x3AB86, symSize: 0x0 }
  - { offsetInCU: 0xECB, offset: 0x182B7, size: 0x4, addend: 0x0, symName: _VR512_with_sub_xmm_in_FR32, symObjAddr: 0x54E6, symBinAddr: 0x3ABA2, symSize: 0x0 }
  - { offsetInCU: 0xEDD, offset: 0x182C9, size: 0x4, addend: 0x0, symName: _VR512_with_sub_xmm_in_FR32Bits, symObjAddr: 0x5506, symBinAddr: 0x3ABC2, symSize: 0x0 }
  - { offsetInCU: 0xEFB, offset: 0x182E7, size: 0x4, addend: 0x0, symName: _X86RegDiffLists, symObjAddr: 0x4A2C, symBinAddr: 0x3A0E8, symSize: 0x0 }
  - { offsetInCU: 0xF19, offset: 0x18305, size: 0x4, addend: 0x0, symName: _X86SubRegIdxLists, symObjAddr: 0x4B60, symBinAddr: 0x3A21C, symSize: 0x0 }
  - { offsetInCU: 0xF3B, offset: 0x18327, size: 0x4, addend: 0x0, symName: _segmentRegnums, symObjAddr: 0x551F, symBinAddr: 0x3ABDB, symSize: 0x0 }
  - { offsetInCU: 0x4A87, offset: 0x1BE73, size: 0x4, addend: 0x0, symName: _X86_init, symObjAddr: 0x0, symBinAddr: 0x6E60, symSize: 0x40 }
  - { offsetInCU: 0x528B, offset: 0x1C677, size: 0x4, addend: 0x0, symName: _X86_getInstruction, symObjAddr: 0x40, symBinAddr: 0x6EA0, symSize: 0x6B0 }
  - { offsetInCU: 0x528F, offset: 0x1C67B, size: 0x4, addend: 0x0, symName: _reader, symObjAddr: 0x6F0, symBinAddr: 0x7550, symSize: 0x30 }
  - { offsetInCU: 0x547F, offset: 0x1C86B, size: 0x4, addend: 0x0, symName: _reader, symObjAddr: 0x6F0, symBinAddr: 0x7550, symSize: 0x30 }
  - { offsetInCU: 0x556B, offset: 0x1C957, size: 0x4, addend: 0x0, symName: _translateRM, symObjAddr: 0x720, symBinAddr: 0x7580, symSize: 0x295A }
  - { offsetInCU: 0x27, offset: 0x1CB1A, size: 0x4, addend: 0x0, symName: __ZN4Lilu9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x9EE0, symSize: 0x30 }
  - { offsetInCU: 0x36, offset: 0x1CB29, size: 0x4, addend: 0x0, symName: __ZN4Lilu10gMetaClassE, symObjAddr: 0x31858, symBinAddr: 0x43B2C, symSize: 0x0 }
  - { offsetInCU: 0x5284, offset: 0x21D77, size: 0x4, addend: 0x0, symName: __ZN4Lilu9metaClassE, symObjAddr: 0x1268, symBinAddr: 0x3ABE4, symSize: 0x0 }
  - { offsetInCU: 0x5293, offset: 0x21D86, size: 0x4, addend: 0x0, symName: __ZN4Lilu10superClassE, symObjAddr: 0x126C, symBinAddr: 0x3ABE8, symSize: 0x0 }
  - { offsetInCU: 0x52A9, offset: 0x21D9C, size: 0x4, addend: 0x0, symName: _kauth_listener_vnode, symObjAddr: 0x31870, symBinAddr: 0x43B44, symSize: 0x0 }
  - { offsetInCU: 0x52D3, offset: 0x21DC6, size: 0x4, addend: 0x0, symName: _Lilu_config, symObjAddr: 0x31874, symBinAddr: 0x43B48, symSize: 0x0 }
  - { offsetInCU: 0xC3DA, offset: 0x28ECD, size: 0x4, addend: 0x0, symName: __ZL11kextVersion, symObjAddr: 0x178C, symBinAddr: 0x3B108, symSize: 0x0 }
  - { offsetInCU: 0xC437, offset: 0x28F2A, size: 0x4, addend: 0x0, symName: __ZN4Lilu9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x9EE0, symSize: 0x30 }
  - { offsetInCU: 0xC48D, offset: 0x28F80, size: 0x4, addend: 0x0, symName: __ZN4Lilu9MetaClassD1Ev, symObjAddr: 0x30, symBinAddr: 0x9F10, symSize: 0x10 }
  - { offsetInCU: 0xC4C6, offset: 0x28FB9, size: 0x4, addend: 0x0, symName: __ZN4LiluC2EPK11OSMetaClass, symObjAddr: 0x40, symBinAddr: 0x9F20, symSize: 0x30 }
  - { offsetInCU: 0xC51B, offset: 0x2900E, size: 0x4, addend: 0x0, symName: __ZN4LiluC1EPK11OSMetaClass, symObjAddr: 0x70, symBinAddr: 0x9F50, symSize: 0x30 }
  - { offsetInCU: 0xC577, offset: 0x2906A, size: 0x4, addend: 0x0, symName: __ZN4LiluD2Ev, symObjAddr: 0xA0, symBinAddr: 0x9F80, symSize: 0x10 }
  - { offsetInCU: 0xC5B2, offset: 0x290A5, size: 0x4, addend: 0x0, symName: __ZN4LiluD1Ev, symObjAddr: 0xB0, symBinAddr: 0x9F90, symSize: 0x10 }
  - { offsetInCU: 0xC608, offset: 0x290FB, size: 0x4, addend: 0x0, symName: __ZN4LiluD0Ev, symObjAddr: 0xC0, symBinAddr: 0x9FA0, symSize: 0x30 }
  - { offsetInCU: 0xC667, offset: 0x2915A, size: 0x4, addend: 0x0, symName: __ZNK4Lilu12getMetaClassEv, symObjAddr: 0xF0, symBinAddr: 0x9FD0, symSize: 0x10 }
  - { offsetInCU: 0xC689, offset: 0x2917C, size: 0x4, addend: 0x0, symName: __ZN4Lilu9MetaClassC2Ev, symObjAddr: 0x100, symBinAddr: 0x9FE0, symSize: 0x30 }
  - { offsetInCU: 0xC6E1, offset: 0x291D4, size: 0x4, addend: 0x0, symName: __ZNK4Lilu9MetaClass5allocEv, symObjAddr: 0x130, symBinAddr: 0xA010, symSize: 0x40 }
  - { offsetInCU: 0xC737, offset: 0x2922A, size: 0x4, addend: 0x0, symName: __ZN4LiluC1Ev, symObjAddr: 0x170, symBinAddr: 0xA050, symSize: 0x40 }
  - { offsetInCU: 0xC770, offset: 0x29263, size: 0x4, addend: 0x0, symName: __ZN4LiluC2Ev, symObjAddr: 0x1B0, symBinAddr: 0xA090, symSize: 0x40 }
  - { offsetInCU: 0xC7CF, offset: 0x292C2, size: 0x4, addend: 0x0, symName: _kauth_callback, symObjAddr: 0x1F0, symBinAddr: 0xA0D0, symSize: 0xA0 }
  - { offsetInCU: 0xC872, offset: 0x29365, size: 0x4, addend: 0x0, symName: __ZN4Lilu5probeEP9IOServicePl, symObjAddr: 0x290, symBinAddr: 0xA170, symSize: 0x50 }
  - { offsetInCU: 0xC8C5, offset: 0x293B8, size: 0x4, addend: 0x0, symName: __ZN4Lilu5startEP9IOService, symObjAddr: 0x2E0, symBinAddr: 0xA1C0, symSize: 0x90 }
  - { offsetInCU: 0xC8F9, offset: 0x293EC, size: 0x4, addend: 0x0, symName: __ZN4Lilu4stopEP9IOService, symObjAddr: 0x370, symBinAddr: 0xA250, symSize: 0x10 }
  - { offsetInCU: 0xC9C8, offset: 0x294BB, size: 0x4, addend: 0x0, symName: __ZN13Configuration16performEarlyInitEv, symObjAddr: 0x380, symBinAddr: 0xA260, symSize: 0xD0 }
  - { offsetInCU: 0xCA55, offset: 0x29548, size: 0x4, addend: 0x0, symName: __ZN13Configuration11initConsoleEP8PE_Videoi, symObjAddr: 0x450, symBinAddr: 0xA330, symSize: 0x80 }
  - { offsetInCU: 0xCADD, offset: 0x295D0, size: 0x4, addend: 0x0, symName: __ZN13Configuration17performCommonInitEv, symObjAddr: 0x4D0, symBinAddr: 0xA3B0, symSize: 0xA0 }
  - { offsetInCU: 0xCB0E, offset: 0x29601, size: 0x4, addend: 0x0, symName: __ZN13Configuration11performInitEv, symObjAddr: 0x570, symBinAddr: 0xA450, symSize: 0x50 }
  - { offsetInCU: 0xCB12, offset: 0x29605, size: 0x4, addend: 0x0, symName: __ZN13Configuration16getBootArgumentsEv, symObjAddr: 0x5C0, symBinAddr: 0xA4A0, symSize: 0x350 }
  - { offsetInCU: 0xCBA1, offset: 0x29694, size: 0x4, addend: 0x0, symName: __ZN13Configuration16getBootArgumentsEv, symObjAddr: 0x5C0, symBinAddr: 0xA4A0, symSize: 0x350 }
  - { offsetInCU: 0xD078, offset: 0x29B6B, size: 0x4, addend: 0x0, symName: __ZN13Configuration14registerPolicyEv, symObjAddr: 0x910, symBinAddr: 0xA7F0, symSize: 0x40 }
  - { offsetInCU: 0xD0B1, offset: 0x29BA4, size: 0x4, addend: 0x0, symName: _Lilu_kern_start, symObjAddr: 0x950, symBinAddr: 0xA830, symSize: 0x70 }
  - { offsetInCU: 0xD0F8, offset: 0x29BEB, size: 0x4, addend: 0x0, symName: _Lilu_kern_stop, symObjAddr: 0x9C0, symBinAddr: 0xA8A0, symSize: 0x10 }
  - { offsetInCU: 0xD146, offset: 0x29C39, size: 0x4, addend: 0x0, symName: __ZN4Lilu9MetaClassD0Ev, symObjAddr: 0x9D0, symBinAddr: 0xA8B0, symSize: 0x10 }
  - { offsetInCU: 0xD1F5, offset: 0x29CE8, size: 0x4, addend: 0x0, symName: '__ZZN13Configuration11initConsoleEP8PE_VideoiEN3$_08__invokeEPvS3_', symObjAddr: 0x9E0, symBinAddr: 0xA8C0, symSize: 0x30 }
  - { offsetInCU: 0xD5B0, offset: 0x2A0A3, size: 0x4, addend: 0x0, symName: __GLOBAL__sub_I_kern_start.cpp, symObjAddr: 0xA10, symBinAddr: 0xA8F0, symSize: 0x840 }
  - { offsetInCU: 0xD7F6, offset: 0x2A2E9, size: 0x4, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x1250, symBinAddr: 0xB130, symSize: 0x17 }
  - { offsetInCU: 0x27, offset: 0x2A35B, size: 0x4, addend: 0x0, symName: __ZN12Disassembler20quickInstructionSizeEym, symObjAddr: 0x0, symBinAddr: 0xB150, symSize: 0x70 }
  - { offsetInCU: 0x26D, offset: 0x2A5A1, size: 0x4, addend: 0x0, symName: __ZN12Disassembler20quickInstructionSizeEym, symObjAddr: 0x0, symBinAddr: 0xB150, symSize: 0x70 }
  - { offsetInCU: 0x2D3, offset: 0x2A607, size: 0x4, addend: 0x0, symName: __ZN12Disassembler9hdeDisasmEyP6hde32s, symObjAddr: 0x70, symBinAddr: 0xB1C0, symSize: 0x19 }
  - { offsetInCU: 0x27, offset: 0x2A65F, size: 0x4, addend: 0x0, symName: __Z14lilu_os_memchrPKvim, symObjAddr: 0x0, symBinAddr: 0xB1E0, symSize: 0x100 }
  - { offsetInCU: 0x52, offset: 0x2A68A, size: 0x4, addend: 0x0, symName: __Z14lilu_os_memchrPKvim, symObjAddr: 0x0, symBinAddr: 0xB1E0, symSize: 0x100 }
  - { offsetInCU: 0x307, offset: 0x2A93F, size: 0x4, addend: 0x0, symName: __Z14lilu_os_memmemPKvmS0_m, symObjAddr: 0x100, symBinAddr: 0xB2E0, symSize: 0x5FA }
  - { offsetInCU: 0x27, offset: 0x2AB7C, size: 0x4, addend: 0x0, symName: __ZN18EfiRuntimeServices18setRuntimeServicesEv, symObjAddr: 0x0, symBinAddr: 0xB8E0, symSize: 0x30 }
  - { offsetInCU: 0x36, offset: 0x2AB8B, size: 0x4, addend: 0x0, symName: __ZN18EfiRuntimeServices8instanceE, symObjAddr: 0xEC58, symBinAddr: 0x46E7C, symSize: 0x0 }
  - { offsetInCU: 0x2A2, offset: 0x2ADF7, size: 0x4, addend: 0x0, symName: __ZN18EfiRuntimeServices14LiluVendorGuidE, symObjAddr: 0x5B0, symBinAddr: 0x3B178, symSize: 0x0 }
  - { offsetInCU: 0x2B1, offset: 0x2AE06, size: 0x4, addend: 0x0, symName: __ZN18EfiRuntimeServices16LiluReadOnlyGuidE, symObjAddr: 0x5C0, symBinAddr: 0x3B188, symSize: 0x0 }
  - { offsetInCU: 0x2C0, offset: 0x2AE15, size: 0x4, addend: 0x0, symName: __ZN18EfiRuntimeServices17LiluWriteOnlyGuidE, symObjAddr: 0x5D0, symBinAddr: 0x3B198, symSize: 0x0 }
  - { offsetInCU: 0x17CB, offset: 0x2C320, size: 0x4, addend: 0x0, symName: __ZN18EfiRuntimeServices18setRuntimeServicesEv, symObjAddr: 0x0, symBinAddr: 0xB8E0, symSize: 0x30 }
  - { offsetInCU: 0x18FB, offset: 0x2C450, size: 0x4, addend: 0x0, symName: __ZN18EfiRuntimeServices8activateEv, symObjAddr: 0x30, symBinAddr: 0xB910, symSize: 0x190 }
  - { offsetInCU: 0x1A4F, offset: 0x2C5A4, size: 0x4, addend: 0x0, symName: __ZN18EfiRuntimeServices3getEb, symObjAddr: 0x1C0, symBinAddr: 0xBAA0, symSize: 0x30 }
  - { offsetInCU: 0x1A6E, offset: 0x2C5C3, size: 0x4, addend: 0x0, symName: __ZN18EfiRuntimeServices3putEv, symObjAddr: 0x1F0, symBinAddr: 0xBAD0, symSize: 0x20 }
  - { offsetInCU: 0x1B86, offset: 0x2C6DB, size: 0x4, addend: 0x0, symName: __ZN18EfiRuntimeServices11resetSystemE14EFI_RESET_TYPE, symObjAddr: 0x210, symBinAddr: 0xBAF0, symSize: 0x120 }
  - { offsetInCU: 0x1C6F, offset: 0x2C7C4, size: 0x4, addend: 0x0, symName: __ZN18EfiRuntimeServices11getVariableEPKDsPK8EFI_GUIDPjPyPv, symObjAddr: 0x330, symBinAddr: 0xBC10, symSize: 0x140 }
  - { offsetInCU: 0x1D6D, offset: 0x2C8C2, size: 0x4, addend: 0x0, symName: __ZN18EfiRuntimeServices11setVariableEPKDsPK8EFI_GUIDjyPv, symObjAddr: 0x470, symBinAddr: 0xBD50, symSize: 0x13F }
  - { offsetInCU: 0x27, offset: 0x2DBDD, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI4initEv, symObjAddr: 0x0, symBinAddr: 0xBE90, symSize: 0x50 }
  - { offsetInCU: 0x3D, offset: 0x2DBF3, size: 0x4, addend: 0x0, symName: _lilu, symObjAddr: 0x1BD04, symBinAddr: 0x46E80, symSize: 0x0 }
  - { offsetInCU: 0x30D9, offset: 0x30C8F, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI4initEv, symObjAddr: 0x0, symBinAddr: 0xBE90, symSize: 0x50 }
  - { offsetInCU: 0x30FE, offset: 0x30CB4, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI6deinitEv, symObjAddr: 0x50, symBinAddr: 0xBEE0, symSize: 0x30 }
  - { offsetInCU: 0x3123, offset: 0x30CD9, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI13requestAccessEmb, symObjAddr: 0x80, symBinAddr: 0xBF10, symSize: 0x80 }
  - { offsetInCU: 0x3173, offset: 0x30D29, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI13releaseAccessEv, symObjAddr: 0x100, symBinAddr: 0xBF90, symSize: 0x20 }
  - { offsetInCU: 0x320D, offset: 0x30DC3, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI10shouldLoadEPKcmjPS1_mS2_mS2_m13KernelVersionS3_Rb, symObjAddr: 0x120, symBinAddr: 0xBFB0, symSize: 0x170 }
  - { offsetInCU: 0x34B1, offset: 0x31067, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI13onPatcherLoadEPFvPvR13KernelPatcherES0_, symObjAddr: 0x290, symBinAddr: 0xC120, symSize: 0xC0 }
  - { offsetInCU: 0x3698, offset: 0x3124E, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI10onKextLoadEPN13KernelPatcher8KextInfoEmPFvPvRS0_mymES3_, symObjAddr: 0x350, symBinAddr: 0xC1E0, symSize: 0x160 }
  - { offsetInCU: 0x369C, offset: 0x31252, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI10onProcLoadEPN11UserPatcher8ProcInfoEmPFvPvRS0_P8ipc_portPKcmES3_PNS0_13BinaryModInfoEm, symObjAddr: 0x4B0, symBinAddr: 0xC340, symSize: 0x210 }
  - { offsetInCU: 0x3998, offset: 0x3154E, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI10onProcLoadEPN11UserPatcher8ProcInfoEmPFvPvRS0_P8ipc_portPKcmES3_PNS0_13BinaryModInfoEm, symObjAddr: 0x4B0, symBinAddr: 0xC340, symSize: 0x210 }
  - { offsetInCU: 0x3BFD, offset: 0x317B3, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI20onEntitlementRequestEPFvPvP4taskPKcRP8OSObjectES0_, symObjAddr: 0x6C0, symBinAddr: 0xC550, symSize: 0xC0 }
  - { offsetInCU: 0x3CC8, offset: 0x3187E, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI16finaliseRequestsEv, symObjAddr: 0x780, symBinAddr: 0xC610, symSize: 0x30 }
  - { offsetInCU: 0x3F0C, offset: 0x31AC2, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI27processPatcherLoadCallbacksER13KernelPatcher, symObjAddr: 0x7B0, symBinAddr: 0xC640, symSize: 0x2E0 }
  - { offsetInCU: 0x41B2, offset: 0x31D68, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI21copyClientEntitlementEP4taskPKc, symObjAddr: 0xA90, symBinAddr: 0xC920, symSize: 0x90 }
  - { offsetInCU: 0x4255, offset: 0x31E0B, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI24processKextLoadCallbacksER13KernelPatchermymb, symObjAddr: 0xB20, symBinAddr: 0xC9B0, symSize: 0x70 }
  - { offsetInCU: 0x42F7, offset: 0x31EAD, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI24processUserLoadCallbacksER11UserPatcher, symObjAddr: 0xB90, symBinAddr: 0xCA20, symSize: 0xE0 }
  - { offsetInCU: 0x43C4, offset: 0x31F7A, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI26processBinaryLoadCallbacksER11UserPatcherP8ipc_portPKcm, symObjAddr: 0xC70, symBinAddr: 0xCB00, symSize: 0x50 }
  - { offsetInCU: 0x442E, offset: 0x31FE4, size: 0x4, addend: 0x0, symName: __ZN7LiluAPI8activateER13KernelPatcherR11UserPatcher, symObjAddr: 0xCC0, symBinAddr: 0xCB50, symSize: 0x30 }
  - { offsetInCU: 0x452B, offset: 0x320E1, size: 0x4, addend: 0x0, symName: '__ZZN7LiluAPI27processPatcherLoadCallbacksER13KernelPatcherEN3$_08__invokeEPNS0_11KextHandlerE', symObjAddr: 0xCF0, symBinAddr: 0xCB80, symSize: 0xA0 }
  - { offsetInCU: 0x46BC, offset: 0x32272, size: 0x4, addend: 0x0, symName: '__ZZN7LiluAPI24processUserLoadCallbacksER11UserPatcherEN3$_18__invokeEPvS1_P8ipc_portPKcm', symObjAddr: 0xD90, symBinAddr: 0xCC20, symSize: 0x4B }
  - { offsetInCU: 0x27, offset: 0x323AF, size: 0x4, addend: 0x0, symName: __ZN10DeviceInfo14updateLayoutIdEv, symObjAddr: 0x0, symBinAddr: 0xCC70, symSize: 0xB0 }
  - { offsetInCU: 0x2B, offset: 0x323B3, size: 0x4, addend: 0x0, symName: _globalBaseDeviceInfo, symObjAddr: 0x2258, symBinAddr: 0x43878, symSize: 0x0 }
  - { offsetInCU: 0x3D, offset: 0x323C5, size: 0x4, addend: 0x0, symName: _globalBaseDeviceInfo, symObjAddr: 0x2258, symBinAddr: 0x43878, symSize: 0x0 }
  - { offsetInCU: 0x2EC8, offset: 0x35250, size: 0x4, addend: 0x0, symName: _globalDeviceInfo, symObjAddr: 0x21348, symBinAddr: 0x46EE4, symSize: 0x0 }
  - { offsetInCU: 0x5B51, offset: 0x37ED9, size: 0x4, addend: 0x0, symName: __ZN10DeviceInfo14updateLayoutIdEv, symObjAddr: 0x0, symBinAddr: 0xCC70, symSize: 0xB0 }
  - { offsetInCU: 0x5C45, offset: 0x37FCD, size: 0x4, addend: 0x0, symName: __ZN10DeviceInfo19updateFramebufferIdEv, symObjAddr: 0xB0, symBinAddr: 0xCD20, symSize: 0x470 }
  - { offsetInCU: 0x5D8F, offset: 0x38117, size: 0x4, addend: 0x0, symName: __ZN14BaseDeviceInfo3getEv, symObjAddr: 0x520, symBinAddr: 0xD190, symSize: 0x10 }
  - { offsetInCU: 0x5DA2, offset: 0x3812A, size: 0x4, addend: 0x0, symName: __ZN10DeviceInfo22getLegacyFramebufferIdEv, symObjAddr: 0x530, symBinAddr: 0xD1A0, symSize: 0x1C0 }
  - { offsetInCU: 0x5DFE, offset: 0x38186, size: 0x4, addend: 0x0, symName: __ZN10DeviceInfo25isConnectorLessPlatformIdEj, symObjAddr: 0x6F0, symBinAddr: 0xD360, symSize: 0xF0 }
  - { offsetInCU: 0x5E39, offset: 0x381C1, size: 0x4, addend: 0x0, symName: __ZN10DeviceInfo15awaitPublishingEP15IORegistryEntry, symObjAddr: 0x7E0, symBinAddr: 0xD450, symSize: 0xC0 }
  - { offsetInCU: 0x5E9B, offset: 0x38223, size: 0x4, addend: 0x0, symName: __ZN10DeviceInfo21checkForAndSetAMDiGPUEP15IORegistryEntry, symObjAddr: 0x8A0, symBinAddr: 0xD510, symSize: 0x120 }
  - { offsetInCU: 0x5FDA, offset: 0x38362, size: 0x4, addend: 0x0, symName: __ZN10DeviceInfo22grabDevicesFromPciRootEP15IORegistryEntry, symObjAddr: 0x9C0, symBinAddr: 0xD630, symSize: 0x7E0 }
  - { offsetInCU: 0x6367, offset: 0x386EF, size: 0x4, addend: 0x0, symName: __ZN10DeviceInfo12createCachedEv, symObjAddr: 0x11A0, symBinAddr: 0xDE10, symSize: 0x50 }
  - { offsetInCU: 0x636B, offset: 0x386F3, size: 0x4, addend: 0x0, symName: __ZN10DeviceInfo6createEv, symObjAddr: 0x11F0, symBinAddr: 0xDE60, symSize: 0x3D0 }
  - { offsetInCU: 0x642E, offset: 0x387B6, size: 0x4, addend: 0x0, symName: __ZN10DeviceInfo6createEv, symObjAddr: 0x11F0, symBinAddr: 0xDE60, symSize: 0x3D0 }
  - { offsetInCU: 0x66F9, offset: 0x38A81, size: 0x4, addend: 0x0, symName: __ZN10DeviceInfo7deleterEPS_, symObjAddr: 0x15C0, symBinAddr: 0xE230, symSize: 0x50 }
  - { offsetInCU: 0x679E, offset: 0x38B26, size: 0x4, addend: 0x0, symName: __ZN10DeviceInfo16processSwitchOffEv, symObjAddr: 0x1610, symBinAddr: 0xE280, symSize: 0x5F0 }
  - { offsetInCU: 0x6AA3, offset: 0x38E2B, size: 0x4, addend: 0x0, symName: __ZN14BaseDeviceInfo20updateFirmwareVendorEv, symObjAddr: 0x1C00, symBinAddr: 0xE870, symSize: 0x2D0 }
  - { offsetInCU: 0x6B7B, offset: 0x38F03, size: 0x4, addend: 0x0, symName: __ZN14BaseDeviceInfo15updateModelInfoEv, symObjAddr: 0x1ED0, symBinAddr: 0xEB40, symSize: 0x360 }
  - { offsetInCU: 0x6C6A, offset: 0x38FF2, size: 0x4, addend: 0x0, symName: __ZN14BaseDeviceInfo4initEv, symObjAddr: 0x2230, symBinAddr: 0xEEA0, symSize: 0x28 }
  - { offsetInCU: 0x6C6E, offset: 0x38FF6, size: 0x4, addend: 0x0, symName: _globalBaseDeviceInfo, symObjAddr: 0x2258, symBinAddr: 0x43878, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x393B5, size: 0x4, addend: 0x0, symName: _MCInst_Init, symObjAddr: 0x0, symBinAddr: 0xEED0, symSize: 0x170 }
  - { offsetInCU: 0x15F2, offset: 0x3A980, size: 0x4, addend: 0x0, symName: _MCInst_Init, symObjAddr: 0x0, symBinAddr: 0xEED0, symSize: 0x170 }
  - { offsetInCU: 0x1627, offset: 0x3A9B5, size: 0x4, addend: 0x0, symName: _MCInst_clear, symObjAddr: 0x170, symBinAddr: 0xF040, symSize: 0x10 }
  - { offsetInCU: 0x164C, offset: 0x3A9DA, size: 0x4, addend: 0x0, symName: _MCInst_insert0, symObjAddr: 0x180, symBinAddr: 0xF050, symSize: 0x90 }
  - { offsetInCU: 0x1650, offset: 0x3A9DE, size: 0x4, addend: 0x0, symName: _MCInst_setOpcode, symObjAddr: 0x210, symBinAddr: 0xF0E0, symSize: 0x10 }
  - { offsetInCU: 0x169F, offset: 0x3AA2D, size: 0x4, addend: 0x0, symName: _MCInst_setOpcode, symObjAddr: 0x210, symBinAddr: 0xF0E0, symSize: 0x10 }
  - { offsetInCU: 0x16D3, offset: 0x3AA61, size: 0x4, addend: 0x0, symName: _MCInst_setOpcodePub, symObjAddr: 0x220, symBinAddr: 0xF0F0, symSize: 0x10 }
  - { offsetInCU: 0x1707, offset: 0x3AA95, size: 0x4, addend: 0x0, symName: _MCInst_getOpcode, symObjAddr: 0x230, symBinAddr: 0xF100, symSize: 0x10 }
  - { offsetInCU: 0x1730, offset: 0x3AABE, size: 0x4, addend: 0x0, symName: _MCInst_getOpcodePub, symObjAddr: 0x240, symBinAddr: 0xF110, symSize: 0x10 }
  - { offsetInCU: 0x1759, offset: 0x3AAE7, size: 0x4, addend: 0x0, symName: _MCInst_getOperand, symObjAddr: 0x250, symBinAddr: 0xF120, symSize: 0x20 }
  - { offsetInCU: 0x1791, offset: 0x3AB1F, size: 0x4, addend: 0x0, symName: _MCInst_getNumOperands, symObjAddr: 0x270, symBinAddr: 0xF140, symSize: 0x10 }
  - { offsetInCU: 0x17BA, offset: 0x3AB48, size: 0x4, addend: 0x0, symName: _MCInst_addOperand2, symObjAddr: 0x280, symBinAddr: 0xF150, symSize: 0x30 }
  - { offsetInCU: 0x17EE, offset: 0x3AB7C, size: 0x4, addend: 0x0, symName: _MCOperand_Init, symObjAddr: 0x2B0, symBinAddr: 0xF180, symSize: 0x20 }
  - { offsetInCU: 0x1813, offset: 0x3ABA1, size: 0x4, addend: 0x0, symName: _MCOperand_isValid, symObjAddr: 0x2D0, symBinAddr: 0xF1A0, symSize: 0x10 }
  - { offsetInCU: 0x183C, offset: 0x3ABCA, size: 0x4, addend: 0x0, symName: _MCOperand_isReg, symObjAddr: 0x2E0, symBinAddr: 0xF1B0, symSize: 0x10 }
  - { offsetInCU: 0x1865, offset: 0x3ABF3, size: 0x4, addend: 0x0, symName: _MCOperand_isImm, symObjAddr: 0x2F0, symBinAddr: 0xF1C0, symSize: 0x10 }
  - { offsetInCU: 0x188E, offset: 0x3AC1C, size: 0x4, addend: 0x0, symName: _MCOperand_isFPImm, symObjAddr: 0x300, symBinAddr: 0xF1D0, symSize: 0x10 }
  - { offsetInCU: 0x18B7, offset: 0x3AC45, size: 0x4, addend: 0x0, symName: _MCOperand_getReg, symObjAddr: 0x310, symBinAddr: 0xF1E0, symSize: 0x10 }
  - { offsetInCU: 0x18E0, offset: 0x3AC6E, size: 0x4, addend: 0x0, symName: _MCOperand_setReg, symObjAddr: 0x320, symBinAddr: 0xF1F0, symSize: 0x10 }
  - { offsetInCU: 0x1914, offset: 0x3ACA2, size: 0x4, addend: 0x0, symName: _MCOperand_getImm, symObjAddr: 0x330, symBinAddr: 0xF200, symSize: 0x10 }
  - { offsetInCU: 0x193D, offset: 0x3ACCB, size: 0x4, addend: 0x0, symName: _MCOperand_setImm, symObjAddr: 0x340, symBinAddr: 0xF210, symSize: 0x20 }
  - { offsetInCU: 0x1971, offset: 0x3ACFF, size: 0x4, addend: 0x0, symName: _MCOperand_getFPImm, symObjAddr: 0x360, symBinAddr: 0xF230, symSize: 0x10 }
  - { offsetInCU: 0x199A, offset: 0x3AD28, size: 0x4, addend: 0x0, symName: _MCOperand_setFPImm, symObjAddr: 0x370, symBinAddr: 0xF240, symSize: 0x20 }
  - { offsetInCU: 0x19CE, offset: 0x3AD5C, size: 0x4, addend: 0x0, symName: _MCOperand_CreateReg1, symObjAddr: 0x390, symBinAddr: 0xF260, symSize: 0x20 }
  - { offsetInCU: 0x1A16, offset: 0x3ADA4, size: 0x4, addend: 0x0, symName: _MCOperand_CreateReg0, symObjAddr: 0x3B0, symBinAddr: 0xF280, symSize: 0x30 }
  - { offsetInCU: 0x1A55, offset: 0x3ADE3, size: 0x4, addend: 0x0, symName: _MCOperand_CreateImm1, symObjAddr: 0x3E0, symBinAddr: 0xF2B0, symSize: 0x30 }
  - { offsetInCU: 0x1A9D, offset: 0x3AE2B, size: 0x4, addend: 0x0, symName: _MCOperand_CreateImm0, symObjAddr: 0x410, symBinAddr: 0xF2E0, symSize: 0x2E }
  - { offsetInCU: 0x27, offset: 0x3C166, size: 0x4, addend: 0x0, symName: __Z5qsortPvmmPFiPKvS1_E, symObjAddr: 0x0, symBinAddr: 0xF310, symSize: 0xB3D }
  - { offsetInCU: 0x15B, offset: 0x3C29A, size: 0x4, addend: 0x0, symName: __Z5qsortPvmmPFiPKvS1_E, symObjAddr: 0x0, symBinAddr: 0xF310, symSize: 0xB3D }
  - { offsetInCU: 0x27, offset: 0x3C992, size: 0x4, addend: 0x0, symName: _X86_global_init, symObjAddr: 0x0, symBinAddr: 0xFE50, symSize: 0x80 }
  - { offsetInCU: 0x1630, offset: 0x3DF9B, size: 0x4, addend: 0x0, symName: _X86_global_init, symObjAddr: 0x0, symBinAddr: 0xFE50, symSize: 0x80 }
  - { offsetInCU: 0x1669, offset: 0x3DFD4, size: 0x4, addend: 0x0, symName: _X86_option, symObjAddr: 0x80, symBinAddr: 0xFED0, symSize: 0x6D }
  - { offsetInCU: 0x27, offset: 0x3F52C, size: 0x4, addend: 0x0, symName: _MCRegisterInfo_InitMCRegisterInfo, symObjAddr: 0x0, symBinAddr: 0xFF40, symSize: 0x50 }
  - { offsetInCU: 0x58, offset: 0x3F55D, size: 0x4, addend: 0x0, symName: _MCRegisterInfo_InitMCRegisterInfo, symObjAddr: 0x0, symBinAddr: 0xFF40, symSize: 0x50 }
  - { offsetInCU: 0x4A6, offset: 0x3F9AB, size: 0x4, addend: 0x0, symName: _MCRegisterInfo_getMatchingSuperReg, symObjAddr: 0x50, symBinAddr: 0xFF90, symSize: 0x120 }
  - { offsetInCU: 0x5F9, offset: 0x3FAFE, size: 0x4, addend: 0x0, symName: _MCRegisterClass_contains, symObjAddr: 0x170, symBinAddr: 0x100B0, symSize: 0x40 }
  - { offsetInCU: 0x627, offset: 0x3FB2C, size: 0x4, addend: 0x0, symName: _MCRegisterInfo_getSubReg, symObjAddr: 0x1B0, symBinAddr: 0x100F0, symSize: 0x80 }
  - { offsetInCU: 0x699, offset: 0x3FB9E, size: 0x4, addend: 0x0, symName: _MCRegisterInfo_getRegClass, symObjAddr: 0x230, symBinAddr: 0x10170, symSize: 0x1B }
  - { offsetInCU: 0x27, offset: 0x3FC02, size: 0x4, addend: 0x0, symName: __ZN9NVStorage4initEv, symObjAddr: 0x0, symBinAddr: 0x10190, symSize: 0xB0 }
  - { offsetInCU: 0x2772, offset: 0x4234D, size: 0x4, addend: 0x0, symName: __ZN9NVStorage4saveEPKcjb, symObjAddr: 0xE70, symBinAddr: 0x11000, symSize: 0x170 }
  - { offsetInCU: 0x60AD, offset: 0x45C88, size: 0x4, addend: 0x0, symName: __ZN9NVStorage4initEv, symObjAddr: 0x0, symBinAddr: 0x10190, symSize: 0xB0 }
  - { offsetInCU: 0x60D2, offset: 0x45CAD, size: 0x4, addend: 0x0, symName: __ZN9NVStorage6deinitEv, symObjAddr: 0xB0, symBinAddr: 0x10240, symSize: 0x30 }
  - { offsetInCU: 0x60F7, offset: 0x45CD2, size: 0x4, addend: 0x0, symName: __ZN9NVStorage4readEPKcRjhPKh, symObjAddr: 0xE0, symBinAddr: 0x10270, symSize: 0x340 }
  - { offsetInCU: 0x638C, offset: 0x45F67, size: 0x4, addend: 0x0, symName: __ZN9NVStorage10decompressEPKhRjb, symObjAddr: 0x420, symBinAddr: 0x105B0, symSize: 0x110 }
  - { offsetInCU: 0x645B, offset: 0x46036, size: 0x4, addend: 0x0, symName: __ZN9NVStorage4readEPKchPKh, symObjAddr: 0x530, symBinAddr: 0x106C0, symSize: 0xE0 }
  - { offsetInCU: 0x6515, offset: 0x460F0, size: 0x4, addend: 0x0, symName: __ZN9NVStorage5writeEPKcPKhjhS3_, symObjAddr: 0x610, symBinAddr: 0x107A0, symSize: 0x520 }
  - { offsetInCU: 0x68D3, offset: 0x464AE, size: 0x4, addend: 0x0, symName: __ZN9NVStorage8compressEPKhRjb, symObjAddr: 0xB30, symBinAddr: 0x10CC0, symSize: 0x130 }
  - { offsetInCU: 0x69D9, offset: 0x465B4, size: 0x4, addend: 0x0, symName: __ZN9NVStorage5writeEPKcPK6OSDatahPKh, symObjAddr: 0xC60, symBinAddr: 0x10DF0, symSize: 0x70 }
  - { offsetInCU: 0x69DD, offset: 0x465B8, size: 0x4, addend: 0x0, symName: __ZN9NVStorage6removeEPKcb, symObjAddr: 0xCD0, symBinAddr: 0x10E60, symSize: 0x150 }
  - { offsetInCU: 0x6A64, offset: 0x4663F, size: 0x4, addend: 0x0, symName: __ZN9NVStorage6removeEPKcb, symObjAddr: 0xCD0, symBinAddr: 0x10E60, symSize: 0x150 }
  - { offsetInCU: 0x6B0E, offset: 0x466E9, size: 0x4, addend: 0x0, symName: __ZN9NVStorage4syncEv, symObjAddr: 0xE20, symBinAddr: 0x10FB0, symSize: 0x50 }
  - { offsetInCU: 0x6B36, offset: 0x46711, size: 0x4, addend: 0x0, symName: __ZN9NVStorage6existsEPKc, symObjAddr: 0xFE0, symBinAddr: 0x11170, symSize: 0x36 }
  - { offsetInCU: 0x27, offset: 0x4676E, size: 0x4, addend: 0x0, symName: _printSrcIdx8, symObjAddr: 0x0, symBinAddr: 0x111B0, symSize: 0x40 }
  - { offsetInCU: 0x162C, offset: 0x47D73, size: 0x4, addend: 0x0, symName: _printSrcIdx8, symObjAddr: 0x0, symBinAddr: 0x111B0, symSize: 0x40 }
  - { offsetInCU: 0x2981, offset: 0x490C8, size: 0x4, addend: 0x0, symName: _printSrcIdx, symObjAddr: 0x40, symBinAddr: 0x111F0, symSize: 0x210 }
  - { offsetInCU: 0x2A8B, offset: 0x491D2, size: 0x4, addend: 0x0, symName: _printSrcIdx16, symObjAddr: 0x250, symBinAddr: 0x11400, symSize: 0x40 }
  - { offsetInCU: 0x2AB3, offset: 0x491FA, size: 0x4, addend: 0x0, symName: _printSrcIdx32, symObjAddr: 0x290, symBinAddr: 0x11440, symSize: 0x40 }
  - { offsetInCU: 0x2ADB, offset: 0x49222, size: 0x4, addend: 0x0, symName: _printSrcIdx64, symObjAddr: 0x2D0, symBinAddr: 0x11480, symSize: 0x40 }
  - { offsetInCU: 0x2B03, offset: 0x4924A, size: 0x4, addend: 0x0, symName: _printDstIdx8, symObjAddr: 0x310, symBinAddr: 0x114C0, symSize: 0x40 }
  - { offsetInCU: 0x2B2B, offset: 0x49272, size: 0x4, addend: 0x0, symName: _printDstIdx, symObjAddr: 0x350, symBinAddr: 0x11500, symSize: 0x190 }
  - { offsetInCU: 0x2B2F, offset: 0x49276, size: 0x4, addend: 0x0, symName: _printDstIdx16, symObjAddr: 0x4E0, symBinAddr: 0x11690, symSize: 0x40 }
  - { offsetInCU: 0x2BB4, offset: 0x492FB, size: 0x4, addend: 0x0, symName: _printDstIdx16, symObjAddr: 0x4E0, symBinAddr: 0x11690, symSize: 0x40 }
  - { offsetInCU: 0x2BDC, offset: 0x49323, size: 0x4, addend: 0x0, symName: _printDstIdx32, symObjAddr: 0x520, symBinAddr: 0x116D0, symSize: 0x40 }
  - { offsetInCU: 0x2C04, offset: 0x4934B, size: 0x4, addend: 0x0, symName: _printDstIdx64, symObjAddr: 0x560, symBinAddr: 0x11710, symSize: 0x40 }
  - { offsetInCU: 0x3225, offset: 0x4996C, size: 0x4, addend: 0x0, symName: _X86_Intel_printInst, symObjAddr: 0x5A0, symBinAddr: 0x11750, symSize: 0xF90 }
  - { offsetInCU: 0x397E, offset: 0x4A0C5, size: 0x4, addend: 0x0, symName: _printOperand, symObjAddr: 0x1530, symBinAddr: 0x126E0, symSize: 0x400 }
  - { offsetInCU: 0x3B36, offset: 0x4A27D, size: 0x4, addend: 0x0, symName: _printMemReference, symObjAddr: 0x1930, symBinAddr: 0x12AE0, symSize: 0x4A0 }
  - { offsetInCU: 0x3D0E, offset: 0x4A455, size: 0x4, addend: 0x0, symName: _printMemOffset, symObjAddr: 0x1DD0, symBinAddr: 0x12F80, symSize: 0x29E }
  - { offsetInCU: 0x10, offset: 0x4A54E, size: 0x4, addend: 0x0, symName: _performEfiCallAsm64, symObjAddr: 0x0, symBinAddr: 0x1321E, symSize: 0x64 }
  - { offsetInCU: 0x3C, offset: 0x4A57A, size: 0x4, addend: 0x0, symName: _performEfiCallAsm64, symObjAddr: 0x0, symBinAddr: 0x1321E, symSize: 0x64 }
  - { offsetInCU: 0x4E, offset: 0x4A58C, size: 0x4, addend: 0x0, symName: _performEfiCallAsm32, symObjAddr: 0x64, symBinAddr: 0x13282, symSize: 0x4B }
  - { offsetInCU: 0x27, offset: 0x4A5A9, size: 0x4, addend: 0x0, symName: _cs_version, symObjAddr: 0x0, symBinAddr: 0x132D0, symSize: 0x30 }
  - { offsetInCU: 0x2B, offset: 0x4A5AD, size: 0x4, addend: 0x0, symName: _cs_arch_disallowed_mode_mask, symObjAddr: 0x15BC, symBinAddr: 0x43904, symSize: 0x0 }
  - { offsetInCU: 0x3D, offset: 0x4A5BF, size: 0x4, addend: 0x0, symName: _cs_arch_disallowed_mode_mask, symObjAddr: 0x15BC, symBinAddr: 0x43904, symSize: 0x0 }
  - { offsetInCU: 0xDE, offset: 0x4A660, size: 0x4, addend: 0x0, symName: _cs_mem_malloc, symObjAddr: 0xBD00, symBinAddr: 0x46EE8, symSize: 0x0 }
  - { offsetInCU: 0x12C, offset: 0x4A6AE, size: 0x4, addend: 0x0, symName: _cs_mem_calloc, symObjAddr: 0xBD04, symBinAddr: 0x46EEC, symSize: 0x0 }
  - { offsetInCU: 0x161, offset: 0x4A6E3, size: 0x4, addend: 0x0, symName: _cs_mem_realloc, symObjAddr: 0xBD08, symBinAddr: 0x46EF0, symSize: 0x0 }
  - { offsetInCU: 0x196, offset: 0x4A718, size: 0x4, addend: 0x0, symName: _cs_mem_free, symObjAddr: 0xBD0C, symBinAddr: 0x46EF4, symSize: 0x0 }
  - { offsetInCU: 0x1C2, offset: 0x4A744, size: 0x4, addend: 0x0, symName: _cs_vsnprintf, symObjAddr: 0xBD10, symBinAddr: 0x46EF8, symSize: 0x0 }
  - { offsetInCU: 0x29F2, offset: 0x4CF74, size: 0x4, addend: 0x0, symName: _cs_arch_option, symObjAddr: 0x17D4, symBinAddr: 0x3CCA0, symSize: 0x0 }
  - { offsetInCU: 0x2AC0, offset: 0x4D042, size: 0x4, addend: 0x0, symName: _cs_version, symObjAddr: 0x0, symBinAddr: 0x132D0, symSize: 0x30 }
  - { offsetInCU: 0x2AF8, offset: 0x4D07A, size: 0x4, addend: 0x0, symName: _cs_support, symObjAddr: 0x30, symBinAddr: 0x13300, symSize: 0x30 }
  - { offsetInCU: 0x2B21, offset: 0x4D0A3, size: 0x4, addend: 0x0, symName: _cs_errno, symObjAddr: 0x60, symBinAddr: 0x13330, symSize: 0x20 }
  - { offsetInCU: 0x2B5A, offset: 0x4D0DC, size: 0x4, addend: 0x0, symName: _cs_strerror, symObjAddr: 0x80, symBinAddr: 0x13350, symSize: 0xB0 }
  - { offsetInCU: 0x2B83, offset: 0x4D105, size: 0x4, addend: 0x0, symName: _cs_open, symObjAddr: 0x130, symBinAddr: 0x13400, symSize: 0xE0 }
  - { offsetInCU: 0x2BEA, offset: 0x4D16C, size: 0x4, addend: 0x0, symName: _cs_close, symObjAddr: 0x210, symBinAddr: 0x134E0, symSize: 0x70 }
  - { offsetInCU: 0x2C3F, offset: 0x4D1C1, size: 0x4, addend: 0x0, symName: _cs_option, symObjAddr: 0x280, symBinAddr: 0x13550, symSize: 0x110 }
  - { offsetInCU: 0x2D26, offset: 0x4D2A8, size: 0x4, addend: 0x0, symName: _cs_disasm, symObjAddr: 0x390, symBinAddr: 0x13660, symSize: 0x470 }
  - { offsetInCU: 0x2F06, offset: 0x4D488, size: 0x4, addend: 0x0, symName: _cs_disasm_ex, symObjAddr: 0x800, symBinAddr: 0x13AD0, symSize: 0x10 }
  - { offsetInCU: 0x2F7A, offset: 0x4D4FC, size: 0x4, addend: 0x0, symName: _cs_free, symObjAddr: 0x810, symBinAddr: 0x13AE0, symSize: 0x50 }
  - { offsetInCU: 0x2FBF, offset: 0x4D541, size: 0x4, addend: 0x0, symName: _cs_malloc, symObjAddr: 0x860, symBinAddr: 0x13B30, symSize: 0x70 }
  - { offsetInCU: 0x3008, offset: 0x4D58A, size: 0x4, addend: 0x0, symName: _cs_disasm_iter, symObjAddr: 0x8D0, symBinAddr: 0x13BA0, symSize: 0x1E0 }
  - { offsetInCU: 0x311F, offset: 0x4D6A1, size: 0x4, addend: 0x0, symName: _cs_reg_name, symObjAddr: 0xAB0, symBinAddr: 0x13D80, symSize: 0x20 }
  - { offsetInCU: 0x3167, offset: 0x4D6E9, size: 0x4, addend: 0x0, symName: _cs_insn_name, symObjAddr: 0xAD0, symBinAddr: 0x13DA0, symSize: 0x20 }
  - { offsetInCU: 0x31AF, offset: 0x4D731, size: 0x4, addend: 0x0, symName: _cs_group_name, symObjAddr: 0xAF0, symBinAddr: 0x13DC0, symSize: 0x20 }
  - { offsetInCU: 0x323C, offset: 0x4D7BE, size: 0x4, addend: 0x0, symName: _cs_insn_group, symObjAddr: 0xB10, symBinAddr: 0x13DE0, symSize: 0x90 }
  - { offsetInCU: 0x32C1, offset: 0x4D843, size: 0x4, addend: 0x0, symName: _cs_reg_read, symObjAddr: 0xBA0, symBinAddr: 0x13E70, symSize: 0x70 }
  - { offsetInCU: 0x3348, offset: 0x4D8CA, size: 0x4, addend: 0x0, symName: _cs_reg_write, symObjAddr: 0xC10, symBinAddr: 0x13EE0, symSize: 0x90 }
  - { offsetInCU: 0x33CD, offset: 0x4D94F, size: 0x4, addend: 0x0, symName: _cs_op_count, symObjAddr: 0xCA0, symBinAddr: 0x13F70, symSize: 0x690 }
  - { offsetInCU: 0x3444, offset: 0x4D9C6, size: 0x4, addend: 0x0, symName: _cs_op_index, symObjAddr: 0x1330, symBinAddr: 0x14600, symSize: 0x28C }
  - { offsetInCU: 0x3448, offset: 0x4D9CA, size: 0x4, addend: 0x0, symName: _cs_arch_disallowed_mode_mask, symObjAddr: 0x15BC, symBinAddr: 0x43904, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x4DA9B, size: 0x4, addend: 0x0, symName: __ZN11Compression10decompressEjjPKhjPh, symObjAddr: 0x0, symBinAddr: 0x14890, symSize: 0x30 }
  - { offsetInCU: 0x47, offset: 0x4DABB, size: 0x4, addend: 0x0, symName: __ZN11Compression10decompressEjjPKhjPh, symObjAddr: 0x0, symBinAddr: 0x14890, symSize: 0x30 }
  - { offsetInCU: 0xB6, offset: 0x4DB2A, size: 0x4, addend: 0x0, symName: __ZN11Compression10decompressEjPjPKhjPh, symObjAddr: 0x310, symBinAddr: 0x14BA0, symSize: 0x30 }
  - { offsetInCU: 0x124, offset: 0x4DB98, size: 0x4, addend: 0x0, symName: __ZN11Compression8compressEjRjPKhjPh, symObjAddr: 0x340, symBinAddr: 0x14BD0, symSize: 0x760 }
  - { offsetInCU: 0x8A6, offset: 0x4E31A, size: 0x4, addend: 0x0, symName: __ZL18decompressInternaljPjPKhjPhb, symObjAddr: 0x30, symBinAddr: 0x148C0, symSize: 0x2E0 }
  - { offsetInCU: 0xBCD, offset: 0x4E641, size: 0x4, addend: 0x0, symName: __ZL7z_allocPvjj, symObjAddr: 0xAA0, symBinAddr: 0x15330, symSize: 0x30 }
  - { offsetInCU: 0xC56, offset: 0x4E6CA, size: 0x4, addend: 0x0, symName: __ZL6z_freePvS_, symObjAddr: 0xAD0, symBinAddr: 0x15360, symSize: 0x20 }
  - { offsetInCU: 0xCAC, offset: 0x4E720, size: 0x4, addend: 0x0, symName: __ZL11insert_nodeP12encode_statei, symObjAddr: 0xAF0, symBinAddr: 0x15380, symSize: 0x307 }
  - { offsetInCU: 0x27, offset: 0x4E7C1, size: 0x4, addend: 0x0, symName: _MCOperandInfo_isPredicate, symObjAddr: 0x0, symBinAddr: 0x15690, symSize: 0x10 }
  - { offsetInCU: 0x30, offset: 0x4E7CA, size: 0x4, addend: 0x0, symName: _MCOperandInfo_isPredicate, symObjAddr: 0x0, symBinAddr: 0x15690, symSize: 0x10 }
  - { offsetInCU: 0x59, offset: 0x4E7F3, size: 0x4, addend: 0x0, symName: _MCOperandInfo_isOptionalDef, symObjAddr: 0x10, symBinAddr: 0x156A0, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x4E8EC, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher9vmProtectEP8ipc_portjjii, symObjAddr: 0x0, symBinAddr: 0x156B0, symSize: 0x190 }
  - { offsetInCU: 0x1F52, offset: 0x50817, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher13LookupStorage7deleterEPS0_, symObjAddr: 0x190, symBinAddr: 0x15840, symSize: 0xF0 }
  - { offsetInCU: 0x23BE, offset: 0x50C83, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher13injectPayloadEP8ipc_portPhmPv, symObjAddr: 0x3070, symBinAddr: 0x18720, symSize: 0x650 }
  - { offsetInCU: 0x261F, offset: 0x50EE4, size: 0x4, addend: 0x0, symName: __ZL4that, symObjAddr: 0x21008, symBinAddr: 0x46F2C, symSize: 0x0 }
  - { offsetInCU: 0x2885, offset: 0x5114A, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher9vmProtectEP8ipc_portjjii, symObjAddr: 0x0, symBinAddr: 0x156B0, symSize: 0x190 }
  - { offsetInCU: 0x2991, offset: 0x51256, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher13LookupStorage7deleterEPS0_, symObjAddr: 0x190, symBinAddr: 0x15840, symSize: 0xF0 }
  - { offsetInCU: 0x2A27, offset: 0x512EC, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher12execListenerEP5ucredPvimmmm, symObjAddr: 0x280, symBinAddr: 0x15930, symSize: 0x50 }
  - { offsetInCU: 0x2B91, offset: 0x51456, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher6onPathEPKcj, symObjAddr: 0x2D0, symBinAddr: 0x15980, symSize: 0x290 }
  - { offsetInCU: 0x2B95, offset: 0x5145A, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher4initER13KernelPatcherb, symObjAddr: 0x560, symBinAddr: 0x15C10, symSize: 0xA0 }
  - { offsetInCU: 0x2BDA, offset: 0x5149F, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher4initER13KernelPatcherb, symObjAddr: 0x560, symBinAddr: 0x15C10, symSize: 0xA0 }
  - { offsetInCU: 0x2D17, offset: 0x515DC, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher4initER13KernelPatcherb, symObjAddr: 0x560, symBinAddr: 0x15C10, symSize: 0xA0 }
  - { offsetInCU: 0x2D57, offset: 0x5161C, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher15registerPatchesEPPNS_8ProcInfoEmPPNS_13BinaryModInfoEmPFvPvRS_P8ipc_portPKcmES6_, symObjAddr: 0x600, symBinAddr: 0x15CB0, symSize: 0x130 }
  - { offsetInCU: 0x3098, offset: 0x5195D, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher20loadFilesForPatchingEv, symObjAddr: 0x730, symBinAddr: 0x15DE0, symSize: 0x700 }
  - { offsetInCU: 0x35E3, offset: 0x51EA8, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher26loadDyldSharedCacheMappingEv, symObjAddr: 0xE30, symBinAddr: 0x164E0, symSize: 0x250 }
  - { offsetInCU: 0x371D, offset: 0x51FE2, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher11loadLookupsEv, symObjAddr: 0x1080, symBinAddr: 0x16730, symSize: 0x400 }
  - { offsetInCU: 0x3E2E, offset: 0x526F3, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher16hookMemoryAccessEv, symObjAddr: 0x1480, symBinAddr: 0x16B30, symSize: 0x430 }
  - { offsetInCU: 0x4183, offset: 0x52A48, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher6deinitEv, symObjAddr: 0x18B0, symBinAddr: 0x16F60, symSize: 0x3F0 }
  - { offsetInCU: 0x4232, offset: 0x52AF7, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher16performPagePatchEPKvm, symObjAddr: 0x1CA0, symBinAddr: 0x17350, symSize: 0x2B0 }
  - { offsetInCU: 0x4236, offset: 0x52AFB, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher27codeSignValidatePageWrapperEPvP8ipc_portyPKvPj, symObjAddr: 0x1F50, symBinAddr: 0x17600, symSize: 0x50 }
  - { offsetInCU: 0x4386, offset: 0x52C4B, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher27codeSignValidatePageWrapperEPvP8ipc_portyPKvPj, symObjAddr: 0x1F50, symBinAddr: 0x17600, symSize: 0x50 }
  - { offsetInCU: 0x43FB, offset: 0x52CC0, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher28codeSignValidateRangeWrapperEPvP8ipc_portyPKvyPj, symObjAddr: 0x1FA0, symBinAddr: 0x17650, symSize: 0x60 }
  - { offsetInCU: 0x4480, offset: 0x52D45, size: 0x4, addend: 0x0, symName: __ZN11ThreadLocalIPN11UserPatcher11PendingUserELm32EE3getEv, symObjAddr: 0x2000, symBinAddr: 0x176B0, symSize: 0x280 }
  - { offsetInCU: 0x4484, offset: 0x52D49, size: 0x4, addend: 0x0, symName: __ZN11ThreadLocalIPN11UserPatcher11PendingUserELm32EE5eraseEv, symObjAddr: 0x2280, symBinAddr: 0x17930, symSize: 0x310 }
  - { offsetInCU: 0x44D0, offset: 0x52D95, size: 0x4, addend: 0x0, symName: __ZN11ThreadLocalIPN11UserPatcher11PendingUserELm32EE5eraseEv, symObjAddr: 0x2280, symBinAddr: 0x17930, symSize: 0x310 }
  - { offsetInCU: 0x453A, offset: 0x52DFF, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher11patchBinaryEP8ipc_portPKcj, symObjAddr: 0x2590, symBinAddr: 0x17C40, symSize: 0x90 }
  - { offsetInCU: 0x4624, offset: 0x52EE9, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher16patchSharedCacheEP8ipc_portjib, symObjAddr: 0x2620, symBinAddr: 0x17CD0, symSize: 0x2E0 }
  - { offsetInCU: 0x488D, offset: 0x53152, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher14injectRestrictEP8ipc_port, symObjAddr: 0x2900, symBinAddr: 0x17FB0, symSize: 0x590 }
  - { offsetInCU: 0x4AC7, offset: 0x5338C, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher13getTaskHeaderEP8ipc_portR14mach_header_64, symObjAddr: 0x2E90, symBinAddr: 0x18540, symSize: 0x60 }
  - { offsetInCU: 0x4B2B, offset: 0x533F0, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher17getPageProtectionEP8ipc_porty, symObjAddr: 0x2EF0, symBinAddr: 0x185A0, symSize: 0x90 }
  - { offsetInCU: 0x4B65, offset: 0x5342A, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher13injectSegmentEP8ipc_portjPhmi, symObjAddr: 0x2F80, symBinAddr: 0x18630, symSize: 0xF0 }
  - { offsetInCU: 0x4BFF, offset: 0x534C4, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher21vmSharedRegionMapFileEPvjPS0_P8ipc_portyS0_jyy, symObjAddr: 0x36C0, symBinAddr: 0x18D70, symSize: 0x60 }
  - { offsetInCU: 0x4C03, offset: 0x534C8, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher19vmSharedRegionSlideEjyyyyP8ipc_port, symObjAddr: 0x3720, symBinAddr: 0x18DD0, symSize: 0x40 }
  - { offsetInCU: 0x4CB4, offset: 0x53579, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher19vmSharedRegionSlideEjyyyyP8ipc_port, symObjAddr: 0x3720, symBinAddr: 0x18DD0, symSize: 0x40 }
  - { offsetInCU: 0x4D28, offset: 0x535ED, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher25vmSharedRegionSlideMojaveEjyyyyyP8ipc_port, symObjAddr: 0x3760, symBinAddr: 0x18E10, symSize: 0x40 }
  - { offsetInCU: 0x4DAC, offset: 0x53671, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher20taskSetMainThreadQosEP4taskP6thread, symObjAddr: 0x37A0, symBinAddr: 0x18E50, symSize: 0x110 }
  - { offsetInCU: 0x4E25, offset: 0x536EA, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher12mapAddressesEPKcPNS_8MapEntryEm, symObjAddr: 0x38B0, symBinAddr: 0x18F60, symSize: 0x280 }
  - { offsetInCU: 0x4F28, offset: 0x537ED, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher8activateEv, symObjAddr: 0x3B30, symBinAddr: 0x191E0, symSize: 0x10 }
  - { offsetInCU: 0x4F4F, offset: 0x53814, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher18getSharedCachePathEv, symObjAddr: 0x3B40, symBinAddr: 0x191F0, symSize: 0x60 }
  - { offsetInCU: 0x4F82, offset: 0x53847, size: 0x4, addend: 0x0, symName: __ZN11UserPatcher20matchSharedCachePathEPKc, symObjAddr: 0x3BA0, symBinAddr: 0x19250, symSize: 0xCC }
  - { offsetInCU: 0x27, offset: 0x53917, size: 0x4, addend: 0x0, symName: _hde32_disasm, symObjAddr: 0x0, symBinAddr: 0x19320, symSize: 0x1192 }
  - { offsetInCU: 0x2B, offset: 0x5391B, size: 0x4, addend: 0x0, symName: _hde32_table, symObjAddr: 0x1192, symBinAddr: 0x43924, symSize: 0x0 }
  - { offsetInCU: 0x3D, offset: 0x5392D, size: 0x4, addend: 0x0, symName: _hde32_table, symObjAddr: 0x1192, symBinAddr: 0x43924, symSize: 0x0 }
  - { offsetInCU: 0xB2, offset: 0x539A2, size: 0x4, addend: 0x0, symName: _hde32_disasm, symObjAddr: 0x0, symBinAddr: 0x19320, symSize: 0x1192 }
  - { offsetInCU: 0xB6, offset: 0x539A6, size: 0x4, addend: 0x0, symName: _hde32_table, symObjAddr: 0x1192, symBinAddr: 0x43924, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x53CE8, size: 0x4, addend: 0x0, symName: __ZN7CPUInfo4initEv, symObjAddr: 0x0, symBinAddr: 0x1A4C0, symSize: 0x390 }
  - { offsetInCU: 0x14E, offset: 0x53E0F, size: 0x4, addend: 0x0, symName: __ZN7CPUInfo4initEv, symObjAddr: 0x0, symBinAddr: 0x1A4C0, symSize: 0x390 }
  - { offsetInCU: 0x3DC, offset: 0x5409D, size: 0x4, addend: 0x0, symName: __ZN7CPUInfo8getCpuidEjjPjS0_S0_S0_, symObjAddr: 0x390, symBinAddr: 0x1A850, symSize: 0x80 }
  - { offsetInCU: 0x45B, offset: 0x5411C, size: 0x4, addend: 0x0, symName: __ZN7CPUInfo13getGenerationEPjS0_S0_, symObjAddr: 0x410, symBinAddr: 0x1A8D0, symSize: 0x40 }
  - { offsetInCU: 0x4B5, offset: 0x54176, size: 0x4, addend: 0x0, symName: __ZN7CPUInfo14getCpuTopologyERNS_11CpuTopologyE, symObjAddr: 0x450, symBinAddr: 0x1A910, symSize: 0x27 }
  - { offsetInCU: 0x27, offset: 0x543EA, size: 0x4, addend: 0x0, symName: _lilu_os_log, symObjAddr: 0x0, symBinAddr: 0x1A940, symSize: 0x80 }
  - { offsetInCU: 0x3D, offset: 0x54400, size: 0x4, addend: 0x0, symName: _Lilu_debugEnabled, symObjAddr: 0x77FC, symBinAddr: 0x46EFC, symSize: 0x0 }
  - { offsetInCU: 0x57, offset: 0x5441A, size: 0x4, addend: 0x0, symName: _Lilu_debugPrintDelay, symObjAddr: 0x7800, symBinAddr: 0x46F00, symSize: 0x0 }
  - { offsetInCU: 0x6A, offset: 0x5442D, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapper10gMetaClassE, symObjAddr: 0x7804, symBinAddr: 0x46F04, symSize: 0x0 }
  - { offsetInCU: 0x886, offset: 0x54C49, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapper9metaClassE, symObjAddr: 0x5AC, symBinAddr: 0x3CCC0, symSize: 0x0 }
  - { offsetInCU: 0x895, offset: 0x54C58, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapper10superClassE, symObjAddr: 0x5B0, symBinAddr: 0x3CCC4, symSize: 0x0 }
  - { offsetInCU: 0x8C4, offset: 0x54C87, size: 0x4, addend: 0x0, symName: _lilu_os_log, symObjAddr: 0x0, symBinAddr: 0x1A940, symSize: 0x80 }
  - { offsetInCU: 0x942, offset: 0x54D05, size: 0x4, addend: 0x0, symName: __Z6strstrPKcS0_m, symObjAddr: 0x80, symBinAddr: 0x1A9C0, symSize: 0x60 }
  - { offsetInCU: 0x99E, offset: 0x54D61, size: 0x4, addend: 0x0, symName: __Z7strrchrPKci, symObjAddr: 0xE0, symBinAddr: 0x1AA20, symSize: 0x30 }
  - { offsetInCU: 0x9EA, offset: 0x54DAD, size: 0x4, addend: 0x0, symName: _kern_os_calloc, symObjAddr: 0x110, symBinAddr: 0x1AA50, symSize: 0x20 }
  - { offsetInCU: 0xA21, offset: 0x54DE4, size: 0x4, addend: 0x0, symName: _lilu_os_free, symObjAddr: 0x130, symBinAddr: 0x1AA70, symSize: 0x20 }
  - { offsetInCU: 0xA45, offset: 0x54E08, size: 0x4, addend: 0x0, symName: __Z12lilu_strlcpyPcPKcm, symObjAddr: 0x150, symBinAddr: 0x1AA90, symSize: 0x60 }
  - { offsetInCU: 0xB61, offset: 0x54F24, size: 0x4, addend: 0x0, symName: __ZN4Page5allocEv, symObjAddr: 0x1B0, symBinAddr: 0x1AAF0, symSize: 0x60 }
  - { offsetInCU: 0xB86, offset: 0x54F49, size: 0x4, addend: 0x0, symName: __ZN4Page7protectEi, symObjAddr: 0x210, symBinAddr: 0x1AB50, symSize: 0x40 }
  - { offsetInCU: 0xBF6, offset: 0x54FB9, size: 0x4, addend: 0x0, symName: __ZN4Page6createEv, symObjAddr: 0x250, symBinAddr: 0x1AB90, symSize: 0x20 }
  - { offsetInCU: 0xC3E, offset: 0x55001, size: 0x4, addend: 0x0, symName: __ZN4Page7deleterEPS_, symObjAddr: 0x270, symBinAddr: 0x1ABB0, symSize: 0x30 }
  - { offsetInCU: 0xC82, offset: 0x55045, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapper9MetaClassC1Ev, symObjAddr: 0x2A0, symBinAddr: 0x1ABE0, symSize: 0x30 }
  - { offsetInCU: 0xCD9, offset: 0x5509C, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapper9MetaClassD1Ev, symObjAddr: 0x2D0, symBinAddr: 0x1AC10, symSize: 0x10 }
  - { offsetInCU: 0xD13, offset: 0x550D6, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapperC2EPK11OSMetaClass, symObjAddr: 0x2E0, symBinAddr: 0x1AC20, symSize: 0x30 }
  - { offsetInCU: 0xD63, offset: 0x55126, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapperC1EPK11OSMetaClass, symObjAddr: 0x310, symBinAddr: 0x1AC50, symSize: 0x30 }
  - { offsetInCU: 0xDBF, offset: 0x55182, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapperD2Ev, symObjAddr: 0x340, symBinAddr: 0x1AC80, symSize: 0x10 }
  - { offsetInCU: 0xDFA, offset: 0x551BD, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapperD1Ev, symObjAddr: 0x350, symBinAddr: 0x1AC90, symSize: 0x10 }
  - { offsetInCU: 0xE50, offset: 0x55213, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapperD0Ev, symObjAddr: 0x360, symBinAddr: 0x1ACA0, symSize: 0x30 }
  - { offsetInCU: 0xEAF, offset: 0x55272, size: 0x4, addend: 0x0, symName: __ZNK15OSObjectWrapper12getMetaClassEv, symObjAddr: 0x390, symBinAddr: 0x1ACD0, symSize: 0x10 }
  - { offsetInCU: 0xED1, offset: 0x55294, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapper9MetaClassC2Ev, symObjAddr: 0x3A0, symBinAddr: 0x1ACE0, symSize: 0x30 }
  - { offsetInCU: 0xF29, offset: 0x552EC, size: 0x4, addend: 0x0, symName: __ZNK15OSObjectWrapper9MetaClass5allocEv, symObjAddr: 0x3D0, symBinAddr: 0x1AD10, symSize: 0x50 }
  - { offsetInCU: 0xF7F, offset: 0x55342, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapperC1Ev, symObjAddr: 0x420, symBinAddr: 0x1AD60, symSize: 0x40 }
  - { offsetInCU: 0xFB8, offset: 0x5537B, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapperC2Ev, symObjAddr: 0x460, symBinAddr: 0x1ADA0, symSize: 0x40 }
  - { offsetInCU: 0xFD6, offset: 0x55399, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapper4initEPv, symObjAddr: 0x4A0, symBinAddr: 0x1ADE0, symSize: 0x30 }
  - { offsetInCU: 0x1022, offset: 0x553E5, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapper4withEPv, symObjAddr: 0x4D0, symBinAddr: 0x1AE10, symSize: 0x70 }
  - { offsetInCU: 0x10CA, offset: 0x5548D, size: 0x4, addend: 0x0, symName: __ZN15OSObjectWrapper9MetaClassD0Ev, symObjAddr: 0x540, symBinAddr: 0x1AE80, symSize: 0x10 }
  - { offsetInCU: 0x1152, offset: 0x55515, size: 0x4, addend: 0x0, symName: __GLOBAL__sub_I_kern_util.cpp, symObjAddr: 0x550, symBinAddr: 0x1AE90, symSize: 0x30 }
  - { offsetInCU: 0x11A6, offset: 0x55569, size: 0x4, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x580, symBinAddr: 0x1AEC0, symSize: 0x17 }
  - { offsetInCU: 0x27, offset: 0x55606, size: 0x4, addend: 0x0, symName: __ZN8MachInfo4initEPKPKcmPS_b, symObjAddr: 0x0, symBinAddr: 0x1AEE0, symSize: 0x170 }
  - { offsetInCU: 0x10D, offset: 0x556EC, size: 0x4, addend: 0x0, symName: __ZN8MachInfo14findKernelBaseEv, symObjAddr: 0x990, symBinAddr: 0x1B870, symSize: 0x100 }
  - { offsetInCU: 0x12D, offset: 0x5570C, size: 0x4, addend: 0x0, symName: __ZZN8MachInfo14findKernelBaseEvE13m_kernel_base, symObjAddr: 0x25CC8, symBinAddr: 0x46F30, symSize: 0x0 }
  - { offsetInCU: 0x1E0, offset: 0x557BF, size: 0x4, addend: 0x0, symName: __ZN8MachInfo16setKernelWritingEbP13_IOSimpleLock, symObjAddr: 0x1620, symBinAddr: 0x1C500, symSize: 0xE0 }
  - { offsetInCU: 0x200, offset: 0x557DF, size: 0x4, addend: 0x0, symName: __ZZN8MachInfo16setKernelWritingEbP13_IOSimpleLockE18interruptsDisabled, symObjAddr: 0x25CD0, symBinAddr: 0x46F38, symSize: 0x0 }
  - { offsetInCU: 0x30B, offset: 0x558EA, size: 0x4, addend: 0x0, symName: __ZN8MachInfo9findImageEPKcRjRyRb, symObjAddr: 0xA90, symBinAddr: 0x1B970, symSize: 0x280 }
  - { offsetInCU: 0x32F, offset: 0x5590E, size: 0x4, addend: 0x0, symName: __ZZN8MachInfo9findImageEPKcRjRyRbE8imageArr, symObjAddr: 0x25CD4, symBinAddr: 0x46F3C, symSize: 0x0 }
  - { offsetInCU: 0x342, offset: 0x55921, size: 0x4, addend: 0x0, symName: __ZZN8MachInfo9findImageEPKcRjRyRbE8imageNum, symObjAddr: 0x25CD8, symBinAddr: 0x46F40, symSize: 0x0 }
  - { offsetInCU: 0x442, offset: 0x55A21, size: 0x4, addend: 0x0, symName: __ZN8MachInfo8setWPBitEb, symObjAddr: 0x1700, symBinAddr: 0x1C5E0, symSize: 0x70 }
  - { offsetInCU: 0x45F, offset: 0x55A3E, size: 0x4, addend: 0x0, symName: __ZZN8MachInfo8setWPBitEbE23writeProtectionDisabled, symObjAddr: 0x25CDC, symBinAddr: 0x46F44, symSize: 0x0 }
  - { offsetInCU: 0xA02, offset: 0x55FE1, size: 0x4, addend: 0x0, symName: __ZN8MachInfo4initEPKPKcmPS_b, symObjAddr: 0x0, symBinAddr: 0x1AEE0, symSize: 0x170 }
  - { offsetInCU: 0xB31, offset: 0x56110, size: 0x4, addend: 0x0, symName: __ZN8MachInfo14initFromMemoryEv, symObjAddr: 0x170, symBinAddr: 0x1B050, symSize: 0x100 }
  - { offsetInCU: 0xD12, offset: 0x562F1, size: 0x4, addend: 0x0, symName: __ZN8MachInfo18initFromFileSystemEPKPKcm, symObjAddr: 0x270, symBinAddr: 0x1B150, symSize: 0x4A0 }
  - { offsetInCU: 0xF70, offset: 0x5654F, size: 0x4, addend: 0x0, symName: __ZN8MachInfo17initFromPrelinkedEPS_, symObjAddr: 0x710, symBinAddr: 0x1B5F0, symSize: 0x1E0 }
  - { offsetInCU: 0x10AA, offset: 0x56689, size: 0x4, addend: 0x0, symName: __ZN8MachInfo6deinitEv, symObjAddr: 0x8F0, symBinAddr: 0x1B7D0, symSize: 0x60 }
  - { offsetInCU: 0x111F, offset: 0x566FE, size: 0x4, addend: 0x0, symName: __ZN8MachInfo23freeFileBufferResourcesEv, symObjAddr: 0x950, symBinAddr: 0x1B830, symSize: 0x40 }
  - { offsetInCU: 0x1158, offset: 0x56737, size: 0x4, addend: 0x0, symName: __ZN8MachInfo17processMachHeaderEPv, symObjAddr: 0xD10, symBinAddr: 0x1BBF0, symSize: 0x1B0 }
  - { offsetInCU: 0x127B, offset: 0x5685A, size: 0x4, addend: 0x0, symName: __ZN8MachInfo8loadUUIDEPv, symObjAddr: 0xEC0, symBinAddr: 0x1BDA0, symSize: 0xE0 }
  - { offsetInCU: 0x127F, offset: 0x5685E, size: 0x4, addend: 0x0, symName: __ZN8MachInfo11readSymbolsEP5vnodeP11vfs_context, symObjAddr: 0xFA0, symBinAddr: 0x1BE80, symSize: 0x140 }
  - { offsetInCU: 0x1348, offset: 0x56927, size: 0x4, addend: 0x0, symName: __ZN8MachInfo11readSymbolsEP5vnodeP11vfs_context, symObjAddr: 0xFA0, symBinAddr: 0x1BE80, symSize: 0x140 }
  - { offsetInCU: 0x1404, offset: 0x569E3, size: 0x4, addend: 0x0, symName: __ZN8MachInfo14readMachHeaderEPhP5vnodeP11vfs_contextx, symObjAddr: 0x10E0, symBinAddr: 0x1BFC0, symSize: 0x310 }
  - { offsetInCU: 0x15DF, offset: 0x56BBE, size: 0x4, addend: 0x0, symName: __ZN8MachInfo15isCurrentBinaryEy, symObjAddr: 0x13F0, symBinAddr: 0x1C2D0, symSize: 0x210 }
  - { offsetInCU: 0x1782, offset: 0x56D61, size: 0x4, addend: 0x0, symName: __ZN8MachInfo13setInterruptsEb, symObjAddr: 0x1600, symBinAddr: 0x1C4E0, symSize: 0x20 }
  - { offsetInCU: 0x182B, offset: 0x56E0A, size: 0x4, addend: 0x0, symName: __ZN8MachInfo11solveSymbolEPKc, symObjAddr: 0x1770, symBinAddr: 0x1C650, symSize: 0x170 }
  - { offsetInCU: 0x18D5, offset: 0x56EB4, size: 0x4, addend: 0x0, symName: __ZN8MachInfo17findSectionBoundsEPvmRjS1_RS0_RmPKcS5_i, symObjAddr: 0x18E0, symBinAddr: 0x1C7C0, symSize: 0x3D0 }
  - { offsetInCU: 0x1B50, offset: 0x5712F, size: 0x4, addend: 0x0, symName: __ZN8MachInfo17updatePrelinkInfoEv, symObjAddr: 0x1CB0, symBinAddr: 0x1CB90, symSize: 0x1B0 }
  - { offsetInCU: 0x1B54, offset: 0x57133, size: 0x4, addend: 0x0, symName: __ZN8MachInfo21kcGetRunningAddressesEy, symObjAddr: 0x1E60, symBinAddr: 0x1CD40, symSize: 0x10 }
  - { offsetInCU: 0x1B79, offset: 0x57158, size: 0x4, addend: 0x0, symName: __ZN8MachInfo21kcGetRunningAddressesEy, symObjAddr: 0x1E60, symBinAddr: 0x1CD40, symSize: 0x10 }
  - { offsetInCU: 0x1BF8, offset: 0x571D7, size: 0x4, addend: 0x0, symName: __ZN8MachInfo21kcGetRunningAddressesEy, symObjAddr: 0x1E60, symBinAddr: 0x1CD40, symSize: 0x10 }
  - { offsetInCU: 0x1C28, offset: 0x57207, size: 0x4, addend: 0x0, symName: __ZN8MachInfo14getAddressSlotEv, symObjAddr: 0x1E70, symBinAddr: 0x1CD50, symSize: 0x50 }
  - { offsetInCU: 0x1CA0, offset: 0x5727F, size: 0x4, addend: 0x0, symName: __ZN8MachInfo19getRunningAddressesEymb, symObjAddr: 0x1EC0, symBinAddr: 0x1CDA0, symSize: 0x380 }
  - { offsetInCU: 0x1ED0, offset: 0x574AF, size: 0x4, addend: 0x0, symName: __ZN8MachInfo19setRunningAddressesEym, symObjAddr: 0x2240, symBinAddr: 0x1D120, symSize: 0x30 }
  - { offsetInCU: 0x1F16, offset: 0x574F5, size: 0x4, addend: 0x0, symName: __ZN8MachInfo18getRunningPositionERPhRm, symObjAddr: 0x2270, symBinAddr: 0x1D150, symSize: 0x30 }
  - { offsetInCU: 0x1F5C, offset: 0x5753B, size: 0x4, addend: 0x0, symName: __ZN8MachInfo7getUUIDEPv, symObjAddr: 0x22A0, symBinAddr: 0x1D180, symSize: 0xB9 }
  - { offsetInCU: 0x27, offset: 0x57636, size: 0x4, addend: 0x0, symName: __ZN6FileIO16readFileToBufferEPKcRm, symObjAddr: 0x0, symBinAddr: 0x1D240, symSize: 0x1A0 }
  - { offsetInCU: 0x15E, offset: 0x5776D, size: 0x4, addend: 0x0, symName: __ZN6FileIO16readFileToBufferEPKcRm, symObjAddr: 0x0, symBinAddr: 0x1D240, symSize: 0x1A0 }
  - { offsetInCU: 0x282, offset: 0x57891, size: 0x4, addend: 0x0, symName: __ZN6FileIO12readFileSizeEP5vnodeP11vfs_context, symObjAddr: 0x1A0, symBinAddr: 0x1D3E0, symSize: 0x70 }
  - { offsetInCU: 0x2AF, offset: 0x578BE, size: 0x4, addend: 0x0, symName: __ZN6FileIO12readFileDataEPvxmP5vnodeP11vfs_context, symObjAddr: 0x210, symBinAddr: 0x1D450, symSize: 0x30 }
  - { offsetInCU: 0x2ED, offset: 0x578FC, size: 0x4, addend: 0x0, symName: __ZN6FileIO13performFileIOEPvxmP5vnodeP11vfs_contextb, symObjAddr: 0x240, symBinAddr: 0x1D480, symSize: 0x120 }
  - { offsetInCU: 0x3D1, offset: 0x579E0, size: 0x4, addend: 0x0, symName: __ZN6FileIO17writeBufferToFileEPKcPvmii, symObjAddr: 0x360, symBinAddr: 0x1D5A0, symSize: 0xF0 }
  - { offsetInCU: 0x49C, offset: 0x57AAB, size: 0x4, addend: 0x0, symName: __ZN6FileIO13writeFileDataEPvxmP5vnodeP11vfs_context, symObjAddr: 0x450, symBinAddr: 0x1D690, symSize: 0x27 }
  - { offsetInCU: 0x27, offset: 0x57B95, size: 0x4, addend: 0x0, symName: _umm_init, symObjAddr: 0x0, symBinAddr: 0x1D6C0, symSize: 0x90 }
  - { offsetInCU: 0x3D, offset: 0x57BAB, size: 0x4, addend: 0x0, symName: _umm_heap, symObjAddr: 0x298C, symBinAddr: 0x46F1C, symSize: 0x0 }
  - { offsetInCU: 0x10B, offset: 0x57C79, size: 0x4, addend: 0x0, symName: _umm_numblocks, symObjAddr: 0x2990, symBinAddr: 0x46F20, symSize: 0x0 }
  - { offsetInCU: 0x11C, offset: 0x57C8A, size: 0x4, addend: 0x0, symName: _default_umm_heap, symObjAddr: 0x2992, symBinAddr: 0x46F45, symSize: 0x0 }
  - { offsetInCU: 0x156, offset: 0x57CC4, size: 0x4, addend: 0x0, symName: _umm_init, symObjAddr: 0x0, symBinAddr: 0x1D6C0, symSize: 0x90 }
  - { offsetInCU: 0x1D8, offset: 0x57D46, size: 0x4, addend: 0x0, symName: _umm_free, symObjAddr: 0x90, symBinAddr: 0x1D750, symSize: 0x110 }
  - { offsetInCU: 0x2C3, offset: 0x57E31, size: 0x4, addend: 0x0, symName: _umm_malloc, symObjAddr: 0x1A0, symBinAddr: 0x1D860, symSize: 0x210 }
  - { offsetInCU: 0x3EE, offset: 0x57F5C, size: 0x4, addend: 0x0, symName: _umm_realloc, symObjAddr: 0x3B0, symBinAddr: 0x1DA70, symSize: 0x730 }
  - { offsetInCU: 0x70D, offset: 0x5827B, size: 0x4, addend: 0x0, symName: _umm_calloc, symObjAddr: 0xAE0, symBinAddr: 0x1E1A0, symSize: 0x33 }
  - { offsetInCU: 0x27, offset: 0x582EA, size: 0x4, addend: 0x0, symName: _SStream_Init, symObjAddr: 0x0, symBinAddr: 0x1E1E0, symSize: 0x20 }
  - { offsetInCU: 0x30, offset: 0x582F3, size: 0x4, addend: 0x0, symName: _SStream_Init, symObjAddr: 0x0, symBinAddr: 0x1E1E0, symSize: 0x20 }
  - { offsetInCU: 0x55, offset: 0x58318, size: 0x4, addend: 0x0, symName: _SStream_concat0, symObjAddr: 0x20, symBinAddr: 0x1E200, symSize: 0x10 }
  - { offsetInCU: 0x89, offset: 0x5834C, size: 0x4, addend: 0x0, symName: _SStream_concat, symObjAddr: 0x30, symBinAddr: 0x1E210, symSize: 0x10 }
  - { offsetInCU: 0xB7, offset: 0x5837A, size: 0x4, addend: 0x0, symName: _printInt64Bang, symObjAddr: 0x40, symBinAddr: 0x1E220, symSize: 0x10 }
  - { offsetInCU: 0xEB, offset: 0x583AE, size: 0x4, addend: 0x0, symName: _printUInt64Bang, symObjAddr: 0x50, symBinAddr: 0x1E230, symSize: 0x10 }
  - { offsetInCU: 0x11F, offset: 0x583E2, size: 0x4, addend: 0x0, symName: _printInt64, symObjAddr: 0x60, symBinAddr: 0x1E240, symSize: 0x10 }
  - { offsetInCU: 0x153, offset: 0x58416, size: 0x4, addend: 0x0, symName: _printInt32BangDec, symObjAddr: 0x70, symBinAddr: 0x1E250, symSize: 0x10 }
  - { offsetInCU: 0x187, offset: 0x5844A, size: 0x4, addend: 0x0, symName: _printInt32Bang, symObjAddr: 0x80, symBinAddr: 0x1E260, symSize: 0x10 }
  - { offsetInCU: 0x1BB, offset: 0x5847E, size: 0x4, addend: 0x0, symName: _printInt32, symObjAddr: 0x90, symBinAddr: 0x1E270, symSize: 0x10 }
  - { offsetInCU: 0x1EF, offset: 0x584B2, size: 0x4, addend: 0x0, symName: _printUInt32Bang, symObjAddr: 0xA0, symBinAddr: 0x1E280, symSize: 0x10 }
  - { offsetInCU: 0x223, offset: 0x584E6, size: 0x4, addend: 0x0, symName: _printUInt32, symObjAddr: 0xB0, symBinAddr: 0x1E290, symSize: 0x5 }
  - { offsetInCU: 0x27, offset: 0x585FB, size: 0x4, addend: 0x0, symName: _lzvn_decode, symObjAddr: 0x0, symBinAddr: 0x1E2A0, symSize: 0x1130 }
  - { offsetInCU: 0x1A5, offset: 0x58779, size: 0x4, addend: 0x0, symName: _lzvn_decode, symObjAddr: 0x0, symBinAddr: 0x1E2A0, symSize: 0x1130 }
  - { offsetInCU: 0x57A, offset: 0x58B4E, size: 0x4, addend: 0x0, symName: _lzvn_decode_buffer, symObjAddr: 0x1130, symBinAddr: 0x1F3D0, symSize: 0x60 }
  - { offsetInCU: 0x27, offset: 0x58CB2, size: 0x4, addend: 0x0, symName: _x86_map_sib_base, symObjAddr: 0x0, symBinAddr: 0x1F430, symSize: 0x10 }
  - { offsetInCU: 0x3D, offset: 0x58CC8, size: 0x4, addend: 0x0, symName: _arch_masks, symObjAddr: 0x878, symBinAddr: 0x3CDD8, symSize: 0x0 }
  - { offsetInCU: 0x85, offset: 0x58D10, size: 0x4, addend: 0x0, symName: _regsize_map_32, symObjAddr: 0xBA4, symBinAddr: 0x3D104, symSize: 0x0 }
  - { offsetInCU: 0xC7, offset: 0x58D52, size: 0x4, addend: 0x0, symName: _regsize_map_64, symObjAddr: 0xC8E, symBinAddr: 0x3D1EE, symSize: 0x0 }
  - { offsetInCU: 0xD9, offset: 0x58D64, size: 0x4, addend: 0x0, symName: _sib_base_map, symObjAddr: 0x8C0, symBinAddr: 0x3CE20, symSize: 0x0 }
  - { offsetInCU: 0x6FC, offset: 0x59387, size: 0x4, addend: 0x0, symName: _sib_index_map, symObjAddr: 0x944, symBinAddr: 0x3CEA4, symSize: 0x0 }
  - { offsetInCU: 0x719, offset: 0x593A4, size: 0x4, addend: 0x0, symName: _segment_map, symObjAddr: 0xB88, symBinAddr: 0x3D0E8, symSize: 0x0 }
  - { offsetInCU: 0x737, offset: 0x593C2, size: 0x4, addend: 0x0, symName: _insns, symObjAddr: 0xD78, symBinAddr: 0x3D2D8, symSize: 0x0 }
  - { offsetInCU: 0x792, offset: 0x5941D, size: 0x4, addend: 0x0, symName: _insn_regs_intel, symObjAddr: 0x2598, symBinAddr: 0x3EAF8, symSize: 0x0 }
  - { offsetInCU: 0x7F3, offset: 0x5947E, size: 0x4, addend: 0x0, symName: _insn_regs_intel2, symObjAddr: 0x2848, symBinAddr: 0x3EDA8, symSize: 0x0 }
  - { offsetInCU: 0x84D, offset: 0x594D8, size: 0x4, addend: 0x0, symName: _insn_regs_att, symObjAddr: 0x2890, symBinAddr: 0x3EDF0, symSize: 0x0 }
  - { offsetInCU: 0x86B, offset: 0x594F6, size: 0x4, addend: 0x0, symName: _x86_imm_size, symObjAddr: 0x2B10, symBinAddr: 0x3F070, symSize: 0x0 }
  - { offsetInCU: 0x186A, offset: 0x5A4F5, size: 0x4, addend: 0x0, symName: _x86_map_sib_base, symObjAddr: 0x0, symBinAddr: 0x1F430, symSize: 0x10 }
  - { offsetInCU: 0x1893, offset: 0x5A51E, size: 0x4, addend: 0x0, symName: _x86_map_sib_index, symObjAddr: 0x10, symBinAddr: 0x1F440, symSize: 0x10 }
  - { offsetInCU: 0x18BC, offset: 0x5A547, size: 0x4, addend: 0x0, symName: _x86_map_segment, symObjAddr: 0x20, symBinAddr: 0x1F450, symSize: 0x10 }
  - { offsetInCU: 0x18E5, offset: 0x5A570, size: 0x4, addend: 0x0, symName: _X86_reg_name, symObjAddr: 0x30, symBinAddr: 0x1F460, symSize: 0x10 }
  - { offsetInCU: 0x191D, offset: 0x5A5A8, size: 0x4, addend: 0x0, symName: _X86_insn_name, symObjAddr: 0x40, symBinAddr: 0x1F470, symSize: 0x10 }
  - { offsetInCU: 0x1955, offset: 0x5A5E0, size: 0x4, addend: 0x0, symName: _X86_group_name, symObjAddr: 0x50, symBinAddr: 0x1F480, symSize: 0x10 }
  - { offsetInCU: 0x198D, offset: 0x5A618, size: 0x4, addend: 0x0, symName: _X86_get_insn_id, symObjAddr: 0x60, symBinAddr: 0x1F490, symSize: 0x40 }
  - { offsetInCU: 0x19E1, offset: 0x5A66C, size: 0x4, addend: 0x0, symName: _X86_insn_reg_intel, symObjAddr: 0xA0, symBinAddr: 0x1F4D0, symSize: 0x40 }
  - { offsetInCU: 0x19E5, offset: 0x5A670, size: 0x4, addend: 0x0, symName: _X86_insn_reg_intel2, symObjAddr: 0xE0, symBinAddr: 0x1F510, symSize: 0x90 }
  - { offsetInCU: 0x1A16, offset: 0x5A6A1, size: 0x4, addend: 0x0, symName: _X86_insn_reg_intel2, symObjAddr: 0xE0, symBinAddr: 0x1F510, symSize: 0x90 }
  - { offsetInCU: 0x1A6B, offset: 0x5A6F6, size: 0x4, addend: 0x0, symName: _X86_insn_reg_att2, symObjAddr: 0x170, symBinAddr: 0x1F5A0, symSize: 0x90 }
  - { offsetInCU: 0x1AC0, offset: 0x5A74B, size: 0x4, addend: 0x0, symName: _X86_insn_reg_att, symObjAddr: 0x200, symBinAddr: 0x1F630, symSize: 0x90 }
  - { offsetInCU: 0x2E0A, offset: 0x5BA95, size: 0x4, addend: 0x0, symName: _X86_lockrep, symObjAddr: 0x290, symBinAddr: 0x1F6C0, symSize: 0x320 }
  - { offsetInCU: 0x2EEA, offset: 0x5BB75, size: 0x4, addend: 0x0, symName: _op_addReg, symObjAddr: 0x5B0, symBinAddr: 0x1F9E0, symSize: 0xA0 }
  - { offsetInCU: 0x2F1F, offset: 0x5BBAA, size: 0x4, addend: 0x0, symName: _op_addImm, symObjAddr: 0x650, symBinAddr: 0x1FA80, symSize: 0xC0 }
  - { offsetInCU: 0x2F54, offset: 0x5BBDF, size: 0x4, addend: 0x0, symName: _op_addSseCC, symObjAddr: 0x710, symBinAddr: 0x1FB40, symSize: 0x30 }
  - { offsetInCU: 0x2F89, offset: 0x5BC14, size: 0x4, addend: 0x0, symName: _op_addAvxCC, symObjAddr: 0x740, symBinAddr: 0x1FB70, symSize: 0x30 }
  - { offsetInCU: 0x2FBE, offset: 0x5BC49, size: 0x4, addend: 0x0, symName: _op_addAvxRoundingMode, symObjAddr: 0x770, symBinAddr: 0x1FBA0, symSize: 0x30 }
  - { offsetInCU: 0x2FF3, offset: 0x5BC7E, size: 0x4, addend: 0x0, symName: _op_addAvxZeroOpmask, symObjAddr: 0x7A0, symBinAddr: 0x1FBD0, symSize: 0x30 }
  - { offsetInCU: 0x3019, offset: 0x5BCA4, size: 0x4, addend: 0x0, symName: _op_addAvxSae, symObjAddr: 0x7D0, symBinAddr: 0x1FC00, symSize: 0x30 }
  - { offsetInCU: 0x303F, offset: 0x5BCCA, size: 0x4, addend: 0x0, symName: _op_addAvxBroadcast, symObjAddr: 0x800, symBinAddr: 0x1FC30, symSize: 0x30 }
  - { offsetInCU: 0x3074, offset: 0x5BCFF, size: 0x4, addend: 0x0, symName: _X86_immediate_size, symObjAddr: 0x830, symBinAddr: 0x1FC60, symSize: 0x44 }
  - { offsetInCU: 0x27, offset: 0x5BD83, size: 0x4, addend: 0x0, symName: __ZN6WIOKit11getPropertyEP15IORegistryEntryPKc, symObjAddr: 0x0, symBinAddr: 0x1FCB0, symSize: 0x80 }
  - { offsetInCU: 0x46, offset: 0x5BDA2, size: 0x4, addend: 0x0, symName: __ZN6WIOKit11getPropertyEP15IORegistryEntryPKc, symObjAddr: 0x0, symBinAddr: 0x1FCB0, symSize: 0x80 }
  - { offsetInCU: 0xA7, offset: 0x5BE03, size: 0x4, addend: 0x0, symName: __ZN6WIOKit15awaitPublishingEP15IORegistryEntry, symObjAddr: 0x80, symBinAddr: 0x1FD30, symSize: 0xC0 }
  - { offsetInCU: 0x111, offset: 0x5BE6D, size: 0x4, addend: 0x0, symName: __ZN6WIOKit18readPCIConfigValueEP15IORegistryEntryjjj, symObjAddr: 0x140, symBinAddr: 0x1FDF0, symSize: 0x330 }
  - { offsetInCU: 0x1D7, offset: 0x5BF33, size: 0x4, addend: 0x0, symName: __ZN6WIOKit16getDeviceAddressEP15IORegistryEntryRhS2_S2_, symObjAddr: 0x470, symBinAddr: 0x20120, symSize: 0x50 }
  - { offsetInCU: 0x258, offset: 0x5BFB4, size: 0x4, addend: 0x0, symName: __ZN6WIOKit16getComputerModelEv, symObjAddr: 0x4C0, symBinAddr: 0x20170, symSize: 0x20 }
  - { offsetInCU: 0x274, offset: 0x5BFD0, size: 0x4, addend: 0x0, symName: __ZN6WIOKit15getComputerInfoEPcmS0_m, symObjAddr: 0x4E0, symBinAddr: 0x20190, symSize: 0x60 }
  - { offsetInCU: 0x2CD, offset: 0x5C029, size: 0x4, addend: 0x0, symName: __ZN6WIOKit17findEntryByPrefixEPKcS1_PK15IORegistryPlanePFbPvP15IORegistryEntryEbS5_, symObjAddr: 0x540, symBinAddr: 0x201F0, symSize: 0x60 }
  - { offsetInCU: 0x36B, offset: 0x5C0C7, size: 0x4, addend: 0x0, symName: __ZN6WIOKit17findEntryByPrefixEP15IORegistryEntryPKcPK15IORegistryPlanePFbPvS1_EbS7_, symObjAddr: 0x5A0, symBinAddr: 0x20250, symSize: 0x2F0 }
  - { offsetInCU: 0x451, offset: 0x5C1AD, size: 0x4, addend: 0x0, symName: __ZN6WIOKit19usingPrelinkedCacheEv, symObjAddr: 0x890, symBinAddr: 0x20540, symSize: 0x70 }
  - { offsetInCU: 0x455, offset: 0x5C1B1, size: 0x4, addend: 0x0, symName: __ZN6WIOKit12renameDeviceEP15IORegistryEntryPKcb, symObjAddr: 0x900, symBinAddr: 0x205B0, symSize: 0x1BB }
  - { offsetInCU: 0x498, offset: 0x5C1F4, size: 0x4, addend: 0x0, symName: __ZN6WIOKit12renameDeviceEP15IORegistryEntryPKcb, symObjAddr: 0x900, symBinAddr: 0x205B0, symSize: 0x1BB }
  - { offsetInCU: 0x27, offset: 0x5E91D, size: 0x4, addend: 0x0, symName: __ZN6Crypto12genUniqueKeyEj, symObjAddr: 0x0, symBinAddr: 0x20770, symSize: 0x70 }
  - { offsetInCU: 0xB0, offset: 0x5E9A6, size: 0x4, addend: 0x0, symName: __ZN6Crypto12genUniqueKeyEj, symObjAddr: 0x0, symBinAddr: 0x20770, symSize: 0x70 }
  - { offsetInCU: 0x141, offset: 0x5EA37, size: 0x4, addend: 0x0, symName: __ZN6Crypto7encryptEPKhS1_Rj, symObjAddr: 0x70, symBinAddr: 0x207E0, symSize: 0x3E0 }
  - { offsetInCU: 0x34B, offset: 0x5EC41, size: 0x4, addend: 0x0, symName: __ZN6Crypto7decryptEPKhS1_Rj, symObjAddr: 0x450, symBinAddr: 0x20BC0, symSize: 0x280 }
  - { offsetInCU: 0x4E9, offset: 0x5EDDF, size: 0x4, addend: 0x0, symName: __ZN6Crypto4hashEPKhj, symObjAddr: 0x6D0, symBinAddr: 0x20E40, symSize: 0x24A }
  - { offsetInCU: 0x27, offset: 0x5F0FE, size: 0x4, addend: 0x0, symName: _insn_find, symObjAddr: 0x0, symBinAddr: 0x21090, symSize: 0x80 }
  - { offsetInCU: 0xD0, offset: 0x5F1A7, size: 0x4, addend: 0x0, symName: _insn_find, symObjAddr: 0x0, symBinAddr: 0x21090, symSize: 0x80 }
  - { offsetInCU: 0x15D, offset: 0x5F234, size: 0x4, addend: 0x0, symName: _name2id, symObjAddr: 0x80, symBinAddr: 0x21110, symSize: 0x50 }
  - { offsetInCU: 0x1B4, offset: 0x5F28B, size: 0x4, addend: 0x0, symName: _count_positive, symObjAddr: 0xD0, symBinAddr: 0x21160, symSize: 0x20 }
  - { offsetInCU: 0x1E8, offset: 0x5F2BF, size: 0x4, addend: 0x0, symName: _cs_strdup, symObjAddr: 0xF0, symBinAddr: 0x21180, symSize: 0x40 }
  - { offsetInCU: 0x1EC, offset: 0x5F2C3, size: 0x4, addend: 0x0, symName: _cs_snprintf, symObjAddr: 0x130, symBinAddr: 0x211C0, symSize: 0x24 }
  - { offsetInCU: 0x231, offset: 0x5F308, size: 0x4, addend: 0x0, symName: _cs_snprintf, symObjAddr: 0x130, symBinAddr: 0x211C0, symSize: 0x24 }
  - { offsetInCU: 0x27, offset: 0x5F42A, size: 0x4, addend: 0x0, symName: _sha256_transform, symObjAddr: 0x0, symBinAddr: 0x211F0, symSize: 0x270 }
  - { offsetInCU: 0x3C, offset: 0x5F43F, size: 0x4, addend: 0x0, symName: _k, symObjAddr: 0x6D0, symBinAddr: 0x3F570, symSize: 0x0 }
  - { offsetInCU: 0x81, offset: 0x5F484, size: 0x4, addend: 0x0, symName: _sha256_transform, symObjAddr: 0x0, symBinAddr: 0x211F0, symSize: 0x270 }
  - { offsetInCU: 0x185, offset: 0x5F588, size: 0x4, addend: 0x0, symName: _sha256_init, symObjAddr: 0x270, symBinAddr: 0x21460, symSize: 0x40 }
  - { offsetInCU: 0x1AA, offset: 0x5F5AD, size: 0x4, addend: 0x0, symName: _sha256_update, symObjAddr: 0x2B0, symBinAddr: 0x214A0, symSize: 0x70 }
  - { offsetInCU: 0x1FD, offset: 0x5F600, size: 0x4, addend: 0x0, symName: _sha256_final, symObjAddr: 0x320, symBinAddr: 0x21510, symSize: 0x387 }
...
