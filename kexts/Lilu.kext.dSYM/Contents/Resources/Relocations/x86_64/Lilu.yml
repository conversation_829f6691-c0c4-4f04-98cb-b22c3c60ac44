---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/work/Lilu/Lilu/build/Release/Lilu.kext/Contents/MacOS/Lilu'
relocations:
  - { offsetInCU: 0x10, offset: 0x10, size: 0x8, addend: 0x0, symName: _performEfiCallAsm64, symObjAddr: 0x0, symBinAddr: 0xED0, symSize: 0x63 }
  - { offsetInCU: 0x3B, offset: 0x3B, size: 0x8, addend: 0x0, symName: _performEfiCallAsm64, symObjAddr: 0x0, symBinAddr: 0xED0, symSize: 0x63 }
  - { offsetInCU: 0x35, offset: 0x69, size: 0x8, addend: 0x0, symName: _kmod_info, symObjAddr: 0x19D50, symBinAddr: 0x206E0, symSize: 0x0 }
  - { offsetInCU: 0x206, offset: 0x23A, size: 0x8, addend: 0x0, symName: __realmain, symObjAddr: 0x19E18, symBinAddr: 0x207A8, symSize: 0x0 }
  - { offsetInCU: 0x21C, offset: 0x250, size: 0x8, addend: 0x0, symName: __antimain, symObjAddr: 0x19E20, symBinAddr: 0x207B0, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x280, size: 0x8, addend: 0x0, symName: __ZN6Policy14registerPolicyEv, symObjAddr: 0x0, symBinAddr: 0xF70, symSize: 0x20 }
  - { offsetInCU: 0x48, offset: 0x2A1, size: 0x8, addend: 0x0, symName: __ZN6Policy14registerPolicyEv, symObjAddr: 0x0, symBinAddr: 0xF70, symSize: 0x20 }
  - { offsetInCU: 0x75, offset: 0x2CE, size: 0x8, addend: 0x0, symName: __ZN6Policy16unregisterPolicyEv, symObjAddr: 0x20, symBinAddr: 0xF90, symSize: 0x40 }
  - { offsetInCU: 0x27, offset: 0x400, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher8getErrorEv, symObjAddr: 0x60, symBinAddr: 0xFD0, symSize: 0x10 }
  - { offsetInCU: 0x3E, offset: 0x417, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher15kernelWriteLockE, symObjAddr: 0xEA098, symBinAddr: 0x20930, symSize: 0x0 }
  - { offsetInCU: 0x8128, offset: 0x8501, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher23eraseCoverageInstPrefixEymx, symObjAddr: 0x8C0, symBinAddr: 0x1830, symSize: 0x320 }
  - { offsetInCU: 0x8154, offset: 0x852D, size: 0x8, addend: 0x0, symName: __ZZN13KernelPatcher23eraseCoverageInstPrefixEymxE13IncInstPrefix, symObjAddr: 0x1DD50, symBinAddr: 0x1ACB0, symSize: 0x0 }
  - { offsetInCU: 0x85D1, offset: 0x89AA, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher20tempExecutableMemoryE, symObjAddr: 0x18D20, symBinAddr: 0x19C90, symSize: 0x1000 }
  - { offsetInCU: 0x85EF, offset: 0x89C8, size: 0x8, addend: 0x0, symName: __ZL4that, symObjAddr: 0xE5F20, symBinAddr: 0x20960, symSize: 0x0 }
  - { offsetInCU: 0x8745, offset: 0x8B1E, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher8getErrorEv, symObjAddr: 0x60, symBinAddr: 0xFD0, symSize: 0x10 }
  - { offsetInCU: 0x8771, offset: 0x8B4A, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher10clearErrorEv, symObjAddr: 0x70, symBinAddr: 0xFE0, symSize: 0x10 }
  - { offsetInCU: 0x87BD, offset: 0x8B96, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher4initEv, symObjAddr: 0x80, symBinAddr: 0xFF0, symSize: 0x170 }
  - { offsetInCU: 0x8A0B, offset: 0x8DE4, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher9loadKinfoEPKcPKS1_mbbb, symObjAddr: 0x1F0, symBinAddr: 0x1160, symSize: 0x2C0 }
  - { offsetInCU: 0x8EA1, offset: 0x927A, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher6deinitEv, symObjAddr: 0x4B0, symBinAddr: 0x1420, symSize: 0x280 }
  - { offsetInCU: 0x92F4, offset: 0x96CD, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher9loadKinfoEPNS_8KextInfoE, symObjAddr: 0x730, symBinAddr: 0x16A0, symSize: 0x90 }
  - { offsetInCU: 0x9365, offset: 0x973E, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher17updateRunningInfoEmymb, symObjAddr: 0x7C0, symBinAddr: 0x1730, symSize: 0xB0 }
  - { offsetInCU: 0x940A, offset: 0x97E3, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher16compatibleKernelEjj, symObjAddr: 0x870, symBinAddr: 0x17E0, symSize: 0x30 }
  - { offsetInCU: 0x943F, offset: 0x9818, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher23eraseCoverageInstPrefixEym, symObjAddr: 0x8A0, symBinAddr: 0x1810, symSize: 0x20 }
  - { offsetInCU: 0x9495, offset: 0x986E, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher11solveSymbolEmPKc, symObjAddr: 0xBE0, symBinAddr: 0x1B50, symSize: 0x60 }
  - { offsetInCU: 0x9776, offset: 0x9B4F, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher18setupKextListeningEv, symObjAddr: 0xC40, symBinAddr: 0x1BB0, symSize: 0x150 }
  - { offsetInCU: 0x99A1, offset: 0x9D7A, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher14onOSKextUnloadEPv, symObjAddr: 0xD90, symBinAddr: 0x1D00, symSize: 0x30 }
  - { offsetInCU: 0x9A20, offset: 0x9DF9, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher31onOSKextSaveLoadedKextPanicListEv, symObjAddr: 0xDC0, symBinAddr: 0x1D30, symSize: 0xA0 }
  - { offsetInCU: 0x9B38, offset: 0x9F11, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher21routeMultipleInternalEmPNS_12RouteRequestEmymbbNS_8JumpTypeE, symObjAddr: 0xE60, symBinAddr: 0x1DD0, symSize: 0x4B0 }
  - { offsetInCU: 0xA49C, offset: 0xA875, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher21routeFunctionInternalEyybbbNS_8JumpTypeEP8MachInfoPy, symObjAddr: 0x1310, symBinAddr: 0x2280, symSize: 0xB50 }
  - { offsetInCU: 0xBC49, offset: 0xC022, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher16createTrampolineEymPKhm, symObjAddr: 0x1E60, symBinAddr: 0x2DD0, symSize: 0x390 }
  - { offsetInCU: 0xC1DC, offset: 0xC5B5, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher11processKextEP9kmod_infob, symObjAddr: 0x21F0, symBinAddr: 0x3160, symSize: 0x1D0 }
  - { offsetInCU: 0xC2F9, offset: 0xC6D2, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher13routeMultipleEmPNS_12RouteRequestEmymbb, symObjAddr: 0x23C0, symBinAddr: 0x3330, symSize: 0x30 }
  - { offsetInCU: 0xC40A, offset: 0xC7E3, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher10waitOnKextEPNS_11KextHandlerE, symObjAddr: 0x23F0, symBinAddr: 0x3360, symSize: 0xD0 }
  - { offsetInCU: 0xC4BC, offset: 0xC895, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher16applyLookupPatchEPKNS_11LookupPatchE, symObjAddr: 0x24C0, symBinAddr: 0x3430, symSize: 0x10 }
  - { offsetInCU: 0xC504, offset: 0xC8DD, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher16applyLookupPatchEPKNS_11LookupPatchEPhm, symObjAddr: 0x24D0, symBinAddr: 0x3440, symSize: 0x260 }
  - { offsetInCU: 0xC7B5, offset: 0xCB8E, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher13routeFunctionEyybbb, symObjAddr: 0x2730, symBinAddr: 0x36A0, symSize: 0x20 }
  - { offsetInCU: 0xC848, offset: 0xCC21, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher17routeFunctionLongEyybbb, symObjAddr: 0x2750, symBinAddr: 0x36C0, symSize: 0x20 }
  - { offsetInCU: 0xC8DB, offset: 0xCCB4, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher18routeFunctionShortEyybbb, symObjAddr: 0x2770, symBinAddr: 0x36E0, symSize: 0x20 }
  - { offsetInCU: 0xC96E, offset: 0xCD47, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher10routeBlockEyPKhmbb, symObjAddr: 0x2790, symBinAddr: 0x3700, symSize: 0x160 }
  - { offsetInCU: 0xCB71, offset: 0xCF4A, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher17routeMultipleLongEmPNS_12RouteRequestEmymbb, symObjAddr: 0x28F0, symBinAddr: 0x3860, symSize: 0x30 }
  - { offsetInCU: 0xCBF4, offset: 0xCFCD, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher18routeMultipleShortEmPNS_12RouteRequestEmymbb, symObjAddr: 0x2920, symBinAddr: 0x3890, symSize: 0x30 }
  - { offsetInCU: 0xCCB5, offset: 0xD08E, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher11findPatternEPKvS1_mS1_mPm, symObjAddr: 0x2950, symBinAddr: 0x38C0, symSize: 0xA0 }
  - { offsetInCU: 0xCE95, offset: 0xD26E, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher22findAndReplaceWithMaskEPvmPKvmS2_mS2_mS2_mmm, symObjAddr: 0x29F0, symBinAddr: 0x3960, symSize: 0x350 }
  - { offsetInCU: 0x27, offset: 0xD8E2, size: 0x8, addend: 0x0, symName: __ZN10RTCStorage4initEb, symObjAddr: 0x2D40, symBinAddr: 0x3CB0, symSize: 0xB0 }
  - { offsetInCU: 0xCC71, offset: 0x1A52C, size: 0x8, addend: 0x0, symName: __ZN10RTCStorage4initEb, symObjAddr: 0x2D40, symBinAddr: 0x3CB0, symSize: 0xB0 }
  - { offsetInCU: 0xCCE0, offset: 0x1A59B, size: 0x8, addend: 0x0, symName: __ZN10RTCStorage6deinitEv, symObjAddr: 0x2DF0, symBinAddr: 0x3D60, symSize: 0x30 }
  - { offsetInCU: 0xCD14, offset: 0x1A5CF, size: 0x8, addend: 0x0, symName: __ZN10RTCStorage19checkExtendedMemoryEv, symObjAddr: 0x2E20, symBinAddr: 0x3D90, symSize: 0x30 }
  - { offsetInCU: 0xCD75, offset: 0x1A630, size: 0x8, addend: 0x0, symName: __ZN10RTCStorage4readEyjPh, symObjAddr: 0x2E50, symBinAddr: 0x3DC0, symSize: 0x150 }
  - { offsetInCU: 0xCE2E, offset: 0x1A6E9, size: 0x8, addend: 0x0, symName: __ZN10RTCStorage5writeEyjPh, symObjAddr: 0x2FA0, symBinAddr: 0x3F10, symSize: 0x160 }
  - { offsetInCU: 0xCF33, offset: 0x1A7EE, size: 0x8, addend: 0x0, symName: __ZN10RTCStorage10readDirectEP20IOACPIPlatformDevicehtPhb, symObjAddr: 0x3100, symBinAddr: 0x4070, symSize: 0xA0 }
  - { offsetInCU: 0xD0F9, offset: 0x1A9B4, size: 0x8, addend: 0x0, symName: __ZN10RTCStorage11writeDirectEP20IOACPIPlatformDevicehtPhbb, symObjAddr: 0x31A0, symBinAddr: 0x4110, symSize: 0x230 }
  - { offsetInCU: 0x27, offset: 0x1ACF4, size: 0x8, addend: 0x0, symName: __ZN4LiluD1Ev, symObjAddr: 0x33D0, symBinAddr: 0x4340, symSize: 0x10 }
  - { offsetInCU: 0x2F, offset: 0x1ACFC, size: 0x8, addend: 0x0, symName: __ZN12Disassembler20quickInstructionSizeEym, symObjAddr: 0x87E0, symBinAddr: 0x9750, symSize: 0xA0 }
  - { offsetInCU: 0x3E, offset: 0x1AD0B, size: 0x8, addend: 0x0, symName: __ZN4Lilu10gMetaClassE, symObjAddr: 0xE9FB0, symBinAddr: 0x20848, symSize: 0x0 }
  - { offsetInCU: 0x293, offset: 0x1AF60, size: 0x8, addend: 0x0, symName: __ZN4Lilu9metaClassE, symObjAddr: 0x1E0F0, symBinAddr: 0x1F0E0, symSize: 0x0 }
  - { offsetInCU: 0x2AA, offset: 0x1AF77, size: 0x8, addend: 0x0, symName: __ZN4Lilu10superClassE, symObjAddr: 0x1E0F8, symBinAddr: 0x1F0E8, symSize: 0x0 }
  - { offsetInCU: 0x2CF, offset: 0x1AF9C, size: 0x8, addend: 0x0, symName: _Lilu_config, symObjAddr: 0xE5F50, symBinAddr: 0x20990, symSize: 0x0 }
  - { offsetInCU: 0x9185, offset: 0x23E52, size: 0x8, addend: 0x0, symName: __ZL11kextVersion, symObjAddr: 0x1DD60, symBinAddr: 0x1ACC0, symSize: 0x0 }
  - { offsetInCU: 0x9223, offset: 0x23EF0, size: 0x8, addend: 0x0, symName: __ZN4LiluD1Ev, symObjAddr: 0x33D0, symBinAddr: 0x4340, symSize: 0x10 }
  - { offsetInCU: 0x929D, offset: 0x23F6A, size: 0x8, addend: 0x0, symName: __ZN4LiluD0Ev, symObjAddr: 0x33E0, symBinAddr: 0x4350, symSize: 0x30 }
  - { offsetInCU: 0x9328, offset: 0x23FF5, size: 0x8, addend: 0x0, symName: __ZNK4Lilu12getMetaClassEv, symObjAddr: 0x3410, symBinAddr: 0x4380, symSize: 0x10 }
  - { offsetInCU: 0x9358, offset: 0x24025, size: 0x8, addend: 0x0, symName: __ZN4Lilu5probeEP9IOServicePi, symObjAddr: 0x3420, symBinAddr: 0x4390, symSize: 0x60 }
  - { offsetInCU: 0x93C5, offset: 0x24092, size: 0x8, addend: 0x0, symName: __ZN4Lilu5startEP9IOService, symObjAddr: 0x3480, symBinAddr: 0x43F0, symSize: 0x50 }
  - { offsetInCU: 0x940C, offset: 0x240D9, size: 0x8, addend: 0x0, symName: __ZN4Lilu4stopEP9IOService, symObjAddr: 0x34D0, symBinAddr: 0x4440, symSize: 0x20 }
  - { offsetInCU: 0x947D, offset: 0x2414A, size: 0x8, addend: 0x0, symName: __ZN4Lilu9MetaClassD1Ev, symObjAddr: 0x34F0, symBinAddr: 0x4460, symSize: 0x10 }
  - { offsetInCU: 0x94F7, offset: 0x241C4, size: 0x8, addend: 0x0, symName: __ZN4Lilu9MetaClassD0Ev, symObjAddr: 0x3500, symBinAddr: 0x4470, symSize: 0x10 }
  - { offsetInCU: 0x95C9, offset: 0x24296, size: 0x8, addend: 0x0, symName: __ZNK4Lilu9MetaClass5allocEv, symObjAddr: 0x3510, symBinAddr: 0x4480, symSize: 0x40 }
  - { offsetInCU: 0x95D1, offset: 0x2429E, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_kern_start.cpp, symObjAddr: 0x3550, symBinAddr: 0x44C0, symSize: 0x1700 }
  - { offsetInCU: 0x9B9E, offset: 0x2486B, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_kern_start.cpp, symObjAddr: 0x3550, symBinAddr: 0x44C0, symSize: 0x1700 }
  - { offsetInCU: 0x9FA3, offset: 0x24C70, size: 0x8, addend: 0x0, symName: __ZN13Configuration13policyInitBSDEP15mac_policy_conf, symObjAddr: 0x4C50, symBinAddr: 0x5BC0, symSize: 0x20 }
  - { offsetInCU: 0xA00D, offset: 0x24CDA, size: 0x8, addend: 0x0, symName: __ZN13Configuration10policyInitEPKc, symObjAddr: 0x4C70, symBinAddr: 0x5BE0, symSize: 0x80 }
  - { offsetInCU: 0xA0B2, offset: 0x24D7F, size: 0x8, addend: 0x0, symName: __ZN13Configuration17performCommonInitEv, symObjAddr: 0x4CF0, symBinAddr: 0x5C60, symSize: 0x20E0 }
  - { offsetInCU: 0xBE0D, offset: 0x26ADA, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x6DD0, symBinAddr: 0x7D40, symSize: 0x20 }
  - { offsetInCU: 0xBE7B, offset: 0x26B48, size: 0x8, addend: 0x0, symName: __ZN4Lilu9MetaClassC1Ev, symObjAddr: 0x6DF0, symBinAddr: 0x7D60, symSize: 0x40 }
  - { offsetInCU: 0xBED2, offset: 0x26B9F, size: 0x8, addend: 0x0, symName: __ZN4LiluC2EPK11OSMetaClass, symObjAddr: 0x6E30, symBinAddr: 0x7DA0, symSize: 0x20 }
  - { offsetInCU: 0xBF40, offset: 0x26C0D, size: 0x8, addend: 0x0, symName: __ZN4LiluC1EPK11OSMetaClass, symObjAddr: 0x6E50, symBinAddr: 0x7DC0, symSize: 0x20 }
  - { offsetInCU: 0xBFC2, offset: 0x26C8F, size: 0x8, addend: 0x0, symName: __ZN4LiluD2Ev, symObjAddr: 0x6E70, symBinAddr: 0x7DE0, symSize: 0x10 }
  - { offsetInCU: 0xBFEE, offset: 0x26CBB, size: 0x8, addend: 0x0, symName: __ZN4Lilu9MetaClassC2Ev, symObjAddr: 0x6E80, symBinAddr: 0x7DF0, symSize: 0x40 }
  - { offsetInCU: 0xC01B, offset: 0x26CE8, size: 0x8, addend: 0x0, symName: __ZN4LiluC1Ev, symObjAddr: 0x6EC0, symBinAddr: 0x7E30, symSize: 0x30 }
  - { offsetInCU: 0xC072, offset: 0x26D3F, size: 0x8, addend: 0x0, symName: __ZN4LiluC2Ev, symObjAddr: 0x6EF0, symBinAddr: 0x7E60, symSize: 0x30 }
  - { offsetInCU: 0xC0A0, offset: 0x26D6D, size: 0x8, addend: 0x0, symName: __ZN13Configuration11initConsoleEP8PE_Videoi, symObjAddr: 0x6F20, symBinAddr: 0x7E90, symSize: 0xA0 }
  - { offsetInCU: 0xC1D9, offset: 0x26EA6, size: 0x8, addend: 0x0, symName: '__ZZN13Configuration11initConsoleEP8PE_VideoiEN3$_08__invokeEPvS3_', symObjAddr: 0x6FC0, symBinAddr: 0x7F30, symSize: 0x20 }
  - { offsetInCU: 0xC241, offset: 0x26F0E, size: 0x8, addend: 0x0, symName: __ZN13Configuration18policyCheckRemountEP5ucredP5mountP5label, symObjAddr: 0x6FE0, symBinAddr: 0x7F50, symSize: 0x10 }
  - { offsetInCU: 0xC284, offset: 0x26F51, size: 0x8, addend: 0x0, symName: __ZN13Configuration32policyCredCheckLabelUpdateExecveEP5ucredP5vnodez, symObjAddr: 0x6FF0, symBinAddr: 0x7F60, symSize: 0x10 }
  - { offsetInCU: 0xC44F, offset: 0x2711C, size: 0x8, addend: 0x0, symName: _Lilu_kern_start, symObjAddr: 0x7000, symBinAddr: 0x7F70, symSize: 0x17D0 }
  - { offsetInCU: 0xCF9C, offset: 0x27C69, size: 0x8, addend: 0x0, symName: _Lilu_kern_stop, symObjAddr: 0x87D0, symBinAddr: 0x9740, symSize: 0x10 }
  - { offsetInCU: 0xCFA4, offset: 0x27C71, size: 0x8, addend: 0x0, symName: __ZN12Disassembler20quickInstructionSizeEym, symObjAddr: 0x87E0, symBinAddr: 0x9750, symSize: 0xA0 }
  - { offsetInCU: 0x27, offset: 0x27CEA, size: 0x8, addend: 0x0, symName: __ZN12Disassembler20quickInstructionSizeEym, symObjAddr: 0x87E0, symBinAddr: 0x9750, symSize: 0xA0 }
  - { offsetInCU: 0x3C3, offset: 0x28086, size: 0x8, addend: 0x0, symName: __ZN12Disassembler20quickInstructionSizeEym, symObjAddr: 0x87E0, symBinAddr: 0x9750, symSize: 0xA0 }
  - { offsetInCU: 0x42A, offset: 0x280ED, size: 0x8, addend: 0x0, symName: __ZN12Disassembler9hdeDisasmEyP6hde64s, symObjAddr: 0x8880, symBinAddr: 0x97F0, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x28158, size: 0x8, addend: 0x0, symName: __Z14lilu_os_memchrPKvim, symObjAddr: 0x8890, symBinAddr: 0x9800, symSize: 0x1C0 }
  - { offsetInCU: 0x5D, offset: 0x2818E, size: 0x8, addend: 0x0, symName: __Z14lilu_os_memchrPKvim, symObjAddr: 0x8890, symBinAddr: 0x9800, symSize: 0x1C0 }
  - { offsetInCU: 0x22A, offset: 0x2835B, size: 0x8, addend: 0x0, symName: __Z14lilu_os_memmemPKvmS0_m, symObjAddr: 0x8A50, symBinAddr: 0x99C0, symSize: 0x330 }
  - { offsetInCU: 0x3E7, offset: 0x28518, size: 0x8, addend: 0x0, symName: __ZL14twobyte_memmemPKhmS0_, symObjAddr: 0x8D80, symBinAddr: 0x9CF0, symSize: 0x70 }
  - { offsetInCU: 0x46A, offset: 0x2859B, size: 0x8, addend: 0x0, symName: __ZL13twoway_memmemPKhS0_S0_m, symObjAddr: 0x8DF0, symBinAddr: 0x9D60, symSize: 0x380 }
  - { offsetInCU: 0x27, offset: 0x28736, size: 0x8, addend: 0x0, symName: __ZN18EfiRuntimeServices3getEb, symObjAddr: 0x9170, symBinAddr: 0xA0E0, symSize: 0x30 }
  - { offsetInCU: 0x3E, offset: 0x2874D, size: 0x8, addend: 0x0, symName: __ZN18EfiRuntimeServices8instanceE, symObjAddr: 0xE5F28, symBinAddr: 0x20968, symSize: 0x0 }
  - { offsetInCU: 0x322, offset: 0x28A31, size: 0x8, addend: 0x0, symName: __ZN18EfiRuntimeServices14LiluVendorGuidE, symObjAddr: 0x1DD74, symBinAddr: 0x1ACD4, symSize: 0x0 }
  - { offsetInCU: 0x339, offset: 0x28A48, size: 0x8, addend: 0x0, symName: __ZN18EfiRuntimeServices16LiluReadOnlyGuidE, symObjAddr: 0x1DD84, symBinAddr: 0x1ACE4, symSize: 0x0 }
  - { offsetInCU: 0x350, offset: 0x28A5F, size: 0x8, addend: 0x0, symName: __ZN18EfiRuntimeServices17LiluWriteOnlyGuidE, symObjAddr: 0x1DD94, symBinAddr: 0x1ACF4, symSize: 0x0 }
  - { offsetInCU: 0x40A, offset: 0x28B19, size: 0x8, addend: 0x0, symName: __ZN18EfiRuntimeServices3getEb, symObjAddr: 0x9170, symBinAddr: 0xA0E0, symSize: 0x30 }
  - { offsetInCU: 0x438, offset: 0x28B47, size: 0x8, addend: 0x0, symName: __ZN18EfiRuntimeServices3putEv, symObjAddr: 0x91A0, symBinAddr: 0xA110, symSize: 0x20 }
  - { offsetInCU: 0x53A, offset: 0x28C49, size: 0x8, addend: 0x0, symName: __ZN18EfiRuntimeServices11resetSystemE14EFI_RESET_TYPE, symObjAddr: 0x91C0, symBinAddr: 0xA130, symSize: 0xC0 }
  - { offsetInCU: 0x628, offset: 0x28D37, size: 0x8, addend: 0x0, symName: __ZN18EfiRuntimeServices11getVariableEPKDsPK8EFI_GUIDPjPyPv, symObjAddr: 0x9280, symBinAddr: 0xA1F0, symSize: 0xB0 }
  - { offsetInCU: 0x763, offset: 0x28E72, size: 0x8, addend: 0x0, symName: __ZN18EfiRuntimeServices11setVariableEPKDsPK8EFI_GUIDjyPv, symObjAddr: 0x9330, symBinAddr: 0xA2A0, symSize: 0xB0 }
  - { offsetInCU: 0x27, offset: 0x29003, size: 0x8, addend: 0x0, symName: __ZN7LiluAPI13requestAccessEmb, symObjAddr: 0x93E0, symBinAddr: 0xA350, symSize: 0x70 }
  - { offsetInCU: 0x45, offset: 0x29021, size: 0x8, addend: 0x0, symName: _lilu, symObjAddr: 0xE9FD8, symBinAddr: 0x20870, symSize: 0x0 }
  - { offsetInCU: 0x22CC, offset: 0x2B2A8, size: 0x8, addend: 0x0, symName: __ZN7LiluAPI13requestAccessEmb, symObjAddr: 0x93E0, symBinAddr: 0xA350, symSize: 0x70 }
  - { offsetInCU: 0x2335, offset: 0x2B311, size: 0x8, addend: 0x0, symName: __ZN7LiluAPI13releaseAccessEv, symObjAddr: 0x9450, symBinAddr: 0xA3C0, symSize: 0x10 }
  - { offsetInCU: 0x233D, offset: 0x2B319, size: 0x8, addend: 0x0, symName: __ZN7LiluAPI10shouldLoadEPKcmjPS1_mS2_mS2_m13KernelVersionS3_Rb, symObjAddr: 0x9460, symBinAddr: 0xA3D0, symSize: 0x2A0 }
  - { offsetInCU: 0x23A3, offset: 0x2B37F, size: 0x8, addend: 0x0, symName: __ZN7LiluAPI10shouldLoadEPKcmjPS1_mS2_mS2_m13KernelVersionS3_Rb, symObjAddr: 0x9460, symBinAddr: 0xA3D0, symSize: 0x2A0 }
  - { offsetInCU: 0x23AB, offset: 0x2B387, size: 0x8, addend: 0x0, symName: __ZN7LiluAPI13onPatcherLoadEPFvPvR13KernelPatcherES0_, symObjAddr: 0x9700, symBinAddr: 0xA670, symSize: 0xD0 }
  - { offsetInCU: 0x26EE, offset: 0x2B6CA, size: 0x8, addend: 0x0, symName: __ZN7LiluAPI13onPatcherLoadEPFvPvR13KernelPatcherES0_, symObjAddr: 0x9700, symBinAddr: 0xA670, symSize: 0xD0 }
  - { offsetInCU: 0x2956, offset: 0x2B932, size: 0x8, addend: 0x0, symName: __ZN7LiluAPI10onKextLoadEPN13KernelPatcher8KextInfoEmPFvPvRS0_mymES3_, symObjAddr: 0x97D0, symBinAddr: 0xA740, symSize: 0x180 }
  - { offsetInCU: 0x2D04, offset: 0x2BCE0, size: 0x8, addend: 0x0, symName: __ZN7LiluAPI10onProcLoadEPN11UserPatcher8ProcInfoEmPFvPvRS0_P8ipc_portPKcmES3_PNS0_13BinaryModInfoEm, symObjAddr: 0x9950, symBinAddr: 0xA8C0, symSize: 0x270 }
  - { offsetInCU: 0x3020, offset: 0x2BFFC, size: 0x8, addend: 0x0, symName: __ZN7LiluAPI20onEntitlementRequestEPFvPvP4taskPKcRP8OSObjectES0_, symObjAddr: 0x9BC0, symBinAddr: 0xAB30, symSize: 0xD0 }
  - { offsetInCU: 0x3173, offset: 0x2C14F, size: 0x8, addend: 0x0, symName: __ZN7LiluAPI21copyClientEntitlementEP4taskPKc, symObjAddr: 0x9C90, symBinAddr: 0xAC00, symSize: 0xA0 }
  - { offsetInCU: 0x3385, offset: 0x2C361, size: 0x8, addend: 0x0, symName: '__ZZN7LiluAPI27processPatcherLoadCallbacksER13KernelPatcherEN3$_08__invokeEPNS0_11KextHandlerE', symObjAddr: 0x9D30, symBinAddr: 0xACA0, symSize: 0x130 }
  - { offsetInCU: 0x33BE, offset: 0x2C39A, size: 0x8, addend: 0x0, symName: '__ZZN7LiluAPI27processPatcherLoadCallbacksER13KernelPatcherEN3$_08__invokeEPNS0_11KextHandlerE', symObjAddr: 0x9D30, symBinAddr: 0xACA0, symSize: 0x130 }
  - { offsetInCU: 0x36A4, offset: 0x2C680, size: 0x8, addend: 0x0, symName: '__ZZN7LiluAPI24processUserLoadCallbacksER11UserPatcherEN3$_18__invokeEPvS1_P8ipc_portPKcm', symObjAddr: 0x9E60, symBinAddr: 0xADD0, symSize: 0x70 }
  - { offsetInCU: 0x27, offset: 0x2C837, size: 0x8, addend: 0x0, symName: __ZN10DeviceInfo22getLegacyFramebufferIdEv, symObjAddr: 0x9ED0, symBinAddr: 0xAE40, symSize: 0x1B0 }
  - { offsetInCU: 0x45, offset: 0x2C855, size: 0x8, addend: 0x0, symName: _globalBaseDeviceInfo, symObjAddr: 0x19E28, symBinAddr: 0x207B8, symSize: 0x0 }
  - { offsetInCU: 0xC5D, offset: 0x2D46D, size: 0x8, addend: 0x0, symName: _globalDeviceInfo, symObjAddr: 0xE5F30, symBinAddr: 0x20970, symSize: 0x0 }
  - { offsetInCU: 0xF04, offset: 0x2D714, size: 0x8, addend: 0x0, symName: __ZN10DeviceInfo22getLegacyFramebufferIdEv, symObjAddr: 0x9ED0, symBinAddr: 0xAE40, symSize: 0x1B0 }
  - { offsetInCU: 0xF83, offset: 0x2D793, size: 0x8, addend: 0x0, symName: __ZN14BaseDeviceInfo3getEv, symObjAddr: 0xA080, symBinAddr: 0xAFF0, symSize: 0x10 }
  - { offsetInCU: 0xFAB, offset: 0x2D7BB, size: 0x8, addend: 0x0, symName: __ZN10DeviceInfo21checkForAndSetAMDiGPUEP15IORegistryEntry, symObjAddr: 0xA090, symBinAddr: 0xB000, symSize: 0x130 }
  - { offsetInCU: 0x18A0, offset: 0x2E0B0, size: 0x8, addend: 0x0, symName: __ZN10DeviceInfo6createEv, symObjAddr: 0xA1C0, symBinAddr: 0xB130, symSize: 0x11A0 }
  - { offsetInCU: 0x217C, offset: 0x2E98C, size: 0x8, addend: 0x0, symName: __ZN10DeviceInfo7deleterEPS_, symObjAddr: 0xB360, symBinAddr: 0xC2D0, symSize: 0x50 }
  - { offsetInCU: 0x2249, offset: 0x2EA59, size: 0x8, addend: 0x0, symName: __ZN10DeviceInfo16processSwitchOffEv, symObjAddr: 0xB3B0, symBinAddr: 0xC320, symSize: 0x770 }
  - { offsetInCU: 0x27, offset: 0x2EEF4, size: 0x8, addend: 0x0, symName: __Z5qsortPvmmPFiPKvS1_E, symObjAddr: 0xBB20, symBinAddr: 0xCA90, symSize: 0xC50 }
  - { offsetInCU: 0x18F, offset: 0x2F05C, size: 0x8, addend: 0x0, symName: __Z5qsortPvmmPFiPKvS1_E, symObjAddr: 0xBB20, symBinAddr: 0xCA90, symSize: 0xC50 }
  - { offsetInCU: 0x27, offset: 0x2F986, size: 0x8, addend: 0x0, symName: __ZN9NVStorage4initEv, symObjAddr: 0xC770, symBinAddr: 0xD6E0, symSize: 0xC0 }
  - { offsetInCU: 0x385, offset: 0x2FCE4, size: 0x8, addend: 0x0, symName: __ZN9NVStorage4saveEPKcjb, symObjAddr: 0xD6B0, symBinAddr: 0xE620, symSize: 0x170 }
  - { offsetInCU: 0x13C5, offset: 0x30D24, size: 0x8, addend: 0x0, symName: __ZN9NVStorage4initEv, symObjAddr: 0xC770, symBinAddr: 0xD6E0, symSize: 0xC0 }
  - { offsetInCU: 0x13F9, offset: 0x30D58, size: 0x8, addend: 0x0, symName: __ZN9NVStorage6deinitEv, symObjAddr: 0xC830, symBinAddr: 0xD7A0, symSize: 0x30 }
  - { offsetInCU: 0x142D, offset: 0x30D8C, size: 0x8, addend: 0x0, symName: __ZN9NVStorage4readEPKcRjhPKh, symObjAddr: 0xC860, symBinAddr: 0xD7D0, symSize: 0x310 }
  - { offsetInCU: 0x1686, offset: 0x30FE5, size: 0x8, addend: 0x0, symName: '__ZZN9NVStorage4readEPKcRjhPKhENK3$_0clES4_j', symObjAddr: 0xCB70, symBinAddr: 0xDAE0, symSize: 0xB0 }
  - { offsetInCU: 0x179D, offset: 0x310FC, size: 0x8, addend: 0x0, symName: __ZN9NVStorage10decompressEPKhRjb, symObjAddr: 0xCC20, symBinAddr: 0xDB90, symSize: 0x140 }
  - { offsetInCU: 0x1956, offset: 0x312B5, size: 0x8, addend: 0x0, symName: __ZN9NVStorage4readEPKchPKh, symObjAddr: 0xCD60, symBinAddr: 0xDCD0, symSize: 0xE0 }
  - { offsetInCU: 0x1A91, offset: 0x313F0, size: 0x8, addend: 0x0, symName: __ZN9NVStorage5writeEPKcPKhjhS3_, symObjAddr: 0xCE40, symBinAddr: 0xDDB0, symSize: 0x500 }
  - { offsetInCU: 0x20C4, offset: 0x31A23, size: 0x8, addend: 0x0, symName: __ZN9NVStorage8compressEPKhRjb, symObjAddr: 0xD340, symBinAddr: 0xE2B0, symSize: 0x130 }
  - { offsetInCU: 0x2260, offset: 0x31BBF, size: 0x8, addend: 0x0, symName: __ZN9NVStorage5writeEPKcPK6OSDatahPKh, symObjAddr: 0xD470, symBinAddr: 0xE3E0, symSize: 0xA0 }
  - { offsetInCU: 0x2318, offset: 0x31C77, size: 0x8, addend: 0x0, symName: __ZN9NVStorage6removeEPKcb, symObjAddr: 0xD510, symBinAddr: 0xE480, symSize: 0x150 }
  - { offsetInCU: 0x2400, offset: 0x31D5F, size: 0x8, addend: 0x0, symName: __ZN9NVStorage4syncEv, symObjAddr: 0xD660, symBinAddr: 0xE5D0, symSize: 0x50 }
  - { offsetInCU: 0x243C, offset: 0x31D9B, size: 0x8, addend: 0x0, symName: __ZN9NVStorage6existsEPKc, symObjAddr: 0xD820, symBinAddr: 0xE790, symSize: 0x30 }
  - { offsetInCU: 0x27, offset: 0x31E15, size: 0x8, addend: 0x0, symName: _hde64_disasm, symObjAddr: 0xD850, symBinAddr: 0xE7C0, symSize: 0x2170 }
  - { offsetInCU: 0x45, offset: 0x31E33, size: 0x8, addend: 0x0, symName: _hde64_table, symObjAddr: 0x1DDB0, symBinAddr: 0x1AD10, symSize: 0x0 }
  - { offsetInCU: 0x68, offset: 0x31E56, size: 0x8, addend: 0x0, symName: _hde64_disasm, symObjAddr: 0xD850, symBinAddr: 0xE7C0, symSize: 0x2170 }
  - { offsetInCU: 0x27, offset: 0x32356, size: 0x8, addend: 0x0, symName: __ZN11Compression10decompressEjjPKhjPh, symObjAddr: 0xF9C0, symBinAddr: 0x10930, symSize: 0x20 }
  - { offsetInCU: 0xC1, offset: 0x323F0, size: 0x8, addend: 0x0, symName: __ZN11Compression10decompressEjjPKhjPh, symObjAddr: 0xF9C0, symBinAddr: 0x10930, symSize: 0x20 }
  - { offsetInCU: 0xC9, offset: 0x323F8, size: 0x8, addend: 0x0, symName: __ZL18decompressInternaljPjPKhjPhb, symObjAddr: 0xF9E0, symBinAddr: 0x10950, symSize: 0x4C0 }
  - { offsetInCU: 0x11F, offset: 0x3244E, size: 0x8, addend: 0x0, symName: __ZN11Compression10decompressEjPjPKhjPh, symObjAddr: 0xFEF0, symBinAddr: 0x10E60, symSize: 0x10 }
  - { offsetInCU: 0x1A8, offset: 0x324D7, size: 0x8, addend: 0x0, symName: __ZN11Compression8compressEjRjPKhjPh, symObjAddr: 0xFF00, symBinAddr: 0x10E70, symSize: 0x8A0 }
  - { offsetInCU: 0xC03, offset: 0x32F32, size: 0x8, addend: 0x0, symName: __ZL18decompressInternaljPjPKhjPhb, symObjAddr: 0xF9E0, symBinAddr: 0x10950, symSize: 0x4C0 }
  - { offsetInCU: 0xED5, offset: 0x33204, size: 0x8, addend: 0x0, symName: __ZL7z_allocPvjj, symObjAddr: 0xFEA0, symBinAddr: 0x10E10, symSize: 0x30 }
  - { offsetInCU: 0xF7E, offset: 0x332AD, size: 0x8, addend: 0x0, symName: __ZL6z_freePvS_, symObjAddr: 0xFED0, symBinAddr: 0x10E40, symSize: 0x20 }
  - { offsetInCU: 0x11A5, offset: 0x334D4, size: 0x8, addend: 0x0, symName: __ZL11insert_nodeP12encode_statei, symObjAddr: 0x107A0, symBinAddr: 0x11710, symSize: 0x360 }
  - { offsetInCU: 0x27, offset: 0x3358B, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher9vmProtectEP8ipc_portmmii, symObjAddr: 0x10B00, symBinAddr: 0x11A70, symSize: 0x1D0 }
  - { offsetInCU: 0x39, offset: 0x3359D, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher13injectPayloadEP8ipc_portPhmPv, symObjAddr: 0x128B0, symBinAddr: 0x13820, symSize: 0x5B0 }
  - { offsetInCU: 0x33A, offset: 0x3389E, size: 0x8, addend: 0x0, symName: __ZL4that.296, symObjAddr: 0xE5F38, symBinAddr: 0x20978, symSize: 0x0 }
  - { offsetInCU: 0x1CBB, offset: 0x3521F, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher9vmProtectEP8ipc_portmmii, symObjAddr: 0x10B00, symBinAddr: 0x11A70, symSize: 0x1D0 }
  - { offsetInCU: 0x1F92, offset: 0x354F6, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher12execListenerEP5ucredPvimmmm, symObjAddr: 0x10CD0, symBinAddr: 0x11C40, symSize: 0x310 }
  - { offsetInCU: 0x2259, offset: 0x357BD, size: 0x8, addend: 0x0, symName: __ZN11ThreadLocalIPN11UserPatcher11PendingUserELm32EE3getEv, symObjAddr: 0x10FE0, symBinAddr: 0x11F50, symSize: 0x300 }
  - { offsetInCU: 0x22C8, offset: 0x3582C, size: 0x8, addend: 0x0, symName: __ZN11ThreadLocalIPN11UserPatcher11PendingUserELm32EE5eraseEv, symObjAddr: 0x112E0, symBinAddr: 0x12250, symSize: 0x310 }
  - { offsetInCU: 0x2444, offset: 0x359A8, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher16patchSharedCacheEP8ipc_portjib, symObjAddr: 0x115F0, symBinAddr: 0x12560, symSize: 0x430 }
  - { offsetInCU: 0x28CE, offset: 0x35E32, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher14injectRestrictEP8ipc_port, symObjAddr: 0x11A20, symBinAddr: 0x12990, symSize: 0x560 }
  - { offsetInCU: 0x2BD2, offset: 0x36136, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher28codeSignValidateRangeWrapperEPvP8ipc_portyPKvyPj, symObjAddr: 0x11F80, symBinAddr: 0x12EF0, symSize: 0x40 }
  - { offsetInCU: 0x2C85, offset: 0x361E9, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher27codeSignValidatePageWrapperEPvP8ipc_portyPKvPj, symObjAddr: 0x11FC0, symBinAddr: 0x12F30, symSize: 0x40 }
  - { offsetInCU: 0x2D23, offset: 0x36287, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher20taskSetMainThreadQosEP4taskP6thread, symObjAddr: 0x12000, symBinAddr: 0x12F70, symSize: 0x110 }
  - { offsetInCU: 0x2DC5, offset: 0x36329, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher21vmSharedRegionMapFileEPvjPS0_P8ipc_portyS0_jyy, symObjAddr: 0x12110, symBinAddr: 0x13080, symSize: 0x50 }
  - { offsetInCU: 0x2EB4, offset: 0x36418, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher25vmSharedRegionSlideMojaveEjyyyyyP8ipc_port, symObjAddr: 0x12160, symBinAddr: 0x130D0, symSize: 0x70 }
  - { offsetInCU: 0x2F66, offset: 0x364CA, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher19vmSharedRegionSlideEjyyyyP8ipc_port, symObjAddr: 0x121D0, symBinAddr: 0x13140, symSize: 0x70 }
  - { offsetInCU: 0x3003, offset: 0x36567, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher16performPagePatchEPKvm, symObjAddr: 0x12240, symBinAddr: 0x131B0, symSize: 0x350 }
  - { offsetInCU: 0x334E, offset: 0x368B2, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher13LookupStorage7deleterEPS0_, symObjAddr: 0x12590, symBinAddr: 0x13500, symSize: 0x100 }
  - { offsetInCU: 0x3466, offset: 0x369CA, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher13getTaskHeaderEP8ipc_portR14mach_header_64, symObjAddr: 0x12690, symBinAddr: 0x13600, symSize: 0x70 }
  - { offsetInCU: 0x34EF, offset: 0x36A53, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher17getPageProtectionEP8ipc_porty, symObjAddr: 0x12700, symBinAddr: 0x13670, symSize: 0x90 }
  - { offsetInCU: 0x3545, offset: 0x36AA9, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher13injectSegmentEP8ipc_portmPhmi, symObjAddr: 0x12790, symBinAddr: 0x13700, symSize: 0x120 }
  - { offsetInCU: 0x360E, offset: 0x36B72, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher18getSharedCachePathEv, symObjAddr: 0x12E60, symBinAddr: 0x13DD0, symSize: 0x60 }
  - { offsetInCU: 0x3661, offset: 0x36BC5, size: 0x8, addend: 0x0, symName: __ZN11UserPatcher20matchSharedCachePathEPKc, symObjAddr: 0x12EC0, symBinAddr: 0x13E30, symSize: 0xC0 }
  - { offsetInCU: 0x27, offset: 0x36CCF, size: 0x8, addend: 0x0, symName: __ZN7CPUInfo8getCpuidEjjPjS0_S0_S0_, symObjAddr: 0x12F80, symBinAddr: 0x13EF0, symSize: 0x70 }
  - { offsetInCU: 0x2F8, offset: 0x36FA0, size: 0x8, addend: 0x0, symName: __ZN7CPUInfo8getCpuidEjjPjS0_S0_S0_, symObjAddr: 0x12F80, symBinAddr: 0x13EF0, symSize: 0x70 }
  - { offsetInCU: 0x3B3, offset: 0x3705B, size: 0x8, addend: 0x0, symName: __ZN7CPUInfo13getGenerationEPjS0_S0_, symObjAddr: 0x12FF0, symBinAddr: 0x13F60, symSize: 0x40 }
  - { offsetInCU: 0x425, offset: 0x370CD, size: 0x8, addend: 0x0, symName: __ZN7CPUInfo14getCpuTopologyERNS_11CpuTopologyE, symObjAddr: 0x13030, symBinAddr: 0x13FA0, symSize: 0x1C0 }
  - { offsetInCU: 0x27, offset: 0x37F5F, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapperD1Ev, symObjAddr: 0x131F0, symBinAddr: 0x14160, symSize: 0x10 }
  - { offsetInCU: 0x4A, offset: 0x37F82, size: 0x8, addend: 0x0, symName: _Lilu_debugEnabled, symObjAddr: 0xE5F40, symBinAddr: 0x20980, symSize: 0x0 }
  - { offsetInCU: 0x65, offset: 0x37F9D, size: 0x8, addend: 0x0, symName: _Lilu_debugPrintDelay, symObjAddr: 0xE5F44, symBinAddr: 0x20984, symSize: 0x0 }
  - { offsetInCU: 0x74, offset: 0x37FAC, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapper10gMetaClassE, symObjAddr: 0xEA0A0, symBinAddr: 0x20938, symSize: 0x0 }
  - { offsetInCU: 0x297, offset: 0x381CF, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapper9metaClassE, symObjAddr: 0x1ECC0, symBinAddr: 0x1FCB0, symSize: 0x0 }
  - { offsetInCU: 0x2AE, offset: 0x381E6, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapper10superClassE, symObjAddr: 0x1ECC8, symBinAddr: 0x1FCB8, symSize: 0x0 }
  - { offsetInCU: 0x3E6, offset: 0x3831E, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapperD1Ev, symObjAddr: 0x131F0, symBinAddr: 0x14160, symSize: 0x10 }
  - { offsetInCU: 0x460, offset: 0x38398, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapperD0Ev, symObjAddr: 0x13200, symBinAddr: 0x14170, symSize: 0x30 }
  - { offsetInCU: 0x4EB, offset: 0x38423, size: 0x8, addend: 0x0, symName: __ZNK15OSObjectWrapper12getMetaClassEv, symObjAddr: 0x13230, symBinAddr: 0x141A0, symSize: 0x10 }
  - { offsetInCU: 0x548, offset: 0x38480, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapper9MetaClassD1Ev, symObjAddr: 0x13240, symBinAddr: 0x141B0, symSize: 0x10 }
  - { offsetInCU: 0x5C5, offset: 0x384FD, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapper9MetaClassD0Ev, symObjAddr: 0x13250, symBinAddr: 0x141C0, symSize: 0x10 }
  - { offsetInCU: 0x69C, offset: 0x385D4, size: 0x8, addend: 0x0, symName: __ZNK15OSObjectWrapper9MetaClass5allocEv, symObjAddr: 0x13260, symBinAddr: 0x141D0, symSize: 0x50 }
  - { offsetInCU: 0x76F, offset: 0x386A7, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_kern_util.cpp, symObjAddr: 0x132B0, symBinAddr: 0x14220, symSize: 0x40 }
  - { offsetInCU: 0x7FB, offset: 0x38733, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a.394, symObjAddr: 0x132F0, symBinAddr: 0x14260, symSize: 0x20 }
  - { offsetInCU: 0x86C, offset: 0x387A4, size: 0x8, addend: 0x0, symName: _lilu_os_log, symObjAddr: 0x13310, symBinAddr: 0x14280, symSize: 0x130 }
  - { offsetInCU: 0x8C6, offset: 0x387FE, size: 0x8, addend: 0x0, symName: __Z6strstrPKcS0_m, symObjAddr: 0x13440, symBinAddr: 0x143B0, symSize: 0x80 }
  - { offsetInCU: 0x918, offset: 0x38850, size: 0x8, addend: 0x0, symName: __Z7strrchrPKci, symObjAddr: 0x134C0, symBinAddr: 0x14430, symSize: 0x30 }
  - { offsetInCU: 0x978, offset: 0x388B0, size: 0x8, addend: 0x0, symName: _lilu_os_free, symObjAddr: 0x134F0, symBinAddr: 0x14460, symSize: 0x20 }
  - { offsetInCU: 0x9A0, offset: 0x388D8, size: 0x8, addend: 0x0, symName: __ZN4Page5allocEv, symObjAddr: 0x13510, symBinAddr: 0x14480, symSize: 0x60 }
  - { offsetInCU: 0x9CD, offset: 0x38905, size: 0x8, addend: 0x0, symName: __ZN4Page7protectEi, symObjAddr: 0x13570, symBinAddr: 0x144E0, symSize: 0x30 }
  - { offsetInCU: 0x9D5, offset: 0x3890D, size: 0x8, addend: 0x0, symName: __ZN4Page6createEv, symObjAddr: 0x135A0, symBinAddr: 0x14510, symSize: 0x20 }
  - { offsetInCU: 0xA15, offset: 0x3894D, size: 0x8, addend: 0x0, symName: __ZN4Page6createEv, symObjAddr: 0x135A0, symBinAddr: 0x14510, symSize: 0x20 }
  - { offsetInCU: 0xA86, offset: 0x389BE, size: 0x8, addend: 0x0, symName: __ZN4Page7deleterEPS_, symObjAddr: 0x135C0, symBinAddr: 0x14530, symSize: 0x40 }
  - { offsetInCU: 0xAAF, offset: 0x389E7, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapper9MetaClassC1Ev, symObjAddr: 0x13600, symBinAddr: 0x14570, symSize: 0x40 }
  - { offsetInCU: 0xB06, offset: 0x38A3E, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapperC2EPK11OSMetaClass, symObjAddr: 0x13640, symBinAddr: 0x145B0, symSize: 0x30 }
  - { offsetInCU: 0xB74, offset: 0x38AAC, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapperC1EPK11OSMetaClass, symObjAddr: 0x13670, symBinAddr: 0x145E0, symSize: 0x30 }
  - { offsetInCU: 0xBF6, offset: 0x38B2E, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapperD2Ev, symObjAddr: 0x136A0, symBinAddr: 0x14610, symSize: 0x10 }
  - { offsetInCU: 0xC22, offset: 0x38B5A, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapper9MetaClassC2Ev, symObjAddr: 0x136B0, symBinAddr: 0x14620, symSize: 0x40 }
  - { offsetInCU: 0xC4F, offset: 0x38B87, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapperC1Ev, symObjAddr: 0x136F0, symBinAddr: 0x14660, symSize: 0x40 }
  - { offsetInCU: 0xCA6, offset: 0x38BDE, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapperC2Ev, symObjAddr: 0x13730, symBinAddr: 0x146A0, symSize: 0x40 }
  - { offsetInCU: 0xCD3, offset: 0x38C0B, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapper4initEPv, symObjAddr: 0x13770, symBinAddr: 0x146E0, symSize: 0x30 }
  - { offsetInCU: 0xD3C, offset: 0x38C74, size: 0x8, addend: 0x0, symName: __ZN15OSObjectWrapper4withEPv, symObjAddr: 0x137A0, symBinAddr: 0x14710, symSize: 0x80 }
  - { offsetInCU: 0x27, offset: 0x38DF5, size: 0x8, addend: 0x0, symName: __ZN8MachInfo4initEPKPKcmPS_b, symObjAddr: 0x13820, symBinAddr: 0x14790, symSize: 0x740 }
  - { offsetInCU: 0x3F, offset: 0x38E0D, size: 0x8, addend: 0x0, symName: __ZN8MachInfo14findKernelBaseEv, symObjAddr: 0x13F60, symBinAddr: 0x14ED0, symSize: 0x1E0 }
  - { offsetInCU: 0x71, offset: 0x38E3F, size: 0x8, addend: 0x0, symName: __ZZN8MachInfo14findKernelBaseEvE13m_kernel_base, symObjAddr: 0xE9F90, symBinAddr: 0x249D0, symSize: 0x0 }
  - { offsetInCU: 0x8A, offset: 0x38E58, size: 0x8, addend: 0x0, symName: __ZZN8MachInfo14findKernelBaseEvE19m_kernel_collection, symObjAddr: 0xE9F98, symBinAddr: 0x249D8, symSize: 0x0 }
  - { offsetInCU: 0x1A7, offset: 0x38F75, size: 0x8, addend: 0x0, symName: __ZN8MachInfo16setKernelWritingEbP13_IOSimpleLock, symObjAddr: 0x15360, symBinAddr: 0x162D0, symSize: 0x110 }
  - { offsetInCU: 0x1D4, offset: 0x38FA2, size: 0x8, addend: 0x0, symName: __ZZN8MachInfo16setKernelWritingEbP13_IOSimpleLockE18interruptsDisabled, symObjAddr: 0xE9F99, symBinAddr: 0x249D9, symSize: 0x0 }
  - { offsetInCU: 0xC86, offset: 0x39A54, size: 0x8, addend: 0x0, symName: __ZN8MachInfo4initEPKPKcmPS_b, symObjAddr: 0x13820, symBinAddr: 0x14790, symSize: 0x740 }
  - { offsetInCU: 0x119C, offset: 0x39F6A, size: 0x8, addend: 0x0, symName: __ZN8MachInfo17initFromPrelinkedEPS_, symObjAddr: 0x14140, symBinAddr: 0x150B0, symSize: 0x670 }
  - { offsetInCU: 0x11A4, offset: 0x39F72, size: 0x8, addend: 0x0, symName: __ZN8MachInfo17processMachHeaderEPv, symObjAddr: 0x147B0, symBinAddr: 0x15720, symSize: 0x160 }
  - { offsetInCU: 0x1452, offset: 0x3A220, size: 0x8, addend: 0x0, symName: __ZN8MachInfo17processMachHeaderEPv, symObjAddr: 0x147B0, symBinAddr: 0x15720, symSize: 0x160 }
  - { offsetInCU: 0x1593, offset: 0x3A361, size: 0x8, addend: 0x0, symName: __ZN8MachInfo11readSymbolsEP5vnodeP11vfs_context, symObjAddr: 0x14910, symBinAddr: 0x15880, symSize: 0x130 }
  - { offsetInCU: 0x16F9, offset: 0x3A4C7, size: 0x8, addend: 0x0, symName: __ZN8MachInfo17findSectionBoundsEPvmRmS1_RS0_S1_PKcS4_i, symObjAddr: 0x14A40, symBinAddr: 0x159B0, symSize: 0x440 }
  - { offsetInCU: 0x19FB, offset: 0x3A7C9, size: 0x8, addend: 0x0, symName: __ZN8MachInfo14readMachHeaderEPhP5vnodeP11vfs_contextx, symObjAddr: 0x14E80, symBinAddr: 0x15DF0, symSize: 0x350 }
  - { offsetInCU: 0x1E1B, offset: 0x3ABE9, size: 0x8, addend: 0x0, symName: __ZN8MachInfo15isCurrentBinaryEy, symObjAddr: 0x151D0, symBinAddr: 0x16140, symSize: 0x110 }
  - { offsetInCU: 0x1F9F, offset: 0x3AD6D, size: 0x8, addend: 0x0, symName: __ZN8MachInfo6deinitEv, symObjAddr: 0x152E0, symBinAddr: 0x16250, symSize: 0x60 }
  - { offsetInCU: 0x20A1, offset: 0x3AE6F, size: 0x8, addend: 0x0, symName: __ZN8MachInfo13setInterruptsEb, symObjAddr: 0x15340, symBinAddr: 0x162B0, symSize: 0x20 }
  - { offsetInCU: 0x20D6, offset: 0x3AEA4, size: 0x8, addend: 0x0, symName: __ZN8MachInfo11solveSymbolEPKc, symObjAddr: 0x15470, symBinAddr: 0x163E0, symSize: 0x170 }
  - { offsetInCU: 0x22D4, offset: 0x3B0A2, size: 0x8, addend: 0x0, symName: __ZN8MachInfo19getRunningAddressesEymb, symObjAddr: 0x155E0, symBinAddr: 0x16550, symSize: 0x480 }
  - { offsetInCU: 0x25A3, offset: 0x3B371, size: 0x8, addend: 0x0, symName: __ZN8MachInfo19setRunningAddressesEym, symObjAddr: 0x15A60, symBinAddr: 0x169D0, symSize: 0x20 }
  - { offsetInCU: 0x25FD, offset: 0x3B3CB, size: 0x8, addend: 0x0, symName: __ZN8MachInfo18getRunningPositionERPhRm, symObjAddr: 0x15A80, symBinAddr: 0x169F0, symSize: 0x30 }
  - { offsetInCU: 0x27, offset: 0x3B436, size: 0x8, addend: 0x0, symName: __ZN6FileIO16readFileToBufferEPKcRm, symObjAddr: 0x15AB0, symBinAddr: 0x16A20, symSize: 0x1D0 }
  - { offsetInCU: 0x2F, offset: 0x3B43E, size: 0x8, addend: 0x0, symName: _lzvn_decode, symObjAddr: 0x15F80, symBinAddr: 0x16EF0, symSize: 0xF90 }
  - { offsetInCU: 0x178, offset: 0x3B587, size: 0x8, addend: 0x0, symName: __ZN6FileIO16readFileToBufferEPKcRm, symObjAddr: 0x15AB0, symBinAddr: 0x16A20, symSize: 0x1D0 }
  - { offsetInCU: 0x338, offset: 0x3B747, size: 0x8, addend: 0x0, symName: __ZN6FileIO13performFileIOEPvxmP5vnodeP11vfs_contextb, symObjAddr: 0x15C80, symBinAddr: 0x16BF0, symSize: 0x120 }
  - { offsetInCU: 0x3F5, offset: 0x3B804, size: 0x8, addend: 0x0, symName: __ZN6FileIO12readFileSizeEP5vnodeP11vfs_context, symObjAddr: 0x15DA0, symBinAddr: 0x16D10, symSize: 0x90 }
  - { offsetInCU: 0x43A, offset: 0x3B849, size: 0x8, addend: 0x0, symName: __ZN6FileIO12readFileDataEPvxmP5vnodeP11vfs_context, symObjAddr: 0x15E30, symBinAddr: 0x16DA0, symSize: 0x10 }
  - { offsetInCU: 0x4F6, offset: 0x3B905, size: 0x8, addend: 0x0, symName: __ZN6FileIO17writeBufferToFileEPKcPvmii, symObjAddr: 0x15E40, symBinAddr: 0x16DB0, symSize: 0x130 }
  - { offsetInCU: 0x610, offset: 0x3BA1F, size: 0x8, addend: 0x0, symName: __ZN6FileIO13writeFileDataEPvxmP5vnodeP11vfs_context, symObjAddr: 0x15F70, symBinAddr: 0x16EE0, symSize: 0x10 }
  - { offsetInCU: 0x618, offset: 0x3BA27, size: 0x8, addend: 0x0, symName: _lzvn_decode, symObjAddr: 0x15F80, symBinAddr: 0x16EF0, symSize: 0xF90 }
  - { offsetInCU: 0x27, offset: 0x3BB0C, size: 0x8, addend: 0x0, symName: _lzvn_decode, symObjAddr: 0x15F80, symBinAddr: 0x16EF0, symSize: 0xF90 }
  - { offsetInCU: 0x39, offset: 0x3BB1E, size: 0x8, addend: 0x0, symName: _lzvn_decode, symObjAddr: 0x15F80, symBinAddr: 0x16EF0, symSize: 0xF90 }
  - { offsetInCU: 0x62, offset: 0x3BB47, size: 0x8, addend: 0x0, symName: _lzvn_decode.opc_tbl, symObjAddr: 0x1EEF0, symBinAddr: 0x1FEE0, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x3C45B, size: 0x8, addend: 0x0, symName: __ZN6WIOKit11getPropertyEP15IORegistryEntryPKc, symObjAddr: 0x16F10, symBinAddr: 0x17E80, symSize: 0x80 }
  - { offsetInCU: 0x2F, offset: 0x3C463, size: 0x8, addend: 0x0, symName: __ZN6Crypto12genUniqueKeyEj, symObjAddr: 0x17AD0, symBinAddr: 0x18A40, symSize: 0x80 }
  - { offsetInCU: 0x11E, offset: 0x3C552, size: 0x8, addend: 0x0, symName: __ZN6WIOKit11getPropertyEP15IORegistryEntryPKc, symObjAddr: 0x16F10, symBinAddr: 0x17E80, symSize: 0x80 }
  - { offsetInCU: 0x1A5, offset: 0x3C5D9, size: 0x8, addend: 0x0, symName: __ZN6WIOKit15awaitPublishingEP15IORegistryEntry, symObjAddr: 0x16F90, symBinAddr: 0x17F00, symSize: 0xB0 }
  - { offsetInCU: 0x206, offset: 0x3C63A, size: 0x8, addend: 0x0, symName: __ZN6WIOKit18readPCIConfigValueEP15IORegistryEntryjjj, symObjAddr: 0x17040, symBinAddr: 0x17FB0, symSize: 0x3D0 }
  - { offsetInCU: 0x30A, offset: 0x3C73E, size: 0x8, addend: 0x0, symName: __ZN6WIOKit16getDeviceAddressEP15IORegistryEntryRhS2_S2_, symObjAddr: 0x17410, symBinAddr: 0x18380, symSize: 0x60 }
  - { offsetInCU: 0x3B0, offset: 0x3C7E4, size: 0x8, addend: 0x0, symName: __ZN6WIOKit16getComputerModelEv, symObjAddr: 0x17470, symBinAddr: 0x183E0, symSize: 0x10 }
  - { offsetInCU: 0x3D8, offset: 0x3C80C, size: 0x8, addend: 0x0, symName: __ZN6WIOKit15getComputerInfoEPcmS0_m, symObjAddr: 0x17480, symBinAddr: 0x183F0, symSize: 0x50 }
  - { offsetInCU: 0x44D, offset: 0x3C881, size: 0x8, addend: 0x0, symName: __ZN6WIOKit17findEntryByPrefixEPKcS1_PK15IORegistryPlanePFbPvP15IORegistryEntryEbS5_, symObjAddr: 0x174D0, symBinAddr: 0x18440, symSize: 0x70 }
  - { offsetInCU: 0x455, offset: 0x3C889, size: 0x8, addend: 0x0, symName: __ZN6WIOKit17findEntryByPrefixEP15IORegistryEntryPKcPK15IORegistryPlanePFbPvS1_EbS7_, symObjAddr: 0x17540, symBinAddr: 0x184B0, symSize: 0x340 }
  - { offsetInCU: 0x51C, offset: 0x3C950, size: 0x8, addend: 0x0, symName: __ZN6WIOKit17findEntryByPrefixEP15IORegistryEntryPKcPK15IORegistryPlanePFbPvS1_EbS7_, symObjAddr: 0x17540, symBinAddr: 0x184B0, symSize: 0x340 }
  - { offsetInCU: 0x637, offset: 0x3CA6B, size: 0x8, addend: 0x0, symName: __ZN6WIOKit19usingPrelinkedCacheEv, symObjAddr: 0x17880, symBinAddr: 0x187F0, symSize: 0x80 }
  - { offsetInCU: 0x682, offset: 0x3CAB6, size: 0x8, addend: 0x0, symName: __ZN6WIOKit12renameDeviceEP15IORegistryEntryPKcb, symObjAddr: 0x17900, symBinAddr: 0x18870, symSize: 0x1D0 }
  - { offsetInCU: 0x68A, offset: 0x3CABE, size: 0x8, addend: 0x0, symName: __ZN6Crypto12genUniqueKeyEj, symObjAddr: 0x17AD0, symBinAddr: 0x18A40, symSize: 0x80 }
  - { offsetInCU: 0x27, offset: 0x3CE04, size: 0x8, addend: 0x0, symName: __ZN6Crypto12genUniqueKeyEj, symObjAddr: 0x17AD0, symBinAddr: 0x18A40, symSize: 0x80 }
  - { offsetInCU: 0xD4, offset: 0x3CEB1, size: 0x8, addend: 0x0, symName: __ZN6Crypto12genUniqueKeyEj, symObjAddr: 0x17AD0, symBinAddr: 0x18A40, symSize: 0x80 }
  - { offsetInCU: 0x18C, offset: 0x3CF69, size: 0x8, addend: 0x0, symName: __ZN6Crypto7encryptEPKhS1_Rj, symObjAddr: 0x17B50, symBinAddr: 0x18AC0, symSize: 0x4E0 }
  - { offsetInCU: 0x48B, offset: 0x3D268, size: 0x8, addend: 0x0, symName: __ZN6Crypto7decryptEPKhS1_Rj, symObjAddr: 0x18030, symBinAddr: 0x18FA0, symSize: 0x300 }
  - { offsetInCU: 0x493, offset: 0x3D270, size: 0x8, addend: 0x0, symName: __ZN6Crypto4hashEPKhj, symObjAddr: 0x18330, symBinAddr: 0x192A0, symSize: 0x740 }
  - { offsetInCU: 0x578, offset: 0x3D355, size: 0x8, addend: 0x0, symName: __ZN6Crypto4hashEPKhj, symObjAddr: 0x18330, symBinAddr: 0x192A0, symSize: 0x740 }
  - { offsetInCU: 0x5CA, offset: 0x3D3A7, size: 0x8, addend: 0x0, symName: __ZN6Crypto4hashEPKhj, symObjAddr: 0x18330, symBinAddr: 0x192A0, symSize: 0x740 }
  - { offsetInCU: 0x6CD, offset: 0x3D4AA, size: 0x8, addend: 0x0, symName: __ZN6Crypto4hashEPKhj, symObjAddr: 0x18330, symBinAddr: 0x192A0, symSize: 0x740 }
  - { offsetInCU: 0x27, offset: 0x3D873, size: 0x8, addend: 0x0, symName: _sha256_transform, symObjAddr: 0x18A70, symBinAddr: 0x199E0, symSize: 0x2B0 }
  - { offsetInCU: 0x2F, offset: 0x3D87B, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher20tempExecutableMemoryE, symObjAddr: 0x18D20, symBinAddr: 0x19C90, symSize: 0x1000 }
  - { offsetInCU: 0x45, offset: 0x3D891, size: 0x8, addend: 0x0, symName: _k, symObjAddr: 0x1DFD0, symBinAddr: 0x1AF30, symSize: 0x0 }
  - { offsetInCU: 0x188, offset: 0x3D9D4, size: 0x8, addend: 0x0, symName: _sha256_transform, symObjAddr: 0x18A70, symBinAddr: 0x199E0, symSize: 0x2B0 }
  - { offsetInCU: 0x190, offset: 0x3D9DC, size: 0x8, addend: 0x0, symName: __ZN13KernelPatcher20tempExecutableMemoryE, symObjAddr: 0x18D20, symBinAddr: 0x19C90, symSize: 0x1000 }
...
