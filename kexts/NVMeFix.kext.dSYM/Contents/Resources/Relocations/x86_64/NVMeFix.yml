---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/work/NVMeFix/NVMeFix/build/Release/NVMeFix.kext/Contents/MacOS/NVMeFix'
relocations:
  - { offsetInCU: 0x33, offset: 0x33, size: 0x8, addend: 0x0, symName: _kmod_info, symObjAddr: 0x3010, symBinAddr: 0x5338, symSize: 0x0 }
  - { offsetInCU: 0x1DF, offset: 0x1DF, size: 0x8, addend: 0x0, symName: __realmain, symObjAddr: 0x30D8, symBinAddr: 0x5400, symSize: 0x0 }
  - { offsetInCU: 0x1F4, offset: 0x1F4, size: 0x8, addend: 0x0, symName: __antimain, symObjAddr: 0x30E0, symBinAddr: 0x5408, symSize: 0x0 }
  - { offsetInCU: 0x26, offset: 0x223, size: 0x8, addend: 0x0, symName: __ZN7NVMeFixD1Ev, symObjAddr: 0x0, symBinAddr: 0x5F0, symSize: 0x10 }
  - { offsetInCU: 0x3F, offset: 0x23C, size: 0x8, addend: 0x0, symName: _NVMeFix_startSuccess, symObjAddr: 0x47A70, symBinAddr: 0x55B0, symSize: 0x0 }
  - { offsetInCU: 0x62, offset: 0x25F, size: 0x8, addend: 0x0, symName: _NVMeFix_debugEnabled, symObjAddr: 0x47A71, symBinAddr: 0x55B1, symSize: 0x0 }
  - { offsetInCU: 0x77, offset: 0x274, size: 0x8, addend: 0x0, symName: _NVMeFix_debugPrintDelay, symObjAddr: 0x47A74, symBinAddr: 0x55B4, symSize: 0x0 }
  - { offsetInCU: 0x86, offset: 0x283, size: 0x8, addend: 0x0, symName: __ZN7NVMeFix10gMetaClassE, symObjAddr: 0x47AA8, symBinAddr: 0x5588, symSize: 0x0 }
  - { offsetInCU: 0x9372, offset: 0x956F, size: 0x8, addend: 0x0, symName: __ZN7NVMeFix9metaClassE, symObjAddr: 0x3280, symBinAddr: 0x4088, symSize: 0x0 }
  - { offsetInCU: 0x9385, offset: 0x9582, size: 0x8, addend: 0x0, symName: __ZN7NVMeFix10superClassE, symObjAddr: 0x3288, symBinAddr: 0x4090, symSize: 0x0 }
  - { offsetInCU: 0x93AA, offset: 0x95A7, size: 0x8, addend: 0x0, symName: _NVMeFix_selfInstance, symObjAddr: 0x47A78, symBinAddr: 0x55B8, symSize: 0x0 }
  - { offsetInCU: 0x93C4, offset: 0x95C1, size: 0x8, addend: 0x0, symName: __ZL11kextVersion, symObjAddr: 0x4ED0, symBinAddr: 0x3FA0, symSize: 0x0 }
  - { offsetInCU: 0xD53E, offset: 0xD73B, size: 0x8, addend: 0x0, symName: __ZN7NVMeFixD1Ev, symObjAddr: 0x0, symBinAddr: 0x5F0, symSize: 0x10 }
  - { offsetInCU: 0xD596, offset: 0xD793, size: 0x8, addend: 0x0, symName: __ZN7NVMeFixD0Ev, symObjAddr: 0x10, symBinAddr: 0x600, symSize: 0x30 }
  - { offsetInCU: 0xD5FF, offset: 0xD7FC, size: 0x8, addend: 0x0, symName: __ZNK7NVMeFix12getMetaClassEv, symObjAddr: 0x40, symBinAddr: 0x630, symSize: 0x10 }
  - { offsetInCU: 0xD623, offset: 0xD820, size: 0x8, addend: 0x0, symName: __ZN7NVMeFix5probeEP9IOServicePi, symObjAddr: 0x50, symBinAddr: 0x640, symSize: 0x60 }
  - { offsetInCU: 0xD694, offset: 0xD891, size: 0x8, addend: 0x0, symName: __ZN7NVMeFix5startEP9IOService, symObjAddr: 0xB0, symBinAddr: 0x6A0, symSize: 0x60 }
  - { offsetInCU: 0xD6F6, offset: 0xD8F3, size: 0x8, addend: 0x0, symName: __ZN7NVMeFix4stopEP9IOService, symObjAddr: 0x110, symBinAddr: 0x700, symSize: 0x20 }
  - { offsetInCU: 0xD755, offset: 0xD952, size: 0x8, addend: 0x0, symName: __ZN7NVMeFix9MetaClassD1Ev, symObjAddr: 0x130, symBinAddr: 0x720, symSize: 0x10 }
  - { offsetInCU: 0xD7AD, offset: 0xD9AA, size: 0x8, addend: 0x0, symName: __ZN7NVMeFix9MetaClassD0Ev, symObjAddr: 0x140, symBinAddr: 0x730, symSize: 0x10 }
  - { offsetInCU: 0xD848, offset: 0xDA45, size: 0x8, addend: 0x0, symName: __ZNK7NVMeFix9MetaClass5allocEv, symObjAddr: 0x150, symBinAddr: 0x740, symSize: 0x40 }
  - { offsetInCU: 0xD8E3, offset: 0xDAE0, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_plugin_start.cpp, symObjAddr: 0x190, symBinAddr: 0x780, symSize: 0x40 }
  - { offsetInCU: 0xD94D, offset: 0xDB4A, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x1D0, symBinAddr: 0x7C0, symSize: 0x20 }
  - { offsetInCU: 0xD9A1, offset: 0xDB9E, size: 0x8, addend: 0x0, symName: __ZN7NVMeFix9MetaClassC1Ev, symObjAddr: 0x1F0, symBinAddr: 0x7E0, symSize: 0x40 }
  - { offsetInCU: 0xD9E3, offset: 0xDBE0, size: 0x8, addend: 0x0, symName: __ZN7NVMeFixC2EPK11OSMetaClass, symObjAddr: 0x230, symBinAddr: 0x820, symSize: 0x20 }
  - { offsetInCU: 0xDA35, offset: 0xDC32, size: 0x8, addend: 0x0, symName: __ZN7NVMeFixC1EPK11OSMetaClass, symObjAddr: 0x250, symBinAddr: 0x840, symSize: 0x20 }
  - { offsetInCU: 0xDA9C, offset: 0xDC99, size: 0x8, addend: 0x0, symName: __ZN7NVMeFixD2Ev, symObjAddr: 0x270, symBinAddr: 0x860, symSize: 0x10 }
  - { offsetInCU: 0xDABC, offset: 0xDCB9, size: 0x8, addend: 0x0, symName: __ZN7NVMeFix9MetaClassC2Ev, symObjAddr: 0x280, symBinAddr: 0x870, symSize: 0x40 }
  - { offsetInCU: 0xDADE, offset: 0xDCDB, size: 0x8, addend: 0x0, symName: __ZN7NVMeFixC1Ev, symObjAddr: 0x2C0, symBinAddr: 0x8B0, symSize: 0x30 }
  - { offsetInCU: 0xDB20, offset: 0xDD1D, size: 0x8, addend: 0x0, symName: __ZN7NVMeFixC2Ev, symObjAddr: 0x2F0, symBinAddr: 0x8E0, symSize: 0x30 }
  - { offsetInCU: 0xDB7B, offset: 0xDD78, size: 0x8, addend: 0x0, symName: _NVMeFix_kern_start, symObjAddr: 0x320, symBinAddr: 0x910, symSize: 0x320 }
  - { offsetInCU: 0xDD43, offset: 0xDF40, size: 0x8, addend: 0x0, symName: _NVMeFix_kern_stop, symObjAddr: 0x640, symBinAddr: 0xC30, symSize: 0x10 }
  - { offsetInCU: 0x26, offset: 0xEA9F, size: 0x8, addend: 0x0, symName: __ZN11NVMePMProxyD1Ev, symObjAddr: 0x650, symBinAddr: 0xC40, symSize: 0x10 }
  - { offsetInCU: 0x40, offset: 0xEAB9, size: 0x8, addend: 0x0, symName: __ZN11NVMePMProxy10gMetaClassE, symObjAddr: 0x47A80, symBinAddr: 0x55C0, symSize: 0x0 }
  - { offsetInCU: 0x33CD, offset: 0x11E46, size: 0x8, addend: 0x0, symName: __ZN11NVMePMProxyD1Ev, symObjAddr: 0x650, symBinAddr: 0xC40, symSize: 0x10 }
  - { offsetInCU: 0x3425, offset: 0x11E9E, size: 0x8, addend: 0x0, symName: __ZN11NVMePMProxyD0Ev, symObjAddr: 0x660, symBinAddr: 0xC50, symSize: 0x30 }
  - { offsetInCU: 0x348E, offset: 0x11F07, size: 0x8, addend: 0x0, symName: __ZNK11NVMePMProxy12getMetaClassEv, symObjAddr: 0x690, symBinAddr: 0xC80, symSize: 0x10 }
  - { offsetInCU: 0x34B2, offset: 0x11F2B, size: 0x8, addend: 0x0, symName: __ZN11NVMePMProxy13setPowerStateEmP9IOService, symObjAddr: 0x6A0, symBinAddr: 0xC90, symSize: 0x1D0 }
  - { offsetInCU: 0x37E2, offset: 0x1225B, size: 0x8, addend: 0x0, symName: __ZN11NVMePMProxy21powerStateDidChangeToEmmP9IOService, symObjAddr: 0x870, symBinAddr: 0xE60, symSize: 0x90 }
  - { offsetInCU: 0x38EB, offset: 0x12364, size: 0x8, addend: 0x0, symName: __ZN11NVMePMProxy9MetaClassD1Ev, symObjAddr: 0x900, symBinAddr: 0xEF0, symSize: 0x10 }
  - { offsetInCU: 0x3946, offset: 0x123BF, size: 0x8, addend: 0x0, symName: __ZN11NVMePMProxy9MetaClassD0Ev, symObjAddr: 0x910, symBinAddr: 0xF00, symSize: 0x10 }
  - { offsetInCU: 0x39E4, offset: 0x1245D, size: 0x8, addend: 0x0, symName: __ZNK11NVMePMProxy9MetaClass5allocEv, symObjAddr: 0x920, symBinAddr: 0xF10, symSize: 0x50 }
  - { offsetInCU: 0x3A7F, offset: 0x124F8, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_nvme_pm.cpp, symObjAddr: 0x970, symBinAddr: 0xF60, symSize: 0x40 }
  - { offsetInCU: 0x3AE9, offset: 0x12562, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a.24, symObjAddr: 0x9B0, symBinAddr: 0xFA0, symSize: 0x20 }
  - { offsetInCU: 0x3B7C, offset: 0x125F5, size: 0x8, addend: 0x0, symName: __ZN13NVMeFixPlugin2PM14activityTickleEPvmm, symObjAddr: 0x9D0, symBinAddr: 0xFC0, symSize: 0xD0 }
  - { offsetInCU: 0x26, offset: 0x12906, size: 0x8, addend: 0x0, symName: __ZN13NVMeFixPlugin10enableAPSTERNS_15ControllerEntryEPKN4NVMe12nvme_id_ctrlE, symObjAddr: 0xAA0, symBinAddr: 0x1090, symSize: 0x2C0 }
  - { offsetInCU: 0x14D, offset: 0x12A2D, size: 0x8, addend: 0x0, symName: __ZN13NVMeFixPlugin10enableAPSTERNS_15ControllerEntryEPKN4NVMe12nvme_id_ctrlE, symObjAddr: 0xAA0, symBinAddr: 0x1090, symSize: 0x2C0 }
  - { offsetInCU: 0x26, offset: 0x12C16, size: 0x8, addend: 0x0, symName: __ZN13NVMeFixPlugin27matchingNotificationHandlerEPvS0_P9IOServiceP10IONotifier, symObjAddr: 0xD60, symBinAddr: 0x1350, symSize: 0x250 }
  - { offsetInCU: 0x40, offset: 0x12C30, size: 0x8, addend: 0x0, symName: __ZL6plugin, symObjAddr: 0x30E8, symBinAddr: 0x5410, symSize: 0x0 }
  - { offsetInCU: 0x6D, offset: 0x12C5D, size: 0x8, addend: 0x0, symName: __ZL10bootargOff, symObjAddr: 0x3250, symBinAddr: 0x5578, symSize: 0x0 }
  - { offsetInCU: 0x94, offset: 0x12C84, size: 0x8, addend: 0x0, symName: __ZL12bootargDebug, symObjAddr: 0x3258, symBinAddr: 0x5580, symSize: 0x0 }
  - { offsetInCU: 0x1AF3, offset: 0x146E3, size: 0x8, addend: 0x0, symName: __ZN13NVMeFixPlugin27matchingNotificationHandlerEPvS0_P9IOServiceP10IONotifier, symObjAddr: 0xD60, symBinAddr: 0x1350, symSize: 0x250 }
  - { offsetInCU: 0x1D82, offset: 0x14972, size: 0x8, addend: 0x0, symName: __ZN13NVMeFixPlugin15ControllerEntry7deleterEPS0_, symObjAddr: 0x2DB0, symBinAddr: 0x33A0, symSize: 0x80 }
  - { offsetInCU: 0x1DD9, offset: 0x149C9, size: 0x8, addend: 0x0, symName: __ZN13NVMeFixPlugin16handleControllerERNS_15ControllerEntryE, symObjAddr: 0x1730, symBinAddr: 0x1D20, symSize: 0x1500 }
  - { offsetInCU: 0x2C15, offset: 0x15805, size: 0x8, addend: 0x0, symName: __ZN13NVMeFixPlugin29terminatedNotificationHandlerEPvS0_P9IOServiceP10IONotifier, symObjAddr: 0xFB0, symBinAddr: 0x15A0, symSize: 0xE0 }
  - { offsetInCU: 0x3313, offset: 0x15F03, size: 0x8, addend: 0x0, symName: __ZN13NVMeFixPlugin11processKextEPvR13KernelPatchermym, symObjAddr: 0x1090, symBinAddr: 0x1680, symSize: 0x6A0 }
  - { offsetInCU: 0x3C47, offset: 0x16837, size: 0x8, addend: 0x0, symName: __ZN13NVMeFixPlugin15forceEnableASPMEP9IOService, symObjAddr: 0x2C30, symBinAddr: 0x3220, symSize: 0x180 }
  - { offsetInCU: 0x3E78, offset: 0x16A68, size: 0x8, addend: 0x0, symName: __ZN13NVMeFixPlugin12NVMeFeaturesERNS_15ControllerEntryEjPjP24IOBufferMemoryDescriptorS2_b, symObjAddr: 0x2E30, symBinAddr: 0x3420, symSize: 0x1DF }
...
