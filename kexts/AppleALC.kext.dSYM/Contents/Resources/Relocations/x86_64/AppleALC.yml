---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/work/AppleALC/AppleALC/build/Release/AppleALC.kext/Contents/MacOS/AppleALC'
relocations:
  - { offsetInCU: 0x35, offset: 0x35, size: 0x8, addend: 0x0, symName: _kmod_info, symObjAddr: 0x4B20, symBinAddr: 0x1A74D0, symSize: 0x0 }
  - { offsetInCU: 0x206, offset: 0x206, size: 0x8, addend: 0x0, symName: __realmain, symObjAddr: 0x4BE8, symBinAddr: 0x1A7598, symSize: 0x0 }
  - { offsetInCU: 0x21C, offset: 0x21C, size: 0x8, addend: 0x0, symName: __antimain, symObjAddr: 0x4BF0, symBinAddr: 0x1A75A0, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x24C, size: 0x8, addend: 0x0, symName: '__ZZN10AlcEnabler4initEvEN3$_08__invokeEPvR13KernelPatcher', symObjAddr: 0x0, symBinAddr: 0x6B0, symSize: 0xFA0 }
  - { offsetInCU: 0x3E, offset: 0x263, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler11callbackAlcE, symObjAddr: 0x2300C8, symBinAddr: 0x1B5DF8, symSize: 0x0 }
  - { offsetInCU: 0x130C3, offset: 0x132E8, size: 0x8, addend: 0x0, symName: __ZL10alcEnabler, symObjAddr: 0x4BF8, symBinAddr: 0x1A75A8, symSize: 0x0 }
  - { offsetInCU: 0x19DD2, offset: 0x19FF7, size: 0x8, addend: 0x0, symName: '__ZZN10AlcEnabler4initEvEN3$_08__invokeEPvR13KernelPatcher', symObjAddr: 0x0, symBinAddr: 0x6B0, symSize: 0xFA0 }
  - { offsetInCU: 0x1B897, offset: 0x1BABC, size: 0x8, addend: 0x0, symName: '__ZZN10AlcEnabler4initEvEN3$_18__invokeEPvR13KernelPatchermym', symObjAddr: 0xFA0, symBinAddr: 0x1650, symSize: 0x11B0 }
  - { offsetInCU: 0x1CB83, offset: 0x1CDA8, size: 0x8, addend: 0x0, symName: '__ZZN10AlcEnabler4initEvEN3$_28__invokeEPvP4taskPKcRP8OSObject', symObjAddr: 0x2150, symBinAddr: 0x2800, symSize: 0x50 }
  - { offsetInCU: 0x1CC5A, offset: 0x1CE7F, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler8gfxProbeEP9IOServiceS1_Pi, symObjAddr: 0x21A0, symBinAddr: 0x2850, symSize: 0x70 }
  - { offsetInCU: 0x1CCCE, offset: 0x1CEF3, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler18performPowerChangeEP9IOServicejjPj, symObjAddr: 0x2210, symBinAddr: 0x28C0, symSize: 0x200 }
  - { offsetInCU: 0x1CE4C, offset: 0x1D071, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler19initializePinConfigEP9IOServiceS1_, symObjAddr: 0x2410, symBinAddr: 0x2AC0, symSize: 0x40 }
  - { offsetInCU: 0x1CE96, offset: 0x1D0BB, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler25initializePinConfigLegacyEP9IOService, symObjAddr: 0x2450, symBinAddr: 0x2B00, symSize: 0xC0 }
  - { offsetInCU: 0x1CEFC, offset: 0x1D121, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler18layoutLoadCallbackEjiPKvjPv, symObjAddr: 0x2510, symBinAddr: 0x2BC0, symSize: 0x60 }
  - { offsetInCU: 0x1CF85, offset: 0x1D1AA, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler20platformLoadCallbackEjiPKvjPv, symObjAddr: 0x2570, symBinAddr: 0x2C20, symSize: 0x60 }
  - { offsetInCU: 0x1D00E, offset: 0x1D233, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler20AppleHDADriver_startEP9IOServiceS1_, symObjAddr: 0x25D0, symBinAddr: 0x2C80, symSize: 0x30 }
  - { offsetInCU: 0x1D058, offset: 0x1D27D, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler28IOHDACodecDevice_executeVerbEPvtttPjb, symObjAddr: 0x2600, symBinAddr: 0x2CB0, symSize: 0x10 }
  - { offsetInCU: 0x1D0BB, offset: 0x1D2E0, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler24AppleHDAController_startEP9IOServiceS1_, symObjAddr: 0x2610, symBinAddr: 0x2CC0, symSize: 0x120 }
  - { offsetInCU: 0x1D192, offset: 0x1D3B7, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler28AppleHDAPlatformDriver_startEP9IOServiceS1_, symObjAddr: 0x2730, symBinAddr: 0x2DE0, symSize: 0x30 }
  - { offsetInCU: 0x1D1DC, offset: 0x1D401, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler30replaceAppleHDADriverResourcesEP9IOService, symObjAddr: 0x2760, symBinAddr: 0x2E10, symSize: 0x5E0 }
  - { offsetInCU: 0x1D4C2, offset: 0x1D6E7, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler26unserializeCodecDictionaryEPKhj, symObjAddr: 0x2D40, symBinAddr: 0x33F0, symSize: 0x110 }
  - { offsetInCU: 0x1D5EE, offset: 0x1D813, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler14updateResourceENS_8ResourceERiRPKvRj, symObjAddr: 0x2E50, symBinAddr: 0x3500, symSize: 0x1E0 }
  - { offsetInCU: 0x1D7DC, offset: 0x1DA01, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler14patchPinConfigEP9IOServiceP15IORegistryEntry, symObjAddr: 0x3030, symBinAddr: 0x36E0, symSize: 0x6B0 }
  - { offsetInCU: 0x1DDA0, offset: 0x1DFC5, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler16insertControllerEjjjbjjP15IORegistryEntry, symObjAddr: 0x36E0, symBinAddr: 0x3D90, symSize: 0x120 }
  - { offsetInCU: 0x1E08A, offset: 0x1E2AF, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler22updateDevicePropertiesEP15IORegistryEntryP10DeviceInfoPKcb, symObjAddr: 0x3800, symBinAddr: 0x3EB0, symSize: 0x3C0 }
  - { offsetInCU: 0x27, offset: 0x1E798, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderD1Ev, symObjAddr: 0x3BC0, symBinAddr: 0x4270, symSize: 0x10 }
  - { offsetInCU: 0x3E, offset: 0x1E7AF, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider10gMetaClassE, symObjAddr: 0x230040, symBinAddr: 0x1B5D70, symSize: 0x0 }
  - { offsetInCU: 0x305, offset: 0x1EA76, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider9metaClassE, symObjAddr: 0x1AC618, symBinAddr: 0x19E4A8, symSize: 0x0 }
  - { offsetInCU: 0x31C, offset: 0x1EA8D, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider10superClassE, symObjAddr: 0x1AB230, symBinAddr: 0x19D0C0, symSize: 0x0 }
  - { offsetInCU: 0x353, offset: 0x1EAC4, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderD1Ev, symObjAddr: 0x3BC0, symBinAddr: 0x4270, symSize: 0x10 }
  - { offsetInCU: 0x3CF, offset: 0x1EB40, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderD0Ev, symObjAddr: 0x3BD0, symBinAddr: 0x4280, symSize: 0x30 }
  - { offsetInCU: 0x45D, offset: 0x1EBCE, size: 0x8, addend: 0x0, symName: __ZNK21ALCUserClientProvider12getMetaClassEv, symObjAddr: 0x3C00, symBinAddr: 0x42B0, symSize: 0x10 }
  - { offsetInCU: 0x546, offset: 0x1ECB7, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider5probeEP9IOServicePi, symObjAddr: 0x3C10, symBinAddr: 0x42C0, symSize: 0x100 }
  - { offsetInCU: 0x676, offset: 0x1EDE7, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider5startEP9IOService, symObjAddr: 0x3D10, symBinAddr: 0x43C0, symSize: 0x60 }
  - { offsetInCU: 0x6C0, offset: 0x1EE31, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider4stopEP9IOService, symObjAddr: 0x3D70, symBinAddr: 0x4420, symSize: 0x20 }
  - { offsetInCU: 0x706, offset: 0x1EE77, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider14sendHdaCommandEttt, symObjAddr: 0x3D90, symBinAddr: 0x4440, symSize: 0x60 }
  - { offsetInCU: 0x833, offset: 0x1EFA4, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassD1Ev, symObjAddr: 0x3DF0, symBinAddr: 0x44A0, symSize: 0x10 }
  - { offsetInCU: 0x8AF, offset: 0x1F020, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassD0Ev, symObjAddr: 0x3E00, symBinAddr: 0x44B0, symSize: 0x10 }
  - { offsetInCU: 0x985, offset: 0x1F0F6, size: 0x8, addend: 0x0, symName: __ZNK21ALCUserClientProvider9MetaClass5allocEv, symObjAddr: 0x3E10, symBinAddr: 0x44C0, symSize: 0x60 }
  - { offsetInCU: 0xA5E, offset: 0x1F1CF, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_ALCUserClientProvider.cpp, symObjAddr: 0x3E70, symBinAddr: 0x4520, symSize: 0x40 }
  - { offsetInCU: 0xAEE, offset: 0x1F25F, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x3EB0, symBinAddr: 0x4560, symSize: 0x20 }
  - { offsetInCU: 0xB60, offset: 0x1F2D1, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassC1Ev, symObjAddr: 0x3ED0, symBinAddr: 0x4580, symSize: 0x40 }
  - { offsetInCU: 0xBBB, offset: 0x1F32C, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderC2EPK11OSMetaClass, symObjAddr: 0x3F10, symBinAddr: 0x45C0, symSize: 0x40 }
  - { offsetInCU: 0xC2C, offset: 0x1F39D, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderC1EPK11OSMetaClass, symObjAddr: 0x3F50, symBinAddr: 0x4600, symSize: 0x40 }
  - { offsetInCU: 0xCB4, offset: 0x1F425, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderD2Ev, symObjAddr: 0x3F90, symBinAddr: 0x4640, symSize: 0x10 }
  - { offsetInCU: 0xCE1, offset: 0x1F452, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassC2Ev, symObjAddr: 0x3FA0, symBinAddr: 0x4650, symSize: 0x40 }
  - { offsetInCU: 0xD10, offset: 0x1F481, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderC1Ev, symObjAddr: 0x3FE0, symBinAddr: 0x4690, symSize: 0x50 }
  - { offsetInCU: 0xD6B, offset: 0x1F4DC, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderC2Ev, symObjAddr: 0x4030, symBinAddr: 0x46E0, symSize: 0x50 }
  - { offsetInCU: 0x27, offset: 0x1F543, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientD1Ev, symObjAddr: 0x4080, symBinAddr: 0x4730, symSize: 0x10 }
  - { offsetInCU: 0x3E, offset: 0x1F55A, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient10gMetaClassE, symObjAddr: 0x230068, symBinAddr: 0x1B5D98, symSize: 0x0 }
  - { offsetInCU: 0x39A, offset: 0x1F8B6, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient9metaClassE, symObjAddr: 0x1ABB90, symBinAddr: 0x19DA20, symSize: 0x0 }
  - { offsetInCU: 0x3B1, offset: 0x1F8CD, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient10superClassE, symObjAddr: 0x1ABB98, symBinAddr: 0x19DA28, symSize: 0x0 }
  - { offsetInCU: 0x3C8, offset: 0x1F8E4, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient8sMethodsE, symObjAddr: 0x1AC600, symBinAddr: 0x19E490, symSize: 0x0 }
  - { offsetInCU: 0x408, offset: 0x1F924, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientD1Ev, symObjAddr: 0x4080, symBinAddr: 0x4730, symSize: 0x10 }
  - { offsetInCU: 0x484, offset: 0x1F9A0, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientD0Ev, symObjAddr: 0x4090, symBinAddr: 0x4740, symSize: 0x30 }
  - { offsetInCU: 0x512, offset: 0x1FA2E, size: 0x8, addend: 0x0, symName: __ZNK13ALCUserClient12getMetaClassEv, symObjAddr: 0x40C0, symBinAddr: 0x4770, symSize: 0x10 }
  - { offsetInCU: 0x544, offset: 0x1FA60, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient5startEP9IOService, symObjAddr: 0x40D0, symBinAddr: 0x4780, symSize: 0x50 }
  - { offsetInCU: 0x5A2, offset: 0x1FABE, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient14externalMethodEjP25IOExternalMethodArgumentsP24IOExternalMethodDispatchP8OSObjectPv, symObjAddr: 0x4120, symBinAddr: 0x47D0, symSize: 0x30 }
  - { offsetInCU: 0x5AA, offset: 0x1FAC6, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient12initWithTaskEP4taskPvjP12OSDictionary, symObjAddr: 0x4150, symBinAddr: 0x4800, symSize: 0x40 }
  - { offsetInCU: 0x638, offset: 0x1FB54, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient12initWithTaskEP4taskPvjP12OSDictionary, symObjAddr: 0x4150, symBinAddr: 0x4800, symSize: 0x40 }
  - { offsetInCU: 0x640, offset: 0x1FB5C, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient11clientCloseEv, symObjAddr: 0x4190, symBinAddr: 0x4840, symSize: 0x30 }
  - { offsetInCU: 0x6B9, offset: 0x1FBD5, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient11clientCloseEv, symObjAddr: 0x4190, symBinAddr: 0x4840, symSize: 0x30 }
  - { offsetInCU: 0x6EF, offset: 0x1FC0B, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient17methodExecuteVerbEP21ALCUserClientProviderPvP25IOExternalMethodArguments, symObjAddr: 0x41C0, symBinAddr: 0x4870, symSize: 0x40 }
  - { offsetInCU: 0x7AF, offset: 0x1FCCB, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassD1Ev, symObjAddr: 0x4200, symBinAddr: 0x48B0, symSize: 0x10 }
  - { offsetInCU: 0x82B, offset: 0x1FD47, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassD0Ev, symObjAddr: 0x4210, symBinAddr: 0x48C0, symSize: 0x10 }
  - { offsetInCU: 0x901, offset: 0x1FE1D, size: 0x8, addend: 0x0, symName: __ZNK13ALCUserClient9MetaClass5allocEv, symObjAddr: 0x4220, symBinAddr: 0x48D0, symSize: 0x60 }
  - { offsetInCU: 0x9DA, offset: 0x1FEF6, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_ALCUserClient.cpp, symObjAddr: 0x4280, symBinAddr: 0x4930, symSize: 0x40 }
  - { offsetInCU: 0xA6A, offset: 0x1FF86, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a.80, symObjAddr: 0x42C0, symBinAddr: 0x4970, symSize: 0x20 }
  - { offsetInCU: 0xADC, offset: 0x1FFF8, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassC1Ev, symObjAddr: 0x42E0, symBinAddr: 0x4990, symSize: 0x40 }
  - { offsetInCU: 0xB37, offset: 0x20053, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientC2EPK11OSMetaClass, symObjAddr: 0x4320, symBinAddr: 0x49D0, symSize: 0x40 }
  - { offsetInCU: 0xBA8, offset: 0x200C4, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientC1EPK11OSMetaClass, symObjAddr: 0x4360, symBinAddr: 0x4A10, symSize: 0x40 }
  - { offsetInCU: 0xC30, offset: 0x2014C, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientD2Ev, symObjAddr: 0x43A0, symBinAddr: 0x4A50, symSize: 0x10 }
  - { offsetInCU: 0xC5D, offset: 0x20179, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassC2Ev, symObjAddr: 0x43B0, symBinAddr: 0x4A60, symSize: 0x40 }
  - { offsetInCU: 0xC8C, offset: 0x201A8, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientC1Ev, symObjAddr: 0x43F0, symBinAddr: 0x4AA0, symSize: 0x50 }
  - { offsetInCU: 0xCE7, offset: 0x20203, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientC2Ev, symObjAddr: 0x4440, symBinAddr: 0x4AF0, symSize: 0x50 }
  - { offsetInCU: 0x27, offset: 0x2026A, size: 0x8, addend: 0x0, symName: __ZN8AppleALCD1Ev, symObjAddr: 0x4490, symBinAddr: 0x4B40, symSize: 0x10 }
  - { offsetInCU: 0x2F, offset: 0x20272, size: 0x8, addend: 0x0, symName: _kmod_info, symObjAddr: 0x4B20, symBinAddr: 0x1A74D0, symSize: 0x0 }
  - { offsetInCU: 0x4A, offset: 0x2028D, size: 0x8, addend: 0x0, symName: _AppleALC_startSuccess, symObjAddr: 0x2300B8, symBinAddr: 0x1B5DE8, symSize: 0x0 }
  - { offsetInCU: 0x6B, offset: 0x202AE, size: 0x8, addend: 0x0, symName: _AppleALC_debugEnabled, symObjAddr: 0x2300B9, symBinAddr: 0x1B5DE9, symSize: 0x0 }
  - { offsetInCU: 0x86, offset: 0x202C9, size: 0x8, addend: 0x0, symName: _AppleALC_debugPrintDelay, symObjAddr: 0x2300BC, symBinAddr: 0x1B5DEC, symSize: 0x0 }
  - { offsetInCU: 0x95, offset: 0x202D8, size: 0x8, addend: 0x0, symName: __ZN8AppleALC10gMetaClassE, symObjAddr: 0x230090, symBinAddr: 0x1B5DC0, symSize: 0x0 }
  - { offsetInCU: 0x2EA, offset: 0x2052D, size: 0x8, addend: 0x0, symName: __ZN8AppleALC9metaClassE, symObjAddr: 0x1AC620, symBinAddr: 0x19E4B0, symSize: 0x0 }
  - { offsetInCU: 0x301, offset: 0x20544, size: 0x8, addend: 0x0, symName: __ZN8AppleALC10superClassE, symObjAddr: 0x1AC628, symBinAddr: 0x19E4B8, symSize: 0x0 }
  - { offsetInCU: 0x31F, offset: 0x20562, size: 0x8, addend: 0x0, symName: _AppleALC_selfInstance, symObjAddr: 0x2300C0, symBinAddr: 0x1B5DF0, symSize: 0x0 }
  - { offsetInCU: 0x347, offset: 0x2058A, size: 0x8, addend: 0x0, symName: __ZL11kextVersion, symObjAddr: 0x144C0, symBinAddr: 0x62C0, symSize: 0x0 }
  - { offsetInCU: 0x379, offset: 0x205BC, size: 0x8, addend: 0x0, symName: __ZN8AppleALCD1Ev, symObjAddr: 0x4490, symBinAddr: 0x4B40, symSize: 0x10 }
  - { offsetInCU: 0x3F5, offset: 0x20638, size: 0x8, addend: 0x0, symName: __ZN8AppleALCD0Ev, symObjAddr: 0x44A0, symBinAddr: 0x4B50, symSize: 0x30 }
  - { offsetInCU: 0x483, offset: 0x206C6, size: 0x8, addend: 0x0, symName: __ZNK8AppleALC12getMetaClassEv, symObjAddr: 0x44D0, symBinAddr: 0x4B80, symSize: 0x10 }
  - { offsetInCU: 0x4B5, offset: 0x206F8, size: 0x8, addend: 0x0, symName: __ZN8AppleALC5probeEP9IOServicePi, symObjAddr: 0x44E0, symBinAddr: 0x4B90, symSize: 0x60 }
  - { offsetInCU: 0x527, offset: 0x2076A, size: 0x8, addend: 0x0, symName: __ZN8AppleALC5startEP9IOService, symObjAddr: 0x4540, symBinAddr: 0x4BF0, symSize: 0x60 }
  - { offsetInCU: 0x571, offset: 0x207B4, size: 0x8, addend: 0x0, symName: __ZN8AppleALC4stopEP9IOService, symObjAddr: 0x45A0, symBinAddr: 0x4C50, symSize: 0x20 }
  - { offsetInCU: 0x5E4, offset: 0x20827, size: 0x8, addend: 0x0, symName: __ZN8AppleALC9MetaClassD1Ev, symObjAddr: 0x45C0, symBinAddr: 0x4C70, symSize: 0x10 }
  - { offsetInCU: 0x660, offset: 0x208A3, size: 0x8, addend: 0x0, symName: __ZN8AppleALC9MetaClassD0Ev, symObjAddr: 0x45D0, symBinAddr: 0x4C80, symSize: 0x10 }
  - { offsetInCU: 0x736, offset: 0x20979, size: 0x8, addend: 0x0, symName: __ZNK8AppleALC9MetaClass5allocEv, symObjAddr: 0x45E0, symBinAddr: 0x4C90, symSize: 0x40 }
  - { offsetInCU: 0x73E, offset: 0x20981, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_plugin_start.cpp, symObjAddr: 0x4620, symBinAddr: 0x4CD0, symSize: 0x40 }
  - { offsetInCU: 0x80F, offset: 0x20A52, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_plugin_start.cpp, symObjAddr: 0x4620, symBinAddr: 0x4CD0, symSize: 0x40 }
  - { offsetInCU: 0x89F, offset: 0x20AE2, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a.107, symObjAddr: 0x4660, symBinAddr: 0x4D10, symSize: 0x20 }
  - { offsetInCU: 0x911, offset: 0x20B54, size: 0x8, addend: 0x0, symName: __ZN8AppleALC9MetaClassC1Ev, symObjAddr: 0x4680, symBinAddr: 0x4D30, symSize: 0x40 }
  - { offsetInCU: 0x96C, offset: 0x20BAF, size: 0x8, addend: 0x0, symName: __ZN8AppleALCC2EPK11OSMetaClass, symObjAddr: 0x46C0, symBinAddr: 0x4D70, symSize: 0x20 }
  - { offsetInCU: 0x9DD, offset: 0x20C20, size: 0x8, addend: 0x0, symName: __ZN8AppleALCC1EPK11OSMetaClass, symObjAddr: 0x46E0, symBinAddr: 0x4D90, symSize: 0x20 }
  - { offsetInCU: 0xA65, offset: 0x20CA8, size: 0x8, addend: 0x0, symName: __ZN8AppleALCD2Ev, symObjAddr: 0x4700, symBinAddr: 0x4DB0, symSize: 0x10 }
  - { offsetInCU: 0xA92, offset: 0x20CD5, size: 0x8, addend: 0x0, symName: __ZN8AppleALC9MetaClassC2Ev, symObjAddr: 0x4710, symBinAddr: 0x4DC0, symSize: 0x40 }
  - { offsetInCU: 0xAC1, offset: 0x20D04, size: 0x8, addend: 0x0, symName: __ZN8AppleALCC1Ev, symObjAddr: 0x4750, symBinAddr: 0x4E00, symSize: 0x30 }
  - { offsetInCU: 0xB1C, offset: 0x20D5F, size: 0x8, addend: 0x0, symName: __ZN8AppleALCC2Ev, symObjAddr: 0x4780, symBinAddr: 0x4E30, symSize: 0x30 }
  - { offsetInCU: 0xB85, offset: 0x20DC8, size: 0x8, addend: 0x0, symName: _AppleALC_kern_start, symObjAddr: 0x47B0, symBinAddr: 0x4E60, symSize: 0x360 }
  - { offsetInCU: 0xE67, offset: 0x210AA, size: 0x8, addend: 0x0, symName: _AppleALC_kern_stop, symObjAddr: 0x4B10, symBinAddr: 0x51C0, symSize: 0x10 }
  - { offsetInCU: 0xE6F, offset: 0x210B2, size: 0x8, addend: 0x0, symName: _kmod_info, symObjAddr: 0x4B20, symBinAddr: 0x1A74D0, symSize: 0x0 }
  - { offsetInCU: 0x35, offset: 0x2112D, size: 0x8, addend: 0x0, symName: __ZL10bootargOff, symObjAddr: 0x4CF0, symBinAddr: 0x1A76A0, symSize: 0x0 }
  - { offsetInCU: 0x6D, offset: 0x21165, size: 0x8, addend: 0x0, symName: __ZL12bootargDebug, symObjAddr: 0x4CF8, symBinAddr: 0x1A76A8, symSize: 0x0 }
  - { offsetInCU: 0x8A, offset: 0x21182, size: 0x8, addend: 0x0, symName: __ZL11bootargBeta, symObjAddr: 0x4D00, symBinAddr: 0x1A76B0, symSize: 0x0 }
  - { offsetInCU: 0x3C, offset: 0x21243, size: 0x8, addend: 0x0, symName: _AppleALC_kextList, symObjAddr: 0x4D30, symBinAddr: 0x1A76E0, symSize: 0x0 }
  - { offsetInCU: 0x6F, offset: 0x21276, size: 0x8, addend: 0x0, symName: _AppleALC_vendorMod, symObjAddr: 0x1ACF80, symBinAddr: 0x19EE10, symSize: 0x0 }
  - { offsetInCU: 0xF3, offset: 0x212FA, size: 0x8, addend: 0x0, symName: _AppleALC_controllerMod, symObjAddr: 0x7F90, symBinAddr: 0x1AA940, symSize: 0x0 }
  - { offsetInCU: 0x125, offset: 0x2132C, size: 0x8, addend: 0x0, symName: __ZL9kextPath0, symObjAddr: 0x4D08, symBinAddr: 0x1A76B8, symSize: 0x0 }
  - { offsetInCU: 0x142, offset: 0x21349, size: 0x8, addend: 0x0, symName: __ZL9kextPath1, symObjAddr: 0x4D10, symBinAddr: 0x1A76C0, symSize: 0x0 }
  - { offsetInCU: 0x15F, offset: 0x21366, size: 0x8, addend: 0x0, symName: __ZL9kextPath2, symObjAddr: 0x4D18, symBinAddr: 0x1A76C8, symSize: 0x0 }
  - { offsetInCU: 0x17C, offset: 0x21383, size: 0x8, addend: 0x0, symName: __ZL9kextPath3, symObjAddr: 0x4D20, symBinAddr: 0x1A76D0, symSize: 0x0 }
  - { offsetInCU: 0x199, offset: 0x213A0, size: 0x8, addend: 0x0, symName: __ZL9kextPath4, symObjAddr: 0x4D28, symBinAddr: 0x1A76D8, symSize: 0x0 }
  - { offsetInCU: 0x1B2, offset: 0x213B9, size: 0x8, addend: 0x0, symName: __ZL11codecModAMD, symObjAddr: 0x2300D0, symBinAddr: 0x1B5E00, symSize: 0x0 }
  - { offsetInCU: 0x1E4, offset: 0x213EB, size: 0x8, addend: 0x0, symName: __ZL11codecModVIA, symObjAddr: 0x4E00, symBinAddr: 0x1A77B0, symSize: 0x0 }
  - { offsetInCU: 0x216, offset: 0x2141D, size: 0x8, addend: 0x0, symName: __ZL10revisions0, symObjAddr: 0x144D4, symBinAddr: 0x62D4, symSize: 0x0 }
  - { offsetInCU: 0x247, offset: 0x2144E, size: 0x8, addend: 0x0, symName: __ZL10platforms0, symObjAddr: 0x1AD100, symBinAddr: 0x19EF90, symSize: 0x0 }
  - { offsetInCU: 0x278, offset: 0x2147F, size: 0x8, addend: 0x0, symName: __ZL5file0, symObjAddr: 0x144E0, symBinAddr: 0x62E0, symSize: 0x0 }
  - { offsetInCU: 0x2AA, offset: 0x214B1, size: 0x8, addend: 0x0, symName: __ZL5file1, symObjAddr: 0x14640, symBinAddr: 0x6440, symSize: 0x0 }
  - { offsetInCU: 0x2DC, offset: 0x214E3, size: 0x8, addend: 0x0, symName: __ZL5file2, symObjAddr: 0x147A0, symBinAddr: 0x65A0, symSize: 0x0 }
  - { offsetInCU: 0x30E, offset: 0x21515, size: 0x8, addend: 0x0, symName: __ZL5file3, symObjAddr: 0x14910, symBinAddr: 0x6710, symSize: 0x0 }
  - { offsetInCU: 0x340, offset: 0x21547, size: 0x8, addend: 0x0, symName: __ZL8layouts0, symObjAddr: 0x1AD160, symBinAddr: 0x19EFF0, symSize: 0x0 }
  - { offsetInCU: 0x35D, offset: 0x21564, size: 0x8, addend: 0x0, symName: __ZL5file4, symObjAddr: 0x14A60, symBinAddr: 0x6860, symSize: 0x0 }
  - { offsetInCU: 0x38E, offset: 0x21595, size: 0x8, addend: 0x0, symName: __ZL5file5, symObjAddr: 0x14B40, symBinAddr: 0x6940, symSize: 0x0 }
  - { offsetInCU: 0x3C0, offset: 0x215C7, size: 0x8, addend: 0x0, symName: __ZL5file6, symObjAddr: 0x14CD0, symBinAddr: 0x6AD0, symSize: 0x0 }
  - { offsetInCU: 0x3F2, offset: 0x215F9, size: 0x8, addend: 0x0, symName: __ZL5file7, symObjAddr: 0x15110, symBinAddr: 0x6F10, symSize: 0x0 }
  - { offsetInCU: 0x423, offset: 0x2162A, size: 0x8, addend: 0x0, symName: __ZL8patches0, symObjAddr: 0x88C0, symBinAddr: 0x1AB270, symSize: 0x0 }
  - { offsetInCU: 0x458, offset: 0x2165F, size: 0x8, addend: 0x0, symName: __ZL9patchBuf0, symObjAddr: 0x151E1, symBinAddr: 0x6FE1, symSize: 0x0 }
  - { offsetInCU: 0x475, offset: 0x2167C, size: 0x8, addend: 0x0, symName: __ZL9patchBuf1, symObjAddr: 0x151E9, symBinAddr: 0x6FE9, symSize: 0x0 }
  - { offsetInCU: 0x492, offset: 0x21699, size: 0x8, addend: 0x0, symName: __ZL9patchBuf2, symObjAddr: 0x151F1, symBinAddr: 0x6FF1, symSize: 0x0 }
  - { offsetInCU: 0x4AF, offset: 0x216B6, size: 0x8, addend: 0x0, symName: __ZL9patchBuf3, symObjAddr: 0x151F9, symBinAddr: 0x6FF9, symSize: 0x0 }
  - { offsetInCU: 0x4CC, offset: 0x216D3, size: 0x8, addend: 0x0, symName: __ZL9patchBuf4, symObjAddr: 0x15201, symBinAddr: 0x7001, symSize: 0x0 }
  - { offsetInCU: 0x4E9, offset: 0x216F0, size: 0x8, addend: 0x0, symName: __ZL9patchBuf5, symObjAddr: 0x15209, symBinAddr: 0x7009, symSize: 0x0 }
  - { offsetInCU: 0x506, offset: 0x2170D, size: 0x8, addend: 0x0, symName: __ZL9patchBuf6, symObjAddr: 0x15211, symBinAddr: 0x7011, symSize: 0x0 }
  - { offsetInCU: 0x523, offset: 0x2172A, size: 0x8, addend: 0x0, symName: __ZL9patchBuf7, symObjAddr: 0x15219, symBinAddr: 0x7019, symSize: 0x0 }
  - { offsetInCU: 0x53C, offset: 0x21743, size: 0x8, addend: 0x0, symName: __ZL9patchBuf8, symObjAddr: 0x15221, symBinAddr: 0x7021, symSize: 0x0 }
  - { offsetInCU: 0x56D, offset: 0x21774, size: 0x8, addend: 0x0, symName: __ZL9patchBuf9, symObjAddr: 0x15225, symBinAddr: 0x7025, symSize: 0x0 }
  - { offsetInCU: 0x58A, offset: 0x21791, size: 0x8, addend: 0x0, symName: __ZL10patchBuf10, symObjAddr: 0x15229, symBinAddr: 0x7029, symSize: 0x0 }
  - { offsetInCU: 0x5A7, offset: 0x217AE, size: 0x8, addend: 0x0, symName: __ZL10patchBuf11, symObjAddr: 0x1522D, symBinAddr: 0x702D, symSize: 0x0 }
  - { offsetInCU: 0x5C4, offset: 0x217CB, size: 0x8, addend: 0x0, symName: __ZL10patchBuf12, symObjAddr: 0x15231, symBinAddr: 0x7031, symSize: 0x0 }
  - { offsetInCU: 0x5E1, offset: 0x217E8, size: 0x8, addend: 0x0, symName: __ZL10revisions1, symObjAddr: 0x144D8, symBinAddr: 0x62D8, symSize: 0x0 }
  - { offsetInCU: 0x5FF, offset: 0x21806, size: 0x8, addend: 0x0, symName: __ZL10platforms1, symObjAddr: 0x1AD1C0, symBinAddr: 0x19F050, symSize: 0x0 }
  - { offsetInCU: 0x61D, offset: 0x21824, size: 0x8, addend: 0x0, symName: __ZL5file8, symObjAddr: 0x15240, symBinAddr: 0x7040, symSize: 0x0 }
  - { offsetInCU: 0x650, offset: 0x21857, size: 0x8, addend: 0x0, symName: __ZL5file9, symObjAddr: 0x15450, symBinAddr: 0x7250, symSize: 0x0 }
  - { offsetInCU: 0x684, offset: 0x2188B, size: 0x8, addend: 0x0, symName: __ZL6file10, symObjAddr: 0x155C0, symBinAddr: 0x73C0, symSize: 0x0 }
  - { offsetInCU: 0x6B8, offset: 0x218BF, size: 0x8, addend: 0x0, symName: __ZL8layouts1, symObjAddr: 0x1AD220, symBinAddr: 0x19F0B0, symSize: 0x0 }
  - { offsetInCU: 0x6D7, offset: 0x218DE, size: 0x8, addend: 0x0, symName: __ZL6file11, symObjAddr: 0x16A00, symBinAddr: 0x8800, symSize: 0x0 }
  - { offsetInCU: 0x70A, offset: 0x21911, size: 0x8, addend: 0x0, symName: __ZL6file12, symObjAddr: 0x16AD0, symBinAddr: 0x88D0, symSize: 0x0 }
  - { offsetInCU: 0x73D, offset: 0x21944, size: 0x8, addend: 0x0, symName: __ZL6file13, symObjAddr: 0x16BA0, symBinAddr: 0x89A0, symSize: 0x0 }
  - { offsetInCU: 0x771, offset: 0x21978, size: 0x8, addend: 0x0, symName: __ZL6file14, symObjAddr: 0x17120, symBinAddr: 0x8F20, symSize: 0x0 }
  - { offsetInCU: 0x7A4, offset: 0x219AB, size: 0x8, addend: 0x0, symName: __ZL8patches1, symObjAddr: 0x8A10, symBinAddr: 0x1AB3C0, symSize: 0x0 }
  - { offsetInCU: 0x7D7, offset: 0x219DE, size: 0x8, addend: 0x0, symName: __ZL10patchBuf13, symObjAddr: 0x171DC, symBinAddr: 0x8FDC, symSize: 0x0 }
  - { offsetInCU: 0x7F6, offset: 0x219FD, size: 0x8, addend: 0x0, symName: __ZL10patchBuf14, symObjAddr: 0x171E0, symBinAddr: 0x8FE0, symSize: 0x0 }
  - { offsetInCU: 0x815, offset: 0x21A1C, size: 0x8, addend: 0x0, symName: __ZL10revisions2, symObjAddr: 0x144DC, symBinAddr: 0x62DC, symSize: 0x0 }
  - { offsetInCU: 0x834, offset: 0x21A3B, size: 0x8, addend: 0x0, symName: __ZL10platforms2, symObjAddr: 0x1AD280, symBinAddr: 0x19F110, symSize: 0x0 }
  - { offsetInCU: 0x867, offset: 0x21A6E, size: 0x8, addend: 0x0, symName: __ZL6file15, symObjAddr: 0x171F0, symBinAddr: 0x8FF0, symSize: 0x0 }
  - { offsetInCU: 0x89B, offset: 0x21AA2, size: 0x8, addend: 0x0, symName: __ZL8layouts2, symObjAddr: 0x1AD2A0, symBinAddr: 0x19F130, symSize: 0x0 }
  - { offsetInCU: 0x8BA, offset: 0x21AC1, size: 0x8, addend: 0x0, symName: __ZL6file16, symObjAddr: 0x17340, symBinAddr: 0x9140, symSize: 0x0 }
  - { offsetInCU: 0x8ED, offset: 0x21AF4, size: 0x8, addend: 0x0, symName: __ZL8patches2, symObjAddr: 0x8B90, symBinAddr: 0x1AB540, symSize: 0x0 }
  - { offsetInCU: 0x90C, offset: 0x21B13, size: 0x8, addend: 0x0, symName: __ZL10patchBuf15, symObjAddr: 0x17402, symBinAddr: 0x9202, symSize: 0x0 }
  - { offsetInCU: 0x92B, offset: 0x21B32, size: 0x8, addend: 0x0, symName: __ZL19codecModCirrusLogic, symObjAddr: 0x4EF0, symBinAddr: 0x1A78A0, symSize: 0x0 }
  - { offsetInCU: 0x94A, offset: 0x21B51, size: 0x8, addend: 0x0, symName: __ZL10revisions3, symObjAddr: 0x17408, symBinAddr: 0x9208, symSize: 0x0 }
  - { offsetInCU: 0x969, offset: 0x21B70, size: 0x8, addend: 0x0, symName: __ZL10platforms3, symObjAddr: 0x1AD2C0, symBinAddr: 0x19F150, symSize: 0x0 }
  - { offsetInCU: 0x988, offset: 0x21B8F, size: 0x8, addend: 0x0, symName: __ZL6file17, symObjAddr: 0x17420, symBinAddr: 0x9220, symSize: 0x0 }
  - { offsetInCU: 0x9BC, offset: 0x21BC3, size: 0x8, addend: 0x0, symName: __ZL8layouts3, symObjAddr: 0x1AD2E0, symBinAddr: 0x19F170, symSize: 0x0 }
  - { offsetInCU: 0x9DB, offset: 0x21BE2, size: 0x8, addend: 0x0, symName: __ZL6file18, symObjAddr: 0x17F60, symBinAddr: 0x9D60, symSize: 0x0 }
  - { offsetInCU: 0xA0F, offset: 0x21C16, size: 0x8, addend: 0x0, symName: __ZL8patches3, symObjAddr: 0x8CE0, symBinAddr: 0x1AB690, symSize: 0x0 }
  - { offsetInCU: 0xA42, offset: 0x21C49, size: 0x8, addend: 0x0, symName: __ZL10patchBuf16, symObjAddr: 0x182E3, symBinAddr: 0xA0E3, symSize: 0x0 }
  - { offsetInCU: 0xA61, offset: 0x21C68, size: 0x8, addend: 0x0, symName: __ZL10revisions4, symObjAddr: 0x1740C, symBinAddr: 0x920C, symSize: 0x0 }
  - { offsetInCU: 0xA94, offset: 0x21C9B, size: 0x8, addend: 0x0, symName: __ZL10platforms4, symObjAddr: 0x1AD300, symBinAddr: 0x19F190, symSize: 0x0 }
  - { offsetInCU: 0xAC7, offset: 0x21CCE, size: 0x8, addend: 0x0, symName: __ZL6file19, symObjAddr: 0x182F0, symBinAddr: 0xA0F0, symSize: 0x0 }
  - { offsetInCU: 0xAFB, offset: 0x21D02, size: 0x8, addend: 0x0, symName: __ZL8layouts4, symObjAddr: 0x1AD570, symBinAddr: 0x19F400, symSize: 0x0 }
  - { offsetInCU: 0xB1A, offset: 0x21D21, size: 0x8, addend: 0x0, symName: __ZL6file20, symObjAddr: 0x19110, symBinAddr: 0xAF10, symSize: 0x0 }
  - { offsetInCU: 0xB4E, offset: 0x21D55, size: 0x8, addend: 0x0, symName: __ZL6file21, symObjAddr: 0x1A3C0, symBinAddr: 0xC1C0, symSize: 0x0 }
  - { offsetInCU: 0xB82, offset: 0x21D89, size: 0x8, addend: 0x0, symName: __ZL6file22, symObjAddr: 0x1B250, symBinAddr: 0xD050, symSize: 0x0 }
  - { offsetInCU: 0xBB6, offset: 0x21DBD, size: 0x8, addend: 0x0, symName: __ZL6file23, symObjAddr: 0x1C0A0, symBinAddr: 0xDEA0, symSize: 0x0 }
  - { offsetInCU: 0xBEA, offset: 0x21DF1, size: 0x8, addend: 0x0, symName: __ZL6file24, symObjAddr: 0x1CA30, symBinAddr: 0xE830, symSize: 0x0 }
  - { offsetInCU: 0xC1E, offset: 0x21E25, size: 0x8, addend: 0x0, symName: __ZL6file25, symObjAddr: 0x1D4C0, symBinAddr: 0xF2C0, symSize: 0x0 }
  - { offsetInCU: 0xC52, offset: 0x21E59, size: 0x8, addend: 0x0, symName: __ZL6file26, symObjAddr: 0x1E0D0, symBinAddr: 0xFED0, symSize: 0x0 }
  - { offsetInCU: 0xC86, offset: 0x21E8D, size: 0x8, addend: 0x0, symName: __ZL6file27, symObjAddr: 0x1EE20, symBinAddr: 0x10C20, symSize: 0x0 }
  - { offsetInCU: 0xCBA, offset: 0x21EC1, size: 0x8, addend: 0x0, symName: __ZL6file28, symObjAddr: 0x20260, symBinAddr: 0x12060, symSize: 0x0 }
  - { offsetInCU: 0xCD9, offset: 0x21EE0, size: 0x8, addend: 0x0, symName: __ZL6file29, symObjAddr: 0x216A0, symBinAddr: 0x134A0, symSize: 0x0 }
  - { offsetInCU: 0xD0D, offset: 0x21F14, size: 0x8, addend: 0x0, symName: __ZL6file30, symObjAddr: 0x229D0, symBinAddr: 0x147D0, symSize: 0x0 }
  - { offsetInCU: 0xD41, offset: 0x21F48, size: 0x8, addend: 0x0, symName: __ZL6file31, symObjAddr: 0x23950, symBinAddr: 0x15750, symSize: 0x0 }
  - { offsetInCU: 0xD75, offset: 0x21F7C, size: 0x8, addend: 0x0, symName: __ZL6file32, symObjAddr: 0x24BF0, symBinAddr: 0x169F0, symSize: 0x0 }
  - { offsetInCU: 0xDA9, offset: 0x21FB0, size: 0x8, addend: 0x0, symName: __ZL6file33, symObjAddr: 0x259F0, symBinAddr: 0x177F0, symSize: 0x0 }
  - { offsetInCU: 0xDDD, offset: 0x21FE4, size: 0x8, addend: 0x0, symName: __ZL6file34, symObjAddr: 0x26DE0, symBinAddr: 0x18BE0, symSize: 0x0 }
  - { offsetInCU: 0xE11, offset: 0x22018, size: 0x8, addend: 0x0, symName: __ZL6file35, symObjAddr: 0x281B0, symBinAddr: 0x19FB0, symSize: 0x0 }
  - { offsetInCU: 0xE45, offset: 0x2204C, size: 0x8, addend: 0x0, symName: __ZL6file36, symObjAddr: 0x292B0, symBinAddr: 0x1B0B0, symSize: 0x0 }
  - { offsetInCU: 0xE79, offset: 0x22080, size: 0x8, addend: 0x0, symName: __ZL6file37, symObjAddr: 0x29E80, symBinAddr: 0x1BC80, symSize: 0x0 }
  - { offsetInCU: 0xEAD, offset: 0x220B4, size: 0x8, addend: 0x0, symName: __ZL6file38, symObjAddr: 0x2AA30, symBinAddr: 0x1C830, symSize: 0x0 }
  - { offsetInCU: 0xEE1, offset: 0x220E8, size: 0x8, addend: 0x0, symName: __ZL6file39, symObjAddr: 0x2B5E0, symBinAddr: 0x1D3E0, symSize: 0x0 }
  - { offsetInCU: 0xF00, offset: 0x22107, size: 0x8, addend: 0x0, symName: __ZL6file40, symObjAddr: 0x2C1B0, symBinAddr: 0x1DFB0, symSize: 0x0 }
  - { offsetInCU: 0xF1F, offset: 0x22126, size: 0x8, addend: 0x0, symName: __ZL6file41, symObjAddr: 0x2CD60, symBinAddr: 0x1EB60, symSize: 0x0 }
  - { offsetInCU: 0xF53, offset: 0x2215A, size: 0x8, addend: 0x0, symName: __ZL6file42, symObjAddr: 0x2DC30, symBinAddr: 0x1FA30, symSize: 0x0 }
  - { offsetInCU: 0xF87, offset: 0x2218E, size: 0x8, addend: 0x0, symName: __ZL6file43, symObjAddr: 0x2E800, symBinAddr: 0x20600, symSize: 0x0 }
  - { offsetInCU: 0xFBB, offset: 0x221C2, size: 0x8, addend: 0x0, symName: __ZL6file44, symObjAddr: 0x2EE70, symBinAddr: 0x20C70, symSize: 0x0 }
  - { offsetInCU: 0xFEF, offset: 0x221F6, size: 0x8, addend: 0x0, symName: __ZL6file45, symObjAddr: 0x2F620, symBinAddr: 0x21420, symSize: 0x0 }
  - { offsetInCU: 0x100E, offset: 0x22215, size: 0x8, addend: 0x0, symName: __ZL8patches4, symObjAddr: 0x2300F0, symBinAddr: 0x1B5E20, symSize: 0x0 }
  - { offsetInCU: 0x1041, offset: 0x22248, size: 0x8, addend: 0x0, symName: __ZL10revisions5, symObjAddr: 0x17418, symBinAddr: 0x9218, symSize: 0x0 }
  - { offsetInCU: 0x1060, offset: 0x22267, size: 0x8, addend: 0x0, symName: __ZL10platforms5, symObjAddr: 0x1AD7E0, symBinAddr: 0x19F670, symSize: 0x0 }
  - { offsetInCU: 0x107F, offset: 0x22286, size: 0x8, addend: 0x0, symName: __ZL6file46, symObjAddr: 0x2FDD0, symBinAddr: 0x21BD0, symSize: 0x0 }
  - { offsetInCU: 0x10B3, offset: 0x222BA, size: 0x8, addend: 0x0, symName: __ZL8layouts5, symObjAddr: 0x1AD800, symBinAddr: 0x19F690, symSize: 0x0 }
  - { offsetInCU: 0x10D2, offset: 0x222D9, size: 0x8, addend: 0x0, symName: __ZL6file47, symObjAddr: 0x30810, symBinAddr: 0x22610, symSize: 0x0 }
  - { offsetInCU: 0x1106, offset: 0x2230D, size: 0x8, addend: 0x0, symName: __ZL8patches5, symObjAddr: 0x8E00, symBinAddr: 0x1AB7B0, symSize: 0x0 }
  - { offsetInCU: 0x1139, offset: 0x22340, size: 0x8, addend: 0x0, symName: __ZL10patchBuf17, symObjAddr: 0x315D0, symBinAddr: 0x233D0, symSize: 0x0 }
  - { offsetInCU: 0x1158, offset: 0x2235F, size: 0x8, addend: 0x0, symName: __ZL10patchBuf18, symObjAddr: 0x315D4, symBinAddr: 0x233D4, symSize: 0x0 }
  - { offsetInCU: 0x1177, offset: 0x2237E, size: 0x8, addend: 0x0, symName: __ZL10patchBuf19, symObjAddr: 0x315D8, symBinAddr: 0x233D8, symSize: 0x0 }
  - { offsetInCU: 0x1196, offset: 0x2239D, size: 0x8, addend: 0x0, symName: __ZL10patchBuf20, symObjAddr: 0x315DC, symBinAddr: 0x233DC, symSize: 0x0 }
  - { offsetInCU: 0x11B5, offset: 0x223BC, size: 0x8, addend: 0x0, symName: __ZL10patchBuf21, symObjAddr: 0x315E0, symBinAddr: 0x233E0, symSize: 0x0 }
  - { offsetInCU: 0x11D4, offset: 0x223DB, size: 0x8, addend: 0x0, symName: __ZL21codecModAnalogDevices, symObjAddr: 0x4FE0, symBinAddr: 0x1A7990, symSize: 0x0 }
  - { offsetInCU: 0x1207, offset: 0x2240E, size: 0x8, addend: 0x0, symName: __ZL10platforms6, symObjAddr: 0x1AD820, symBinAddr: 0x19F6B0, symSize: 0x0 }
  - { offsetInCU: 0x1226, offset: 0x2242D, size: 0x8, addend: 0x0, symName: __ZL6file48, symObjAddr: 0x315F0, symBinAddr: 0x233F0, symSize: 0x0 }
  - { offsetInCU: 0x125A, offset: 0x22461, size: 0x8, addend: 0x0, symName: __ZL8layouts6, symObjAddr: 0x1AD840, symBinAddr: 0x19F6D0, symSize: 0x0 }
  - { offsetInCU: 0x1279, offset: 0x22480, size: 0x8, addend: 0x0, symName: __ZL6file49, symObjAddr: 0x317A0, symBinAddr: 0x235A0, symSize: 0x0 }
  - { offsetInCU: 0x12AD, offset: 0x224B4, size: 0x8, addend: 0x0, symName: __ZL8patches6, symObjAddr: 0x9010, symBinAddr: 0x1AB9C0, symSize: 0x0 }
  - { offsetInCU: 0x12CC, offset: 0x224D3, size: 0x8, addend: 0x0, symName: __ZL10patchBuf22, symObjAddr: 0x31B2D, symBinAddr: 0x2392D, symSize: 0x0 }
  - { offsetInCU: 0x12EB, offset: 0x224F2, size: 0x8, addend: 0x0, symName: __ZL10platforms7, symObjAddr: 0x1AD860, symBinAddr: 0x19F6F0, symSize: 0x0 }
  - { offsetInCU: 0x130A, offset: 0x22511, size: 0x8, addend: 0x0, symName: __ZL6file50, symObjAddr: 0x31B40, symBinAddr: 0x23940, symSize: 0x0 }
  - { offsetInCU: 0x133E, offset: 0x22545, size: 0x8, addend: 0x0, symName: __ZL8layouts7, symObjAddr: 0x1AD880, symBinAddr: 0x19F710, symSize: 0x0 }
  - { offsetInCU: 0x135D, offset: 0x22564, size: 0x8, addend: 0x0, symName: __ZL6file51, symObjAddr: 0x31C90, symBinAddr: 0x23A90, symSize: 0x0 }
  - { offsetInCU: 0x1391, offset: 0x22598, size: 0x8, addend: 0x0, symName: __ZL8patches7, symObjAddr: 0x9160, symBinAddr: 0x1ABB10, symSize: 0x0 }
  - { offsetInCU: 0x13B0, offset: 0x225B7, size: 0x8, addend: 0x0, symName: __ZL10patchBuf23, symObjAddr: 0x31FFA, symBinAddr: 0x23DFA, symSize: 0x0 }
  - { offsetInCU: 0x13CF, offset: 0x225D6, size: 0x8, addend: 0x0, symName: __ZL10patchBuf24, symObjAddr: 0x31FFE, symBinAddr: 0x23DFE, symSize: 0x0 }
  - { offsetInCU: 0x13EE, offset: 0x225F5, size: 0x8, addend: 0x0, symName: __ZL10platforms8, symObjAddr: 0x1AD8A0, symBinAddr: 0x19F730, symSize: 0x0 }
  - { offsetInCU: 0x1421, offset: 0x22628, size: 0x8, addend: 0x0, symName: __ZL6file52, symObjAddr: 0x32010, symBinAddr: 0x23E10, symSize: 0x0 }
  - { offsetInCU: 0x1455, offset: 0x2265C, size: 0x8, addend: 0x0, symName: __ZL8layouts8, symObjAddr: 0x1AD8D0, symBinAddr: 0x19F760, symSize: 0x0 }
  - { offsetInCU: 0x1474, offset: 0x2267B, size: 0x8, addend: 0x0, symName: __ZL6file53, symObjAddr: 0x32230, symBinAddr: 0x24030, symSize: 0x0 }
  - { offsetInCU: 0x14A8, offset: 0x226AF, size: 0x8, addend: 0x0, symName: __ZL6file54, symObjAddr: 0x32830, symBinAddr: 0x24630, symSize: 0x0 }
  - { offsetInCU: 0x14C7, offset: 0x226CE, size: 0x8, addend: 0x0, symName: __ZL8patches8, symObjAddr: 0x92E0, symBinAddr: 0x1ABC90, symSize: 0x0 }
  - { offsetInCU: 0x14E6, offset: 0x226ED, size: 0x8, addend: 0x0, symName: __ZL10patchBuf25, symObjAddr: 0x32E28, symBinAddr: 0x24C28, symSize: 0x0 }
  - { offsetInCU: 0x1505, offset: 0x2270C, size: 0x8, addend: 0x0, symName: __ZL10revisions6, symObjAddr: 0x315E4, symBinAddr: 0x233E4, symSize: 0x0 }
  - { offsetInCU: 0x1524, offset: 0x2272B, size: 0x8, addend: 0x0, symName: __ZL10platforms9, symObjAddr: 0x1AD900, symBinAddr: 0x19F790, symSize: 0x0 }
  - { offsetInCU: 0x1557, offset: 0x2275E, size: 0x8, addend: 0x0, symName: __ZL6file55, symObjAddr: 0x32E30, symBinAddr: 0x24C30, symSize: 0x0 }
  - { offsetInCU: 0x158B, offset: 0x22792, size: 0x8, addend: 0x0, symName: __ZL6file56, symObjAddr: 0x32F80, symBinAddr: 0x24D80, symSize: 0x0 }
  - { offsetInCU: 0x15BF, offset: 0x227C6, size: 0x8, addend: 0x0, symName: __ZL6file57, symObjAddr: 0x330D0, symBinAddr: 0x24ED0, symSize: 0x0 }
  - { offsetInCU: 0x15F3, offset: 0x227FA, size: 0x8, addend: 0x0, symName: __ZL8layouts9, symObjAddr: 0x1AD950, symBinAddr: 0x19F7E0, symSize: 0x0 }
  - { offsetInCU: 0x1612, offset: 0x22819, size: 0x8, addend: 0x0, symName: __ZL6file58, symObjAddr: 0x33230, symBinAddr: 0x25030, symSize: 0x0 }
  - { offsetInCU: 0x1646, offset: 0x2284D, size: 0x8, addend: 0x0, symName: __ZL6file59, symObjAddr: 0x338F0, symBinAddr: 0x256F0, symSize: 0x0 }
  - { offsetInCU: 0x167A, offset: 0x22881, size: 0x8, addend: 0x0, symName: __ZL6file60, symObjAddr: 0x33FB0, symBinAddr: 0x25DB0, symSize: 0x0 }
  - { offsetInCU: 0x1699, offset: 0x228A0, size: 0x8, addend: 0x0, symName: __ZL8patches9, symObjAddr: 0x9430, symBinAddr: 0x1ABDE0, symSize: 0x0 }
  - { offsetInCU: 0x16B8, offset: 0x228BF, size: 0x8, addend: 0x0, symName: __ZL10patchBuf26, symObjAddr: 0x3466B, symBinAddr: 0x2646B, symSize: 0x0 }
  - { offsetInCU: 0x16D7, offset: 0x228DE, size: 0x8, addend: 0x0, symName: __ZL11platforms10, symObjAddr: 0x1AD9A0, symBinAddr: 0x19F830, symSize: 0x0 }
  - { offsetInCU: 0x16F6, offset: 0x228FD, size: 0x8, addend: 0x0, symName: __ZL6file61, symObjAddr: 0x34670, symBinAddr: 0x26470, symSize: 0x0 }
  - { offsetInCU: 0x1715, offset: 0x2291C, size: 0x8, addend: 0x0, symName: __ZL6file62, symObjAddr: 0x34890, symBinAddr: 0x26690, symSize: 0x0 }
  - { offsetInCU: 0x1749, offset: 0x22950, size: 0x8, addend: 0x0, symName: __ZL9layouts10, symObjAddr: 0x1AD9F0, symBinAddr: 0x19F880, symSize: 0x0 }
  - { offsetInCU: 0x1768, offset: 0x2296F, size: 0x8, addend: 0x0, symName: __ZL6file63, symObjAddr: 0x34A50, symBinAddr: 0x26850, symSize: 0x0 }
  - { offsetInCU: 0x179C, offset: 0x229A3, size: 0x8, addend: 0x0, symName: __ZL6file64, symObjAddr: 0x35050, symBinAddr: 0x26E50, symSize: 0x0 }
  - { offsetInCU: 0x17BB, offset: 0x229C2, size: 0x8, addend: 0x0, symName: __ZL6file65, symObjAddr: 0x35650, symBinAddr: 0x27450, symSize: 0x0 }
  - { offsetInCU: 0x17EF, offset: 0x229F6, size: 0x8, addend: 0x0, symName: __ZL9patches10, symObjAddr: 0x9580, symBinAddr: 0x1ABF30, symSize: 0x0 }
  - { offsetInCU: 0x180E, offset: 0x22A15, size: 0x8, addend: 0x0, symName: __ZL10revisions7, symObjAddr: 0x315E8, symBinAddr: 0x233E8, symSize: 0x0 }
  - { offsetInCU: 0x182D, offset: 0x22A34, size: 0x8, addend: 0x0, symName: __ZL11platforms11, symObjAddr: 0x1ADA40, symBinAddr: 0x19F8D0, symSize: 0x0 }
  - { offsetInCU: 0x184C, offset: 0x22A53, size: 0x8, addend: 0x0, symName: __ZL6file66, symObjAddr: 0x359E0, symBinAddr: 0x277E0, symSize: 0x0 }
  - { offsetInCU: 0x1880, offset: 0x22A87, size: 0x8, addend: 0x0, symName: __ZL9layouts11, symObjAddr: 0x1ADA60, symBinAddr: 0x19F8F0, symSize: 0x0 }
  - { offsetInCU: 0x189F, offset: 0x22AA6, size: 0x8, addend: 0x0, symName: __ZL6file67, symObjAddr: 0x35B40, symBinAddr: 0x27940, symSize: 0x0 }
  - { offsetInCU: 0x18D3, offset: 0x22ADA, size: 0x8, addend: 0x0, symName: __ZL9patches11, symObjAddr: 0x96A0, symBinAddr: 0x1AC050, symSize: 0x0 }
  - { offsetInCU: 0x18F2, offset: 0x22AF9, size: 0x8, addend: 0x0, symName: __ZL13codecModIntel, symObjAddr: 0x2300D8, symBinAddr: 0x1B5E08, symSize: 0x0 }
  - { offsetInCU: 0x1911, offset: 0x22B18, size: 0x8, addend: 0x0, symName: __ZL16codecModConexant, symObjAddr: 0x51C0, symBinAddr: 0x1A7B70, symSize: 0x0 }
  - { offsetInCU: 0x1944, offset: 0x22B4B, size: 0x8, addend: 0x0, symName: __ZL10revisions8, symObjAddr: 0x361F0, symBinAddr: 0x27FF0, symSize: 0x0 }
  - { offsetInCU: 0x1963, offset: 0x22B6A, size: 0x8, addend: 0x0, symName: __ZL11platforms12, symObjAddr: 0x1ADA80, symBinAddr: 0x19F910, symSize: 0x0 }
  - { offsetInCU: 0x1982, offset: 0x22B89, size: 0x8, addend: 0x0, symName: __ZL6file68, symObjAddr: 0x36210, symBinAddr: 0x28010, symSize: 0x0 }
  - { offsetInCU: 0x19B6, offset: 0x22BBD, size: 0x8, addend: 0x0, symName: __ZL9layouts12, symObjAddr: 0x1ADAA0, symBinAddr: 0x19F930, symSize: 0x0 }
  - { offsetInCU: 0x19D5, offset: 0x22BDC, size: 0x8, addend: 0x0, symName: __ZL6file69, symObjAddr: 0x36370, symBinAddr: 0x28170, symSize: 0x0 }
  - { offsetInCU: 0x1A09, offset: 0x22C10, size: 0x8, addend: 0x0, symName: __ZL9patches12, symObjAddr: 0x97F0, symBinAddr: 0x1AC1A0, symSize: 0x0 }
  - { offsetInCU: 0x1A28, offset: 0x22C2F, size: 0x8, addend: 0x0, symName: __ZL10patchBuf27, symObjAddr: 0x369DA, symBinAddr: 0x287DA, symSize: 0x0 }
  - { offsetInCU: 0x1A47, offset: 0x22C4E, size: 0x8, addend: 0x0, symName: __ZL11platforms13, symObjAddr: 0x1ADAC0, symBinAddr: 0x19F950, symSize: 0x0 }
  - { offsetInCU: 0x1A66, offset: 0x22C6D, size: 0x8, addend: 0x0, symName: __ZL6file70, symObjAddr: 0x369E0, symBinAddr: 0x287E0, symSize: 0x0 }
  - { offsetInCU: 0x1A9A, offset: 0x22CA1, size: 0x8, addend: 0x0, symName: __ZL6file71, symObjAddr: 0x36B10, symBinAddr: 0x28910, symSize: 0x0 }
  - { offsetInCU: 0x1ACE, offset: 0x22CD5, size: 0x8, addend: 0x0, symName: __ZL9layouts13, symObjAddr: 0x1ADAF0, symBinAddr: 0x19F980, symSize: 0x0 }
  - { offsetInCU: 0x1AED, offset: 0x22CF4, size: 0x8, addend: 0x0, symName: __ZL6file72, symObjAddr: 0x36C40, symBinAddr: 0x28A40, symSize: 0x0 }
  - { offsetInCU: 0x1B21, offset: 0x22D28, size: 0x8, addend: 0x0, symName: __ZL6file73, symObjAddr: 0x36D90, symBinAddr: 0x28B90, symSize: 0x0 }
  - { offsetInCU: 0x1B40, offset: 0x22D47, size: 0x8, addend: 0x0, symName: __ZL9patches13, symObjAddr: 0x9940, symBinAddr: 0x1AC2F0, symSize: 0x0 }
  - { offsetInCU: 0x1B5F, offset: 0x22D66, size: 0x8, addend: 0x0, symName: __ZL10patchBuf28, symObjAddr: 0x36ED2, symBinAddr: 0x28CD2, symSize: 0x0 }
  - { offsetInCU: 0x1B7E, offset: 0x22D85, size: 0x8, addend: 0x0, symName: __ZL10patchBuf29, symObjAddr: 0x36ED6, symBinAddr: 0x28CD6, symSize: 0x0 }
  - { offsetInCU: 0x1BB1, offset: 0x22DB8, size: 0x8, addend: 0x0, symName: __ZL10patchBuf30, symObjAddr: 0x36EDB, symBinAddr: 0x28CDB, symSize: 0x0 }
  - { offsetInCU: 0x1BD0, offset: 0x22DD7, size: 0x8, addend: 0x0, symName: __ZL11platforms14, symObjAddr: 0x1ADB20, symBinAddr: 0x19F9B0, symSize: 0x0 }
  - { offsetInCU: 0x1BEF, offset: 0x22DF6, size: 0x8, addend: 0x0, symName: __ZL6file74, symObjAddr: 0x36EE0, symBinAddr: 0x28CE0, symSize: 0x0 }
  - { offsetInCU: 0x1C23, offset: 0x22E2A, size: 0x8, addend: 0x0, symName: __ZL9layouts14, symObjAddr: 0x1ADB40, symBinAddr: 0x19F9D0, symSize: 0x0 }
  - { offsetInCU: 0x1C42, offset: 0x22E49, size: 0x8, addend: 0x0, symName: __ZL6file75, symObjAddr: 0x37040, symBinAddr: 0x28E40, symSize: 0x0 }
  - { offsetInCU: 0x1C75, offset: 0x22E7C, size: 0x8, addend: 0x0, symName: __ZL9patches14, symObjAddr: 0x9A90, symBinAddr: 0x1AC440, symSize: 0x0 }
  - { offsetInCU: 0x1C94, offset: 0x22E9B, size: 0x8, addend: 0x0, symName: __ZL10patchBuf31, symObjAddr: 0x3711C, symBinAddr: 0x28F1C, symSize: 0x0 }
  - { offsetInCU: 0x1CB3, offset: 0x22EBA, size: 0x8, addend: 0x0, symName: __ZL11platforms15, symObjAddr: 0x1ADB60, symBinAddr: 0x19F9F0, symSize: 0x0 }
  - { offsetInCU: 0x1CD2, offset: 0x22ED9, size: 0x8, addend: 0x0, symName: __ZL6file76, symObjAddr: 0x37120, symBinAddr: 0x28F20, symSize: 0x0 }
  - { offsetInCU: 0x1CF1, offset: 0x22EF8, size: 0x8, addend: 0x0, symName: __ZL6file77, symObjAddr: 0x37270, symBinAddr: 0x29070, symSize: 0x0 }
  - { offsetInCU: 0x1D25, offset: 0x22F2C, size: 0x8, addend: 0x0, symName: __ZL9layouts15, symObjAddr: 0x1ADB90, symBinAddr: 0x19FA20, symSize: 0x0 }
  - { offsetInCU: 0x1D44, offset: 0x22F4B, size: 0x8, addend: 0x0, symName: __ZL6file78, symObjAddr: 0x373C0, symBinAddr: 0x291C0, symSize: 0x0 }
  - { offsetInCU: 0x1D78, offset: 0x22F7F, size: 0x8, addend: 0x0, symName: __ZL6file79, symObjAddr: 0x37530, symBinAddr: 0x29330, symSize: 0x0 }
  - { offsetInCU: 0x1DAC, offset: 0x22FB3, size: 0x8, addend: 0x0, symName: __ZL9patches15, symObjAddr: 0x9C10, symBinAddr: 0x1AC5C0, symSize: 0x0 }
  - { offsetInCU: 0x1DCB, offset: 0x22FD2, size: 0x8, addend: 0x0, symName: __ZL10patchBuf32, symObjAddr: 0x376A2, symBinAddr: 0x294A2, symSize: 0x0 }
  - { offsetInCU: 0x1DEA, offset: 0x22FF1, size: 0x8, addend: 0x0, symName: __ZL11platforms16, symObjAddr: 0x1ADBC0, symBinAddr: 0x19FA50, symSize: 0x0 }
  - { offsetInCU: 0x1E09, offset: 0x23010, size: 0x8, addend: 0x0, symName: __ZL6file80, symObjAddr: 0x376B0, symBinAddr: 0x294B0, symSize: 0x0 }
  - { offsetInCU: 0x1E3D, offset: 0x23044, size: 0x8, addend: 0x0, symName: __ZL6file81, symObjAddr: 0x377D0, symBinAddr: 0x295D0, symSize: 0x0 }
  - { offsetInCU: 0x1E71, offset: 0x23078, size: 0x8, addend: 0x0, symName: __ZL9layouts16, symObjAddr: 0x1ADBF0, symBinAddr: 0x19FA80, symSize: 0x0 }
  - { offsetInCU: 0x1E90, offset: 0x23097, size: 0x8, addend: 0x0, symName: __ZL6file82, symObjAddr: 0x378F0, symBinAddr: 0x296F0, symSize: 0x0 }
  - { offsetInCU: 0x1EC4, offset: 0x230CB, size: 0x8, addend: 0x0, symName: __ZL6file83, symObjAddr: 0x37C60, symBinAddr: 0x29A60, symSize: 0x0 }
  - { offsetInCU: 0x1EF8, offset: 0x230FF, size: 0x8, addend: 0x0, symName: __ZL9patches16, symObjAddr: 0x9D60, symBinAddr: 0x1AC710, symSize: 0x0 }
  - { offsetInCU: 0x1F17, offset: 0x2311E, size: 0x8, addend: 0x0, symName: __ZL10patchBuf33, symObjAddr: 0x37FC9, symBinAddr: 0x29DC9, symSize: 0x0 }
  - { offsetInCU: 0x1F36, offset: 0x2313D, size: 0x8, addend: 0x0, symName: __ZL10revisions9, symObjAddr: 0x361F4, symBinAddr: 0x27FF4, symSize: 0x0 }
  - { offsetInCU: 0x1F55, offset: 0x2315C, size: 0x8, addend: 0x0, symName: __ZL11platforms17, symObjAddr: 0x1ADC20, symBinAddr: 0x19FAB0, symSize: 0x0 }
  - { offsetInCU: 0x1F74, offset: 0x2317B, size: 0x8, addend: 0x0, symName: __ZL6file84, symObjAddr: 0x37FD0, symBinAddr: 0x29DD0, symSize: 0x0 }
  - { offsetInCU: 0x1FA8, offset: 0x231AF, size: 0x8, addend: 0x0, symName: __ZL6file85, symObjAddr: 0x38B20, symBinAddr: 0x2A920, symSize: 0x0 }
  - { offsetInCU: 0x1FC7, offset: 0x231CE, size: 0x8, addend: 0x0, symName: __ZL6file86, symObjAddr: 0x39670, symBinAddr: 0x2B470, symSize: 0x0 }
  - { offsetInCU: 0x1FFB, offset: 0x23202, size: 0x8, addend: 0x0, symName: __ZL6file87, symObjAddr: 0x397B0, symBinAddr: 0x2B5B0, symSize: 0x0 }
  - { offsetInCU: 0x202F, offset: 0x23236, size: 0x8, addend: 0x0, symName: __ZL9layouts17, symObjAddr: 0x1ADC80, symBinAddr: 0x19FB10, symSize: 0x0 }
  - { offsetInCU: 0x204E, offset: 0x23255, size: 0x8, addend: 0x0, symName: __ZL6file88, symObjAddr: 0x3A300, symBinAddr: 0x2C100, symSize: 0x0 }
  - { offsetInCU: 0x2082, offset: 0x23289, size: 0x8, addend: 0x0, symName: __ZL6file89, symObjAddr: 0x3A680, symBinAddr: 0x2C480, symSize: 0x0 }
  - { offsetInCU: 0x20B6, offset: 0x232BD, size: 0x8, addend: 0x0, symName: __ZL6file90, symObjAddr: 0x3AA00, symBinAddr: 0x2C800, symSize: 0x0 }
  - { offsetInCU: 0x20EA, offset: 0x232F1, size: 0x8, addend: 0x0, symName: __ZL6file91, symObjAddr: 0x3ADA0, symBinAddr: 0x2CBA0, symSize: 0x0 }
  - { offsetInCU: 0x211E, offset: 0x23325, size: 0x8, addend: 0x0, symName: __ZL9patches17, symObjAddr: 0x9EB0, symBinAddr: 0x1AC860, symSize: 0x0 }
  - { offsetInCU: 0x2151, offset: 0x23358, size: 0x8, addend: 0x0, symName: __ZL10patchBuf34, symObjAddr: 0x3B11F, symBinAddr: 0x2CF1F, symSize: 0x0 }
  - { offsetInCU: 0x2170, offset: 0x23377, size: 0x8, addend: 0x0, symName: __ZL11platforms18, symObjAddr: 0x1ADCE0, symBinAddr: 0x19FB70, symSize: 0x0 }
  - { offsetInCU: 0x218F, offset: 0x23396, size: 0x8, addend: 0x0, symName: __ZL6file92, symObjAddr: 0x3B130, symBinAddr: 0x2CF30, symSize: 0x0 }
  - { offsetInCU: 0x21C3, offset: 0x233CA, size: 0x8, addend: 0x0, symName: __ZL6file93, symObjAddr: 0x3B270, symBinAddr: 0x2D070, symSize: 0x0 }
  - { offsetInCU: 0x21F7, offset: 0x233FE, size: 0x8, addend: 0x0, symName: __ZL6file94, symObjAddr: 0x3B3D0, symBinAddr: 0x2D1D0, symSize: 0x0 }
  - { offsetInCU: 0x222B, offset: 0x23432, size: 0x8, addend: 0x0, symName: __ZL6file95, symObjAddr: 0x3B510, symBinAddr: 0x2D310, symSize: 0x0 }
  - { offsetInCU: 0x225F, offset: 0x23466, size: 0x8, addend: 0x0, symName: __ZL9layouts18, symObjAddr: 0x1ADD40, symBinAddr: 0x19FBD0, symSize: 0x0 }
  - { offsetInCU: 0x227E, offset: 0x23485, size: 0x8, addend: 0x0, symName: __ZL6file96, symObjAddr: 0x3B650, symBinAddr: 0x2D450, symSize: 0x0 }
  - { offsetInCU: 0x22B2, offset: 0x234B9, size: 0x8, addend: 0x0, symName: __ZL6file97, symObjAddr: 0x3B9F0, symBinAddr: 0x2D7F0, symSize: 0x0 }
  - { offsetInCU: 0x22E6, offset: 0x234ED, size: 0x8, addend: 0x0, symName: __ZL6file98, symObjAddr: 0x3C1F0, symBinAddr: 0x2DFF0, symSize: 0x0 }
  - { offsetInCU: 0x231A, offset: 0x23521, size: 0x8, addend: 0x0, symName: __ZL6file99, symObjAddr: 0x3C570, symBinAddr: 0x2E370, symSize: 0x0 }
  - { offsetInCU: 0x2339, offset: 0x23540, size: 0x8, addend: 0x0, symName: __ZL9patches18, symObjAddr: 0xA060, symBinAddr: 0x1ACA10, symSize: 0x0 }
  - { offsetInCU: 0x2358, offset: 0x2355F, size: 0x8, addend: 0x0, symName: __ZL10patchBuf35, symObjAddr: 0x3C900, symBinAddr: 0x2E700, symSize: 0x0 }
  - { offsetInCU: 0x2377, offset: 0x2357E, size: 0x8, addend: 0x0, symName: __ZL11platforms19, symObjAddr: 0x1ADDA0, symBinAddr: 0x19FC30, symSize: 0x0 }
  - { offsetInCU: 0x2396, offset: 0x2359D, size: 0x8, addend: 0x0, symName: __ZL7file100, symObjAddr: 0x3C910, symBinAddr: 0x2E710, symSize: 0x0 }
  - { offsetInCU: 0x23CA, offset: 0x235D1, size: 0x8, addend: 0x0, symName: __ZL7file101, symObjAddr: 0x3D3E0, symBinAddr: 0x2F1E0, symSize: 0x0 }
  - { offsetInCU: 0x23FE, offset: 0x23605, size: 0x8, addend: 0x0, symName: __ZL9layouts19, symObjAddr: 0x1ADDD0, symBinAddr: 0x19FC60, symSize: 0x0 }
  - { offsetInCU: 0x241D, offset: 0x23624, size: 0x8, addend: 0x0, symName: __ZL7file102, symObjAddr: 0x3DF40, symBinAddr: 0x2FD40, symSize: 0x0 }
  - { offsetInCU: 0x2451, offset: 0x23658, size: 0x8, addend: 0x0, symName: __ZL7file103, symObjAddr: 0x3E2E0, symBinAddr: 0x300E0, symSize: 0x0 }
  - { offsetInCU: 0x2470, offset: 0x23677, size: 0x8, addend: 0x0, symName: __ZL9patches19, symObjAddr: 0xA1E0, symBinAddr: 0x1ACB90, symSize: 0x0 }
  - { offsetInCU: 0x248F, offset: 0x23696, size: 0x8, addend: 0x0, symName: __ZL10patchBuf36, symObjAddr: 0x3E663, symBinAddr: 0x30463, symSize: 0x0 }
  - { offsetInCU: 0x24AE, offset: 0x236B5, size: 0x8, addend: 0x0, symName: __ZL11platforms20, symObjAddr: 0x1ADE00, symBinAddr: 0x19FC90, symSize: 0x0 }
  - { offsetInCU: 0x24CD, offset: 0x236D4, size: 0x8, addend: 0x0, symName: __ZL7file104, symObjAddr: 0x3E670, symBinAddr: 0x30470, symSize: 0x0 }
  - { offsetInCU: 0x2501, offset: 0x23708, size: 0x8, addend: 0x0, symName: __ZL9layouts20, symObjAddr: 0x1ADE20, symBinAddr: 0x19FCB0, symSize: 0x0 }
  - { offsetInCU: 0x2520, offset: 0x23727, size: 0x8, addend: 0x0, symName: __ZL7file105, symObjAddr: 0x3E7A0, symBinAddr: 0x305A0, symSize: 0x0 }
  - { offsetInCU: 0x2554, offset: 0x2375B, size: 0x8, addend: 0x0, symName: __ZL9patches20, symObjAddr: 0xA390, symBinAddr: 0x1ACD40, symSize: 0x0 }
  - { offsetInCU: 0x2573, offset: 0x2377A, size: 0x8, addend: 0x0, symName: __ZL10patchBuf37, symObjAddr: 0x3EB45, symBinAddr: 0x30945, symSize: 0x0 }
  - { offsetInCU: 0x2592, offset: 0x23799, size: 0x8, addend: 0x0, symName: __ZL11platforms21, symObjAddr: 0x1ADE40, symBinAddr: 0x19FCD0, symSize: 0x0 }
  - { offsetInCU: 0x25B1, offset: 0x237B8, size: 0x8, addend: 0x0, symName: __ZL7file106, symObjAddr: 0x3EB50, symBinAddr: 0x30950, symSize: 0x0 }
  - { offsetInCU: 0x25D0, offset: 0x237D7, size: 0x8, addend: 0x0, symName: __ZL7file107, symObjAddr: 0x3EC80, symBinAddr: 0x30A80, symSize: 0x0 }
  - { offsetInCU: 0x2604, offset: 0x2380B, size: 0x8, addend: 0x0, symName: __ZL7file108, symObjAddr: 0x3EDA0, symBinAddr: 0x30BA0, symSize: 0x0 }
  - { offsetInCU: 0x2623, offset: 0x2382A, size: 0x8, addend: 0x0, symName: __ZL9layouts21, symObjAddr: 0x1ADE90, symBinAddr: 0x19FD20, symSize: 0x0 }
  - { offsetInCU: 0x2642, offset: 0x23849, size: 0x8, addend: 0x0, symName: __ZL7file109, symObjAddr: 0x3EED0, symBinAddr: 0x30CD0, symSize: 0x0 }
  - { offsetInCU: 0x2676, offset: 0x2387D, size: 0x8, addend: 0x0, symName: __ZL7file110, symObjAddr: 0x3F270, symBinAddr: 0x31070, symSize: 0x0 }
  - { offsetInCU: 0x2695, offset: 0x2389C, size: 0x8, addend: 0x0, symName: __ZL7file111, symObjAddr: 0x3F610, symBinAddr: 0x31410, symSize: 0x0 }
  - { offsetInCU: 0x26C9, offset: 0x238D0, size: 0x8, addend: 0x0, symName: __ZL9patches21, symObjAddr: 0xA4E0, symBinAddr: 0x1ACE90, symSize: 0x0 }
  - { offsetInCU: 0x26E8, offset: 0x238EF, size: 0x8, addend: 0x0, symName: __ZL10patchBuf38, symObjAddr: 0x3F9A6, symBinAddr: 0x317A6, symSize: 0x0 }
  - { offsetInCU: 0x2707, offset: 0x2390E, size: 0x8, addend: 0x0, symName: __ZL11platforms22, symObjAddr: 0x1ADEE0, symBinAddr: 0x19FD70, symSize: 0x0 }
  - { offsetInCU: 0x2726, offset: 0x2392D, size: 0x8, addend: 0x0, symName: __ZL7file112, symObjAddr: 0x3F9B0, symBinAddr: 0x317B0, symSize: 0x0 }
  - { offsetInCU: 0x275A, offset: 0x23961, size: 0x8, addend: 0x0, symName: __ZL9layouts22, symObjAddr: 0x1ADF00, symBinAddr: 0x19FD90, symSize: 0x0 }
  - { offsetInCU: 0x2779, offset: 0x23980, size: 0x8, addend: 0x0, symName: __ZL7file113, symObjAddr: 0x3FAF0, symBinAddr: 0x318F0, symSize: 0x0 }
  - { offsetInCU: 0x27AD, offset: 0x239B4, size: 0x8, addend: 0x0, symName: __ZL9patches22, symObjAddr: 0xA630, symBinAddr: 0x1ACFE0, symSize: 0x0 }
  - { offsetInCU: 0x27CC, offset: 0x239D3, size: 0x8, addend: 0x0, symName: __ZL10patchBuf39, symObjAddr: 0x3FDE8, symBinAddr: 0x31BE8, symSize: 0x0 }
  - { offsetInCU: 0x27EB, offset: 0x239F2, size: 0x8, addend: 0x0, symName: __ZL11platforms23, symObjAddr: 0x1ADF20, symBinAddr: 0x19FDB0, symSize: 0x0 }
  - { offsetInCU: 0x280A, offset: 0x23A11, size: 0x8, addend: 0x0, symName: __ZL7file114, symObjAddr: 0x3FDF0, symBinAddr: 0x31BF0, symSize: 0x0 }
  - { offsetInCU: 0x283E, offset: 0x23A45, size: 0x8, addend: 0x0, symName: __ZL7file115, symObjAddr: 0x3FF30, symBinAddr: 0x31D30, symSize: 0x0 }
  - { offsetInCU: 0x285D, offset: 0x23A64, size: 0x8, addend: 0x0, symName: __ZL9layouts23, symObjAddr: 0x1ADF50, symBinAddr: 0x19FDE0, symSize: 0x0 }
  - { offsetInCU: 0x287C, offset: 0x23A83, size: 0x8, addend: 0x0, symName: __ZL7file116, symObjAddr: 0x40080, symBinAddr: 0x31E80, symSize: 0x0 }
  - { offsetInCU: 0x289B, offset: 0x23AA2, size: 0x8, addend: 0x0, symName: __ZL7file117, symObjAddr: 0x401F0, symBinAddr: 0x31FF0, symSize: 0x0 }
  - { offsetInCU: 0x28CF, offset: 0x23AD6, size: 0x8, addend: 0x0, symName: __ZL9patches23, symObjAddr: 0xA7B0, symBinAddr: 0x1AD160, symSize: 0x0 }
  - { offsetInCU: 0x28EE, offset: 0x23AF5, size: 0x8, addend: 0x0, symName: __ZL10patchBuf40, symObjAddr: 0x40364, symBinAddr: 0x32164, symSize: 0x0 }
  - { offsetInCU: 0x290D, offset: 0x23B14, size: 0x8, addend: 0x0, symName: __ZL11platforms24, symObjAddr: 0x1ADF80, symBinAddr: 0x19FE10, symSize: 0x0 }
  - { offsetInCU: 0x292C, offset: 0x23B33, size: 0x8, addend: 0x0, symName: __ZL7file118, symObjAddr: 0x40370, symBinAddr: 0x32170, symSize: 0x0 }
  - { offsetInCU: 0x2960, offset: 0x23B67, size: 0x8, addend: 0x0, symName: __ZL7file119, symObjAddr: 0x404D0, symBinAddr: 0x322D0, symSize: 0x0 }
  - { offsetInCU: 0x2994, offset: 0x23B9B, size: 0x8, addend: 0x0, symName: __ZL9layouts24, symObjAddr: 0x1ADFB0, symBinAddr: 0x19FE40, symSize: 0x0 }
  - { offsetInCU: 0x29B3, offset: 0x23BBA, size: 0x8, addend: 0x0, symName: __ZL7file120, symObjAddr: 0x40630, symBinAddr: 0x32430, symSize: 0x0 }
  - { offsetInCU: 0x29E6, offset: 0x23BED, size: 0x8, addend: 0x0, symName: __ZL7file121, symObjAddr: 0x40700, symBinAddr: 0x32500, symSize: 0x0 }
  - { offsetInCU: 0x2A1A, offset: 0x23C21, size: 0x8, addend: 0x0, symName: __ZL9patches24, symObjAddr: 0xA930, symBinAddr: 0x1AD2E0, symSize: 0x0 }
  - { offsetInCU: 0x2A39, offset: 0x23C40, size: 0x8, addend: 0x0, symName: __ZL10patchBuf41, symObjAddr: 0x40EEB, symBinAddr: 0x32CEB, symSize: 0x0 }
  - { offsetInCU: 0x2A58, offset: 0x23C5F, size: 0x8, addend: 0x0, symName: __ZL11revisions10, symObjAddr: 0x361F8, symBinAddr: 0x27FF8, symSize: 0x0 }
  - { offsetInCU: 0x2A77, offset: 0x23C7E, size: 0x8, addend: 0x0, symName: __ZL11platforms25, symObjAddr: 0x1ADFE0, symBinAddr: 0x19FE70, symSize: 0x0 }
  - { offsetInCU: 0x2AAA, offset: 0x23CB1, size: 0x8, addend: 0x0, symName: __ZL7file122, symObjAddr: 0x40EF0, symBinAddr: 0x32CF0, symSize: 0x0 }
  - { offsetInCU: 0x2AC9, offset: 0x23CD0, size: 0x8, addend: 0x0, symName: __ZL7file123, symObjAddr: 0x41030, symBinAddr: 0x32E30, symSize: 0x0 }
  - { offsetInCU: 0x2AE8, offset: 0x23CEF, size: 0x8, addend: 0x0, symName: __ZL7file124, symObjAddr: 0x41170, symBinAddr: 0x32F70, symSize: 0x0 }
  - { offsetInCU: 0x2B1C, offset: 0x23D23, size: 0x8, addend: 0x0, symName: __ZL7file125, symObjAddr: 0x42010, symBinAddr: 0x33E10, symSize: 0x0 }
  - { offsetInCU: 0x2B50, offset: 0x23D57, size: 0x8, addend: 0x0, symName: __ZL7file126, symObjAddr: 0x42E90, symBinAddr: 0x34C90, symSize: 0x0 }
  - { offsetInCU: 0x2B84, offset: 0x23D8B, size: 0x8, addend: 0x0, symName: __ZL9layouts25, symObjAddr: 0x1AE060, symBinAddr: 0x19FEF0, symSize: 0x0 }
  - { offsetInCU: 0x2BA3, offset: 0x23DAA, size: 0x8, addend: 0x0, symName: __ZL7file127, symObjAddr: 0x42FD0, symBinAddr: 0x34DD0, symSize: 0x0 }
  - { offsetInCU: 0x2BC2, offset: 0x23DC9, size: 0x8, addend: 0x0, symName: __ZL7file128, symObjAddr: 0x43370, symBinAddr: 0x35170, symSize: 0x0 }
  - { offsetInCU: 0x2BF6, offset: 0x23DFD, size: 0x8, addend: 0x0, symName: __ZL7file129, symObjAddr: 0x43710, symBinAddr: 0x35510, symSize: 0x0 }
  - { offsetInCU: 0x2C2A, offset: 0x23E31, size: 0x8, addend: 0x0, symName: __ZL7file130, symObjAddr: 0x43AD0, symBinAddr: 0x358D0, symSize: 0x0 }
  - { offsetInCU: 0x2C49, offset: 0x23E50, size: 0x8, addend: 0x0, symName: __ZL7file131, symObjAddr: 0x43E90, symBinAddr: 0x35C90, symSize: 0x0 }
  - { offsetInCU: 0x2C68, offset: 0x23E6F, size: 0x8, addend: 0x0, symName: __ZL9patches25, symObjAddr: 0xAAE0, symBinAddr: 0x1AD490, symSize: 0x0 }
  - { offsetInCU: 0x2C87, offset: 0x23E8E, size: 0x8, addend: 0x0, symName: __ZL10patchBuf42, symObjAddr: 0x44227, symBinAddr: 0x36027, symSize: 0x0 }
  - { offsetInCU: 0x2CA6, offset: 0x23EAD, size: 0x8, addend: 0x0, symName: __ZL11platforms26, symObjAddr: 0x1AE0E0, symBinAddr: 0x19FF70, symSize: 0x0 }
  - { offsetInCU: 0x2CC5, offset: 0x23ECC, size: 0x8, addend: 0x0, symName: __ZL7file132, symObjAddr: 0x44230, symBinAddr: 0x36030, symSize: 0x0 }
  - { offsetInCU: 0x2CF9, offset: 0x23F00, size: 0x8, addend: 0x0, symName: __ZL7file133, symObjAddr: 0x44350, symBinAddr: 0x36150, symSize: 0x0 }
  - { offsetInCU: 0x2D2D, offset: 0x23F34, size: 0x8, addend: 0x0, symName: __ZL9layouts26, symObjAddr: 0x1AE110, symBinAddr: 0x19FFA0, symSize: 0x0 }
  - { offsetInCU: 0x2D4C, offset: 0x23F53, size: 0x8, addend: 0x0, symName: __ZL7file134, symObjAddr: 0x44470, symBinAddr: 0x36270, symSize: 0x0 }
  - { offsetInCU: 0x2D6B, offset: 0x23F72, size: 0x8, addend: 0x0, symName: __ZL7file135, symObjAddr: 0x447E0, symBinAddr: 0x365E0, symSize: 0x0 }
  - { offsetInCU: 0x2D8A, offset: 0x23F91, size: 0x8, addend: 0x0, symName: __ZL9patches26, symObjAddr: 0xAC90, symBinAddr: 0x1AD640, symSize: 0x0 }
  - { offsetInCU: 0x2DA9, offset: 0x23FB0, size: 0x8, addend: 0x0, symName: __ZL10patchBuf43, symObjAddr: 0x44B49, symBinAddr: 0x36949, symSize: 0x0 }
  - { offsetInCU: 0x2DC8, offset: 0x23FCF, size: 0x8, addend: 0x0, symName: __ZL11platforms27, symObjAddr: 0x1AE140, symBinAddr: 0x19FFD0, symSize: 0x0 }
  - { offsetInCU: 0x2DE7, offset: 0x23FEE, size: 0x8, addend: 0x0, symName: __ZL7file136, symObjAddr: 0x44B50, symBinAddr: 0x36950, symSize: 0x0 }
  - { offsetInCU: 0x2E1B, offset: 0x24022, size: 0x8, addend: 0x0, symName: __ZL9layouts27, symObjAddr: 0x1AE160, symBinAddr: 0x19FFF0, symSize: 0x0 }
  - { offsetInCU: 0x2E3A, offset: 0x24041, size: 0x8, addend: 0x0, symName: __ZL7file137, symObjAddr: 0x44CF0, symBinAddr: 0x36AF0, symSize: 0x0 }
  - { offsetInCU: 0x2E6E, offset: 0x24075, size: 0x8, addend: 0x0, symName: __ZL9patches27, symObjAddr: 0xADE0, symBinAddr: 0x1AD790, symSize: 0x0 }
  - { offsetInCU: 0x2E8D, offset: 0x24094, size: 0x8, addend: 0x0, symName: __ZL10patchBuf44, symObjAddr: 0x451E8, symBinAddr: 0x36FE8, symSize: 0x0 }
  - { offsetInCU: 0x2EAC, offset: 0x240B3, size: 0x8, addend: 0x0, symName: __ZL11platforms28, symObjAddr: 0x1AE180, symBinAddr: 0x1A0010, symSize: 0x0 }
  - { offsetInCU: 0x2ECB, offset: 0x240D2, size: 0x8, addend: 0x0, symName: __ZL7file138, symObjAddr: 0x451F0, symBinAddr: 0x36FF0, symSize: 0x0 }
  - { offsetInCU: 0x2EEA, offset: 0x240F1, size: 0x8, addend: 0x0, symName: __ZL7file139, symObjAddr: 0x45340, symBinAddr: 0x37140, symSize: 0x0 }
  - { offsetInCU: 0x2F1E, offset: 0x24125, size: 0x8, addend: 0x0, symName: __ZL9layouts28, symObjAddr: 0x1AE1B0, symBinAddr: 0x1A0040, symSize: 0x0 }
  - { offsetInCU: 0x2F3D, offset: 0x24144, size: 0x8, addend: 0x0, symName: __ZL7file140, symObjAddr: 0x45490, symBinAddr: 0x37290, symSize: 0x0 }
  - { offsetInCU: 0x2F5C, offset: 0x24163, size: 0x8, addend: 0x0, symName: __ZL7file141, symObjAddr: 0x45600, symBinAddr: 0x37400, symSize: 0x0 }
  - { offsetInCU: 0x2F7B, offset: 0x24182, size: 0x8, addend: 0x0, symName: __ZL9patches28, symObjAddr: 0xAF60, symBinAddr: 0x1AD910, symSize: 0x0 }
  - { offsetInCU: 0x2F9A, offset: 0x241A1, size: 0x8, addend: 0x0, symName: __ZL10patchBuf45, symObjAddr: 0x45770, symBinAddr: 0x37570, symSize: 0x0 }
  - { offsetInCU: 0x2FB9, offset: 0x241C0, size: 0x8, addend: 0x0, symName: __ZL10patchBuf46, symObjAddr: 0x45774, symBinAddr: 0x37574, symSize: 0x0 }
  - { offsetInCU: 0x2FEC, offset: 0x241F3, size: 0x8, addend: 0x0, symName: __ZL10patchBuf47, symObjAddr: 0x4577A, symBinAddr: 0x3757A, symSize: 0x0 }
  - { offsetInCU: 0x300B, offset: 0x24212, size: 0x8, addend: 0x0, symName: __ZL10patchBuf48, symObjAddr: 0x45780, symBinAddr: 0x37580, symSize: 0x0 }
  - { offsetInCU: 0x302A, offset: 0x24231, size: 0x8, addend: 0x0, symName: __ZL10patchBuf49, symObjAddr: 0x45786, symBinAddr: 0x37586, symSize: 0x0 }
  - { offsetInCU: 0x3049, offset: 0x24250, size: 0x8, addend: 0x0, symName: __ZL11platforms29, symObjAddr: 0x1AE1E0, symBinAddr: 0x1A0070, symSize: 0x0 }
  - { offsetInCU: 0x3068, offset: 0x2426F, size: 0x8, addend: 0x0, symName: __ZL7file142, symObjAddr: 0x45790, symBinAddr: 0x37590, symSize: 0x0 }
  - { offsetInCU: 0x3087, offset: 0x2428E, size: 0x8, addend: 0x0, symName: __ZL7file143, symObjAddr: 0x458E0, symBinAddr: 0x376E0, symSize: 0x0 }
  - { offsetInCU: 0x30A6, offset: 0x242AD, size: 0x8, addend: 0x0, symName: __ZL7file144, symObjAddr: 0x45A20, symBinAddr: 0x37820, symSize: 0x0 }
  - { offsetInCU: 0x30C5, offset: 0x242CC, size: 0x8, addend: 0x0, symName: __ZL7file145, symObjAddr: 0x45B70, symBinAddr: 0x37970, symSize: 0x0 }
  - { offsetInCU: 0x30E4, offset: 0x242EB, size: 0x8, addend: 0x0, symName: __ZL7file146, symObjAddr: 0x45CB0, symBinAddr: 0x37AB0, symSize: 0x0 }
  - { offsetInCU: 0x3118, offset: 0x2431F, size: 0x8, addend: 0x0, symName: __ZL9layouts29, symObjAddr: 0x1AE260, symBinAddr: 0x1A00F0, symSize: 0x0 }
  - { offsetInCU: 0x3137, offset: 0x2433E, size: 0x8, addend: 0x0, symName: __ZL7file147, symObjAddr: 0x45E10, symBinAddr: 0x37C10, symSize: 0x0 }
  - { offsetInCU: 0x316B, offset: 0x24372, size: 0x8, addend: 0x0, symName: __ZL7file148, symObjAddr: 0x45F90, symBinAddr: 0x37D90, symSize: 0x0 }
  - { offsetInCU: 0x318A, offset: 0x24391, size: 0x8, addend: 0x0, symName: __ZL7file149, symObjAddr: 0x460E0, symBinAddr: 0x37EE0, symSize: 0x0 }
  - { offsetInCU: 0x31A9, offset: 0x243B0, size: 0x8, addend: 0x0, symName: __ZL7file150, symObjAddr: 0x46260, symBinAddr: 0x38060, symSize: 0x0 }
  - { offsetInCU: 0x31C8, offset: 0x243CF, size: 0x8, addend: 0x0, symName: __ZL7file151, symObjAddr: 0x463B0, symBinAddr: 0x381B0, symSize: 0x0 }
  - { offsetInCU: 0x31E7, offset: 0x243EE, size: 0x8, addend: 0x0, symName: __ZL9patches29, symObjAddr: 0xB170, symBinAddr: 0x1ADB20, symSize: 0x0 }
  - { offsetInCU: 0x3206, offset: 0x2440D, size: 0x8, addend: 0x0, symName: __ZL10patchBuf50, symObjAddr: 0x46522, symBinAddr: 0x38322, symSize: 0x0 }
  - { offsetInCU: 0x3225, offset: 0x2442C, size: 0x8, addend: 0x0, symName: __ZL11platforms30, symObjAddr: 0x1AE2E0, symBinAddr: 0x1A0170, symSize: 0x0 }
  - { offsetInCU: 0x3244, offset: 0x2444B, size: 0x8, addend: 0x0, symName: __ZL7file152, symObjAddr: 0x46530, symBinAddr: 0x38330, symSize: 0x0 }
  - { offsetInCU: 0x3263, offset: 0x2446A, size: 0x8, addend: 0x0, symName: __ZL7file153, symObjAddr: 0x46690, symBinAddr: 0x38490, symSize: 0x0 }
  - { offsetInCU: 0x3282, offset: 0x24489, size: 0x8, addend: 0x0, symName: __ZL9layouts30, symObjAddr: 0x1AE310, symBinAddr: 0x1A01A0, symSize: 0x0 }
  - { offsetInCU: 0x32A1, offset: 0x244A8, size: 0x8, addend: 0x0, symName: __ZL7file154, symObjAddr: 0x467F0, symBinAddr: 0x385F0, symSize: 0x0 }
  - { offsetInCU: 0x32D5, offset: 0x244DC, size: 0x8, addend: 0x0, symName: __ZL7file155, symObjAddr: 0x468F0, symBinAddr: 0x386F0, symSize: 0x0 }
  - { offsetInCU: 0x32F4, offset: 0x244FB, size: 0x8, addend: 0x0, symName: __ZL9patches30, symObjAddr: 0xB2C0, symBinAddr: 0x1ADC70, symSize: 0x0 }
  - { offsetInCU: 0x3313, offset: 0x2451A, size: 0x8, addend: 0x0, symName: __ZL10patchBuf51, symObjAddr: 0x469F0, symBinAddr: 0x387F0, symSize: 0x0 }
  - { offsetInCU: 0x3332, offset: 0x24539, size: 0x8, addend: 0x0, symName: __ZL11revisions11, symObjAddr: 0x36204, symBinAddr: 0x28004, symSize: 0x0 }
  - { offsetInCU: 0x3365, offset: 0x2456C, size: 0x8, addend: 0x0, symName: __ZL11platforms31, symObjAddr: 0x1AE340, symBinAddr: 0x1A01D0, symSize: 0x0 }
  - { offsetInCU: 0x3384, offset: 0x2458B, size: 0x8, addend: 0x0, symName: __ZL7file156, symObjAddr: 0x46A00, symBinAddr: 0x38800, symSize: 0x0 }
  - { offsetInCU: 0x33A3, offset: 0x245AA, size: 0x8, addend: 0x0, symName: __ZL7file157, symObjAddr: 0x46B40, symBinAddr: 0x38940, symSize: 0x0 }
  - { offsetInCU: 0x33D7, offset: 0x245DE, size: 0x8, addend: 0x0, symName: __ZL7file158, symObjAddr: 0x47660, symBinAddr: 0x39460, symSize: 0x0 }
  - { offsetInCU: 0x340B, offset: 0x24612, size: 0x8, addend: 0x0, symName: __ZL9layouts31, symObjAddr: 0x1AE390, symBinAddr: 0x1A0220, symSize: 0x0 }
  - { offsetInCU: 0x342A, offset: 0x24631, size: 0x8, addend: 0x0, symName: __ZL7file159, symObjAddr: 0x48180, symBinAddr: 0x39F80, symSize: 0x0 }
  - { offsetInCU: 0x345D, offset: 0x24664, size: 0x8, addend: 0x0, symName: __ZL7file160, symObjAddr: 0x48270, symBinAddr: 0x3A070, symSize: 0x0 }
  - { offsetInCU: 0x3491, offset: 0x24698, size: 0x8, addend: 0x0, symName: __ZL7file161, symObjAddr: 0x49220, symBinAddr: 0x3B020, symSize: 0x0 }
  - { offsetInCU: 0x34C5, offset: 0x246CC, size: 0x8, addend: 0x0, symName: __ZL9patches31, symObjAddr: 0xB440, symBinAddr: 0x1ADDF0, symSize: 0x0 }
  - { offsetInCU: 0x34F8, offset: 0x246FF, size: 0x8, addend: 0x0, symName: __ZL10patchBuf52, symObjAddr: 0x4A1CB, symBinAddr: 0x3BFCB, symSize: 0x0 }
  - { offsetInCU: 0x3517, offset: 0x2471E, size: 0x8, addend: 0x0, symName: __ZL10patchBuf53, symObjAddr: 0x4A1CF, symBinAddr: 0x3BFCF, symSize: 0x0 }
  - { offsetInCU: 0x3536, offset: 0x2473D, size: 0x8, addend: 0x0, symName: __ZL10patchBuf54, symObjAddr: 0x4A1D5, symBinAddr: 0x3BFD5, symSize: 0x0 }
  - { offsetInCU: 0x3555, offset: 0x2475C, size: 0x8, addend: 0x0, symName: __ZL10patchBuf55, symObjAddr: 0x4A1DB, symBinAddr: 0x3BFDB, symSize: 0x0 }
  - { offsetInCU: 0x3588, offset: 0x2478F, size: 0x8, addend: 0x0, symName: __ZL10patchBuf56, symObjAddr: 0x4A1E4, symBinAddr: 0x3BFE4, symSize: 0x0 }
  - { offsetInCU: 0x35A7, offset: 0x247AE, size: 0x8, addend: 0x0, symName: __ZL11codecModIDT, symObjAddr: 0x5800, symBinAddr: 0x1A81B0, symSize: 0x0 }
  - { offsetInCU: 0x35DA, offset: 0x247E1, size: 0x8, addend: 0x0, symName: __ZL11platforms32, symObjAddr: 0x1AE3E0, symBinAddr: 0x1A0270, symSize: 0x0 }
  - { offsetInCU: 0x35F9, offset: 0x24800, size: 0x8, addend: 0x0, symName: __ZL7file162, symObjAddr: 0x4A200, symBinAddr: 0x3C000, symSize: 0x0 }
  - { offsetInCU: 0x3618, offset: 0x2481F, size: 0x8, addend: 0x0, symName: __ZL9layouts32, symObjAddr: 0x1AE410, symBinAddr: 0x1A02A0, symSize: 0x0 }
  - { offsetInCU: 0x3637, offset: 0x2483E, size: 0x8, addend: 0x0, symName: __ZL7file163, symObjAddr: 0x4A350, symBinAddr: 0x3C150, symSize: 0x0 }
  - { offsetInCU: 0x366A, offset: 0x24871, size: 0x8, addend: 0x0, symName: __ZL9patches32, symObjAddr: 0xB680, symBinAddr: 0x1AE030, symSize: 0x0 }
  - { offsetInCU: 0x3689, offset: 0x24890, size: 0x8, addend: 0x0, symName: __ZL10patchBuf57, symObjAddr: 0x4A445, symBinAddr: 0x3C245, symSize: 0x0 }
  - { offsetInCU: 0x36A8, offset: 0x248AF, size: 0x8, addend: 0x0, symName: __ZL11revisions12, symObjAddr: 0x4A1F0, symBinAddr: 0x3BFF0, symSize: 0x0 }
  - { offsetInCU: 0x36C7, offset: 0x248CE, size: 0x8, addend: 0x0, symName: __ZL11platforms33, symObjAddr: 0x1AE440, symBinAddr: 0x1A02D0, symSize: 0x0 }
  - { offsetInCU: 0x36E6, offset: 0x248ED, size: 0x8, addend: 0x0, symName: __ZL7file164, symObjAddr: 0x4A450, symBinAddr: 0x3C250, symSize: 0x0 }
  - { offsetInCU: 0x371A, offset: 0x24921, size: 0x8, addend: 0x0, symName: __ZL7file165, symObjAddr: 0x4A6B0, symBinAddr: 0x3C4B0, symSize: 0x0 }
  - { offsetInCU: 0x374E, offset: 0x24955, size: 0x8, addend: 0x0, symName: __ZL9layouts33, symObjAddr: 0x1AE470, symBinAddr: 0x1A0300, symSize: 0x0 }
  - { offsetInCU: 0x376D, offset: 0x24974, size: 0x8, addend: 0x0, symName: __ZL7file166, symObjAddr: 0x4A830, symBinAddr: 0x3C630, symSize: 0x0 }
  - { offsetInCU: 0x37A1, offset: 0x249A8, size: 0x8, addend: 0x0, symName: __ZL7file167, symObjAddr: 0x4AAA0, symBinAddr: 0x3C8A0, symSize: 0x0 }
  - { offsetInCU: 0x37D5, offset: 0x249DC, size: 0x8, addend: 0x0, symName: __ZL9patches33, symObjAddr: 0xB7D0, symBinAddr: 0x1AE180, symSize: 0x0 }
  - { offsetInCU: 0x37F4, offset: 0x249FB, size: 0x8, addend: 0x0, symName: __ZL10patchBuf58, symObjAddr: 0x4C0BF, symBinAddr: 0x3DEBF, symSize: 0x0 }
  - { offsetInCU: 0x3813, offset: 0x24A1A, size: 0x8, addend: 0x0, symName: __ZL11platforms34, symObjAddr: 0x1AE4A0, symBinAddr: 0x1A0330, symSize: 0x0 }
  - { offsetInCU: 0x3832, offset: 0x24A39, size: 0x8, addend: 0x0, symName: __ZL7file168, symObjAddr: 0x4C0D0, symBinAddr: 0x3DED0, symSize: 0x0 }
  - { offsetInCU: 0x3866, offset: 0x24A6D, size: 0x8, addend: 0x0, symName: __ZL7file169, symObjAddr: 0x4C210, symBinAddr: 0x3E010, symSize: 0x0 }
  - { offsetInCU: 0x3885, offset: 0x24A8C, size: 0x8, addend: 0x0, symName: __ZL9layouts34, symObjAddr: 0x1AE4D0, symBinAddr: 0x1A0360, symSize: 0x0 }
  - { offsetInCU: 0x38A4, offset: 0x24AAB, size: 0x8, addend: 0x0, symName: __ZL7file170, symObjAddr: 0x4C350, symBinAddr: 0x3E150, symSize: 0x0 }
  - { offsetInCU: 0x38D8, offset: 0x24ADF, size: 0x8, addend: 0x0, symName: __ZL7file171, symObjAddr: 0x4D890, symBinAddr: 0x3F690, symSize: 0x0 }
  - { offsetInCU: 0x38F7, offset: 0x24AFE, size: 0x8, addend: 0x0, symName: __ZL9patches34, symObjAddr: 0xB920, symBinAddr: 0x1AE2D0, symSize: 0x0 }
  - { offsetInCU: 0x3916, offset: 0x24B1D, size: 0x8, addend: 0x0, symName: __ZL10patchBuf59, symObjAddr: 0x4DAFC, symBinAddr: 0x3F8FC, symSize: 0x0 }
  - { offsetInCU: 0x3935, offset: 0x24B3C, size: 0x8, addend: 0x0, symName: __ZL11revisions13, symObjAddr: 0x4A1F4, symBinAddr: 0x3BFF4, symSize: 0x0 }
  - { offsetInCU: 0x3954, offset: 0x24B5B, size: 0x8, addend: 0x0, symName: __ZL11platforms35, symObjAddr: 0x1AE500, symBinAddr: 0x1A0390, symSize: 0x0 }
  - { offsetInCU: 0x3973, offset: 0x24B7A, size: 0x8, addend: 0x0, symName: __ZL7file172, symObjAddr: 0x4DB00, symBinAddr: 0x3F900, symSize: 0x0 }
  - { offsetInCU: 0x3992, offset: 0x24B99, size: 0x8, addend: 0x0, symName: __ZL7file173, symObjAddr: 0x4DC40, symBinAddr: 0x3FA40, symSize: 0x0 }
  - { offsetInCU: 0x39C6, offset: 0x24BCD, size: 0x8, addend: 0x0, symName: __ZL7file174, symObjAddr: 0x4DD90, symBinAddr: 0x3FB90, symSize: 0x0 }
  - { offsetInCU: 0x39E5, offset: 0x24BEC, size: 0x8, addend: 0x0, symName: __ZL7file175, symObjAddr: 0x4DED0, symBinAddr: 0x3FCD0, symSize: 0x0 }
  - { offsetInCU: 0x3A19, offset: 0x24C20, size: 0x8, addend: 0x0, symName: __ZL7file176, symObjAddr: 0x4E010, symBinAddr: 0x3FE10, symSize: 0x0 }
  - { offsetInCU: 0x3A4D, offset: 0x24C54, size: 0x8, addend: 0x0, symName: __ZL9layouts35, symObjAddr: 0x1AE580, symBinAddr: 0x1A0410, symSize: 0x0 }
  - { offsetInCU: 0x3A6C, offset: 0x24C73, size: 0x8, addend: 0x0, symName: __ZL7file177, symObjAddr: 0x4E160, symBinAddr: 0x3FF60, symSize: 0x0 }
  - { offsetInCU: 0x3AA0, offset: 0x24CA7, size: 0x8, addend: 0x0, symName: __ZL7file178, symObjAddr: 0x4F680, symBinAddr: 0x41480, symSize: 0x0 }
  - { offsetInCU: 0x3AD4, offset: 0x24CDB, size: 0x8, addend: 0x0, symName: __ZL7file179, symObjAddr: 0x4F8D0, symBinAddr: 0x416D0, symSize: 0x0 }
  - { offsetInCU: 0x3B08, offset: 0x24D0F, size: 0x8, addend: 0x0, symName: __ZL7file180, symObjAddr: 0x4FB20, symBinAddr: 0x41920, symSize: 0x0 }
  - { offsetInCU: 0x3B3C, offset: 0x24D43, size: 0x8, addend: 0x0, symName: __ZL7file181, symObjAddr: 0x4FD60, symBinAddr: 0x41B60, symSize: 0x0 }
  - { offsetInCU: 0x3B70, offset: 0x24D77, size: 0x8, addend: 0x0, symName: __ZL9patches35, symObjAddr: 0xBAA0, symBinAddr: 0x1AE450, symSize: 0x0 }
  - { offsetInCU: 0x3B8F, offset: 0x24D96, size: 0x8, addend: 0x0, symName: __ZL10patchBuf60, symObjAddr: 0x4FFAE, symBinAddr: 0x41DAE, symSize: 0x0 }
  - { offsetInCU: 0x3BAE, offset: 0x24DB5, size: 0x8, addend: 0x0, symName: __ZL11platforms36, symObjAddr: 0x1AE600, symBinAddr: 0x1A0490, symSize: 0x0 }
  - { offsetInCU: 0x3BE1, offset: 0x24DE8, size: 0x8, addend: 0x0, symName: __ZL7file182, symObjAddr: 0x4FFC0, symBinAddr: 0x41DC0, symSize: 0x0 }
  - { offsetInCU: 0x3C15, offset: 0x24E1C, size: 0x8, addend: 0x0, symName: __ZL7file183, symObjAddr: 0x500F0, symBinAddr: 0x41EF0, symSize: 0x0 }
  - { offsetInCU: 0x3C34, offset: 0x24E3B, size: 0x8, addend: 0x0, symName: __ZL7file184, symObjAddr: 0x50240, symBinAddr: 0x42040, symSize: 0x0 }
  - { offsetInCU: 0x3C68, offset: 0x24E6F, size: 0x8, addend: 0x0, symName: __ZL7file185, symObjAddr: 0x50E80, symBinAddr: 0x42C80, symSize: 0x0 }
  - { offsetInCU: 0x3C9C, offset: 0x24EA3, size: 0x8, addend: 0x0, symName: __ZL7file186, symObjAddr: 0x51960, symBinAddr: 0x43760, symSize: 0x0 }
  - { offsetInCU: 0x3CD0, offset: 0x24ED7, size: 0x8, addend: 0x0, symName: __ZL7file187, symObjAddr: 0x52440, symBinAddr: 0x44240, symSize: 0x0 }
  - { offsetInCU: 0x3CEF, offset: 0x24EF6, size: 0x8, addend: 0x0, symName: __ZL7file188, symObjAddr: 0x52590, symBinAddr: 0x44390, symSize: 0x0 }
  - { offsetInCU: 0x3D0E, offset: 0x24F15, size: 0x8, addend: 0x0, symName: __ZL9layouts36, symObjAddr: 0x1AE6B0, symBinAddr: 0x1A0540, symSize: 0x0 }
  - { offsetInCU: 0x3D2D, offset: 0x24F34, size: 0x8, addend: 0x0, symName: __ZL7file189, symObjAddr: 0x526E0, symBinAddr: 0x444E0, symSize: 0x0 }
  - { offsetInCU: 0x3D60, offset: 0x24F67, size: 0x8, addend: 0x0, symName: __ZL7file190, symObjAddr: 0x527E0, symBinAddr: 0x445E0, symSize: 0x0 }
  - { offsetInCU: 0x3D93, offset: 0x24F9A, size: 0x8, addend: 0x0, symName: __ZL7file191, symObjAddr: 0x528B0, symBinAddr: 0x446B0, symSize: 0x0 }
  - { offsetInCU: 0x3DC7, offset: 0x24FCE, size: 0x8, addend: 0x0, symName: __ZL7file192, symObjAddr: 0x52B20, symBinAddr: 0x44920, symSize: 0x0 }
  - { offsetInCU: 0x3DFB, offset: 0x25002, size: 0x8, addend: 0x0, symName: __ZL7file193, symObjAddr: 0x52C80, symBinAddr: 0x44A80, symSize: 0x0 }
  - { offsetInCU: 0x3E2F, offset: 0x25036, size: 0x8, addend: 0x0, symName: __ZL7file194, symObjAddr: 0x52DE0, symBinAddr: 0x44BE0, symSize: 0x0 }
  - { offsetInCU: 0x3E63, offset: 0x2506A, size: 0x8, addend: 0x0, symName: __ZL7file195, symObjAddr: 0x53050, symBinAddr: 0x44E50, symSize: 0x0 }
  - { offsetInCU: 0x3E97, offset: 0x2509E, size: 0x8, addend: 0x0, symName: __ZL9patches36, symObjAddr: 0xBC20, symBinAddr: 0x1AE5D0, symSize: 0x0 }
  - { offsetInCU: 0x3EB6, offset: 0x250BD, size: 0x8, addend: 0x0, symName: __ZL10patchBuf61, symObjAddr: 0x532E2, symBinAddr: 0x450E2, symSize: 0x0 }
  - { offsetInCU: 0x3ED5, offset: 0x250DC, size: 0x8, addend: 0x0, symName: __ZL11platforms37, symObjAddr: 0x1AE760, symBinAddr: 0x1A05F0, symSize: 0x0 }
  - { offsetInCU: 0x3EF4, offset: 0x250FB, size: 0x8, addend: 0x0, symName: __ZL7file196, symObjAddr: 0x532F0, symBinAddr: 0x450F0, symSize: 0x0 }
  - { offsetInCU: 0x3F13, offset: 0x2511A, size: 0x8, addend: 0x0, symName: __ZL9layouts37, symObjAddr: 0x1AE780, symBinAddr: 0x1A0610, symSize: 0x0 }
  - { offsetInCU: 0x3F32, offset: 0x25139, size: 0x8, addend: 0x0, symName: __ZL7file197, symObjAddr: 0x53440, symBinAddr: 0x45240, symSize: 0x0 }
  - { offsetInCU: 0x3F51, offset: 0x25158, size: 0x8, addend: 0x0, symName: __ZL9patches37, symObjAddr: 0xBDA0, symBinAddr: 0x1AE750, symSize: 0x0 }
  - { offsetInCU: 0x3F70, offset: 0x25177, size: 0x8, addend: 0x0, symName: __ZL10patchBuf62, symObjAddr: 0x53535, symBinAddr: 0x45335, symSize: 0x0 }
  - { offsetInCU: 0x3F8F, offset: 0x25196, size: 0x8, addend: 0x0, symName: __ZL11platforms38, symObjAddr: 0x1AE7A0, symBinAddr: 0x1A0630, symSize: 0x0 }
  - { offsetInCU: 0x3FAE, offset: 0x251B5, size: 0x8, addend: 0x0, symName: __ZL7file198, symObjAddr: 0x53540, symBinAddr: 0x45340, symSize: 0x0 }
  - { offsetInCU: 0x3FCD, offset: 0x251D4, size: 0x8, addend: 0x0, symName: __ZL9layouts38, symObjAddr: 0x1AE7C0, symBinAddr: 0x1A0650, symSize: 0x0 }
  - { offsetInCU: 0x3FEC, offset: 0x251F3, size: 0x8, addend: 0x0, symName: __ZL7file199, symObjAddr: 0x536B0, symBinAddr: 0x454B0, symSize: 0x0 }
  - { offsetInCU: 0x4020, offset: 0x25227, size: 0x8, addend: 0x0, symName: __ZL9patches38, symObjAddr: 0xBEF0, symBinAddr: 0x1AE8A0, symSize: 0x0 }
  - { offsetInCU: 0x403F, offset: 0x25246, size: 0x8, addend: 0x0, symName: __ZL10patchBuf63, symObjAddr: 0x537BB, symBinAddr: 0x455BB, symSize: 0x0 }
  - { offsetInCU: 0x405E, offset: 0x25265, size: 0x8, addend: 0x0, symName: __ZL11platforms39, symObjAddr: 0x1AE7E0, symBinAddr: 0x1A0670, symSize: 0x0 }
  - { offsetInCU: 0x407D, offset: 0x25284, size: 0x8, addend: 0x0, symName: __ZL7file200, symObjAddr: 0x537C0, symBinAddr: 0x455C0, symSize: 0x0 }
  - { offsetInCU: 0x409C, offset: 0x252A3, size: 0x8, addend: 0x0, symName: __ZL9layouts39, symObjAddr: 0x1AE800, symBinAddr: 0x1A0690, symSize: 0x0 }
  - { offsetInCU: 0x40BB, offset: 0x252C2, size: 0x8, addend: 0x0, symName: __ZL7file201, symObjAddr: 0x53A20, symBinAddr: 0x45820, symSize: 0x0 }
  - { offsetInCU: 0x40DA, offset: 0x252E1, size: 0x8, addend: 0x0, symName: __ZL9patches39, symObjAddr: 0xC070, symBinAddr: 0x1AEA20, symSize: 0x0 }
  - { offsetInCU: 0x40F9, offset: 0x25300, size: 0x8, addend: 0x0, symName: __ZL10patchBuf64, symObjAddr: 0x53C8C, symBinAddr: 0x45A8C, symSize: 0x0 }
  - { offsetInCU: 0x4118, offset: 0x2531F, size: 0x8, addend: 0x0, symName: __ZL11platforms40, symObjAddr: 0x1AE820, symBinAddr: 0x1A06B0, symSize: 0x0 }
  - { offsetInCU: 0x4137, offset: 0x2533E, size: 0x8, addend: 0x0, symName: __ZL7file202, symObjAddr: 0x53C90, symBinAddr: 0x45A90, symSize: 0x0 }
  - { offsetInCU: 0x416B, offset: 0x25372, size: 0x8, addend: 0x0, symName: __ZL7file203, symObjAddr: 0x53DF0, symBinAddr: 0x45BF0, symSize: 0x0 }
  - { offsetInCU: 0x419F, offset: 0x253A6, size: 0x8, addend: 0x0, symName: __ZL9layouts40, symObjAddr: 0x1AE850, symBinAddr: 0x1A06E0, symSize: 0x0 }
  - { offsetInCU: 0x41BE, offset: 0x253C5, size: 0x8, addend: 0x0, symName: __ZL7file204, symObjAddr: 0x53F30, symBinAddr: 0x45D30, symSize: 0x0 }
  - { offsetInCU: 0x41F2, offset: 0x253F9, size: 0x8, addend: 0x0, symName: __ZL7file205, symObjAddr: 0x55450, symBinAddr: 0x47250, symSize: 0x0 }
  - { offsetInCU: 0x4226, offset: 0x2542D, size: 0x8, addend: 0x0, symName: __ZL9patches40, symObjAddr: 0xC1F0, symBinAddr: 0x1AEBA0, symSize: 0x0 }
  - { offsetInCU: 0x4245, offset: 0x2544C, size: 0x8, addend: 0x0, symName: __ZL10patchBuf65, symObjAddr: 0x56977, symBinAddr: 0x48777, symSize: 0x0 }
  - { offsetInCU: 0x4264, offset: 0x2546B, size: 0x8, addend: 0x0, symName: __ZL11platforms41, symObjAddr: 0x1AE880, symBinAddr: 0x1A0710, symSize: 0x0 }
  - { offsetInCU: 0x4283, offset: 0x2548A, size: 0x8, addend: 0x0, symName: __ZL7file206, symObjAddr: 0x56980, symBinAddr: 0x48780, symSize: 0x0 }
  - { offsetInCU: 0x42B7, offset: 0x254BE, size: 0x8, addend: 0x0, symName: __ZL7file207, symObjAddr: 0x57A70, symBinAddr: 0x49870, symSize: 0x0 }
  - { offsetInCU: 0x42D6, offset: 0x254DD, size: 0x8, addend: 0x0, symName: __ZL7file208, symObjAddr: 0x57CD0, symBinAddr: 0x49AD0, symSize: 0x0 }
  - { offsetInCU: 0x42F5, offset: 0x254FC, size: 0x8, addend: 0x0, symName: __ZL9layouts41, symObjAddr: 0x1AE8D0, symBinAddr: 0x1A0760, symSize: 0x0 }
  - { offsetInCU: 0x4314, offset: 0x2551B, size: 0x8, addend: 0x0, symName: __ZL7file209, symObjAddr: 0x57E10, symBinAddr: 0x49C10, symSize: 0x0 }
  - { offsetInCU: 0x4348, offset: 0x2554F, size: 0x8, addend: 0x0, symName: __ZL7file210, symObjAddr: 0x58060, symBinAddr: 0x49E60, symSize: 0x0 }
  - { offsetInCU: 0x4367, offset: 0x2556E, size: 0x8, addend: 0x0, symName: __ZL7file211, symObjAddr: 0x582D0, symBinAddr: 0x4A0D0, symSize: 0x0 }
  - { offsetInCU: 0x439B, offset: 0x255A2, size: 0x8, addend: 0x0, symName: __ZL9patches41, symObjAddr: 0xC310, symBinAddr: 0x1AECC0, symSize: 0x0 }
  - { offsetInCU: 0x43BA, offset: 0x255C1, size: 0x8, addend: 0x0, symName: __ZL10patchBuf66, symObjAddr: 0x58419, symBinAddr: 0x4A219, symSize: 0x0 }
  - { offsetInCU: 0x43D9, offset: 0x255E0, size: 0x8, addend: 0x0, symName: __ZL11platforms42, symObjAddr: 0x1AE920, symBinAddr: 0x1A07B0, symSize: 0x0 }
  - { offsetInCU: 0x43F8, offset: 0x255FF, size: 0x8, addend: 0x0, symName: __ZL7file212, symObjAddr: 0x58420, symBinAddr: 0x4A220, symSize: 0x0 }
  - { offsetInCU: 0x442C, offset: 0x25633, size: 0x8, addend: 0x0, symName: __ZL9layouts42, symObjAddr: 0x1AE940, symBinAddr: 0x1A07D0, symSize: 0x0 }
  - { offsetInCU: 0x444B, offset: 0x25652, size: 0x8, addend: 0x0, symName: __ZL7file213, symObjAddr: 0x58560, symBinAddr: 0x4A360, symSize: 0x0 }
  - { offsetInCU: 0x446A, offset: 0x25671, size: 0x8, addend: 0x0, symName: __ZL9patches42, symObjAddr: 0xC460, symBinAddr: 0x1AEE10, symSize: 0x0 }
  - { offsetInCU: 0x4489, offset: 0x25690, size: 0x8, addend: 0x0, symName: __ZL10patchBuf67, symObjAddr: 0x58655, symBinAddr: 0x4A455, symSize: 0x0 }
  - { offsetInCU: 0x44A8, offset: 0x256AF, size: 0x8, addend: 0x0, symName: __ZL11platforms43, symObjAddr: 0x1AE960, symBinAddr: 0x1A07F0, symSize: 0x0 }
  - { offsetInCU: 0x44C7, offset: 0x256CE, size: 0x8, addend: 0x0, symName: __ZL7file214, symObjAddr: 0x58660, symBinAddr: 0x4A460, symSize: 0x0 }
  - { offsetInCU: 0x44E6, offset: 0x256ED, size: 0x8, addend: 0x0, symName: __ZL7file215, symObjAddr: 0x587B0, symBinAddr: 0x4A5B0, symSize: 0x0 }
  - { offsetInCU: 0x4505, offset: 0x2570C, size: 0x8, addend: 0x0, symName: __ZL9layouts43, symObjAddr: 0x1AE990, symBinAddr: 0x1A0820, symSize: 0x0 }
  - { offsetInCU: 0x4524, offset: 0x2572B, size: 0x8, addend: 0x0, symName: __ZL7file216, symObjAddr: 0x58900, symBinAddr: 0x4A700, symSize: 0x0 }
  - { offsetInCU: 0x4543, offset: 0x2574A, size: 0x8, addend: 0x0, symName: __ZL7file217, symObjAddr: 0x58A00, symBinAddr: 0x4A800, symSize: 0x0 }
  - { offsetInCU: 0x4576, offset: 0x2577D, size: 0x8, addend: 0x0, symName: __ZL9patches43, symObjAddr: 0xC5B0, symBinAddr: 0x1AEF60, symSize: 0x0 }
  - { offsetInCU: 0x4595, offset: 0x2579C, size: 0x8, addend: 0x0, symName: __ZL10patchBuf68, symObjAddr: 0x58AF4, symBinAddr: 0x4A8F4, symSize: 0x0 }
  - { offsetInCU: 0x45B4, offset: 0x257BB, size: 0x8, addend: 0x0, symName: __ZL11platforms44, symObjAddr: 0x1AE9C0, symBinAddr: 0x1A0850, symSize: 0x0 }
  - { offsetInCU: 0x45D3, offset: 0x257DA, size: 0x8, addend: 0x0, symName: __ZL7file218, symObjAddr: 0x58B00, symBinAddr: 0x4A900, symSize: 0x0 }
  - { offsetInCU: 0x4607, offset: 0x2580E, size: 0x8, addend: 0x0, symName: __ZL9layouts44, symObjAddr: 0x1AE9E0, symBinAddr: 0x1A0870, symSize: 0x0 }
  - { offsetInCU: 0x4626, offset: 0x2582D, size: 0x8, addend: 0x0, symName: __ZL7file219, symObjAddr: 0x58C40, symBinAddr: 0x4AA40, symSize: 0x0 }
  - { offsetInCU: 0x4645, offset: 0x2584C, size: 0x8, addend: 0x0, symName: __ZL9patches44, symObjAddr: 0xC730, symBinAddr: 0x1AF0E0, symSize: 0x0 }
  - { offsetInCU: 0x4664, offset: 0x2586B, size: 0x8, addend: 0x0, symName: __ZL10patchBuf69, symObjAddr: 0x5A151, symBinAddr: 0x4BF51, symSize: 0x0 }
  - { offsetInCU: 0x4683, offset: 0x2588A, size: 0x8, addend: 0x0, symName: __ZL11platforms45, symObjAddr: 0x1AEA00, symBinAddr: 0x1A0890, symSize: 0x0 }
  - { offsetInCU: 0x46A2, offset: 0x258A9, size: 0x8, addend: 0x0, symName: __ZL7file220, symObjAddr: 0x5A160, symBinAddr: 0x4BF60, symSize: 0x0 }
  - { offsetInCU: 0x46C1, offset: 0x258C8, size: 0x8, addend: 0x0, symName: __ZL9layouts45, symObjAddr: 0x1AEA20, symBinAddr: 0x1A08B0, symSize: 0x0 }
  - { offsetInCU: 0x46E0, offset: 0x258E7, size: 0x8, addend: 0x0, symName: __ZL7file221, symObjAddr: 0x5A290, symBinAddr: 0x4C090, symSize: 0x0 }
  - { offsetInCU: 0x46FF, offset: 0x25906, size: 0x8, addend: 0x0, symName: __ZL9patches45, symObjAddr: 0xC8B0, symBinAddr: 0x1AF260, symSize: 0x0 }
  - { offsetInCU: 0x471E, offset: 0x25925, size: 0x8, addend: 0x0, symName: __ZL11platforms46, symObjAddr: 0x1AEA40, symBinAddr: 0x1A08D0, symSize: 0x0 }
  - { offsetInCU: 0x473D, offset: 0x25944, size: 0x8, addend: 0x0, symName: __ZL7file222, symObjAddr: 0x5A390, symBinAddr: 0x4C190, symSize: 0x0 }
  - { offsetInCU: 0x475C, offset: 0x25963, size: 0x8, addend: 0x0, symName: __ZL9layouts46, symObjAddr: 0x1AEA60, symBinAddr: 0x1A08F0, symSize: 0x0 }
  - { offsetInCU: 0x477B, offset: 0x25982, size: 0x8, addend: 0x0, symName: __ZL7file223, symObjAddr: 0x5A4E0, symBinAddr: 0x4C2E0, symSize: 0x0 }
  - { offsetInCU: 0x479A, offset: 0x259A1, size: 0x8, addend: 0x0, symName: __ZL9patches46, symObjAddr: 0xCA30, symBinAddr: 0x1AF3E0, symSize: 0x0 }
  - { offsetInCU: 0x47B9, offset: 0x259C0, size: 0x8, addend: 0x0, symName: __ZL10patchBuf70, symObjAddr: 0x5A5D6, symBinAddr: 0x4C3D6, symSize: 0x0 }
  - { offsetInCU: 0x47D8, offset: 0x259DF, size: 0x8, addend: 0x0, symName: __ZL11revisions14, symObjAddr: 0x4A1FC, symBinAddr: 0x3BFFC, symSize: 0x0 }
  - { offsetInCU: 0x47F7, offset: 0x259FE, size: 0x8, addend: 0x0, symName: __ZL11platforms47, symObjAddr: 0x1AEA80, symBinAddr: 0x1A0910, symSize: 0x0 }
  - { offsetInCU: 0x4816, offset: 0x25A1D, size: 0x8, addend: 0x0, symName: __ZL7file224, symObjAddr: 0x5A5E0, symBinAddr: 0x4C3E0, symSize: 0x0 }
  - { offsetInCU: 0x4835, offset: 0x25A3C, size: 0x8, addend: 0x0, symName: __ZL9layouts47, symObjAddr: 0x1AEAA0, symBinAddr: 0x1A0930, symSize: 0x0 }
  - { offsetInCU: 0x4854, offset: 0x25A5B, size: 0x8, addend: 0x0, symName: __ZL7file225, symObjAddr: 0x5A730, symBinAddr: 0x4C530, symSize: 0x0 }
  - { offsetInCU: 0x4888, offset: 0x25A8F, size: 0x8, addend: 0x0, symName: __ZL9patches47, symObjAddr: 0xCB80, symBinAddr: 0x1AF530, symSize: 0x0 }
  - { offsetInCU: 0x48A7, offset: 0x25AAE, size: 0x8, addend: 0x0, symName: __ZL10patchBuf71, symObjAddr: 0x5A9B5, symBinAddr: 0x4C7B5, symSize: 0x0 }
  - { offsetInCU: 0x48C8, offset: 0x25ACF, size: 0x8, addend: 0x0, symName: __ZL15codecModRealtek, symObjAddr: 0x5D00, symBinAddr: 0x1A86B0, symSize: 0x0 }
  - { offsetInCU: 0x48FB, offset: 0x25B02, size: 0x8, addend: 0x0, symName: __ZL11revisions15, symObjAddr: 0x5A9BC, symBinAddr: 0x4C7BC, symSize: 0x0 }
  - { offsetInCU: 0x491A, offset: 0x25B21, size: 0x8, addend: 0x0, symName: __ZL11platforms48, symObjAddr: 0x1AEAC0, symBinAddr: 0x1A0950, symSize: 0x0 }
  - { offsetInCU: 0x494D, offset: 0x25B54, size: 0x8, addend: 0x0, symName: __ZL7file226, symObjAddr: 0x5AAB0, symBinAddr: 0x4C8B0, symSize: 0x0 }
  - { offsetInCU: 0x4981, offset: 0x25B88, size: 0x8, addend: 0x0, symName: __ZL7file227, symObjAddr: 0x5ACF0, symBinAddr: 0x4CAF0, symSize: 0x0 }
  - { offsetInCU: 0x49B5, offset: 0x25BBC, size: 0x8, addend: 0x0, symName: __ZL7file228, symObjAddr: 0x5AE80, symBinAddr: 0x4CC80, symSize: 0x0 }
  - { offsetInCU: 0x49E9, offset: 0x25BF0, size: 0x8, addend: 0x0, symName: __ZL7file229, symObjAddr: 0x5AFF0, symBinAddr: 0x4CDF0, symSize: 0x0 }
  - { offsetInCU: 0x4A1D, offset: 0x25C24, size: 0x8, addend: 0x0, symName: __ZL7file230, symObjAddr: 0x5B130, symBinAddr: 0x4CF30, symSize: 0x0 }
  - { offsetInCU: 0x4A3C, offset: 0x25C43, size: 0x8, addend: 0x0, symName: __ZL7file231, symObjAddr: 0x5B270, symBinAddr: 0x4D070, symSize: 0x0 }
  - { offsetInCU: 0x4A70, offset: 0x25C77, size: 0x8, addend: 0x0, symName: __ZL7file232, symObjAddr: 0x5B3E0, symBinAddr: 0x4D1E0, symSize: 0x0 }
  - { offsetInCU: 0x4AA4, offset: 0x25CAB, size: 0x8, addend: 0x0, symName: __ZL7file233, symObjAddr: 0x5B540, symBinAddr: 0x4D340, symSize: 0x0 }
  - { offsetInCU: 0x4AD8, offset: 0x25CDF, size: 0x8, addend: 0x0, symName: __ZL7file234, symObjAddr: 0x5B6C0, symBinAddr: 0x4D4C0, symSize: 0x0 }
  - { offsetInCU: 0x4AF7, offset: 0x25CFE, size: 0x8, addend: 0x0, symName: __ZL7file235, symObjAddr: 0x5B810, symBinAddr: 0x4D610, symSize: 0x0 }
  - { offsetInCU: 0x4B2B, offset: 0x25D32, size: 0x8, addend: 0x0, symName: __ZL7file236, symObjAddr: 0x5B990, symBinAddr: 0x4D790, symSize: 0x0 }
  - { offsetInCU: 0x4B4A, offset: 0x25D51, size: 0x8, addend: 0x0, symName: __ZL7file237, symObjAddr: 0x5BB00, symBinAddr: 0x4D900, symSize: 0x0 }
  - { offsetInCU: 0x4B7E, offset: 0x25D85, size: 0x8, addend: 0x0, symName: __ZL7file238, symObjAddr: 0x5BC70, symBinAddr: 0x4DA70, symSize: 0x0 }
  - { offsetInCU: 0x4BB2, offset: 0x25DB9, size: 0x8, addend: 0x0, symName: __ZL7file239, symObjAddr: 0x5BDE0, symBinAddr: 0x4DBE0, symSize: 0x0 }
  - { offsetInCU: 0x4BD1, offset: 0x25DD8, size: 0x8, addend: 0x0, symName: __ZL7file240, symObjAddr: 0x5BF20, symBinAddr: 0x4DD20, symSize: 0x0 }
  - { offsetInCU: 0x4BF0, offset: 0x25DF7, size: 0x8, addend: 0x0, symName: __ZL7file241, symObjAddr: 0x5C060, symBinAddr: 0x4DE60, symSize: 0x0 }
  - { offsetInCU: 0x4C24, offset: 0x25E2B, size: 0x8, addend: 0x0, symName: __ZL7file242, symObjAddr: 0x5C1D0, symBinAddr: 0x4DFD0, symSize: 0x0 }
  - { offsetInCU: 0x4C43, offset: 0x25E4A, size: 0x8, addend: 0x0, symName: __ZL7file243, symObjAddr: 0x5C330, symBinAddr: 0x4E130, symSize: 0x0 }
  - { offsetInCU: 0x4C62, offset: 0x25E69, size: 0x8, addend: 0x0, symName: __ZL7file244, symObjAddr: 0x5C480, symBinAddr: 0x4E280, symSize: 0x0 }
  - { offsetInCU: 0x4C81, offset: 0x25E88, size: 0x8, addend: 0x0, symName: __ZL7file245, symObjAddr: 0x5C5D0, symBinAddr: 0x4E3D0, symSize: 0x0 }
  - { offsetInCU: 0x4CA0, offset: 0x25EA7, size: 0x8, addend: 0x0, symName: __ZL7file246, symObjAddr: 0x5C710, symBinAddr: 0x4E510, symSize: 0x0 }
  - { offsetInCU: 0x4CD4, offset: 0x25EDB, size: 0x8, addend: 0x0, symName: __ZL9layouts48, symObjAddr: 0x1AED00, symBinAddr: 0x1A0B90, symSize: 0x0 }
  - { offsetInCU: 0x4CF3, offset: 0x25EFA, size: 0x8, addend: 0x0, symName: __ZL7file247, symObjAddr: 0x5C8A0, symBinAddr: 0x4E6A0, symSize: 0x0 }
  - { offsetInCU: 0x4D27, offset: 0x25F2E, size: 0x8, addend: 0x0, symName: __ZL7file248, symObjAddr: 0x5CBC0, symBinAddr: 0x4E9C0, symSize: 0x0 }
  - { offsetInCU: 0x4D5B, offset: 0x25F62, size: 0x8, addend: 0x0, symName: __ZL7file249, symObjAddr: 0x5CEE0, symBinAddr: 0x4ECE0, symSize: 0x0 }
  - { offsetInCU: 0x4D7A, offset: 0x25F81, size: 0x8, addend: 0x0, symName: __ZL7file250, symObjAddr: 0x5D200, symBinAddr: 0x4F000, symSize: 0x0 }
  - { offsetInCU: 0x4DAE, offset: 0x25FB5, size: 0x8, addend: 0x0, symName: __ZL7file251, symObjAddr: 0x5D540, symBinAddr: 0x4F340, symSize: 0x0 }
  - { offsetInCU: 0x4DCD, offset: 0x25FD4, size: 0x8, addend: 0x0, symName: __ZL7file252, symObjAddr: 0x5D880, symBinAddr: 0x4F680, symSize: 0x0 }
  - { offsetInCU: 0x4E01, offset: 0x26008, size: 0x8, addend: 0x0, symName: __ZL7file253, symObjAddr: 0x5DBC0, symBinAddr: 0x4F9C0, symSize: 0x0 }
  - { offsetInCU: 0x4E35, offset: 0x2603C, size: 0x8, addend: 0x0, symName: __ZL7file254, symObjAddr: 0x5DED0, symBinAddr: 0x4FCD0, symSize: 0x0 }
  - { offsetInCU: 0x4E69, offset: 0x26070, size: 0x8, addend: 0x0, symName: __ZL7file255, symObjAddr: 0x5E1E0, symBinAddr: 0x4FFE0, symSize: 0x0 }
  - { offsetInCU: 0x4E9D, offset: 0x260A4, size: 0x8, addend: 0x0, symName: __ZL7file256, symObjAddr: 0x5E540, symBinAddr: 0x50340, symSize: 0x0 }
  - { offsetInCU: 0x4ED1, offset: 0x260D8, size: 0x8, addend: 0x0, symName: __ZL7file257, symObjAddr: 0x5E870, symBinAddr: 0x50670, symSize: 0x0 }
  - { offsetInCU: 0x4F05, offset: 0x2610C, size: 0x8, addend: 0x0, symName: __ZL7file258, symObjAddr: 0x5EBF0, symBinAddr: 0x509F0, symSize: 0x0 }
  - { offsetInCU: 0x4F39, offset: 0x26140, size: 0x8, addend: 0x0, symName: __ZL7file259, symObjAddr: 0x5EF30, symBinAddr: 0x50D30, symSize: 0x0 }
  - { offsetInCU: 0x4F58, offset: 0x2615F, size: 0x8, addend: 0x0, symName: __ZL7file260, symObjAddr: 0x5F260, symBinAddr: 0x51060, symSize: 0x0 }
  - { offsetInCU: 0x4F8C, offset: 0x26193, size: 0x8, addend: 0x0, symName: __ZL7file261, symObjAddr: 0x5F580, symBinAddr: 0x51380, symSize: 0x0 }
  - { offsetInCU: 0x4FAB, offset: 0x261B2, size: 0x8, addend: 0x0, symName: __ZL7file262, symObjAddr: 0x5F8A0, symBinAddr: 0x516A0, symSize: 0x0 }
  - { offsetInCU: 0x4FCA, offset: 0x261D1, size: 0x8, addend: 0x0, symName: __ZL7file263, symObjAddr: 0x5FC00, symBinAddr: 0x51A00, symSize: 0x0 }
  - { offsetInCU: 0x4FFE, offset: 0x26205, size: 0x8, addend: 0x0, symName: __ZL7file264, symObjAddr: 0x5FF10, symBinAddr: 0x51D10, symSize: 0x0 }
  - { offsetInCU: 0x501D, offset: 0x26224, size: 0x8, addend: 0x0, symName: __ZL7file265, symObjAddr: 0x60220, symBinAddr: 0x52020, symSize: 0x0 }
  - { offsetInCU: 0x5051, offset: 0x26258, size: 0x8, addend: 0x0, symName: __ZL7file266, symObjAddr: 0x60570, symBinAddr: 0x52370, symSize: 0x0 }
  - { offsetInCU: 0x5085, offset: 0x2628C, size: 0x8, addend: 0x0, symName: __ZL7file267, symObjAddr: 0x60890, symBinAddr: 0x52690, symSize: 0x0 }
  - { offsetInCU: 0x50B9, offset: 0x262C0, size: 0x8, addend: 0x0, symName: __ZL7file268, symObjAddr: 0x60BA0, symBinAddr: 0x529A0, symSize: 0x0 }
  - { offsetInCU: 0x50D8, offset: 0x262DF, size: 0x8, addend: 0x0, symName: __ZL7file269, symObjAddr: 0x60C80, symBinAddr: 0x52A80, symSize: 0x0 }
  - { offsetInCU: 0x510C, offset: 0x26313, size: 0x8, addend: 0x0, symName: __ZL7file270, symObjAddr: 0x60FA0, symBinAddr: 0x52DA0, symSize: 0x0 }
  - { offsetInCU: 0x512B, offset: 0x26332, size: 0x8, addend: 0x0, symName: __ZL9patches48, symObjAddr: 0xCD00, symBinAddr: 0x1AF6B0, symSize: 0x0 }
  - { offsetInCU: 0x514A, offset: 0x26351, size: 0x8, addend: 0x0, symName: __ZL10patchBuf72, symObjAddr: 0x61302, symBinAddr: 0x53102, symSize: 0x0 }
  - { offsetInCU: 0x5169, offset: 0x26370, size: 0x8, addend: 0x0, symName: __ZL11revisions16, symObjAddr: 0x5A9C4, symBinAddr: 0x4C7C4, symSize: 0x0 }
  - { offsetInCU: 0x5188, offset: 0x2638F, size: 0x8, addend: 0x0, symName: __ZL11platforms49, symObjAddr: 0x1AEF40, symBinAddr: 0x1A0DD0, symSize: 0x0 }
  - { offsetInCU: 0x51A7, offset: 0x263AE, size: 0x8, addend: 0x0, symName: __ZL7file271, symObjAddr: 0x61310, symBinAddr: 0x53110, symSize: 0x0 }
  - { offsetInCU: 0x51C6, offset: 0x263CD, size: 0x8, addend: 0x0, symName: __ZL7file272, symObjAddr: 0x61450, symBinAddr: 0x53250, symSize: 0x0 }
  - { offsetInCU: 0x51FA, offset: 0x26401, size: 0x8, addend: 0x0, symName: __ZL7file273, symObjAddr: 0x62880, symBinAddr: 0x54680, symSize: 0x0 }
  - { offsetInCU: 0x522E, offset: 0x26435, size: 0x8, addend: 0x0, symName: __ZL7file274, symObjAddr: 0x63E40, symBinAddr: 0x55C40, symSize: 0x0 }
  - { offsetInCU: 0x5262, offset: 0x26469, size: 0x8, addend: 0x0, symName: __ZL7file275, symObjAddr: 0x63FB0, symBinAddr: 0x55DB0, symSize: 0x0 }
  - { offsetInCU: 0x5281, offset: 0x26488, size: 0x8, addend: 0x0, symName: __ZL9layouts49, symObjAddr: 0x1AEFC0, symBinAddr: 0x1A0E50, symSize: 0x0 }
  - { offsetInCU: 0x52A0, offset: 0x264A7, size: 0x8, addend: 0x0, symName: __ZL7file276, symObjAddr: 0x64100, symBinAddr: 0x55F00, symSize: 0x0 }
  - { offsetInCU: 0x52D4, offset: 0x264DB, size: 0x8, addend: 0x0, symName: __ZL7file277, symObjAddr: 0x64740, symBinAddr: 0x56540, symSize: 0x0 }
  - { offsetInCU: 0x5308, offset: 0x2650F, size: 0x8, addend: 0x0, symName: __ZL7file278, symObjAddr: 0x654C0, symBinAddr: 0x572C0, symSize: 0x0 }
  - { offsetInCU: 0x533C, offset: 0x26543, size: 0x8, addend: 0x0, symName: __ZL7file279, symObjAddr: 0x65AF0, symBinAddr: 0x578F0, symSize: 0x0 }
  - { offsetInCU: 0x5370, offset: 0x26577, size: 0x8, addend: 0x0, symName: __ZL7file280, symObjAddr: 0x660E0, symBinAddr: 0x57EE0, symSize: 0x0 }
  - { offsetInCU: 0x53A4, offset: 0x265AB, size: 0x8, addend: 0x0, symName: __ZL9patches49, symObjAddr: 0xCE50, symBinAddr: 0x1AF800, symSize: 0x0 }
  - { offsetInCU: 0x53C3, offset: 0x265CA, size: 0x8, addend: 0x0, symName: __ZL10patchBuf73, symObjAddr: 0x66666, symBinAddr: 0x58466, symSize: 0x0 }
  - { offsetInCU: 0x53E2, offset: 0x265E9, size: 0x8, addend: 0x0, symName: __ZL10patchBuf74, symObjAddr: 0x6666A, symBinAddr: 0x5846A, symSize: 0x0 }
  - { offsetInCU: 0x5401, offset: 0x26608, size: 0x8, addend: 0x0, symName: __ZL11revisions17, symObjAddr: 0x5A9C8, symBinAddr: 0x4C7C8, symSize: 0x0 }
  - { offsetInCU: 0x5420, offset: 0x26627, size: 0x8, addend: 0x0, symName: __ZL11platforms50, symObjAddr: 0x1AF040, symBinAddr: 0x1A0ED0, symSize: 0x0 }
  - { offsetInCU: 0x5453, offset: 0x2665A, size: 0x8, addend: 0x0, symName: __ZL7file281, symObjAddr: 0x66670, symBinAddr: 0x58470, symSize: 0x0 }
  - { offsetInCU: 0x5472, offset: 0x26679, size: 0x8, addend: 0x0, symName: __ZL7file282, symObjAddr: 0x667C0, symBinAddr: 0x585C0, symSize: 0x0 }
  - { offsetInCU: 0x5491, offset: 0x26698, size: 0x8, addend: 0x0, symName: __ZL7file283, symObjAddr: 0x66900, symBinAddr: 0x58700, symSize: 0x0 }
  - { offsetInCU: 0x54B0, offset: 0x266B7, size: 0x8, addend: 0x0, symName: __ZL7file284, symObjAddr: 0x66A50, symBinAddr: 0x58850, symSize: 0x0 }
  - { offsetInCU: 0x54E4, offset: 0x266EB, size: 0x8, addend: 0x0, symName: __ZL7file285, symObjAddr: 0x67530, symBinAddr: 0x59330, symSize: 0x0 }
  - { offsetInCU: 0x5503, offset: 0x2670A, size: 0x8, addend: 0x0, symName: __ZL7file286, symObjAddr: 0x67690, symBinAddr: 0x59490, symSize: 0x0 }
  - { offsetInCU: 0x5522, offset: 0x26729, size: 0x8, addend: 0x0, symName: __ZL7file287, symObjAddr: 0x677D0, symBinAddr: 0x595D0, symSize: 0x0 }
  - { offsetInCU: 0x5556, offset: 0x2675D, size: 0x8, addend: 0x0, symName: __ZL7file288, symObjAddr: 0x67920, symBinAddr: 0x59720, symSize: 0x0 }
  - { offsetInCU: 0x5575, offset: 0x2677C, size: 0x8, addend: 0x0, symName: __ZL7file289, symObjAddr: 0x67A70, symBinAddr: 0x59870, symSize: 0x0 }
  - { offsetInCU: 0x5594, offset: 0x2679B, size: 0x8, addend: 0x0, symName: __ZL7file290, symObjAddr: 0x67BC0, symBinAddr: 0x599C0, symSize: 0x0 }
  - { offsetInCU: 0x55B3, offset: 0x267BA, size: 0x8, addend: 0x0, symName: __ZL9layouts50, symObjAddr: 0x1AF130, symBinAddr: 0x1A0FC0, symSize: 0x0 }
  - { offsetInCU: 0x55D2, offset: 0x267D9, size: 0x8, addend: 0x0, symName: __ZL7file291, symObjAddr: 0x67D10, symBinAddr: 0x59B10, symSize: 0x0 }
  - { offsetInCU: 0x5606, offset: 0x2680D, size: 0x8, addend: 0x0, symName: __ZL7file292, symObjAddr: 0x68CE0, symBinAddr: 0x5AAE0, symSize: 0x0 }
  - { offsetInCU: 0x563A, offset: 0x26841, size: 0x8, addend: 0x0, symName: __ZL7file293, symObjAddr: 0x692E0, symBinAddr: 0x5B0E0, symSize: 0x0 }
  - { offsetInCU: 0x566E, offset: 0x26875, size: 0x8, addend: 0x0, symName: __ZL7file294, symObjAddr: 0x69950, symBinAddr: 0x5B750, symSize: 0x0 }
  - { offsetInCU: 0x56A2, offset: 0x268A9, size: 0x8, addend: 0x0, symName: __ZL7file295, symObjAddr: 0x69F20, symBinAddr: 0x5BD20, symSize: 0x0 }
  - { offsetInCU: 0x56D6, offset: 0x268DD, size: 0x8, addend: 0x0, symName: __ZL7file296, symObjAddr: 0x6AEF0, symBinAddr: 0x5CCF0, symSize: 0x0 }
  - { offsetInCU: 0x56F5, offset: 0x268FC, size: 0x8, addend: 0x0, symName: __ZL7file297, symObjAddr: 0x6B4C0, symBinAddr: 0x5D2C0, symSize: 0x0 }
  - { offsetInCU: 0x5729, offset: 0x26930, size: 0x8, addend: 0x0, symName: __ZL7file298, symObjAddr: 0x6BB10, symBinAddr: 0x5D910, symSize: 0x0 }
  - { offsetInCU: 0x5748, offset: 0x2694F, size: 0x8, addend: 0x0, symName: __ZL7file299, symObjAddr: 0x6CAE0, symBinAddr: 0x5E8E0, symSize: 0x0 }
  - { offsetInCU: 0x577C, offset: 0x26983, size: 0x8, addend: 0x0, symName: __ZL7file300, symObjAddr: 0x6D120, symBinAddr: 0x5EF20, symSize: 0x0 }
  - { offsetInCU: 0x579B, offset: 0x269A2, size: 0x8, addend: 0x0, symName: __ZL9patches50, symObjAddr: 0xD000, symBinAddr: 0x1AF9B0, symSize: 0x0 }
  - { offsetInCU: 0x57CE, offset: 0x269D5, size: 0x8, addend: 0x0, symName: __ZL10patchBuf75, symObjAddr: 0x6D756, symBinAddr: 0x5F556, symSize: 0x0 }
  - { offsetInCU: 0x57ED, offset: 0x269F4, size: 0x8, addend: 0x0, symName: __ZL11platforms51, symObjAddr: 0x1AF220, symBinAddr: 0x1A10B0, symSize: 0x0 }
  - { offsetInCU: 0x580C, offset: 0x26A13, size: 0x8, addend: 0x0, symName: __ZL7file301, symObjAddr: 0x6D760, symBinAddr: 0x5F560, symSize: 0x0 }
  - { offsetInCU: 0x5840, offset: 0x26A47, size: 0x8, addend: 0x0, symName: __ZL7file302, symObjAddr: 0x6E2B0, symBinAddr: 0x600B0, symSize: 0x0 }
  - { offsetInCU: 0x585F, offset: 0x26A66, size: 0x8, addend: 0x0, symName: __ZL7file303, symObjAddr: 0x6E410, symBinAddr: 0x60210, symSize: 0x0 }
  - { offsetInCU: 0x587E, offset: 0x26A85, size: 0x8, addend: 0x0, symName: __ZL7file304, symObjAddr: 0x6E550, symBinAddr: 0x60350, symSize: 0x0 }
  - { offsetInCU: 0x589D, offset: 0x26AA4, size: 0x8, addend: 0x0, symName: __ZL7file305, symObjAddr: 0x6E690, symBinAddr: 0x60490, symSize: 0x0 }
  - { offsetInCU: 0x58BC, offset: 0x26AC3, size: 0x8, addend: 0x0, symName: __ZL9layouts51, symObjAddr: 0x1AF2A0, symBinAddr: 0x1A1130, symSize: 0x0 }
  - { offsetInCU: 0x58DB, offset: 0x26AE2, size: 0x8, addend: 0x0, symName: __ZL7file306, symObjAddr: 0x6E7E0, symBinAddr: 0x605E0, symSize: 0x0 }
  - { offsetInCU: 0x590F, offset: 0x26B16, size: 0x8, addend: 0x0, symName: __ZL7file307, symObjAddr: 0x6EAD0, symBinAddr: 0x608D0, symSize: 0x0 }
  - { offsetInCU: 0x5943, offset: 0x26B4A, size: 0x8, addend: 0x0, symName: __ZL7file308, symObjAddr: 0x6F010, symBinAddr: 0x60E10, symSize: 0x0 }
  - { offsetInCU: 0x5977, offset: 0x26B7E, size: 0x8, addend: 0x0, symName: __ZL7file309, symObjAddr: 0x6F540, symBinAddr: 0x61340, symSize: 0x0 }
  - { offsetInCU: 0x59AB, offset: 0x26BB2, size: 0x8, addend: 0x0, symName: __ZL7file310, symObjAddr: 0x6FA70, symBinAddr: 0x61870, symSize: 0x0 }
  - { offsetInCU: 0x59DF, offset: 0x26BE6, size: 0x8, addend: 0x0, symName: __ZL9patches51, symObjAddr: 0xD1E0, symBinAddr: 0x1AFB90, symSize: 0x0 }
  - { offsetInCU: 0x59FE, offset: 0x26C05, size: 0x8, addend: 0x0, symName: __ZL10patchBuf76, symObjAddr: 0x6FF9B, symBinAddr: 0x61D9B, symSize: 0x0 }
  - { offsetInCU: 0x5A1D, offset: 0x26C24, size: 0x8, addend: 0x0, symName: __ZL11platforms52, symObjAddr: 0x1AF320, symBinAddr: 0x1A11B0, symSize: 0x0 }
  - { offsetInCU: 0x5A3C, offset: 0x26C43, size: 0x8, addend: 0x0, symName: __ZL7file311, symObjAddr: 0x6FFA0, symBinAddr: 0x61DA0, symSize: 0x0 }
  - { offsetInCU: 0x5A5B, offset: 0x26C62, size: 0x8, addend: 0x0, symName: __ZL7file312, symObjAddr: 0x700D0, symBinAddr: 0x61ED0, symSize: 0x0 }
  - { offsetInCU: 0x5A8F, offset: 0x26C96, size: 0x8, addend: 0x0, symName: __ZL9layouts52, symObjAddr: 0x1AF350, symBinAddr: 0x1A11E0, symSize: 0x0 }
  - { offsetInCU: 0x5AAE, offset: 0x26CB5, size: 0x8, addend: 0x0, symName: __ZL7file313, symObjAddr: 0x70C00, symBinAddr: 0x62A00, symSize: 0x0 }
  - { offsetInCU: 0x5AE2, offset: 0x26CE9, size: 0x8, addend: 0x0, symName: __ZL7file314, symObjAddr: 0x712C0, symBinAddr: 0x630C0, symSize: 0x0 }
  - { offsetInCU: 0x5B16, offset: 0x26D1D, size: 0x8, addend: 0x0, symName: __ZL9patches52, symObjAddr: 0xD360, symBinAddr: 0x1AFD10, symSize: 0x0 }
  - { offsetInCU: 0x5B35, offset: 0x26D3C, size: 0x8, addend: 0x0, symName: __ZL10patchBuf77, symObjAddr: 0x719DF, symBinAddr: 0x637DF, symSize: 0x0 }
  - { offsetInCU: 0x5B54, offset: 0x26D5B, size: 0x8, addend: 0x0, symName: __ZL11platforms53, symObjAddr: 0x1AF380, symBinAddr: 0x1A1210, symSize: 0x0 }
  - { offsetInCU: 0x5B87, offset: 0x26D8E, size: 0x8, addend: 0x0, symName: __ZL7file315, symObjAddr: 0x719F0, symBinAddr: 0x637F0, symSize: 0x0 }
  - { offsetInCU: 0x5BA6, offset: 0x26DAD, size: 0x8, addend: 0x0, symName: __ZL7file316, symObjAddr: 0x71B40, symBinAddr: 0x63940, symSize: 0x0 }
  - { offsetInCU: 0x5BC5, offset: 0x26DCC, size: 0x8, addend: 0x0, symName: __ZL7file317, symObjAddr: 0x71C70, symBinAddr: 0x63A70, symSize: 0x0 }
  - { offsetInCU: 0x5BE4, offset: 0x26DEB, size: 0x8, addend: 0x0, symName: __ZL7file318, symObjAddr: 0x71DB0, symBinAddr: 0x63BB0, symSize: 0x0 }
  - { offsetInCU: 0x5C03, offset: 0x26E0A, size: 0x8, addend: 0x0, symName: __ZL7file319, symObjAddr: 0x71F10, symBinAddr: 0x63D10, symSize: 0x0 }
  - { offsetInCU: 0x5C37, offset: 0x26E3E, size: 0x8, addend: 0x0, symName: __ZL7file320, symObjAddr: 0x72080, symBinAddr: 0x63E80, symSize: 0x0 }
  - { offsetInCU: 0x5C56, offset: 0x26E5D, size: 0x8, addend: 0x0, symName: __ZL7file321, symObjAddr: 0x721E0, symBinAddr: 0x63FE0, symSize: 0x0 }
  - { offsetInCU: 0x5C8A, offset: 0x26E91, size: 0x8, addend: 0x0, symName: __ZL7file322, symObjAddr: 0x72360, symBinAddr: 0x64160, symSize: 0x0 }
  - { offsetInCU: 0x5CA9, offset: 0x26EB0, size: 0x8, addend: 0x0, symName: __ZL7file323, symObjAddr: 0x724C0, symBinAddr: 0x642C0, symSize: 0x0 }
  - { offsetInCU: 0x5CDD, offset: 0x26EE4, size: 0x8, addend: 0x0, symName: __ZL7file324, symObjAddr: 0x72650, symBinAddr: 0x64450, symSize: 0x0 }
  - { offsetInCU: 0x5CFC, offset: 0x26F03, size: 0x8, addend: 0x0, symName: __ZL7file325, symObjAddr: 0x72780, symBinAddr: 0x64580, symSize: 0x0 }
  - { offsetInCU: 0x5D1B, offset: 0x26F22, size: 0x8, addend: 0x0, symName: __ZL9layouts53, symObjAddr: 0x1AF490, symBinAddr: 0x1A1320, symSize: 0x0 }
  - { offsetInCU: 0x5D3A, offset: 0x26F41, size: 0x8, addend: 0x0, symName: __ZL7file326, symObjAddr: 0x728E0, symBinAddr: 0x646E0, symSize: 0x0 }
  - { offsetInCU: 0x5D6E, offset: 0x26F75, size: 0x8, addend: 0x0, symName: __ZL7file327, symObjAddr: 0x72EA0, symBinAddr: 0x64CA0, symSize: 0x0 }
  - { offsetInCU: 0x5DA2, offset: 0x26FA9, size: 0x8, addend: 0x0, symName: __ZL7file328, symObjAddr: 0x734B0, symBinAddr: 0x652B0, symSize: 0x0 }
  - { offsetInCU: 0x5DD6, offset: 0x26FDD, size: 0x8, addend: 0x0, symName: __ZL7file329, symObjAddr: 0x73AC0, symBinAddr: 0x658C0, symSize: 0x0 }
  - { offsetInCU: 0x5E0A, offset: 0x27011, size: 0x8, addend: 0x0, symName: __ZL7file330, symObjAddr: 0x740E0, symBinAddr: 0x65EE0, symSize: 0x0 }
  - { offsetInCU: 0x5E3E, offset: 0x27045, size: 0x8, addend: 0x0, symName: __ZL7file331, symObjAddr: 0x74710, symBinAddr: 0x66510, symSize: 0x0 }
  - { offsetInCU: 0x5E72, offset: 0x27079, size: 0x8, addend: 0x0, symName: __ZL7file332, symObjAddr: 0x74D40, symBinAddr: 0x66B40, symSize: 0x0 }
  - { offsetInCU: 0x5EA6, offset: 0x270AD, size: 0x8, addend: 0x0, symName: __ZL7file333, symObjAddr: 0x753B0, symBinAddr: 0x671B0, symSize: 0x0 }
  - { offsetInCU: 0x5EDA, offset: 0x270E1, size: 0x8, addend: 0x0, symName: __ZL7file334, symObjAddr: 0x759F0, symBinAddr: 0x677F0, symSize: 0x0 }
  - { offsetInCU: 0x5EF9, offset: 0x27100, size: 0x8, addend: 0x0, symName: __ZL7file335, symObjAddr: 0x76010, symBinAddr: 0x67E10, symSize: 0x0 }
  - { offsetInCU: 0x5F2D, offset: 0x27134, size: 0x8, addend: 0x0, symName: __ZL7file336, symObjAddr: 0x76630, symBinAddr: 0x68430, symSize: 0x0 }
  - { offsetInCU: 0x5F4C, offset: 0x27153, size: 0x8, addend: 0x0, symName: __ZL9patches53, symObjAddr: 0xD510, symBinAddr: 0x1AFEC0, symSize: 0x0 }
  - { offsetInCU: 0x5F6B, offset: 0x27172, size: 0x8, addend: 0x0, symName: __ZL10patchBuf78, symObjAddr: 0x76C51, symBinAddr: 0x68A51, symSize: 0x0 }
  - { offsetInCU: 0x5F8A, offset: 0x27191, size: 0x8, addend: 0x0, symName: __ZL11revisions18, symObjAddr: 0x5A9CC, symBinAddr: 0x4C7CC, symSize: 0x0 }
  - { offsetInCU: 0x5FA9, offset: 0x271B0, size: 0x8, addend: 0x0, symName: __ZL11platforms54, symObjAddr: 0x1AF5A0, symBinAddr: 0x1A1430, symSize: 0x0 }
  - { offsetInCU: 0x5FC8, offset: 0x271CF, size: 0x8, addend: 0x0, symName: __ZL7file337, symObjAddr: 0x76C60, symBinAddr: 0x68A60, symSize: 0x0 }
  - { offsetInCU: 0x5FE7, offset: 0x271EE, size: 0x8, addend: 0x0, symName: __ZL7file338, symObjAddr: 0x76EA0, symBinAddr: 0x68CA0, symSize: 0x0 }
  - { offsetInCU: 0x601B, offset: 0x27222, size: 0x8, addend: 0x0, symName: __ZL7file339, symObjAddr: 0x77060, symBinAddr: 0x68E60, symSize: 0x0 }
  - { offsetInCU: 0x604F, offset: 0x27256, size: 0x8, addend: 0x0, symName: __ZL7file340, symObjAddr: 0x77230, symBinAddr: 0x69030, symSize: 0x0 }
  - { offsetInCU: 0x6083, offset: 0x2728A, size: 0x8, addend: 0x0, symName: __ZL9layouts54, symObjAddr: 0x1AF650, symBinAddr: 0x1A14E0, symSize: 0x0 }
  - { offsetInCU: 0x60A2, offset: 0x272A9, size: 0x8, addend: 0x0, symName: __ZL7file341, symObjAddr: 0x773D0, symBinAddr: 0x691D0, symSize: 0x0 }
  - { offsetInCU: 0x60D6, offset: 0x272DD, size: 0x8, addend: 0x0, symName: __ZL7file342, symObjAddr: 0x77720, symBinAddr: 0x69520, symSize: 0x0 }
  - { offsetInCU: 0x60F5, offset: 0x272FC, size: 0x8, addend: 0x0, symName: __ZL7file343, symObjAddr: 0x77A70, symBinAddr: 0x69870, symSize: 0x0 }
  - { offsetInCU: 0x6114, offset: 0x2731B, size: 0x8, addend: 0x0, symName: __ZL7file344, symObjAddr: 0x77DC0, symBinAddr: 0x69BC0, symSize: 0x0 }
  - { offsetInCU: 0x6148, offset: 0x2734F, size: 0x8, addend: 0x0, symName: __ZL7file345, symObjAddr: 0x780F0, symBinAddr: 0x69EF0, symSize: 0x0 }
  - { offsetInCU: 0x6167, offset: 0x2736E, size: 0x8, addend: 0x0, symName: __ZL7file346, symObjAddr: 0x78420, symBinAddr: 0x6A220, symSize: 0x0 }
  - { offsetInCU: 0x619B, offset: 0x273A2, size: 0x8, addend: 0x0, symName: __ZL9patches54, symObjAddr: 0xD690, symBinAddr: 0x1B0040, symSize: 0x0 }
  - { offsetInCU: 0x61BA, offset: 0x273C1, size: 0x8, addend: 0x0, symName: __ZL10patchBuf79, symObjAddr: 0x7876C, symBinAddr: 0x6A56C, symSize: 0x0 }
  - { offsetInCU: 0x61D9, offset: 0x273E0, size: 0x8, addend: 0x0, symName: __ZL11revisions19, symObjAddr: 0x5A9D0, symBinAddr: 0x4C7D0, symSize: 0x0 }
  - { offsetInCU: 0x61F8, offset: 0x273FF, size: 0x8, addend: 0x0, symName: __ZL11platforms55, symObjAddr: 0x1AF700, symBinAddr: 0x1A1590, symSize: 0x0 }
  - { offsetInCU: 0x622B, offset: 0x27432, size: 0x8, addend: 0x0, symName: __ZL7file347, symObjAddr: 0x78770, symBinAddr: 0x6A570, symSize: 0x0 }
  - { offsetInCU: 0x624A, offset: 0x27451, size: 0x8, addend: 0x0, symName: __ZL7file348, symObjAddr: 0x788B0, symBinAddr: 0x6A6B0, symSize: 0x0 }
  - { offsetInCU: 0x6269, offset: 0x27470, size: 0x8, addend: 0x0, symName: __ZL7file349, symObjAddr: 0x78A10, symBinAddr: 0x6A810, symSize: 0x0 }
  - { offsetInCU: 0x629D, offset: 0x274A4, size: 0x8, addend: 0x0, symName: __ZL7file350, symObjAddr: 0x78B90, symBinAddr: 0x6A990, symSize: 0x0 }
  - { offsetInCU: 0x62D1, offset: 0x274D8, size: 0x8, addend: 0x0, symName: __ZL7file351, symObjAddr: 0x78D10, symBinAddr: 0x6AB10, symSize: 0x0 }
  - { offsetInCU: 0x62F0, offset: 0x274F7, size: 0x8, addend: 0x0, symName: __ZL7file352, symObjAddr: 0x78E90, symBinAddr: 0x6AC90, symSize: 0x0 }
  - { offsetInCU: 0x6324, offset: 0x2752B, size: 0x8, addend: 0x0, symName: __ZL7file353, symObjAddr: 0x79030, symBinAddr: 0x6AE30, symSize: 0x0 }
  - { offsetInCU: 0x6343, offset: 0x2754A, size: 0x8, addend: 0x0, symName: __ZL7file354, symObjAddr: 0x791D0, symBinAddr: 0x6AFD0, symSize: 0x0 }
  - { offsetInCU: 0x6377, offset: 0x2757E, size: 0x8, addend: 0x0, symName: __ZL7file355, symObjAddr: 0x79350, symBinAddr: 0x6B150, symSize: 0x0 }
  - { offsetInCU: 0x6396, offset: 0x2759D, size: 0x8, addend: 0x0, symName: __ZL7file356, symObjAddr: 0x79490, symBinAddr: 0x6B290, symSize: 0x0 }
  - { offsetInCU: 0x63B5, offset: 0x275BC, size: 0x8, addend: 0x0, symName: __ZL7file357, symObjAddr: 0x795F0, symBinAddr: 0x6B3F0, symSize: 0x0 }
  - { offsetInCU: 0x63E9, offset: 0x275F0, size: 0x8, addend: 0x0, symName: __ZL7file358, symObjAddr: 0x79720, symBinAddr: 0x6B520, symSize: 0x0 }
  - { offsetInCU: 0x6408, offset: 0x2760F, size: 0x8, addend: 0x0, symName: __ZL7file359, symObjAddr: 0x79860, symBinAddr: 0x6B660, symSize: 0x0 }
  - { offsetInCU: 0x6427, offset: 0x2762E, size: 0x8, addend: 0x0, symName: __ZL7file360, symObjAddr: 0x799D0, symBinAddr: 0x6B7D0, symSize: 0x0 }
  - { offsetInCU: 0x645B, offset: 0x27662, size: 0x8, addend: 0x0, symName: __ZL7file361, symObjAddr: 0x79B00, symBinAddr: 0x6B900, symSize: 0x0 }
  - { offsetInCU: 0x647A, offset: 0x27681, size: 0x8, addend: 0x0, symName: __ZL7file362, symObjAddr: 0x79C90, symBinAddr: 0x6BA90, symSize: 0x0 }
  - { offsetInCU: 0x64AE, offset: 0x276B5, size: 0x8, addend: 0x0, symName: __ZL7file363, symObjAddr: 0x79E00, symBinAddr: 0x6BC00, symSize: 0x0 }
  - { offsetInCU: 0x64CD, offset: 0x276D4, size: 0x8, addend: 0x0, symName: __ZL7file364, symObjAddr: 0x79F70, symBinAddr: 0x6BD70, symSize: 0x0 }
  - { offsetInCU: 0x64EC, offset: 0x276F3, size: 0x8, addend: 0x0, symName: __ZL7file365, symObjAddr: 0x7A0F0, symBinAddr: 0x6BEF0, symSize: 0x0 }
  - { offsetInCU: 0x6520, offset: 0x27727, size: 0x8, addend: 0x0, symName: __ZL7file366, symObjAddr: 0x7A240, symBinAddr: 0x6C040, symSize: 0x0 }
  - { offsetInCU: 0x6554, offset: 0x2775B, size: 0x8, addend: 0x0, symName: __ZL7file367, symObjAddr: 0x7A3C0, symBinAddr: 0x6C1C0, symSize: 0x0 }
  - { offsetInCU: 0x6588, offset: 0x2778F, size: 0x8, addend: 0x0, symName: __ZL7file368, symObjAddr: 0x7A560, symBinAddr: 0x6C360, symSize: 0x0 }
  - { offsetInCU: 0x65BC, offset: 0x277C3, size: 0x8, addend: 0x0, symName: __ZL7file369, symObjAddr: 0x7A6F0, symBinAddr: 0x6C4F0, symSize: 0x0 }
  - { offsetInCU: 0x65F0, offset: 0x277F7, size: 0x8, addend: 0x0, symName: __ZL7file370, symObjAddr: 0x7A860, symBinAddr: 0x6C660, symSize: 0x0 }
  - { offsetInCU: 0x6624, offset: 0x2782B, size: 0x8, addend: 0x0, symName: __ZL7file371, symObjAddr: 0x7A9F0, symBinAddr: 0x6C7F0, symSize: 0x0 }
  - { offsetInCU: 0x6658, offset: 0x2785F, size: 0x8, addend: 0x0, symName: __ZL7file372, symObjAddr: 0x7AB90, symBinAddr: 0x6C990, symSize: 0x0 }
  - { offsetInCU: 0x668C, offset: 0x27893, size: 0x8, addend: 0x0, symName: __ZL7file373, symObjAddr: 0x7AD20, symBinAddr: 0x6CB20, symSize: 0x0 }
  - { offsetInCU: 0x66AB, offset: 0x278B2, size: 0x8, addend: 0x0, symName: __ZL7file374, symObjAddr: 0x7AEB0, symBinAddr: 0x6CCB0, symSize: 0x0 }
  - { offsetInCU: 0x66CA, offset: 0x278D1, size: 0x8, addend: 0x0, symName: __ZL7file375, symObjAddr: 0x7B020, symBinAddr: 0x6CE20, symSize: 0x0 }
  - { offsetInCU: 0x66E9, offset: 0x278F0, size: 0x8, addend: 0x0, symName: __ZL7file376, symObjAddr: 0x7B1A0, symBinAddr: 0x6CFA0, symSize: 0x0 }
  - { offsetInCU: 0x6708, offset: 0x2790F, size: 0x8, addend: 0x0, symName: __ZL7file377, symObjAddr: 0x7B2F0, symBinAddr: 0x6D0F0, symSize: 0x0 }
  - { offsetInCU: 0x6727, offset: 0x2792E, size: 0x8, addend: 0x0, symName: __ZL7file378, symObjAddr: 0x7B440, symBinAddr: 0x6D240, symSize: 0x0 }
  - { offsetInCU: 0x6746, offset: 0x2794D, size: 0x8, addend: 0x0, symName: __ZL9layouts55, symObjAddr: 0x1AFA00, symBinAddr: 0x1A1890, symSize: 0x0 }
  - { offsetInCU: 0x6765, offset: 0x2796C, size: 0x8, addend: 0x0, symName: __ZL7file379, symObjAddr: 0x7B590, symBinAddr: 0x6D390, symSize: 0x0 }
  - { offsetInCU: 0x6799, offset: 0x279A0, size: 0x8, addend: 0x0, symName: __ZL7file380, symObjAddr: 0x7B950, symBinAddr: 0x6D750, symSize: 0x0 }
  - { offsetInCU: 0x67CD, offset: 0x279D4, size: 0x8, addend: 0x0, symName: __ZL7file381, symObjAddr: 0x7BFF0, symBinAddr: 0x6DDF0, symSize: 0x0 }
  - { offsetInCU: 0x6801, offset: 0x27A08, size: 0x8, addend: 0x0, symName: __ZL7file382, symObjAddr: 0x7C690, symBinAddr: 0x6E490, symSize: 0x0 }
  - { offsetInCU: 0x6835, offset: 0x27A3C, size: 0x8, addend: 0x0, symName: __ZL7file383, symObjAddr: 0x7CD30, symBinAddr: 0x6EB30, symSize: 0x0 }
  - { offsetInCU: 0x6854, offset: 0x27A5B, size: 0x8, addend: 0x0, symName: __ZL7file384, symObjAddr: 0x7D3D0, symBinAddr: 0x6F1D0, symSize: 0x0 }
  - { offsetInCU: 0x6888, offset: 0x27A8F, size: 0x8, addend: 0x0, symName: __ZL7file385, symObjAddr: 0x7EA60, symBinAddr: 0x70860, symSize: 0x0 }
  - { offsetInCU: 0x68BC, offset: 0x27AC3, size: 0x8, addend: 0x0, symName: __ZL7file386, symObjAddr: 0x7F9B0, symBinAddr: 0x717B0, symSize: 0x0 }
  - { offsetInCU: 0x68F0, offset: 0x27AF7, size: 0x8, addend: 0x0, symName: __ZL7file387, symObjAddr: 0x80050, symBinAddr: 0x71E50, symSize: 0x0 }
  - { offsetInCU: 0x6924, offset: 0x27B2B, size: 0x8, addend: 0x0, symName: __ZL7file388, symObjAddr: 0x80DE0, symBinAddr: 0x72BE0, symSize: 0x0 }
  - { offsetInCU: 0x6943, offset: 0x27B4A, size: 0x8, addend: 0x0, symName: __ZL7file389, symObjAddr: 0x81480, symBinAddr: 0x73280, symSize: 0x0 }
  - { offsetInCU: 0x6977, offset: 0x27B7E, size: 0x8, addend: 0x0, symName: __ZL7file390, symObjAddr: 0x82200, symBinAddr: 0x74000, symSize: 0x0 }
  - { offsetInCU: 0x69AB, offset: 0x27BB2, size: 0x8, addend: 0x0, symName: __ZL7file391, symObjAddr: 0x830D0, symBinAddr: 0x74ED0, symSize: 0x0 }
  - { offsetInCU: 0x69DF, offset: 0x27BE6, size: 0x8, addend: 0x0, symName: __ZL7file392, symObjAddr: 0x83720, symBinAddr: 0x75520, symSize: 0x0 }
  - { offsetInCU: 0x69FE, offset: 0x27C05, size: 0x8, addend: 0x0, symName: __ZL7file393, symObjAddr: 0x844A0, symBinAddr: 0x762A0, symSize: 0x0 }
  - { offsetInCU: 0x6A32, offset: 0x27C39, size: 0x8, addend: 0x0, symName: __ZL7file394, symObjAddr: 0x84B40, symBinAddr: 0x76940, symSize: 0x0 }
  - { offsetInCU: 0x6A51, offset: 0x27C58, size: 0x8, addend: 0x0, symName: __ZL7file395, symObjAddr: 0x851E0, symBinAddr: 0x76FE0, symSize: 0x0 }
  - { offsetInCU: 0x6A70, offset: 0x27C77, size: 0x8, addend: 0x0, symName: __ZL7file396, symObjAddr: 0x85880, symBinAddr: 0x77680, symSize: 0x0 }
  - { offsetInCU: 0x6A8F, offset: 0x27C96, size: 0x8, addend: 0x0, symName: __ZL7file397, symObjAddr: 0x85F20, symBinAddr: 0x77D20, symSize: 0x0 }
  - { offsetInCU: 0x6AC2, offset: 0x27CC9, size: 0x8, addend: 0x0, symName: __ZL7file398, symObjAddr: 0x86000, symBinAddr: 0x77E00, symSize: 0x0 }
  - { offsetInCU: 0x6AE1, offset: 0x27CE8, size: 0x8, addend: 0x0, symName: __ZL7file399, symObjAddr: 0x866A0, symBinAddr: 0x784A0, symSize: 0x0 }
  - { offsetInCU: 0x6B00, offset: 0x27D07, size: 0x8, addend: 0x0, symName: __ZL7file400, symObjAddr: 0x875F0, symBinAddr: 0x793F0, symSize: 0x0 }
  - { offsetInCU: 0x6B34, offset: 0x27D3B, size: 0x8, addend: 0x0, symName: __ZL7file401, symObjAddr: 0x88550, symBinAddr: 0x7A350, symSize: 0x0 }
  - { offsetInCU: 0x6B53, offset: 0x27D5A, size: 0x8, addend: 0x0, symName: __ZL7file402, symObjAddr: 0x88BF0, symBinAddr: 0x7A9F0, symSize: 0x0 }
  - { offsetInCU: 0x6B87, offset: 0x27D8E, size: 0x8, addend: 0x0, symName: __ZL7file403, symObjAddr: 0x89290, symBinAddr: 0x7B090, symSize: 0x0 }
  - { offsetInCU: 0x6BA6, offset: 0x27DAD, size: 0x8, addend: 0x0, symName: __ZL7file404, symObjAddr: 0x89930, symBinAddr: 0x7B730, symSize: 0x0 }
  - { offsetInCU: 0x6BDA, offset: 0x27DE1, size: 0x8, addend: 0x0, symName: __ZL7file405, symObjAddr: 0x8A6C0, symBinAddr: 0x7C4C0, symSize: 0x0 }
  - { offsetInCU: 0x6C0E, offset: 0x27E15, size: 0x8, addend: 0x0, symName: __ZL7file406, symObjAddr: 0x8B510, symBinAddr: 0x7D310, symSize: 0x0 }
  - { offsetInCU: 0x6C2D, offset: 0x27E34, size: 0x8, addend: 0x0, symName: __ZL7file407, symObjAddr: 0x8BBB0, symBinAddr: 0x7D9B0, symSize: 0x0 }
  - { offsetInCU: 0x6C61, offset: 0x27E68, size: 0x8, addend: 0x0, symName: __ZL7file408, symObjAddr: 0x8CB00, symBinAddr: 0x7E900, symSize: 0x0 }
  - { offsetInCU: 0x6C95, offset: 0x27E9C, size: 0x8, addend: 0x0, symName: __ZL7file409, symObjAddr: 0x8D6F0, symBinAddr: 0x7F4F0, symSize: 0x0 }
  - { offsetInCU: 0x6CC8, offset: 0x27ECF, size: 0x8, addend: 0x0, symName: __ZL7file410, symObjAddr: 0x8D7C0, symBinAddr: 0x7F5C0, symSize: 0x0 }
  - { offsetInCU: 0x6CFC, offset: 0x27F03, size: 0x8, addend: 0x0, symName: __ZL9patches55, symObjAddr: 0xD7E0, symBinAddr: 0x1B0190, symSize: 0x0 }
  - { offsetInCU: 0x6D1B, offset: 0x27F22, size: 0x8, addend: 0x0, symName: __ZL10patchBuf80, symObjAddr: 0x8E3A6, symBinAddr: 0x801A6, symSize: 0x0 }
  - { offsetInCU: 0x6D3A, offset: 0x27F41, size: 0x8, addend: 0x0, symName: __ZL11revisions20, symObjAddr: 0x5A9E0, symBinAddr: 0x4C7E0, symSize: 0x0 }
  - { offsetInCU: 0x6D6D, offset: 0x27F74, size: 0x8, addend: 0x0, symName: __ZL11platforms56, symObjAddr: 0x1AFD00, symBinAddr: 0x1A1B90, symSize: 0x0 }
  - { offsetInCU: 0x6DA0, offset: 0x27FA7, size: 0x8, addend: 0x0, symName: __ZL7file411, symObjAddr: 0x8E3B0, symBinAddr: 0x801B0, symSize: 0x0 }
  - { offsetInCU: 0x6DBF, offset: 0x27FC6, size: 0x8, addend: 0x0, symName: __ZL7file412, symObjAddr: 0x8E500, symBinAddr: 0x80300, symSize: 0x0 }
  - { offsetInCU: 0x6DDE, offset: 0x27FE5, size: 0x8, addend: 0x0, symName: __ZL7file413, symObjAddr: 0x8E670, symBinAddr: 0x80470, symSize: 0x0 }
  - { offsetInCU: 0x6DFD, offset: 0x28004, size: 0x8, addend: 0x0, symName: __ZL7file414, symObjAddr: 0x8E7C0, symBinAddr: 0x805C0, symSize: 0x0 }
  - { offsetInCU: 0x6E1C, offset: 0x28023, size: 0x8, addend: 0x0, symName: __ZL7file415, symObjAddr: 0x8E900, symBinAddr: 0x80700, symSize: 0x0 }
  - { offsetInCU: 0x6E50, offset: 0x28057, size: 0x8, addend: 0x0, symName: __ZL7file416, symObjAddr: 0x8F350, symBinAddr: 0x81150, symSize: 0x0 }
  - { offsetInCU: 0x6E6F, offset: 0x28076, size: 0x8, addend: 0x0, symName: __ZL7file417, symObjAddr: 0x8F4A0, symBinAddr: 0x812A0, symSize: 0x0 }
  - { offsetInCU: 0x6E8E, offset: 0x28095, size: 0x8, addend: 0x0, symName: __ZL7file418, symObjAddr: 0x8F610, symBinAddr: 0x81410, symSize: 0x0 }
  - { offsetInCU: 0x6EAD, offset: 0x280B4, size: 0x8, addend: 0x0, symName: __ZL7file419, symObjAddr: 0x8F760, symBinAddr: 0x81560, symSize: 0x0 }
  - { offsetInCU: 0x6ECC, offset: 0x280D3, size: 0x8, addend: 0x0, symName: __ZL7file420, symObjAddr: 0x8F8B0, symBinAddr: 0x816B0, symSize: 0x0 }
  - { offsetInCU: 0x6EEB, offset: 0x280F2, size: 0x8, addend: 0x0, symName: __ZL7file421, symObjAddr: 0x8FA00, symBinAddr: 0x81800, symSize: 0x0 }
  - { offsetInCU: 0x6F0A, offset: 0x28111, size: 0x8, addend: 0x0, symName: __ZL7file422, symObjAddr: 0x8FB50, symBinAddr: 0x81950, symSize: 0x0 }
  - { offsetInCU: 0x6F3E, offset: 0x28145, size: 0x8, addend: 0x0, symName: __ZL7file423, symObjAddr: 0x90660, symBinAddr: 0x82460, symSize: 0x0 }
  - { offsetInCU: 0x6F5D, offset: 0x28164, size: 0x8, addend: 0x0, symName: __ZL7file424, symObjAddr: 0x907A0, symBinAddr: 0x825A0, symSize: 0x0 }
  - { offsetInCU: 0x6F7C, offset: 0x28183, size: 0x8, addend: 0x0, symName: __ZL7file425, symObjAddr: 0x908E0, symBinAddr: 0x826E0, symSize: 0x0 }
  - { offsetInCU: 0x6FB0, offset: 0x281B7, size: 0x8, addend: 0x0, symName: __ZL7file426, symObjAddr: 0x91430, symBinAddr: 0x83230, symSize: 0x0 }
  - { offsetInCU: 0x6FE4, offset: 0x281EB, size: 0x8, addend: 0x0, symName: __ZL7file427, symObjAddr: 0x91F90, symBinAddr: 0x83D90, symSize: 0x0 }
  - { offsetInCU: 0x7003, offset: 0x2820A, size: 0x8, addend: 0x0, symName: __ZL7file428, symObjAddr: 0x920D0, symBinAddr: 0x83ED0, symSize: 0x0 }
  - { offsetInCU: 0x7022, offset: 0x28229, size: 0x8, addend: 0x0, symName: __ZL7file429, symObjAddr: 0x92210, symBinAddr: 0x84010, symSize: 0x0 }
  - { offsetInCU: 0x7056, offset: 0x2825D, size: 0x8, addend: 0x0, symName: __ZL7file430, symObjAddr: 0x92330, symBinAddr: 0x84130, symSize: 0x0 }
  - { offsetInCU: 0x7075, offset: 0x2827C, size: 0x8, addend: 0x0, symName: __ZL7file431, symObjAddr: 0x92470, symBinAddr: 0x84270, symSize: 0x0 }
  - { offsetInCU: 0x7094, offset: 0x2829B, size: 0x8, addend: 0x0, symName: __ZL7file432, symObjAddr: 0x925B0, symBinAddr: 0x843B0, symSize: 0x0 }
  - { offsetInCU: 0x70C8, offset: 0x282CF, size: 0x8, addend: 0x0, symName: __ZL7file433, symObjAddr: 0x92700, symBinAddr: 0x84500, symSize: 0x0 }
  - { offsetInCU: 0x70E7, offset: 0x282EE, size: 0x8, addend: 0x0, symName: __ZL7file434, symObjAddr: 0x92860, symBinAddr: 0x84660, symSize: 0x0 }
  - { offsetInCU: 0x7106, offset: 0x2830D, size: 0x8, addend: 0x0, symName: __ZL7file435, symObjAddr: 0x929B0, symBinAddr: 0x847B0, symSize: 0x0 }
  - { offsetInCU: 0x7125, offset: 0x2832C, size: 0x8, addend: 0x0, symName: __ZL7file436, symObjAddr: 0x92B00, symBinAddr: 0x84900, symSize: 0x0 }
  - { offsetInCU: 0x7159, offset: 0x28360, size: 0x8, addend: 0x0, symName: __ZL7file437, symObjAddr: 0x92C40, symBinAddr: 0x84A40, symSize: 0x0 }
  - { offsetInCU: 0x718D, offset: 0x28394, size: 0x8, addend: 0x0, symName: __ZL7file438, symObjAddr: 0x92D90, symBinAddr: 0x84B90, symSize: 0x0 }
  - { offsetInCU: 0x71AC, offset: 0x283B3, size: 0x8, addend: 0x0, symName: __ZL7file439, symObjAddr: 0x92EE0, symBinAddr: 0x84CE0, symSize: 0x0 }
  - { offsetInCU: 0x71CB, offset: 0x283D2, size: 0x8, addend: 0x0, symName: __ZL7file440, symObjAddr: 0x93050, symBinAddr: 0x84E50, symSize: 0x0 }
  - { offsetInCU: 0x71EA, offset: 0x283F1, size: 0x8, addend: 0x0, symName: __ZL7file441, symObjAddr: 0x931A0, symBinAddr: 0x84FA0, symSize: 0x0 }
  - { offsetInCU: 0x7209, offset: 0x28410, size: 0x8, addend: 0x0, symName: __ZL7file442, symObjAddr: 0x93310, symBinAddr: 0x85110, symSize: 0x0 }
  - { offsetInCU: 0x7228, offset: 0x2842F, size: 0x8, addend: 0x0, symName: __ZL7file443, symObjAddr: 0x93480, symBinAddr: 0x85280, symSize: 0x0 }
  - { offsetInCU: 0x7247, offset: 0x2844E, size: 0x8, addend: 0x0, symName: __ZL7file444, symObjAddr: 0x935D0, symBinAddr: 0x853D0, symSize: 0x0 }
  - { offsetInCU: 0x7266, offset: 0x2846D, size: 0x8, addend: 0x0, symName: __ZL7file445, symObjAddr: 0x93720, symBinAddr: 0x85520, symSize: 0x0 }
  - { offsetInCU: 0x7285, offset: 0x2848C, size: 0x8, addend: 0x0, symName: __ZL7file446, symObjAddr: 0x93890, symBinAddr: 0x85690, symSize: 0x0 }
  - { offsetInCU: 0x72A4, offset: 0x284AB, size: 0x8, addend: 0x0, symName: __ZL7file447, symObjAddr: 0x939E0, symBinAddr: 0x857E0, symSize: 0x0 }
  - { offsetInCU: 0x72C3, offset: 0x284CA, size: 0x8, addend: 0x0, symName: __ZL7file448, symObjAddr: 0x93B30, symBinAddr: 0x85930, symSize: 0x0 }
  - { offsetInCU: 0x72E2, offset: 0x284E9, size: 0x8, addend: 0x0, symName: __ZL7file449, symObjAddr: 0x93C70, symBinAddr: 0x85A70, symSize: 0x0 }
  - { offsetInCU: 0x7301, offset: 0x28508, size: 0x8, addend: 0x0, symName: __ZL7file450, symObjAddr: 0x93DC0, symBinAddr: 0x85BC0, symSize: 0x0 }
  - { offsetInCU: 0x7335, offset: 0x2853C, size: 0x8, addend: 0x0, symName: __ZL7file451, symObjAddr: 0x948F0, symBinAddr: 0x866F0, symSize: 0x0 }
  - { offsetInCU: 0x7354, offset: 0x2855B, size: 0x8, addend: 0x0, symName: __ZL7file452, symObjAddr: 0x94A30, symBinAddr: 0x86830, symSize: 0x0 }
  - { offsetInCU: 0x7373, offset: 0x2857A, size: 0x8, addend: 0x0, symName: __ZL7file453, symObjAddr: 0x94B80, symBinAddr: 0x86980, symSize: 0x0 }
  - { offsetInCU: 0x73A7, offset: 0x285AE, size: 0x8, addend: 0x0, symName: __ZL7file454, symObjAddr: 0x956C0, symBinAddr: 0x874C0, symSize: 0x0 }
  - { offsetInCU: 0x73C6, offset: 0x285CD, size: 0x8, addend: 0x0, symName: __ZL7file455, symObjAddr: 0x95810, symBinAddr: 0x87610, symSize: 0x0 }
  - { offsetInCU: 0x73FA, offset: 0x28601, size: 0x8, addend: 0x0, symName: __ZL7file456, symObjAddr: 0x96220, symBinAddr: 0x88020, symSize: 0x0 }
  - { offsetInCU: 0x7419, offset: 0x28620, size: 0x8, addend: 0x0, symName: __ZL7file457, symObjAddr: 0x96370, symBinAddr: 0x88170, symSize: 0x0 }
  - { offsetInCU: 0x7438, offset: 0x2863F, size: 0x8, addend: 0x0, symName: __ZL7file458, symObjAddr: 0x964B0, symBinAddr: 0x882B0, symSize: 0x0 }
  - { offsetInCU: 0x7457, offset: 0x2865E, size: 0x8, addend: 0x0, symName: __ZL7file459, symObjAddr: 0x965D0, symBinAddr: 0x883D0, symSize: 0x0 }
  - { offsetInCU: 0x7476, offset: 0x2867D, size: 0x8, addend: 0x0, symName: __ZL7file460, symObjAddr: 0x96720, symBinAddr: 0x88520, symSize: 0x0 }
  - { offsetInCU: 0x7495, offset: 0x2869C, size: 0x8, addend: 0x0, symName: __ZL7file461, symObjAddr: 0x96870, symBinAddr: 0x88670, symSize: 0x0 }
  - { offsetInCU: 0x74C9, offset: 0x286D0, size: 0x8, addend: 0x0, symName: __ZL7file462, symObjAddr: 0x97350, symBinAddr: 0x89150, symSize: 0x0 }
  - { offsetInCU: 0x74E8, offset: 0x286EF, size: 0x8, addend: 0x0, symName: __ZL7file463, symObjAddr: 0x974A0, symBinAddr: 0x892A0, symSize: 0x0 }
  - { offsetInCU: 0x7507, offset: 0x2870E, size: 0x8, addend: 0x0, symName: __ZL7file464, symObjAddr: 0x975F0, symBinAddr: 0x893F0, symSize: 0x0 }
  - { offsetInCU: 0x7526, offset: 0x2872D, size: 0x8, addend: 0x0, symName: __ZL7file465, symObjAddr: 0x97710, symBinAddr: 0x89510, symSize: 0x0 }
  - { offsetInCU: 0x755A, offset: 0x28761, size: 0x8, addend: 0x0, symName: __ZL7file466, symObjAddr: 0x98210, symBinAddr: 0x8A010, symSize: 0x0 }
  - { offsetInCU: 0x7579, offset: 0x28780, size: 0x8, addend: 0x0, symName: __ZL7file467, symObjAddr: 0x98350, symBinAddr: 0x8A150, symSize: 0x0 }
  - { offsetInCU: 0x7598, offset: 0x2879F, size: 0x8, addend: 0x0, symName: __ZL7file468, symObjAddr: 0x98490, symBinAddr: 0x8A290, symSize: 0x0 }
  - { offsetInCU: 0x75B7, offset: 0x287BE, size: 0x8, addend: 0x0, symName: __ZL9layouts56, symObjAddr: 0x1B02A0, symBinAddr: 0x1A2130, symSize: 0x0 }
  - { offsetInCU: 0x75D6, offset: 0x287DD, size: 0x8, addend: 0x0, symName: __ZL7file469, symObjAddr: 0x985D0, symBinAddr: 0x8A3D0, symSize: 0x0 }
  - { offsetInCU: 0x75F5, offset: 0x287FC, size: 0x8, addend: 0x0, symName: __ZL7file470, symObjAddr: 0x99390, symBinAddr: 0x8B190, symSize: 0x0 }
  - { offsetInCU: 0x7614, offset: 0x2881B, size: 0x8, addend: 0x0, symName: __ZL7file471, symObjAddr: 0x99E70, symBinAddr: 0x8BC70, symSize: 0x0 }
  - { offsetInCU: 0x7648, offset: 0x2884F, size: 0x8, addend: 0x0, symName: __ZL7file472, symObjAddr: 0x9A3E0, symBinAddr: 0x8C1E0, symSize: 0x0 }
  - { offsetInCU: 0x767C, offset: 0x28883, size: 0x8, addend: 0x0, symName: __ZL7file473, symObjAddr: 0x9C040, symBinAddr: 0x8DE40, symSize: 0x0 }
  - { offsetInCU: 0x76B0, offset: 0x288B7, size: 0x8, addend: 0x0, symName: __ZL7file474, symObjAddr: 0x9C690, symBinAddr: 0x8E490, symSize: 0x0 }
  - { offsetInCU: 0x76E4, offset: 0x288EB, size: 0x8, addend: 0x0, symName: __ZL7file475, symObjAddr: 0x9D460, symBinAddr: 0x8F260, symSize: 0x0 }
  - { offsetInCU: 0x7703, offset: 0x2890A, size: 0x8, addend: 0x0, symName: __ZL7file476, symObjAddr: 0x9DAB0, symBinAddr: 0x8F8B0, symSize: 0x0 }
  - { offsetInCU: 0x7722, offset: 0x28929, size: 0x8, addend: 0x0, symName: __ZL7file477, symObjAddr: 0x9E880, symBinAddr: 0x90680, symSize: 0x0 }
  - { offsetInCU: 0x7741, offset: 0x28948, size: 0x8, addend: 0x0, symName: __ZL7file478, symObjAddr: 0x9EED0, symBinAddr: 0x90CD0, symSize: 0x0 }
  - { offsetInCU: 0x7760, offset: 0x28967, size: 0x8, addend: 0x0, symName: __ZL7file479, symObjAddr: 0x9F520, symBinAddr: 0x91320, symSize: 0x0 }
  - { offsetInCU: 0x777F, offset: 0x28986, size: 0x8, addend: 0x0, symName: __ZL7file480, symObjAddr: 0x9FB70, symBinAddr: 0x91970, symSize: 0x0 }
  - { offsetInCU: 0x779E, offset: 0x289A5, size: 0x8, addend: 0x0, symName: __ZL7file481, symObjAddr: 0xA0940, symBinAddr: 0x92740, symSize: 0x0 }
  - { offsetInCU: 0x77D2, offset: 0x289D9, size: 0x8, addend: 0x0, symName: __ZL7file482, symObjAddr: 0xA0D30, symBinAddr: 0x92B30, symSize: 0x0 }
  - { offsetInCU: 0x7806, offset: 0x28A0D, size: 0x8, addend: 0x0, symName: __ZL7file483, symObjAddr: 0xA13E0, symBinAddr: 0x931E0, symSize: 0x0 }
  - { offsetInCU: 0x783A, offset: 0x28A41, size: 0x8, addend: 0x0, symName: __ZL7file484, symObjAddr: 0xA21A0, symBinAddr: 0x93FA0, symSize: 0x0 }
  - { offsetInCU: 0x786E, offset: 0x28A75, size: 0x8, addend: 0x0, symName: __ZL7file485, symObjAddr: 0xA2850, symBinAddr: 0x94650, symSize: 0x0 }
  - { offsetInCU: 0x78A2, offset: 0x28AA9, size: 0x8, addend: 0x0, symName: __ZL7file486, symObjAddr: 0xA3EE0, symBinAddr: 0x95CE0, symSize: 0x0 }
  - { offsetInCU: 0x78C1, offset: 0x28AC8, size: 0x8, addend: 0x0, symName: __ZL7file487, symObjAddr: 0xA4590, symBinAddr: 0x96390, symSize: 0x0 }
  - { offsetInCU: 0x78F5, offset: 0x28AFC, size: 0x8, addend: 0x0, symName: __ZL7file488, symObjAddr: 0xA4C50, symBinAddr: 0x96A50, symSize: 0x0 }
  - { offsetInCU: 0x7929, offset: 0x28B30, size: 0x8, addend: 0x0, symName: __ZL7file489, symObjAddr: 0xA4F30, symBinAddr: 0x96D30, symSize: 0x0 }
  - { offsetInCU: 0x795D, offset: 0x28B64, size: 0x8, addend: 0x0, symName: __ZL7file490, symObjAddr: 0xA5580, symBinAddr: 0x97380, symSize: 0x0 }
  - { offsetInCU: 0x797C, offset: 0x28B83, size: 0x8, addend: 0x0, symName: __ZL7file491, symObjAddr: 0xA6340, symBinAddr: 0x98140, symSize: 0x0 }
  - { offsetInCU: 0x79AF, offset: 0x28BB6, size: 0x8, addend: 0x0, symName: __ZL7file492, symObjAddr: 0xA6400, symBinAddr: 0x98200, symSize: 0x0 }
  - { offsetInCU: 0x79E3, offset: 0x28BEA, size: 0x8, addend: 0x0, symName: __ZL7file493, symObjAddr: 0xA6A60, symBinAddr: 0x98860, symSize: 0x0 }
  - { offsetInCU: 0x7A02, offset: 0x28C09, size: 0x8, addend: 0x0, symName: __ZL7file494, symObjAddr: 0xA70B0, symBinAddr: 0x98EB0, symSize: 0x0 }
  - { offsetInCU: 0x7A21, offset: 0x28C28, size: 0x8, addend: 0x0, symName: __ZL7file495, symObjAddr: 0xA7E70, symBinAddr: 0x99C70, symSize: 0x0 }
  - { offsetInCU: 0x7A55, offset: 0x28C5C, size: 0x8, addend: 0x0, symName: __ZL7file496, symObjAddr: 0xA8440, symBinAddr: 0x9A240, symSize: 0x0 }
  - { offsetInCU: 0x7A89, offset: 0x28C90, size: 0x8, addend: 0x0, symName: __ZL7file497, symObjAddr: 0xA89B0, symBinAddr: 0x9A7B0, symSize: 0x0 }
  - { offsetInCU: 0x7ABD, offset: 0x28CC4, size: 0x8, addend: 0x0, symName: __ZL7file498, symObjAddr: 0xA9490, symBinAddr: 0x9B290, symSize: 0x0 }
  - { offsetInCU: 0x7AF1, offset: 0x28CF8, size: 0x8, addend: 0x0, symName: __ZL7file499, symObjAddr: 0xA9AF0, symBinAddr: 0x9B8F0, symSize: 0x0 }
  - { offsetInCU: 0x7B10, offset: 0x28D17, size: 0x8, addend: 0x0, symName: __ZL7file500, symObjAddr: 0xAA5D0, symBinAddr: 0x9C3D0, symSize: 0x0 }
  - { offsetInCU: 0x7B44, offset: 0x28D4B, size: 0x8, addend: 0x0, symName: __ZL7file501, symObjAddr: 0xAAB40, symBinAddr: 0x9C940, symSize: 0x0 }
  - { offsetInCU: 0x7B78, offset: 0x28D7F, size: 0x8, addend: 0x0, symName: __ZL7file502, symObjAddr: 0xAB620, symBinAddr: 0x9D420, symSize: 0x0 }
  - { offsetInCU: 0x7BAC, offset: 0x28DB3, size: 0x8, addend: 0x0, symName: __ZL7file503, symObjAddr: 0xABC50, symBinAddr: 0x9DA50, symSize: 0x0 }
  - { offsetInCU: 0x7BCB, offset: 0x28DD2, size: 0x8, addend: 0x0, symName: __ZL7file504, symObjAddr: 0xAC280, symBinAddr: 0x9E080, symSize: 0x0 }
  - { offsetInCU: 0x7BEA, offset: 0x28DF1, size: 0x8, addend: 0x0, symName: __ZL7file505, symObjAddr: 0xAD040, symBinAddr: 0x9EE40, symSize: 0x0 }
  - { offsetInCU: 0x7C1E, offset: 0x28E25, size: 0x8, addend: 0x0, symName: __ZL7file506, symObjAddr: 0xADE10, symBinAddr: 0x9FC10, symSize: 0x0 }
  - { offsetInCU: 0x7C52, offset: 0x28E59, size: 0x8, addend: 0x0, symName: __ZL7file507, symObjAddr: 0xAEBE0, symBinAddr: 0xA09E0, symSize: 0x0 }
  - { offsetInCU: 0x7C71, offset: 0x28E78, size: 0x8, addend: 0x0, symName: __ZL7file508, symObjAddr: 0xAF230, symBinAddr: 0xA1030, symSize: 0x0 }
  - { offsetInCU: 0x7C90, offset: 0x28E97, size: 0x8, addend: 0x0, symName: __ZL7file509, symObjAddr: 0xAF890, symBinAddr: 0xA1690, symSize: 0x0 }
  - { offsetInCU: 0x7CAF, offset: 0x28EB6, size: 0x8, addend: 0x0, symName: __ZL7file510, symObjAddr: 0xAFEE0, symBinAddr: 0xA1CE0, symSize: 0x0 }
  - { offsetInCU: 0x7CCE, offset: 0x28ED5, size: 0x8, addend: 0x0, symName: __ZL7file511, symObjAddr: 0xB0CB0, symBinAddr: 0xA2AB0, symSize: 0x0 }
  - { offsetInCU: 0x7CED, offset: 0x28EF4, size: 0x8, addend: 0x0, symName: __ZL7file512, symObjAddr: 0xB1300, symBinAddr: 0xA3100, symSize: 0x0 }
  - { offsetInCU: 0x7D21, offset: 0x28F28, size: 0x8, addend: 0x0, symName: __ZL7file513, symObjAddr: 0xB2BC0, symBinAddr: 0xA49C0, symSize: 0x0 }
  - { offsetInCU: 0x7D55, offset: 0x28F5C, size: 0x8, addend: 0x0, symName: __ZL7file514, symObjAddr: 0xB3990, symBinAddr: 0xA5790, symSize: 0x0 }
  - { offsetInCU: 0x7D74, offset: 0x28F7B, size: 0x8, addend: 0x0, symName: __ZL7file515, symObjAddr: 0xB4760, symBinAddr: 0xA6560, symSize: 0x0 }
  - { offsetInCU: 0x7D93, offset: 0x28F9A, size: 0x8, addend: 0x0, symName: __ZL7file516, symObjAddr: 0xB5530, symBinAddr: 0xA7330, symSize: 0x0 }
  - { offsetInCU: 0x7DB2, offset: 0x28FB9, size: 0x8, addend: 0x0, symName: __ZL7file517, symObjAddr: 0xB6300, symBinAddr: 0xA8100, symSize: 0x0 }
  - { offsetInCU: 0x7DE6, offset: 0x28FED, size: 0x8, addend: 0x0, symName: __ZL7file518, symObjAddr: 0xB68C0, symBinAddr: 0xA86C0, symSize: 0x0 }
  - { offsetInCU: 0x7E1A, offset: 0x29021, size: 0x8, addend: 0x0, symName: __ZL7file519, symObjAddr: 0xB6E90, symBinAddr: 0xA8C90, symSize: 0x0 }
  - { offsetInCU: 0x7E39, offset: 0x29040, size: 0x8, addend: 0x0, symName: __ZL7file520, symObjAddr: 0xB7C60, symBinAddr: 0xA9A60, symSize: 0x0 }
  - { offsetInCU: 0x7E6D, offset: 0x29074, size: 0x8, addend: 0x0, symName: __ZL7file521, symObjAddr: 0xB8C30, symBinAddr: 0xAAA30, symSize: 0x0 }
  - { offsetInCU: 0x7EA1, offset: 0x290A8, size: 0x8, addend: 0x0, symName: __ZL7file522, symObjAddr: 0xB9A00, symBinAddr: 0xAB800, symSize: 0x0 }
  - { offsetInCU: 0x7ED5, offset: 0x290DC, size: 0x8, addend: 0x0, symName: __ZL7file523, symObjAddr: 0xBA7D0, symBinAddr: 0xAC5D0, symSize: 0x0 }
  - { offsetInCU: 0x7F09, offset: 0x29110, size: 0x8, addend: 0x0, symName: __ZL7file524, symObjAddr: 0xBACF0, symBinAddr: 0xACAF0, symSize: 0x0 }
  - { offsetInCU: 0x7F3D, offset: 0x29144, size: 0x8, addend: 0x0, symName: __ZL7file525, symObjAddr: 0xBBCD0, symBinAddr: 0xADAD0, symSize: 0x0 }
  - { offsetInCU: 0x7F71, offset: 0x29178, size: 0x8, addend: 0x0, symName: __ZL7file526, symObjAddr: 0xBC320, symBinAddr: 0xAE120, symSize: 0x0 }
  - { offsetInCU: 0x7F90, offset: 0x29197, size: 0x8, addend: 0x0, symName: __ZL7file527, symObjAddr: 0xBD0F0, symBinAddr: 0xAEEF0, symSize: 0x0 }
  - { offsetInCU: 0x7FAF, offset: 0x291B6, size: 0x8, addend: 0x0, symName: __ZL9patches56, symObjAddr: 0xD990, symBinAddr: 0x1B0340, symSize: 0x0 }
  - { offsetInCU: 0x7FCE, offset: 0x291D5, size: 0x8, addend: 0x0, symName: __ZL10patchBuf81, symObjAddr: 0xBDEAF, symBinAddr: 0xAFCAF, symSize: 0x0 }
  - { offsetInCU: 0x7FED, offset: 0x291F4, size: 0x8, addend: 0x0, symName: __ZL11platforms57, symObjAddr: 0x1B0840, symBinAddr: 0x1A26D0, symSize: 0x0 }
  - { offsetInCU: 0x8020, offset: 0x29227, size: 0x8, addend: 0x0, symName: __ZL7file528, symObjAddr: 0xBDEC0, symBinAddr: 0xAFCC0, symSize: 0x0 }
  - { offsetInCU: 0x803F, offset: 0x29246, size: 0x8, addend: 0x0, symName: __ZL7file529, symObjAddr: 0xBE010, symBinAddr: 0xAFE10, symSize: 0x0 }
  - { offsetInCU: 0x805E, offset: 0x29265, size: 0x8, addend: 0x0, symName: __ZL7file530, symObjAddr: 0xBE180, symBinAddr: 0xAFF80, symSize: 0x0 }
  - { offsetInCU: 0x807D, offset: 0x29284, size: 0x8, addend: 0x0, symName: __ZL7file531, symObjAddr: 0xBE2D0, symBinAddr: 0xB00D0, symSize: 0x0 }
  - { offsetInCU: 0x809C, offset: 0x292A3, size: 0x8, addend: 0x0, symName: __ZL7file532, symObjAddr: 0xBE420, symBinAddr: 0xB0220, symSize: 0x0 }
  - { offsetInCU: 0x80BB, offset: 0x292C2, size: 0x8, addend: 0x0, symName: __ZL7file533, symObjAddr: 0xBE570, symBinAddr: 0xB0370, symSize: 0x0 }
  - { offsetInCU: 0x80EF, offset: 0x292F6, size: 0x8, addend: 0x0, symName: __ZL7file534, symObjAddr: 0xBF0B0, symBinAddr: 0xB0EB0, symSize: 0x0 }
  - { offsetInCU: 0x8123, offset: 0x2932A, size: 0x8, addend: 0x0, symName: __ZL7file535, symObjAddr: 0xBF200, symBinAddr: 0xB1000, symSize: 0x0 }
  - { offsetInCU: 0x8157, offset: 0x2935E, size: 0x8, addend: 0x0, symName: __ZL7file536, symObjAddr: 0xBFD40, symBinAddr: 0xB1B40, symSize: 0x0 }
  - { offsetInCU: 0x8176, offset: 0x2937D, size: 0x8, addend: 0x0, symName: __ZL7file537, symObjAddr: 0xBFEA0, symBinAddr: 0xB1CA0, symSize: 0x0 }
  - { offsetInCU: 0x81AA, offset: 0x293B1, size: 0x8, addend: 0x0, symName: __ZL7file538, symObjAddr: 0xC0050, symBinAddr: 0xB1E50, symSize: 0x0 }
  - { offsetInCU: 0x81C9, offset: 0x293D0, size: 0x8, addend: 0x0, symName: __ZL7file539, symObjAddr: 0xC01B0, symBinAddr: 0xB1FB0, symSize: 0x0 }
  - { offsetInCU: 0x81E8, offset: 0x293EF, size: 0x8, addend: 0x0, symName: __ZL7file540, symObjAddr: 0xC0300, symBinAddr: 0xB2100, symSize: 0x0 }
  - { offsetInCU: 0x8207, offset: 0x2940E, size: 0x8, addend: 0x0, symName: __ZL7file541, symObjAddr: 0xC0E40, symBinAddr: 0xB2C40, symSize: 0x0 }
  - { offsetInCU: 0x8226, offset: 0x2942D, size: 0x8, addend: 0x0, symName: __ZL7file542, symObjAddr: 0xC1920, symBinAddr: 0xB3720, symSize: 0x0 }
  - { offsetInCU: 0x8245, offset: 0x2944C, size: 0x8, addend: 0x0, symName: __ZL7file543, symObjAddr: 0xC1A70, symBinAddr: 0xB3870, symSize: 0x0 }
  - { offsetInCU: 0x8264, offset: 0x2946B, size: 0x8, addend: 0x0, symName: __ZL7file544, symObjAddr: 0xC1BC0, symBinAddr: 0xB39C0, symSize: 0x0 }
  - { offsetInCU: 0x8283, offset: 0x2948A, size: 0x8, addend: 0x0, symName: __ZL7file545, symObjAddr: 0xC1D20, symBinAddr: 0xB3B20, symSize: 0x0 }
  - { offsetInCU: 0x82A2, offset: 0x294A9, size: 0x8, addend: 0x0, symName: __ZL7file546, symObjAddr: 0xC1E60, symBinAddr: 0xB3C60, symSize: 0x0 }
  - { offsetInCU: 0x82C1, offset: 0x294C8, size: 0x8, addend: 0x0, symName: __ZL7file547, symObjAddr: 0xC1FC0, symBinAddr: 0xB3DC0, symSize: 0x0 }
  - { offsetInCU: 0x82E0, offset: 0x294E7, size: 0x8, addend: 0x0, symName: __ZL7file548, symObjAddr: 0xC20C0, symBinAddr: 0xB3EC0, symSize: 0x0 }
  - { offsetInCU: 0x82FF, offset: 0x29506, size: 0x8, addend: 0x0, symName: __ZL7file549, symObjAddr: 0xC2220, symBinAddr: 0xB4020, symSize: 0x0 }
  - { offsetInCU: 0x831E, offset: 0x29525, size: 0x8, addend: 0x0, symName: __ZL9layouts57, symObjAddr: 0x1B0A50, symBinAddr: 0x1A28E0, symSize: 0x0 }
  - { offsetInCU: 0x833D, offset: 0x29544, size: 0x8, addend: 0x0, symName: __ZL7file550, symObjAddr: 0xC2390, symBinAddr: 0xB4190, symSize: 0x0 }
  - { offsetInCU: 0x835C, offset: 0x29563, size: 0x8, addend: 0x0, symName: __ZL7file551, symObjAddr: 0xC3360, symBinAddr: 0xB5160, symSize: 0x0 }
  - { offsetInCU: 0x8390, offset: 0x29597, size: 0x8, addend: 0x0, symName: __ZL7file552, symObjAddr: 0xC39A0, symBinAddr: 0xB57A0, symSize: 0x0 }
  - { offsetInCU: 0x83C4, offset: 0x295CB, size: 0x8, addend: 0x0, symName: __ZL7file553, symObjAddr: 0xC4980, symBinAddr: 0xB6780, symSize: 0x0 }
  - { offsetInCU: 0x83F8, offset: 0x295FF, size: 0x8, addend: 0x0, symName: __ZL7file554, symObjAddr: 0xC5960, symBinAddr: 0xB7760, symSize: 0x0 }
  - { offsetInCU: 0x842C, offset: 0x29633, size: 0x8, addend: 0x0, symName: __ZL7file555, symObjAddr: 0xC6930, symBinAddr: 0xB8730, symSize: 0x0 }
  - { offsetInCU: 0x8460, offset: 0x29667, size: 0x8, addend: 0x0, symName: __ZL7file556, symObjAddr: 0xC6F70, symBinAddr: 0xB8D70, symSize: 0x0 }
  - { offsetInCU: 0x8494, offset: 0x2969B, size: 0x8, addend: 0x0, symName: __ZL7file557, symObjAddr: 0xC80E0, symBinAddr: 0xB9EE0, symSize: 0x0 }
  - { offsetInCU: 0x84B3, offset: 0x296BA, size: 0x8, addend: 0x0, symName: __ZL7file558, symObjAddr: 0xC8720, symBinAddr: 0xBA520, symSize: 0x0 }
  - { offsetInCU: 0x84E7, offset: 0x296EE, size: 0x8, addend: 0x0, symName: __ZL7file559, symObjAddr: 0xC8D60, symBinAddr: 0xBAB60, symSize: 0x0 }
  - { offsetInCU: 0x851B, offset: 0x29722, size: 0x8, addend: 0x0, symName: __ZL7file560, symObjAddr: 0xC92D0, symBinAddr: 0xBB0D0, symSize: 0x0 }
  - { offsetInCU: 0x854F, offset: 0x29756, size: 0x8, addend: 0x0, symName: __ZL7file561, symObjAddr: 0xC9910, symBinAddr: 0xBB710, symSize: 0x0 }
  - { offsetInCU: 0x8583, offset: 0x2978A, size: 0x8, addend: 0x0, symName: __ZL7file562, symObjAddr: 0xCAB80, symBinAddr: 0xBC980, symSize: 0x0 }
  - { offsetInCU: 0x85A2, offset: 0x297A9, size: 0x8, addend: 0x0, symName: __ZL7file563, symObjAddr: 0xCB1C0, symBinAddr: 0xBCFC0, symSize: 0x0 }
  - { offsetInCU: 0x85C1, offset: 0x297C8, size: 0x8, addend: 0x0, symName: __ZL7file564, symObjAddr: 0xCBF80, symBinAddr: 0xBDD80, symSize: 0x0 }
  - { offsetInCU: 0x85E0, offset: 0x297E7, size: 0x8, addend: 0x0, symName: __ZL7file565, symObjAddr: 0xCCD40, symBinAddr: 0xBEB40, symSize: 0x0 }
  - { offsetInCU: 0x85FF, offset: 0x29806, size: 0x8, addend: 0x0, symName: __ZL7file566, symObjAddr: 0xCDB10, symBinAddr: 0xBF910, symSize: 0x0 }
  - { offsetInCU: 0x8633, offset: 0x2983A, size: 0x8, addend: 0x0, symName: __ZL7file567, symObjAddr: 0xCE210, symBinAddr: 0xC0010, symSize: 0x0 }
  - { offsetInCU: 0x8666, offset: 0x2986D, size: 0x8, addend: 0x0, symName: __ZL7file568, symObjAddr: 0xCE2D0, symBinAddr: 0xC00D0, symSize: 0x0 }
  - { offsetInCU: 0x8685, offset: 0x2988C, size: 0x8, addend: 0x0, symName: __ZL7file569, symObjAddr: 0xCE9D0, symBinAddr: 0xC07D0, symSize: 0x0 }
  - { offsetInCU: 0x86A4, offset: 0x298AB, size: 0x8, addend: 0x0, symName: __ZL7file570, symObjAddr: 0xCF010, symBinAddr: 0xC0E10, symSize: 0x0 }
  - { offsetInCU: 0x86D8, offset: 0x298DF, size: 0x8, addend: 0x0, symName: __ZL7file571, symObjAddr: 0xCF650, symBinAddr: 0xC1450, symSize: 0x0 }
  - { offsetInCU: 0x870C, offset: 0x29913, size: 0x8, addend: 0x0, symName: __ZL9patches57, symObjAddr: 0xDB10, symBinAddr: 0x1B04C0, symSize: 0x0 }
  - { offsetInCU: 0x872B, offset: 0x29932, size: 0x8, addend: 0x0, symName: __ZL10patchBuf82, symObjAddr: 0xCFCED, symBinAddr: 0xC1AED, symSize: 0x0 }
  - { offsetInCU: 0x874A, offset: 0x29951, size: 0x8, addend: 0x0, symName: __ZL11revisions21, symObjAddr: 0x5A9F0, symBinAddr: 0x4C7F0, symSize: 0x0 }
  - { offsetInCU: 0x8769, offset: 0x29970, size: 0x8, addend: 0x0, symName: __ZL11platforms58, symObjAddr: 0x1B0C60, symBinAddr: 0x1A2AF0, symSize: 0x0 }
  - { offsetInCU: 0x879C, offset: 0x299A3, size: 0x8, addend: 0x0, symName: __ZL7file572, symObjAddr: 0xCFD00, symBinAddr: 0xC1B00, symSize: 0x0 }
  - { offsetInCU: 0x87BB, offset: 0x299C2, size: 0x8, addend: 0x0, symName: __ZL7file573, symObjAddr: 0xCFE60, symBinAddr: 0xC1C60, symSize: 0x0 }
  - { offsetInCU: 0x87DA, offset: 0x299E1, size: 0x8, addend: 0x0, symName: __ZL7file574, symObjAddr: 0xCFFB0, symBinAddr: 0xC1DB0, symSize: 0x0 }
  - { offsetInCU: 0x87F9, offset: 0x29A00, size: 0x8, addend: 0x0, symName: __ZL7file575, symObjAddr: 0xD0100, symBinAddr: 0xC1F00, symSize: 0x0 }
  - { offsetInCU: 0x8818, offset: 0x29A1F, size: 0x8, addend: 0x0, symName: __ZL7file576, symObjAddr: 0xD0260, symBinAddr: 0xC2060, symSize: 0x0 }
  - { offsetInCU: 0x8837, offset: 0x29A3E, size: 0x8, addend: 0x0, symName: __ZL7file577, symObjAddr: 0xD03C0, symBinAddr: 0xC21C0, symSize: 0x0 }
  - { offsetInCU: 0x8856, offset: 0x29A5D, size: 0x8, addend: 0x0, symName: __ZL7file578, symObjAddr: 0xD0510, symBinAddr: 0xC2310, symSize: 0x0 }
  - { offsetInCU: 0x8875, offset: 0x29A7C, size: 0x8, addend: 0x0, symName: __ZL7file579, symObjAddr: 0xD0650, symBinAddr: 0xC2450, symSize: 0x0 }
  - { offsetInCU: 0x8894, offset: 0x29A9B, size: 0x8, addend: 0x0, symName: __ZL9layouts58, symObjAddr: 0x1B0D20, symBinAddr: 0x1A2BB0, symSize: 0x0 }
  - { offsetInCU: 0x88B3, offset: 0x29ABA, size: 0x8, addend: 0x0, symName: __ZL7file580, symObjAddr: 0xD07B0, symBinAddr: 0xC25B0, symSize: 0x0 }
  - { offsetInCU: 0x88E7, offset: 0x29AEE, size: 0x8, addend: 0x0, symName: __ZL7file581, symObjAddr: 0xD0E00, symBinAddr: 0xC2C00, symSize: 0x0 }
  - { offsetInCU: 0x891B, offset: 0x29B22, size: 0x8, addend: 0x0, symName: __ZL7file582, symObjAddr: 0xD1450, symBinAddr: 0xC3250, symSize: 0x0 }
  - { offsetInCU: 0x894E, offset: 0x29B55, size: 0x8, addend: 0x0, symName: __ZL7file583, symObjAddr: 0xD1510, symBinAddr: 0xC3310, symSize: 0x0 }
  - { offsetInCU: 0x8982, offset: 0x29B89, size: 0x8, addend: 0x0, symName: __ZL7file584, symObjAddr: 0xD1B90, symBinAddr: 0xC3990, symSize: 0x0 }
  - { offsetInCU: 0x89B6, offset: 0x29BBD, size: 0x8, addend: 0x0, symName: __ZL7file585, symObjAddr: 0xD2210, symBinAddr: 0xC4010, symSize: 0x0 }
  - { offsetInCU: 0x89D5, offset: 0x29BDC, size: 0x8, addend: 0x0, symName: __ZL7file586, symObjAddr: 0xD2860, symBinAddr: 0xC4660, symSize: 0x0 }
  - { offsetInCU: 0x89F4, offset: 0x29BFB, size: 0x8, addend: 0x0, symName: __ZL7file587, symObjAddr: 0xD2EB0, symBinAddr: 0xC4CB0, symSize: 0x0 }
  - { offsetInCU: 0x8A13, offset: 0x29C1A, size: 0x8, addend: 0x0, symName: __ZL9patches58, symObjAddr: 0xDCF0, symBinAddr: 0x1B06A0, symSize: 0x0 }
  - { offsetInCU: 0x8A32, offset: 0x29C39, size: 0x8, addend: 0x0, symName: __ZL10patchBuf83, symObjAddr: 0xD34F8, symBinAddr: 0xC52F8, symSize: 0x0 }
  - { offsetInCU: 0x8A51, offset: 0x29C58, size: 0x8, addend: 0x0, symName: __ZL11revisions22, symObjAddr: 0x5A9F4, symBinAddr: 0x4C7F4, symSize: 0x0 }
  - { offsetInCU: 0x8A70, offset: 0x29C77, size: 0x8, addend: 0x0, symName: __ZL11platforms59, symObjAddr: 0x1B0DE0, symBinAddr: 0x1A2C70, symSize: 0x0 }
  - { offsetInCU: 0x8A8F, offset: 0x29C96, size: 0x8, addend: 0x0, symName: __ZL7file588, symObjAddr: 0xD3500, symBinAddr: 0xC5300, symSize: 0x0 }
  - { offsetInCU: 0x8AAE, offset: 0x29CB5, size: 0x8, addend: 0x0, symName: __ZL7file589, symObjAddr: 0xD3740, symBinAddr: 0xC5540, symSize: 0x0 }
  - { offsetInCU: 0x8ACD, offset: 0x29CD4, size: 0x8, addend: 0x0, symName: __ZL7file590, symObjAddr: 0xD38B0, symBinAddr: 0xC56B0, symSize: 0x0 }
  - { offsetInCU: 0x8B01, offset: 0x29D08, size: 0x8, addend: 0x0, symName: __ZL7file591, symObjAddr: 0xD3A70, symBinAddr: 0xC5870, symSize: 0x0 }
  - { offsetInCU: 0x8B20, offset: 0x29D27, size: 0x8, addend: 0x0, symName: __ZL7file592, symObjAddr: 0xD3BE0, symBinAddr: 0xC59E0, symSize: 0x0 }
  - { offsetInCU: 0x8B3F, offset: 0x29D46, size: 0x8, addend: 0x0, symName: __ZL7file593, symObjAddr: 0xD3D40, symBinAddr: 0xC5B40, symSize: 0x0 }
  - { offsetInCU: 0x8B5E, offset: 0x29D65, size: 0x8, addend: 0x0, symName: __ZL7file594, symObjAddr: 0xD3EB0, symBinAddr: 0xC5CB0, symSize: 0x0 }
  - { offsetInCU: 0x8B7D, offset: 0x29D84, size: 0x8, addend: 0x0, symName: __ZL7file595, symObjAddr: 0xD4000, symBinAddr: 0xC5E00, symSize: 0x0 }
  - { offsetInCU: 0x8B9C, offset: 0x29DA3, size: 0x8, addend: 0x0, symName: __ZL7file596, symObjAddr: 0xD4170, symBinAddr: 0xC5F70, symSize: 0x0 }
  - { offsetInCU: 0x8BBB, offset: 0x29DC2, size: 0x8, addend: 0x0, symName: __ZL7file597, symObjAddr: 0xD42E0, symBinAddr: 0xC60E0, symSize: 0x0 }
  - { offsetInCU: 0x8BDA, offset: 0x29DE1, size: 0x8, addend: 0x0, symName: __ZL7file598, symObjAddr: 0xD4430, symBinAddr: 0xC6230, symSize: 0x0 }
  - { offsetInCU: 0x8BF9, offset: 0x29E00, size: 0x8, addend: 0x0, symName: __ZL7file599, symObjAddr: 0xD45A0, symBinAddr: 0xC63A0, symSize: 0x0 }
  - { offsetInCU: 0x8C18, offset: 0x29E1F, size: 0x8, addend: 0x0, symName: __ZL7file600, symObjAddr: 0xD4700, symBinAddr: 0xC6500, symSize: 0x0 }
  - { offsetInCU: 0x8C37, offset: 0x29E3E, size: 0x8, addend: 0x0, symName: __ZL7file601, symObjAddr: 0xD4860, symBinAddr: 0xC6660, symSize: 0x0 }
  - { offsetInCU: 0x8C56, offset: 0x29E5D, size: 0x8, addend: 0x0, symName: __ZL7file602, symObjAddr: 0xD49B0, symBinAddr: 0xC67B0, symSize: 0x0 }
  - { offsetInCU: 0x8C75, offset: 0x29E7C, size: 0x8, addend: 0x0, symName: __ZL7file603, symObjAddr: 0xD4B10, symBinAddr: 0xC6910, symSize: 0x0 }
  - { offsetInCU: 0x8CA9, offset: 0x29EB0, size: 0x8, addend: 0x0, symName: __ZL7file604, symObjAddr: 0xD4CA0, symBinAddr: 0xC6AA0, symSize: 0x0 }
  - { offsetInCU: 0x8CC8, offset: 0x29ECF, size: 0x8, addend: 0x0, symName: __ZL7file605, symObjAddr: 0xD4E00, symBinAddr: 0xC6C00, symSize: 0x0 }
  - { offsetInCU: 0x8CE7, offset: 0x29EEE, size: 0x8, addend: 0x0, symName: __ZL7file606, symObjAddr: 0xD4F50, symBinAddr: 0xC6D50, symSize: 0x0 }
  - { offsetInCU: 0x8D06, offset: 0x29F0D, size: 0x8, addend: 0x0, symName: __ZL7file607, symObjAddr: 0xD50A0, symBinAddr: 0xC6EA0, symSize: 0x0 }
  - { offsetInCU: 0x8D25, offset: 0x29F2C, size: 0x8, addend: 0x0, symName: __ZL7file608, symObjAddr: 0xD5220, symBinAddr: 0xC7020, symSize: 0x0 }
  - { offsetInCU: 0x8D59, offset: 0x29F60, size: 0x8, addend: 0x0, symName: __ZL7file609, symObjAddr: 0xD5790, symBinAddr: 0xC7590, symSize: 0x0 }
  - { offsetInCU: 0x8D78, offset: 0x29F7F, size: 0x8, addend: 0x0, symName: __ZL7file610, symObjAddr: 0xD5900, symBinAddr: 0xC7700, symSize: 0x0 }
  - { offsetInCU: 0x8D97, offset: 0x29F9E, size: 0x8, addend: 0x0, symName: __ZL9layouts59, symObjAddr: 0x1B1050, symBinAddr: 0x1A2EE0, symSize: 0x0 }
  - { offsetInCU: 0x8DB6, offset: 0x29FBD, size: 0x8, addend: 0x0, symName: __ZL7file611, symObjAddr: 0xD5A80, symBinAddr: 0xC7880, symSize: 0x0 }
  - { offsetInCU: 0x8DEA, offset: 0x29FF1, size: 0x8, addend: 0x0, symName: __ZL7file612, symObjAddr: 0xD5E80, symBinAddr: 0xC7C80, symSize: 0x0 }
  - { offsetInCU: 0x8E09, offset: 0x2A010, size: 0x8, addend: 0x0, symName: __ZL7file613, symObjAddr: 0xD6280, symBinAddr: 0xC8080, symSize: 0x0 }
  - { offsetInCU: 0x8E3D, offset: 0x2A044, size: 0x8, addend: 0x0, symName: __ZL7file614, symObjAddr: 0xD6680, symBinAddr: 0xC8480, symSize: 0x0 }
  - { offsetInCU: 0x8E71, offset: 0x2A078, size: 0x8, addend: 0x0, symName: __ZL7file615, symObjAddr: 0xD6A80, symBinAddr: 0xC8880, symSize: 0x0 }
  - { offsetInCU: 0x8E90, offset: 0x2A097, size: 0x8, addend: 0x0, symName: __ZL7file616, symObjAddr: 0xD6DA0, symBinAddr: 0xC8BA0, symSize: 0x0 }
  - { offsetInCU: 0x8EAF, offset: 0x2A0B6, size: 0x8, addend: 0x0, symName: __ZL7file617, symObjAddr: 0xD70C0, symBinAddr: 0xC8EC0, symSize: 0x0 }
  - { offsetInCU: 0x8EE3, offset: 0x2A0EA, size: 0x8, addend: 0x0, symName: __ZL7file618, symObjAddr: 0xD7550, symBinAddr: 0xC9350, symSize: 0x0 }
  - { offsetInCU: 0x8F17, offset: 0x2A11E, size: 0x8, addend: 0x0, symName: __ZL7file619, symObjAddr: 0xD7910, symBinAddr: 0xC9710, symSize: 0x0 }
  - { offsetInCU: 0x8F4B, offset: 0x2A152, size: 0x8, addend: 0x0, symName: __ZL7file620, symObjAddr: 0xD7D70, symBinAddr: 0xC9B70, symSize: 0x0 }
  - { offsetInCU: 0x8F7F, offset: 0x2A186, size: 0x8, addend: 0x0, symName: __ZL7file621, symObjAddr: 0xD81D0, symBinAddr: 0xC9FD0, symSize: 0x0 }
  - { offsetInCU: 0x8FB3, offset: 0x2A1BA, size: 0x8, addend: 0x0, symName: __ZL7file622, symObjAddr: 0xD8640, symBinAddr: 0xCA440, symSize: 0x0 }
  - { offsetInCU: 0x8FE7, offset: 0x2A1EE, size: 0x8, addend: 0x0, symName: __ZL7file623, symObjAddr: 0xD8AB0, symBinAddr: 0xCA8B0, symSize: 0x0 }
  - { offsetInCU: 0x901B, offset: 0x2A222, size: 0x8, addend: 0x0, symName: __ZL7file624, symObjAddr: 0xD8F00, symBinAddr: 0xCAD00, symSize: 0x0 }
  - { offsetInCU: 0x904F, offset: 0x2A256, size: 0x8, addend: 0x0, symName: __ZL7file625, symObjAddr: 0xD9310, symBinAddr: 0xCB110, symSize: 0x0 }
  - { offsetInCU: 0x9083, offset: 0x2A28A, size: 0x8, addend: 0x0, symName: __ZL7file626, symObjAddr: 0xD9710, symBinAddr: 0xCB510, symSize: 0x0 }
  - { offsetInCU: 0x90B7, offset: 0x2A2BE, size: 0x8, addend: 0x0, symName: __ZL7file627, symObjAddr: 0xD9B10, symBinAddr: 0xCB910, symSize: 0x0 }
  - { offsetInCU: 0x90D6, offset: 0x2A2DD, size: 0x8, addend: 0x0, symName: __ZL7file628, symObjAddr: 0xD9FA0, symBinAddr: 0xCBDA0, symSize: 0x0 }
  - { offsetInCU: 0x90F5, offset: 0x2A2FC, size: 0x8, addend: 0x0, symName: __ZL7file629, symObjAddr: 0xDA3A0, symBinAddr: 0xCC1A0, symSize: 0x0 }
  - { offsetInCU: 0x9129, offset: 0x2A330, size: 0x8, addend: 0x0, symName: __ZL7file630, symObjAddr: 0xDA810, symBinAddr: 0xCC610, symSize: 0x0 }
  - { offsetInCU: 0x915D, offset: 0x2A364, size: 0x8, addend: 0x0, symName: __ZL7file631, symObjAddr: 0xDAC10, symBinAddr: 0xCCA10, symSize: 0x0 }
  - { offsetInCU: 0x917C, offset: 0x2A383, size: 0x8, addend: 0x0, symName: __ZL7file632, symObjAddr: 0xDAF30, symBinAddr: 0xCCD30, symSize: 0x0 }
  - { offsetInCU: 0x91B0, offset: 0x2A3B7, size: 0x8, addend: 0x0, symName: __ZL7file633, symObjAddr: 0xDB390, symBinAddr: 0xCD190, symSize: 0x0 }
  - { offsetInCU: 0x91CF, offset: 0x2A3D6, size: 0x8, addend: 0x0, symName: __ZL7file634, symObjAddr: 0xDB9D0, symBinAddr: 0xCD7D0, symSize: 0x0 }
  - { offsetInCU: 0x91EE, offset: 0x2A3F5, size: 0x8, addend: 0x0, symName: __ZL7file635, symObjAddr: 0xDBE40, symBinAddr: 0xCDC40, symSize: 0x0 }
  - { offsetInCU: 0x9222, offset: 0x2A429, size: 0x8, addend: 0x0, symName: __ZL9patches59, symObjAddr: 0xDF00, symBinAddr: 0x1B08B0, symSize: 0x0 }
  - { offsetInCU: 0x9241, offset: 0x2A448, size: 0x8, addend: 0x0, symName: __ZL10patchBuf84, symObjAddr: 0xDC2A4, symBinAddr: 0xCE0A4, symSize: 0x0 }
  - { offsetInCU: 0x9260, offset: 0x2A467, size: 0x8, addend: 0x0, symName: __ZL11platforms60, symObjAddr: 0x1B12C0, symBinAddr: 0x1A3150, symSize: 0x0 }
  - { offsetInCU: 0x927F, offset: 0x2A486, size: 0x8, addend: 0x0, symName: __ZL7file636, symObjAddr: 0xDC2B0, symBinAddr: 0xCE0B0, symSize: 0x0 }
  - { offsetInCU: 0x929E, offset: 0x2A4A5, size: 0x8, addend: 0x0, symName: __ZL7file637, symObjAddr: 0xDC400, symBinAddr: 0xCE200, symSize: 0x0 }
  - { offsetInCU: 0x92BD, offset: 0x2A4C4, size: 0x8, addend: 0x0, symName: __ZL9layouts60, symObjAddr: 0x1B12F0, symBinAddr: 0x1A3180, symSize: 0x0 }
  - { offsetInCU: 0x92DC, offset: 0x2A4E3, size: 0x8, addend: 0x0, symName: __ZL7file638, symObjAddr: 0xDC550, symBinAddr: 0xCE350, symSize: 0x0 }
  - { offsetInCU: 0x9310, offset: 0x2A517, size: 0x8, addend: 0x0, symName: __ZL7file639, symObjAddr: 0xDC8B0, symBinAddr: 0xCE6B0, symSize: 0x0 }
  - { offsetInCU: 0x9344, offset: 0x2A54B, size: 0x8, addend: 0x0, symName: __ZL9patches60, symObjAddr: 0xE080, symBinAddr: 0x1B0A30, symSize: 0x0 }
  - { offsetInCU: 0x9363, offset: 0x2A56A, size: 0x8, addend: 0x0, symName: __ZL10patchBuf85, symObjAddr: 0xDCC00, symBinAddr: 0xCEA00, symSize: 0x0 }
  - { offsetInCU: 0x9382, offset: 0x2A589, size: 0x8, addend: 0x0, symName: __ZL11platforms61, symObjAddr: 0x1B1320, symBinAddr: 0x1A31B0, symSize: 0x0 }
  - { offsetInCU: 0x93B5, offset: 0x2A5BC, size: 0x8, addend: 0x0, symName: __ZL7file640, symObjAddr: 0xDCC10, symBinAddr: 0xCEA10, symSize: 0x0 }
  - { offsetInCU: 0x93E9, offset: 0x2A5F0, size: 0x8, addend: 0x0, symName: __ZL7file641, symObjAddr: 0xDCD90, symBinAddr: 0xCEB90, symSize: 0x0 }
  - { offsetInCU: 0x9408, offset: 0x2A60F, size: 0x8, addend: 0x0, symName: __ZL7file642, symObjAddr: 0xDCED0, symBinAddr: 0xCECD0, symSize: 0x0 }
  - { offsetInCU: 0x9427, offset: 0x2A62E, size: 0x8, addend: 0x0, symName: __ZL7file643, symObjAddr: 0xDD040, symBinAddr: 0xCEE40, symSize: 0x0 }
  - { offsetInCU: 0x945B, offset: 0x2A662, size: 0x8, addend: 0x0, symName: __ZL7file644, symObjAddr: 0xDD220, symBinAddr: 0xCF020, symSize: 0x0 }
  - { offsetInCU: 0x947A, offset: 0x2A681, size: 0x8, addend: 0x0, symName: __ZL7file645, symObjAddr: 0xDD360, symBinAddr: 0xCF160, symSize: 0x0 }
  - { offsetInCU: 0x94AE, offset: 0x2A6B5, size: 0x8, addend: 0x0, symName: __ZL7file646, symObjAddr: 0xDDE40, symBinAddr: 0xCFC40, symSize: 0x0 }
  - { offsetInCU: 0x94E2, offset: 0x2A6E9, size: 0x8, addend: 0x0, symName: __ZL7file647, symObjAddr: 0xDDFE0, symBinAddr: 0xCFDE0, symSize: 0x0 }
  - { offsetInCU: 0x9501, offset: 0x2A708, size: 0x8, addend: 0x0, symName: __ZL7file648, symObjAddr: 0xDE180, symBinAddr: 0xCFF80, symSize: 0x0 }
  - { offsetInCU: 0x9520, offset: 0x2A727, size: 0x8, addend: 0x0, symName: __ZL7file649, symObjAddr: 0xDE300, symBinAddr: 0xD0100, symSize: 0x0 }
  - { offsetInCU: 0x9554, offset: 0x2A75B, size: 0x8, addend: 0x0, symName: __ZL7file650, symObjAddr: 0xDEDE0, symBinAddr: 0xD0BE0, symSize: 0x0 }
  - { offsetInCU: 0x9573, offset: 0x2A77A, size: 0x8, addend: 0x0, symName: __ZL7file651, symObjAddr: 0xDEF60, symBinAddr: 0xD0D60, symSize: 0x0 }
  - { offsetInCU: 0x9592, offset: 0x2A799, size: 0x8, addend: 0x0, symName: __ZL9layouts61, symObjAddr: 0x1B1490, symBinAddr: 0x1A3320, symSize: 0x0 }
  - { offsetInCU: 0x95B1, offset: 0x2A7B8, size: 0x8, addend: 0x0, symName: __ZL7file652, symObjAddr: 0xDF0E0, symBinAddr: 0xD0EE0, symSize: 0x0 }
  - { offsetInCU: 0x95D0, offset: 0x2A7D7, size: 0x8, addend: 0x0, symName: __ZL7file653, symObjAddr: 0xDFEB0, symBinAddr: 0xD1CB0, symSize: 0x0 }
  - { offsetInCU: 0x9604, offset: 0x2A80B, size: 0x8, addend: 0x0, symName: __ZL7file654, symObjAddr: 0xE0E50, symBinAddr: 0xD2C50, symSize: 0x0 }
  - { offsetInCU: 0x9623, offset: 0x2A82A, size: 0x8, addend: 0x0, symName: __ZL7file655, symObjAddr: 0xE1480, symBinAddr: 0xD3280, symSize: 0x0 }
  - { offsetInCU: 0x9642, offset: 0x2A849, size: 0x8, addend: 0x0, symName: __ZL7file656, symObjAddr: 0xE2240, symBinAddr: 0xD4040, symSize: 0x0 }
  - { offsetInCU: 0x9676, offset: 0x2A87D, size: 0x8, addend: 0x0, symName: __ZL7file657, symObjAddr: 0xE2860, symBinAddr: 0xD4660, symSize: 0x0 }
  - { offsetInCU: 0x9695, offset: 0x2A89C, size: 0x8, addend: 0x0, symName: __ZL7file658, symObjAddr: 0xE2E90, symBinAddr: 0xD4C90, symSize: 0x0 }
  - { offsetInCU: 0x96C9, offset: 0x2A8D0, size: 0x8, addend: 0x0, symName: __ZL7file659, symObjAddr: 0xE3E30, symBinAddr: 0xD5C30, symSize: 0x0 }
  - { offsetInCU: 0x96E8, offset: 0x2A8EF, size: 0x8, addend: 0x0, symName: __ZL7file660, symObjAddr: 0xE4C00, symBinAddr: 0xD6A00, symSize: 0x0 }
  - { offsetInCU: 0x971C, offset: 0x2A923, size: 0x8, addend: 0x0, symName: __ZL7file661, symObjAddr: 0xE5B70, symBinAddr: 0xD7970, symSize: 0x0 }
  - { offsetInCU: 0x9750, offset: 0x2A957, size: 0x8, addend: 0x0, symName: __ZL7file662, symObjAddr: 0xE6AE0, symBinAddr: 0xD88E0, symSize: 0x0 }
  - { offsetInCU: 0x9784, offset: 0x2A98B, size: 0x8, addend: 0x0, symName: __ZL7file663, symObjAddr: 0xE78A0, symBinAddr: 0xD96A0, symSize: 0x0 }
  - { offsetInCU: 0x97A3, offset: 0x2A9AA, size: 0x8, addend: 0x0, symName: __ZL7file664, symObjAddr: 0xE8660, symBinAddr: 0xDA460, symSize: 0x0 }
  - { offsetInCU: 0x97C2, offset: 0x2A9C9, size: 0x8, addend: 0x0, symName: __ZL7file665, symObjAddr: 0xE9420, symBinAddr: 0xDB220, symSize: 0x0 }
  - { offsetInCU: 0x97F6, offset: 0x2A9FD, size: 0x8, addend: 0x0, symName: __ZL7file666, symObjAddr: 0xEA390, symBinAddr: 0xDC190, symSize: 0x0 }
  - { offsetInCU: 0x9815, offset: 0x2AA1C, size: 0x8, addend: 0x0, symName: __ZL9patches61, symObjAddr: 0xE200, symBinAddr: 0x1B0BB0, symSize: 0x0 }
  - { offsetInCU: 0x9834, offset: 0x2AA3B, size: 0x8, addend: 0x0, symName: __ZL10patchBuf86, symObjAddr: 0xEB150, symBinAddr: 0xDCF50, symSize: 0x0 }
  - { offsetInCU: 0x9853, offset: 0x2AA5A, size: 0x8, addend: 0x0, symName: __ZL11platforms62, symObjAddr: 0x1B1600, symBinAddr: 0x1A3490, symSize: 0x0 }
  - { offsetInCU: 0x9872, offset: 0x2AA79, size: 0x8, addend: 0x0, symName: __ZL7file667, symObjAddr: 0xEB160, symBinAddr: 0xDCF60, symSize: 0x0 }
  - { offsetInCU: 0x98A6, offset: 0x2AAAD, size: 0x8, addend: 0x0, symName: __ZL7file668, symObjAddr: 0xEBA20, symBinAddr: 0xDD820, symSize: 0x0 }
  - { offsetInCU: 0x98C5, offset: 0x2AACC, size: 0x8, addend: 0x0, symName: __ZL7file669, symObjAddr: 0xEBB60, symBinAddr: 0xDD960, symSize: 0x0 }
  - { offsetInCU: 0x98E4, offset: 0x2AAEB, size: 0x8, addend: 0x0, symName: __ZL7file670, symObjAddr: 0xEBCB0, symBinAddr: 0xDDAB0, symSize: 0x0 }
  - { offsetInCU: 0x9903, offset: 0x2AB0A, size: 0x8, addend: 0x0, symName: __ZL7file671, symObjAddr: 0xEBE00, symBinAddr: 0xDDC00, symSize: 0x0 }
  - { offsetInCU: 0x9922, offset: 0x2AB29, size: 0x8, addend: 0x0, symName: __ZL7file672, symObjAddr: 0xEBF50, symBinAddr: 0xDDD50, symSize: 0x0 }
  - { offsetInCU: 0x9941, offset: 0x2AB48, size: 0x8, addend: 0x0, symName: __ZL7file673, symObjAddr: 0xEC0B0, symBinAddr: 0xDDEB0, symSize: 0x0 }
  - { offsetInCU: 0x9960, offset: 0x2AB67, size: 0x8, addend: 0x0, symName: __ZL9layouts62, symObjAddr: 0x1B16B0, symBinAddr: 0x1A3540, symSize: 0x0 }
  - { offsetInCU: 0x997F, offset: 0x2AB86, size: 0x8, addend: 0x0, symName: __ZL7file674, symObjAddr: 0xEC210, symBinAddr: 0xDE010, symSize: 0x0 }
  - { offsetInCU: 0x999E, offset: 0x2ABA5, size: 0x8, addend: 0x0, symName: __ZL7file675, symObjAddr: 0xEC7D0, symBinAddr: 0xDE5D0, symSize: 0x0 }
  - { offsetInCU: 0x99D2, offset: 0x2ABD9, size: 0x8, addend: 0x0, symName: __ZL7file676, symObjAddr: 0xECAC0, symBinAddr: 0xDE8C0, symSize: 0x0 }
  - { offsetInCU: 0x9A06, offset: 0x2AC0D, size: 0x8, addend: 0x0, symName: __ZL7file677, symObjAddr: 0xED850, symBinAddr: 0xDF650, symSize: 0x0 }
  - { offsetInCU: 0x9A25, offset: 0x2AC2C, size: 0x8, addend: 0x0, symName: __ZL7file678, symObjAddr: 0xEE620, symBinAddr: 0xE0420, symSize: 0x0 }
  - { offsetInCU: 0x9A44, offset: 0x2AC4B, size: 0x8, addend: 0x0, symName: __ZL7file679, symObjAddr: 0xEF3F0, symBinAddr: 0xE11F0, symSize: 0x0 }
  - { offsetInCU: 0x9A78, offset: 0x2AC7F, size: 0x8, addend: 0x0, symName: __ZL7file680, symObjAddr: 0xEFB70, symBinAddr: 0xE1970, symSize: 0x0 }
  - { offsetInCU: 0x9AAC, offset: 0x2ACB3, size: 0x8, addend: 0x0, symName: __ZL9patches62, symObjAddr: 0xE380, symBinAddr: 0x1B0D30, symSize: 0x0 }
  - { offsetInCU: 0x9ACB, offset: 0x2ACD2, size: 0x8, addend: 0x0, symName: __ZL10patchBuf87, symObjAddr: 0xF093B, symBinAddr: 0xE273B, symSize: 0x0 }
  - { offsetInCU: 0x9AEA, offset: 0x2ACF1, size: 0x8, addend: 0x0, symName: __ZL11platforms63, symObjAddr: 0x1B1760, symBinAddr: 0x1A35F0, symSize: 0x0 }
  - { offsetInCU: 0x9B09, offset: 0x2AD10, size: 0x8, addend: 0x0, symName: __ZL7file681, symObjAddr: 0xF0940, symBinAddr: 0xE2740, symSize: 0x0 }
  - { offsetInCU: 0x9B28, offset: 0x2AD2F, size: 0x8, addend: 0x0, symName: __ZL7file682, symObjAddr: 0xF0A90, symBinAddr: 0xE2890, symSize: 0x0 }
  - { offsetInCU: 0x9B47, offset: 0x2AD4E, size: 0x8, addend: 0x0, symName: __ZL7file683, symObjAddr: 0xF0BE0, symBinAddr: 0xE29E0, symSize: 0x0 }
  - { offsetInCU: 0x9B66, offset: 0x2AD6D, size: 0x8, addend: 0x0, symName: __ZL9layouts63, symObjAddr: 0x1B17B0, symBinAddr: 0x1A3640, symSize: 0x0 }
  - { offsetInCU: 0x9B85, offset: 0x2AD8C, size: 0x8, addend: 0x0, symName: __ZL7file684, symObjAddr: 0xF0D30, symBinAddr: 0xE2B30, symSize: 0x0 }
  - { offsetInCU: 0x9BA4, offset: 0x2ADAB, size: 0x8, addend: 0x0, symName: __ZL7file685, symObjAddr: 0xF1300, symBinAddr: 0xE3100, symSize: 0x0 }
  - { offsetInCU: 0x9BD8, offset: 0x2ADDF, size: 0x8, addend: 0x0, symName: __ZL7file686, symObjAddr: 0xF1970, symBinAddr: 0xE3770, symSize: 0x0 }
  - { offsetInCU: 0x9C0C, offset: 0x2AE13, size: 0x8, addend: 0x0, symName: __ZL9patches63, symObjAddr: 0xE500, symBinAddr: 0x1B0EB0, symSize: 0x0 }
  - { offsetInCU: 0x9C2B, offset: 0x2AE32, size: 0x8, addend: 0x0, symName: __ZL10patchBuf88, symObjAddr: 0xF1F40, symBinAddr: 0xE3D40, symSize: 0x0 }
  - { offsetInCU: 0x9C4A, offset: 0x2AE51, size: 0x8, addend: 0x0, symName: __ZL11revisions23, symObjAddr: 0x5A9F8, symBinAddr: 0x4C7F8, symSize: 0x0 }
  - { offsetInCU: 0x9C69, offset: 0x2AE70, size: 0x8, addend: 0x0, symName: __ZL11platforms64, symObjAddr: 0x1B1800, symBinAddr: 0x1A3690, symSize: 0x0 }
  - { offsetInCU: 0x9C88, offset: 0x2AE8F, size: 0x8, addend: 0x0, symName: __ZL7file687, symObjAddr: 0xF1F50, symBinAddr: 0xE3D50, symSize: 0x0 }
  - { offsetInCU: 0x9CBC, offset: 0x2AEC3, size: 0x8, addend: 0x0, symName: __ZL7file688, symObjAddr: 0xF29A0, symBinAddr: 0xE47A0, symSize: 0x0 }
  - { offsetInCU: 0x9CDB, offset: 0x2AEE2, size: 0x8, addend: 0x0, symName: __ZL7file689, symObjAddr: 0xF34B0, symBinAddr: 0xE52B0, symSize: 0x0 }
  - { offsetInCU: 0x9CFA, offset: 0x2AF01, size: 0x8, addend: 0x0, symName: __ZL9layouts64, symObjAddr: 0x1B1860, symBinAddr: 0x1A36F0, symSize: 0x0 }
  - { offsetInCU: 0x9D19, offset: 0x2AF20, size: 0x8, addend: 0x0, symName: __ZL7file690, symObjAddr: 0xF3600, symBinAddr: 0xE5400, symSize: 0x0 }
  - { offsetInCU: 0x9D4D, offset: 0x2AF54, size: 0x8, addend: 0x0, symName: __ZL7file691, symObjAddr: 0xF3D40, symBinAddr: 0xE5B40, symSize: 0x0 }
  - { offsetInCU: 0x9D81, offset: 0x2AF88, size: 0x8, addend: 0x0, symName: __ZL7file692, symObjAddr: 0xF4450, symBinAddr: 0xE6250, symSize: 0x0 }
  - { offsetInCU: 0x9DB5, offset: 0x2AFBC, size: 0x8, addend: 0x0, symName: __ZL9patches64, symObjAddr: 0xE650, symBinAddr: 0x1B1000, symSize: 0x0 }
  - { offsetInCU: 0x9DD4, offset: 0x2AFDB, size: 0x8, addend: 0x0, symName: __ZL10patchBuf89, symObjAddr: 0xF4B88, symBinAddr: 0xE6988, symSize: 0x0 }
  - { offsetInCU: 0x9DF3, offset: 0x2AFFA, size: 0x8, addend: 0x0, symName: __ZL11revisions24, symObjAddr: 0x5AA00, symBinAddr: 0x4C800, symSize: 0x0 }
  - { offsetInCU: 0x9E12, offset: 0x2B019, size: 0x8, addend: 0x0, symName: __ZL11platforms65, symObjAddr: 0x1B18C0, symBinAddr: 0x1A3750, symSize: 0x0 }
  - { offsetInCU: 0x9E31, offset: 0x2B038, size: 0x8, addend: 0x0, symName: __ZL7file693, symObjAddr: 0xF4B90, symBinAddr: 0xE6990, symSize: 0x0 }
  - { offsetInCU: 0x9E50, offset: 0x2B057, size: 0x8, addend: 0x0, symName: __ZL7file694, symObjAddr: 0xF4CE0, symBinAddr: 0xE6AE0, symSize: 0x0 }
  - { offsetInCU: 0x9E6F, offset: 0x2B076, size: 0x8, addend: 0x0, symName: __ZL7file695, symObjAddr: 0xF4E20, symBinAddr: 0xE6C20, symSize: 0x0 }
  - { offsetInCU: 0x9EA3, offset: 0x2B0AA, size: 0x8, addend: 0x0, symName: __ZL7file696, symObjAddr: 0xF5F20, symBinAddr: 0xE7D20, symSize: 0x0 }
  - { offsetInCU: 0x9ED7, offset: 0x2B0DE, size: 0x8, addend: 0x0, symName: __ZL7file697, symObjAddr: 0xF6A60, symBinAddr: 0xE8860, symSize: 0x0 }
  - { offsetInCU: 0x9EF6, offset: 0x2B0FD, size: 0x8, addend: 0x0, symName: __ZL9layouts65, symObjAddr: 0x1B1940, symBinAddr: 0x1A37D0, symSize: 0x0 }
  - { offsetInCU: 0x9F15, offset: 0x2B11C, size: 0x8, addend: 0x0, symName: __ZL7file698, symObjAddr: 0xF75B0, symBinAddr: 0xE93B0, symSize: 0x0 }
  - { offsetInCU: 0x9F34, offset: 0x2B13B, size: 0x8, addend: 0x0, symName: __ZL7file699, symObjAddr: 0xF7900, symBinAddr: 0xE9700, symSize: 0x0 }
  - { offsetInCU: 0x9F68, offset: 0x2B16F, size: 0x8, addend: 0x0, symName: __ZL7file700, symObjAddr: 0xF7C60, symBinAddr: 0xE9A60, symSize: 0x0 }
  - { offsetInCU: 0x9F9B, offset: 0x2B1A2, size: 0x8, addend: 0x0, symName: __ZL7file701, symObjAddr: 0xF7D50, symBinAddr: 0xE9B50, symSize: 0x0 }
  - { offsetInCU: 0x9FCE, offset: 0x2B1D5, size: 0x8, addend: 0x0, symName: __ZL9patches65, symObjAddr: 0xE7A0, symBinAddr: 0x1B1150, symSize: 0x0 }
  - { offsetInCU: 0x9FED, offset: 0x2B1F4, size: 0x8, addend: 0x0, symName: __ZL10patchBuf90, symObjAddr: 0xF7E39, symBinAddr: 0xE9C39, symSize: 0x0 }
  - { offsetInCU: 0xA00C, offset: 0x2B213, size: 0x8, addend: 0x0, symName: __ZL11revisions25, symObjAddr: 0x5AA10, symBinAddr: 0x4C810, symSize: 0x0 }
  - { offsetInCU: 0xA02B, offset: 0x2B232, size: 0x8, addend: 0x0, symName: __ZL11platforms66, symObjAddr: 0x1B19C0, symBinAddr: 0x1A3850, symSize: 0x0 }
  - { offsetInCU: 0xA04A, offset: 0x2B251, size: 0x8, addend: 0x0, symName: __ZL7file702, symObjAddr: 0xF7E40, symBinAddr: 0xE9C40, symSize: 0x0 }
  - { offsetInCU: 0xA07E, offset: 0x2B285, size: 0x8, addend: 0x0, symName: __ZL7file703, symObjAddr: 0xF8250, symBinAddr: 0xEA050, symSize: 0x0 }
  - { offsetInCU: 0xA09D, offset: 0x2B2A4, size: 0x8, addend: 0x0, symName: __ZL7file704, symObjAddr: 0xF83C0, symBinAddr: 0xEA1C0, symSize: 0x0 }
  - { offsetInCU: 0xA0BC, offset: 0x2B2C3, size: 0x8, addend: 0x0, symName: __ZL7file705, symObjAddr: 0xF8580, symBinAddr: 0xEA380, symSize: 0x0 }
  - { offsetInCU: 0xA0DB, offset: 0x2B2E2, size: 0x8, addend: 0x0, symName: __ZL7file706, symObjAddr: 0xF86C0, symBinAddr: 0xEA4C0, symSize: 0x0 }
  - { offsetInCU: 0xA10F, offset: 0x2B316, size: 0x8, addend: 0x0, symName: __ZL7file707, symObjAddr: 0xF8800, symBinAddr: 0xEA600, symSize: 0x0 }
  - { offsetInCU: 0xA12E, offset: 0x2B335, size: 0x8, addend: 0x0, symName: __ZL7file708, symObjAddr: 0xF8950, symBinAddr: 0xEA750, symSize: 0x0 }
  - { offsetInCU: 0xA14D, offset: 0x2B354, size: 0x8, addend: 0x0, symName: __ZL9layouts66, symObjAddr: 0x1B1AB0, symBinAddr: 0x1A3940, symSize: 0x0 }
  - { offsetInCU: 0xA16C, offset: 0x2B373, size: 0x8, addend: 0x0, symName: __ZL7file709, symObjAddr: 0xF8AA0, symBinAddr: 0xEA8A0, symSize: 0x0 }
  - { offsetInCU: 0xA18B, offset: 0x2B392, size: 0x8, addend: 0x0, symName: __ZL7file710, symObjAddr: 0xF8DC0, symBinAddr: 0xEABC0, symSize: 0x0 }
  - { offsetInCU: 0xA1BF, offset: 0x2B3C6, size: 0x8, addend: 0x0, symName: __ZL7file711, symObjAddr: 0xF90E0, symBinAddr: 0xEAEE0, symSize: 0x0 }
  - { offsetInCU: 0xA1DE, offset: 0x2B3E5, size: 0x8, addend: 0x0, symName: __ZL7file712, symObjAddr: 0xF9400, symBinAddr: 0xEB200, symSize: 0x0 }
  - { offsetInCU: 0xA212, offset: 0x2B419, size: 0x8, addend: 0x0, symName: __ZL7file713, symObjAddr: 0xF9810, symBinAddr: 0xEB610, symSize: 0x0 }
  - { offsetInCU: 0xA246, offset: 0x2B44D, size: 0x8, addend: 0x0, symName: __ZL7file714, symObjAddr: 0xF9B30, symBinAddr: 0xEB930, symSize: 0x0 }
  - { offsetInCU: 0xA265, offset: 0x2B46C, size: 0x8, addend: 0x0, symName: __ZL7file715, symObjAddr: 0xF9E50, symBinAddr: 0xEBC50, symSize: 0x0 }
  - { offsetInCU: 0xA299, offset: 0x2B4A0, size: 0x8, addend: 0x0, symName: __ZL7file716, symObjAddr: 0xFA1A0, symBinAddr: 0xEBFA0, symSize: 0x0 }
  - { offsetInCU: 0xA2CD, offset: 0x2B4D4, size: 0x8, addend: 0x0, symName: __ZL7file717, symObjAddr: 0xFA4B0, symBinAddr: 0xEC2B0, symSize: 0x0 }
  - { offsetInCU: 0xA2EC, offset: 0x2B4F3, size: 0x8, addend: 0x0, symName: __ZL7file718, symObjAddr: 0xFA7D0, symBinAddr: 0xEC5D0, symSize: 0x0 }
  - { offsetInCU: 0xA30B, offset: 0x2B512, size: 0x8, addend: 0x0, symName: __ZL9patches66, symObjAddr: 0xE920, symBinAddr: 0x1B12D0, symSize: 0x0 }
  - { offsetInCU: 0xA32A, offset: 0x2B531, size: 0x8, addend: 0x0, symName: __ZL10patchBuf91, symObjAddr: 0xFAAE9, symBinAddr: 0xEC8E9, symSize: 0x0 }
  - { offsetInCU: 0xA349, offset: 0x2B550, size: 0x8, addend: 0x0, symName: __ZL11revisions26, symObjAddr: 0x5AA20, symBinAddr: 0x4C820, symSize: 0x0 }
  - { offsetInCU: 0xA368, offset: 0x2B56F, size: 0x8, addend: 0x0, symName: __ZL11platforms67, symObjAddr: 0x1B1BA0, symBinAddr: 0x1A3A30, symSize: 0x0 }
  - { offsetInCU: 0xA387, offset: 0x2B58E, size: 0x8, addend: 0x0, symName: __ZL7file719, symObjAddr: 0xFAAF0, symBinAddr: 0xEC8F0, symSize: 0x0 }
  - { offsetInCU: 0xA3A6, offset: 0x2B5AD, size: 0x8, addend: 0x0, symName: __ZL7file720, symObjAddr: 0xFAC40, symBinAddr: 0xECA40, symSize: 0x0 }
  - { offsetInCU: 0xA3C5, offset: 0x2B5CC, size: 0x8, addend: 0x0, symName: __ZL7file721, symObjAddr: 0xFAD90, symBinAddr: 0xECB90, symSize: 0x0 }
  - { offsetInCU: 0xA3E4, offset: 0x2B5EB, size: 0x8, addend: 0x0, symName: __ZL9layouts67, symObjAddr: 0x1B1BF0, symBinAddr: 0x1A3A80, symSize: 0x0 }
  - { offsetInCU: 0xA403, offset: 0x2B60A, size: 0x8, addend: 0x0, symName: __ZL7file722, symObjAddr: 0xFAEE0, symBinAddr: 0xECCE0, symSize: 0x0 }
  - { offsetInCU: 0xA437, offset: 0x2B63E, size: 0x8, addend: 0x0, symName: __ZL7file723, symObjAddr: 0xFB4C0, symBinAddr: 0xED2C0, symSize: 0x0 }
  - { offsetInCU: 0xA46B, offset: 0x2B672, size: 0x8, addend: 0x0, symName: __ZL7file724, symObjAddr: 0xFBAA0, symBinAddr: 0xED8A0, symSize: 0x0 }
  - { offsetInCU: 0xA49F, offset: 0x2B6A6, size: 0x8, addend: 0x0, symName: __ZL9patches67, symObjAddr: 0xEA70, symBinAddr: 0x1B1420, symSize: 0x0 }
  - { offsetInCU: 0xA4BE, offset: 0x2B6C5, size: 0x8, addend: 0x0, symName: __ZL10patchBuf92, symObjAddr: 0xFC076, symBinAddr: 0xEDE76, symSize: 0x0 }
  - { offsetInCU: 0xA4DD, offset: 0x2B6E4, size: 0x8, addend: 0x0, symName: __ZL11revisions27, symObjAddr: 0x5AA28, symBinAddr: 0x4C828, symSize: 0x0 }
  - { offsetInCU: 0xA4FC, offset: 0x2B703, size: 0x8, addend: 0x0, symName: __ZL11platforms68, symObjAddr: 0x1B1C40, symBinAddr: 0x1A3AD0, symSize: 0x0 }
  - { offsetInCU: 0xA51B, offset: 0x2B722, size: 0x8, addend: 0x0, symName: __ZL7file725, symObjAddr: 0xFC080, symBinAddr: 0xEDE80, symSize: 0x0 }
  - { offsetInCU: 0xA53A, offset: 0x2B741, size: 0x8, addend: 0x0, symName: __ZL7file726, symObjAddr: 0xFC1C0, symBinAddr: 0xEDFC0, symSize: 0x0 }
  - { offsetInCU: 0xA559, offset: 0x2B760, size: 0x8, addend: 0x0, symName: __ZL7file727, symObjAddr: 0xFC300, symBinAddr: 0xEE100, symSize: 0x0 }
  - { offsetInCU: 0xA578, offset: 0x2B77F, size: 0x8, addend: 0x0, symName: __ZL9layouts68, symObjAddr: 0x1B1C90, symBinAddr: 0x1A3B20, symSize: 0x0 }
  - { offsetInCU: 0xA597, offset: 0x2B79E, size: 0x8, addend: 0x0, symName: __ZL7file728, symObjAddr: 0xFC440, symBinAddr: 0xEE240, symSize: 0x0 }
  - { offsetInCU: 0xA5CB, offset: 0x2B7D2, size: 0x8, addend: 0x0, symName: __ZL7file729, symObjAddr: 0xFC990, symBinAddr: 0xEE790, symSize: 0x0 }
  - { offsetInCU: 0xA5FF, offset: 0x2B806, size: 0x8, addend: 0x0, symName: __ZL7file730, symObjAddr: 0xFCEC0, symBinAddr: 0xEECC0, symSize: 0x0 }
  - { offsetInCU: 0xA632, offset: 0x2B839, size: 0x8, addend: 0x0, symName: __ZL9patches68, symObjAddr: 0xEBF0, symBinAddr: 0x1B15A0, symSize: 0x0 }
  - { offsetInCU: 0xA651, offset: 0x2B858, size: 0x8, addend: 0x0, symName: __ZL10patchBuf93, symObjAddr: 0xFCFA0, symBinAddr: 0xEEDA0, symSize: 0x0 }
  - { offsetInCU: 0xA670, offset: 0x2B877, size: 0x8, addend: 0x0, symName: __ZL11revisions28, symObjAddr: 0x5AA30, symBinAddr: 0x4C830, symSize: 0x0 }
  - { offsetInCU: 0xA68F, offset: 0x2B896, size: 0x8, addend: 0x0, symName: __ZL11platforms69, symObjAddr: 0x1B1CE0, symBinAddr: 0x1A3B70, symSize: 0x0 }
  - { offsetInCU: 0xA6C2, offset: 0x2B8C9, size: 0x8, addend: 0x0, symName: __ZL7file731, symObjAddr: 0xFCFB0, symBinAddr: 0xEEDB0, symSize: 0x0 }
  - { offsetInCU: 0xA6E1, offset: 0x2B8E8, size: 0x8, addend: 0x0, symName: __ZL7file732, symObjAddr: 0xFD1F0, symBinAddr: 0xEEFF0, symSize: 0x0 }
  - { offsetInCU: 0xA715, offset: 0x2B91C, size: 0x8, addend: 0x0, symName: __ZL7file733, symObjAddr: 0xFD390, symBinAddr: 0xEF190, symSize: 0x0 }
  - { offsetInCU: 0xA749, offset: 0x2B950, size: 0x8, addend: 0x0, symName: __ZL7file734, symObjAddr: 0xFD500, symBinAddr: 0xEF300, symSize: 0x0 }
  - { offsetInCU: 0xA77D, offset: 0x2B984, size: 0x8, addend: 0x0, symName: __ZL7file735, symObjAddr: 0xFD6A0, symBinAddr: 0xEF4A0, symSize: 0x0 }
  - { offsetInCU: 0xA79C, offset: 0x2B9A3, size: 0x8, addend: 0x0, symName: __ZL7file736, symObjAddr: 0xFD840, symBinAddr: 0xEF640, symSize: 0x0 }
  - { offsetInCU: 0xA7D0, offset: 0x2B9D7, size: 0x8, addend: 0x0, symName: __ZL7file737, symObjAddr: 0xFDDA0, symBinAddr: 0xEFBA0, symSize: 0x0 }
  - { offsetInCU: 0xA804, offset: 0x2BA0B, size: 0x8, addend: 0x0, symName: __ZL7file738, symObjAddr: 0xFE310, symBinAddr: 0xF0110, symSize: 0x0 }
  - { offsetInCU: 0xA838, offset: 0x2BA3F, size: 0x8, addend: 0x0, symName: __ZL7file739, symObjAddr: 0xFE490, symBinAddr: 0xF0290, symSize: 0x0 }
  - { offsetInCU: 0xA86C, offset: 0x2BA73, size: 0x8, addend: 0x0, symName: __ZL9layouts69, symObjAddr: 0x1B1E00, symBinAddr: 0x1A3C90, symSize: 0x0 }
  - { offsetInCU: 0xA88B, offset: 0x2BA92, size: 0x8, addend: 0x0, symName: __ZL7file740, symObjAddr: 0xFE630, symBinAddr: 0xF0430, symSize: 0x0 }
  - { offsetInCU: 0xA8AA, offset: 0x2BAB1, size: 0x8, addend: 0x0, symName: __ZL7file741, symObjAddr: 0xFE990, symBinAddr: 0xF0790, symSize: 0x0 }
  - { offsetInCU: 0xA8C9, offset: 0x2BAD0, size: 0x8, addend: 0x0, symName: __ZL7file742, symObjAddr: 0xFECF0, symBinAddr: 0xF0AF0, symSize: 0x0 }
  - { offsetInCU: 0xA8E8, offset: 0x2BAEF, size: 0x8, addend: 0x0, symName: __ZL7file743, symObjAddr: 0xFF050, symBinAddr: 0xF0E50, symSize: 0x0 }
  - { offsetInCU: 0xA907, offset: 0x2BB0E, size: 0x8, addend: 0x0, symName: __ZL7file744, symObjAddr: 0xFF3B0, symBinAddr: 0xF11B0, symSize: 0x0 }
  - { offsetInCU: 0xA93B, offset: 0x2BB42, size: 0x8, addend: 0x0, symName: __ZL7file745, symObjAddr: 0xFF700, symBinAddr: 0xF1500, symSize: 0x0 }
  - { offsetInCU: 0xA96F, offset: 0x2BB76, size: 0x8, addend: 0x0, symName: __ZL7file746, symObjAddr: 0xFFA50, symBinAddr: 0xF1850, symSize: 0x0 }
  - { offsetInCU: 0xA98E, offset: 0x2BB95, size: 0x8, addend: 0x0, symName: __ZL7file747, symObjAddr: 0xFFDA0, symBinAddr: 0xF1BA0, symSize: 0x0 }
  - { offsetInCU: 0xA9AD, offset: 0x2BBB4, size: 0x8, addend: 0x0, symName: __ZL7file748, symObjAddr: 0x100100, symBinAddr: 0xF1F00, symSize: 0x0 }
  - { offsetInCU: 0xA9E1, offset: 0x2BBE8, size: 0x8, addend: 0x0, symName: __ZL7file749, symObjAddr: 0x100810, symBinAddr: 0xF2610, symSize: 0x0 }
  - { offsetInCU: 0xAA15, offset: 0x2BC1C, size: 0x8, addend: 0x0, symName: __ZL7file750, symObjAddr: 0x100F50, symBinAddr: 0xF2D50, symSize: 0x0 }
  - { offsetInCU: 0xAA49, offset: 0x2BC50, size: 0x8, addend: 0x0, symName: __ZL7file751, symObjAddr: 0x101580, symBinAddr: 0xF3380, symSize: 0x0 }
  - { offsetInCU: 0xAA68, offset: 0x2BC6F, size: 0x8, addend: 0x0, symName: __ZL9patches69, symObjAddr: 0xEDD0, symBinAddr: 0x1B1780, symSize: 0x0 }
  - { offsetInCU: 0xAA87, offset: 0x2BC8E, size: 0x8, addend: 0x0, symName: __ZL10patchBuf94, symObjAddr: 0x1018D1, symBinAddr: 0xF36D1, symSize: 0x0 }
  - { offsetInCU: 0xAAA6, offset: 0x2BCAD, size: 0x8, addend: 0x0, symName: __ZL11platforms70, symObjAddr: 0x1B1F20, symBinAddr: 0x1A3DB0, symSize: 0x0 }
  - { offsetInCU: 0xAAC5, offset: 0x2BCCC, size: 0x8, addend: 0x0, symName: __ZL7file752, symObjAddr: 0x1018E0, symBinAddr: 0xF36E0, symSize: 0x0 }
  - { offsetInCU: 0xAAE4, offset: 0x2BCEB, size: 0x8, addend: 0x0, symName: __ZL7file753, symObjAddr: 0x101A20, symBinAddr: 0xF3820, symSize: 0x0 }
  - { offsetInCU: 0xAB03, offset: 0x2BD0A, size: 0x8, addend: 0x0, symName: __ZL7file754, symObjAddr: 0x101B60, symBinAddr: 0xF3960, symSize: 0x0 }
  - { offsetInCU: 0xAB22, offset: 0x2BD29, size: 0x8, addend: 0x0, symName: __ZL9layouts70, symObjAddr: 0x1B1F70, symBinAddr: 0x1A3E00, symSize: 0x0 }
  - { offsetInCU: 0xAB41, offset: 0x2BD48, size: 0x8, addend: 0x0, symName: __ZL7file755, symObjAddr: 0x101CA0, symBinAddr: 0xF3AA0, symSize: 0x0 }
  - { offsetInCU: 0xAB75, offset: 0x2BD7C, size: 0x8, addend: 0x0, symName: __ZL7file756, symObjAddr: 0x102360, symBinAddr: 0xF4160, symSize: 0x0 }
  - { offsetInCU: 0xAB94, offset: 0x2BD9B, size: 0x8, addend: 0x0, symName: __ZL7file757, symObjAddr: 0x1029A0, symBinAddr: 0xF47A0, symSize: 0x0 }
  - { offsetInCU: 0xABB3, offset: 0x2BDBA, size: 0x8, addend: 0x0, symName: __ZL9patches70, symObjAddr: 0xEF20, symBinAddr: 0x1B18D0, symSize: 0x0 }
  - { offsetInCU: 0xABD2, offset: 0x2BDD9, size: 0x8, addend: 0x0, symName: __ZL10patchBuf95, symObjAddr: 0x102FD4, symBinAddr: 0xF4DD4, symSize: 0x0 }
  - { offsetInCU: 0xABF1, offset: 0x2BDF8, size: 0x8, addend: 0x0, symName: __ZL11revisions29, symObjAddr: 0x5AA34, symBinAddr: 0x4C834, symSize: 0x0 }
  - { offsetInCU: 0xAC10, offset: 0x2BE17, size: 0x8, addend: 0x0, symName: __ZL11platforms71, symObjAddr: 0x1B1FC0, symBinAddr: 0x1A3E50, symSize: 0x0 }
  - { offsetInCU: 0xAC2F, offset: 0x2BE36, size: 0x8, addend: 0x0, symName: __ZL7file758, symObjAddr: 0x102FE0, symBinAddr: 0xF4DE0, symSize: 0x0 }
  - { offsetInCU: 0xAC4E, offset: 0x2BE55, size: 0x8, addend: 0x0, symName: __ZL7file759, symObjAddr: 0x1033F0, symBinAddr: 0xF51F0, symSize: 0x0 }
  - { offsetInCU: 0xAC6D, offset: 0x2BE74, size: 0x8, addend: 0x0, symName: __ZL7file760, symObjAddr: 0x103530, symBinAddr: 0xF5330, symSize: 0x0 }
  - { offsetInCU: 0xAC8C, offset: 0x2BE93, size: 0x8, addend: 0x0, symName: __ZL9layouts71, symObjAddr: 0x1B2040, symBinAddr: 0x1A3ED0, symSize: 0x0 }
  - { offsetInCU: 0xACAB, offset: 0x2BEB2, size: 0x8, addend: 0x0, symName: __ZL7file761, symObjAddr: 0x103690, symBinAddr: 0xF5490, symSize: 0x0 }
  - { offsetInCU: 0xACCA, offset: 0x2BED1, size: 0x8, addend: 0x0, symName: __ZL7file762, symObjAddr: 0x1039B0, symBinAddr: 0xF57B0, symSize: 0x0 }
  - { offsetInCU: 0xACE9, offset: 0x2BEF0, size: 0x8, addend: 0x0, symName: __ZL7file763, symObjAddr: 0x103CD0, symBinAddr: 0xF5AD0, symSize: 0x0 }
  - { offsetInCU: 0xAD08, offset: 0x2BF0F, size: 0x8, addend: 0x0, symName: __ZL7file764, symObjAddr: 0x103FF0, symBinAddr: 0xF5DF0, symSize: 0x0 }
  - { offsetInCU: 0xAD27, offset: 0x2BF2E, size: 0x8, addend: 0x0, symName: __ZL7file765, symObjAddr: 0x104310, symBinAddr: 0xF6110, symSize: 0x0 }
  - { offsetInCU: 0xAD46, offset: 0x2BF4D, size: 0x8, addend: 0x0, symName: __ZL9patches71, symObjAddr: 0xF100, symBinAddr: 0x1B1AB0, symSize: 0x0 }
  - { offsetInCU: 0xAD65, offset: 0x2BF6C, size: 0x8, addend: 0x0, symName: __ZL10patchBuf96, symObjAddr: 0x10447F, symBinAddr: 0xF627F, symSize: 0x0 }
  - { offsetInCU: 0xAD84, offset: 0x2BF8B, size: 0x8, addend: 0x0, symName: __ZL11revisions30, symObjAddr: 0x5AA38, symBinAddr: 0x4C838, symSize: 0x0 }
  - { offsetInCU: 0xADA3, offset: 0x2BFAA, size: 0x8, addend: 0x0, symName: __ZL11platforms72, symObjAddr: 0x1B20C0, symBinAddr: 0x1A3F50, symSize: 0x0 }
  - { offsetInCU: 0xADC2, offset: 0x2BFC9, size: 0x8, addend: 0x0, symName: __ZL7file766, symObjAddr: 0x104490, symBinAddr: 0xF6290, symSize: 0x0 }
  - { offsetInCU: 0xADE1, offset: 0x2BFE8, size: 0x8, addend: 0x0, symName: __ZL7file767, symObjAddr: 0x1045F0, symBinAddr: 0xF63F0, symSize: 0x0 }
  - { offsetInCU: 0xAE00, offset: 0x2C007, size: 0x8, addend: 0x0, symName: __ZL7file768, symObjAddr: 0x104740, symBinAddr: 0xF6540, symSize: 0x0 }
  - { offsetInCU: 0xAE1F, offset: 0x2C026, size: 0x8, addend: 0x0, symName: __ZL7file769, symObjAddr: 0x104890, symBinAddr: 0xF6690, symSize: 0x0 }
  - { offsetInCU: 0xAE3E, offset: 0x2C045, size: 0x8, addend: 0x0, symName: __ZL7file770, symObjAddr: 0x1049E0, symBinAddr: 0xF67E0, symSize: 0x0 }
  - { offsetInCU: 0xAE5D, offset: 0x2C064, size: 0x8, addend: 0x0, symName: __ZL9layouts72, symObjAddr: 0x1B2140, symBinAddr: 0x1A3FD0, symSize: 0x0 }
  - { offsetInCU: 0xAE7C, offset: 0x2C083, size: 0x8, addend: 0x0, symName: __ZL7file771, symObjAddr: 0x104B20, symBinAddr: 0xF6920, symSize: 0x0 }
  - { offsetInCU: 0xAEB0, offset: 0x2C0B7, size: 0x8, addend: 0x0, symName: __ZL7file772, symObjAddr: 0x105150, symBinAddr: 0xF6F50, symSize: 0x0 }
  - { offsetInCU: 0xAECF, offset: 0x2C0D6, size: 0x8, addend: 0x0, symName: __ZL7file773, symObjAddr: 0x105680, symBinAddr: 0xF7480, symSize: 0x0 }
  - { offsetInCU: 0xAEEE, offset: 0x2C0F5, size: 0x8, addend: 0x0, symName: __ZL7file774, symObjAddr: 0x105BB0, symBinAddr: 0xF79B0, symSize: 0x0 }
  - { offsetInCU: 0xAF22, offset: 0x2C129, size: 0x8, addend: 0x0, symName: __ZL7file775, symObjAddr: 0x1060E0, symBinAddr: 0xF7EE0, symSize: 0x0 }
  - { offsetInCU: 0xAF41, offset: 0x2C148, size: 0x8, addend: 0x0, symName: __ZL9patches72, symObjAddr: 0xF250, symBinAddr: 0x1B1C00, symSize: 0x0 }
  - { offsetInCU: 0xAF60, offset: 0x2C167, size: 0x8, addend: 0x0, symName: __ZL10patchBuf97, symObjAddr: 0x10660F, symBinAddr: 0xF840F, symSize: 0x0 }
  - { offsetInCU: 0xAF7F, offset: 0x2C186, size: 0x8, addend: 0x0, symName: __ZL11platforms73, symObjAddr: 0x1B21C0, symBinAddr: 0x1A4050, symSize: 0x0 }
  - { offsetInCU: 0xAFB2, offset: 0x2C1B9, size: 0x8, addend: 0x0, symName: __ZL7file776, symObjAddr: 0x106620, symBinAddr: 0xF8420, symSize: 0x0 }
  - { offsetInCU: 0xAFD1, offset: 0x2C1D8, size: 0x8, addend: 0x0, symName: __ZL7file777, symObjAddr: 0x106770, symBinAddr: 0xF8570, symSize: 0x0 }
  - { offsetInCU: 0xB005, offset: 0x2C20C, size: 0x8, addend: 0x0, symName: __ZL7file778, symObjAddr: 0x1072E0, symBinAddr: 0xF90E0, symSize: 0x0 }
  - { offsetInCU: 0xB038, offset: 0x2C23F, size: 0x8, addend: 0x0, symName: __ZL7file779, symObjAddr: 0x107390, symBinAddr: 0xF9190, symSize: 0x0 }
  - { offsetInCU: 0xB057, offset: 0x2C25E, size: 0x8, addend: 0x0, symName: __ZL7file780, symObjAddr: 0x1074D0, symBinAddr: 0xF92D0, symSize: 0x0 }
  - { offsetInCU: 0xB076, offset: 0x2C27D, size: 0x8, addend: 0x0, symName: __ZL7file781, symObjAddr: 0x107620, symBinAddr: 0xF9420, symSize: 0x0 }
  - { offsetInCU: 0xB095, offset: 0x2C29C, size: 0x8, addend: 0x0, symName: __ZL7file782, symObjAddr: 0x107770, symBinAddr: 0xF9570, symSize: 0x0 }
  - { offsetInCU: 0xB0B4, offset: 0x2C2BB, size: 0x8, addend: 0x0, symName: __ZL7file783, symObjAddr: 0x1078C0, symBinAddr: 0xF96C0, symSize: 0x0 }
  - { offsetInCU: 0xB0D3, offset: 0x2C2DA, size: 0x8, addend: 0x0, symName: __ZL7file784, symObjAddr: 0x107A10, symBinAddr: 0xF9810, symSize: 0x0 }
  - { offsetInCU: 0xB0F2, offset: 0x2C2F9, size: 0x8, addend: 0x0, symName: __ZL9layouts73, symObjAddr: 0x1B22A0, symBinAddr: 0x1A4130, symSize: 0x0 }
  - { offsetInCU: 0xB111, offset: 0x2C318, size: 0x8, addend: 0x0, symName: __ZL7file785, symObjAddr: 0x107B60, symBinAddr: 0xF9960, symSize: 0x0 }
  - { offsetInCU: 0xB130, offset: 0x2C337, size: 0x8, addend: 0x0, symName: __ZL7file786, symObjAddr: 0x108120, symBinAddr: 0xF9F20, symSize: 0x0 }
  - { offsetInCU: 0xB14F, offset: 0x2C356, size: 0x8, addend: 0x0, symName: __ZL7file787, symObjAddr: 0x1086F0, symBinAddr: 0xFA4F0, symSize: 0x0 }
  - { offsetInCU: 0xB182, offset: 0x2C389, size: 0x8, addend: 0x0, symName: __ZL7file788, symObjAddr: 0x108790, symBinAddr: 0xFA590, symSize: 0x0 }
  - { offsetInCU: 0xB1B6, offset: 0x2C3BD, size: 0x8, addend: 0x0, symName: __ZL7file789, symObjAddr: 0x108DB0, symBinAddr: 0xFABB0, symSize: 0x0 }
  - { offsetInCU: 0xB1EA, offset: 0x2C3F1, size: 0x8, addend: 0x0, symName: __ZL7file790, symObjAddr: 0x1093D0, symBinAddr: 0xFB1D0, symSize: 0x0 }
  - { offsetInCU: 0xB21E, offset: 0x2C425, size: 0x8, addend: 0x0, symName: __ZL7file791, symObjAddr: 0x1099F0, symBinAddr: 0xFB7F0, symSize: 0x0 }
  - { offsetInCU: 0xB23D, offset: 0x2C444, size: 0x8, addend: 0x0, symName: __ZL7file792, symObjAddr: 0x10A020, symBinAddr: 0xFBE20, symSize: 0x0 }
  - { offsetInCU: 0xB271, offset: 0x2C478, size: 0x8, addend: 0x0, symName: __ZL7file793, symObjAddr: 0x10A5E0, symBinAddr: 0xFC3E0, symSize: 0x0 }
  - { offsetInCU: 0xB2A5, offset: 0x2C4AC, size: 0x8, addend: 0x0, symName: __ZL9patches73, symObjAddr: 0xF3A0, symBinAddr: 0x1B1D50, symSize: 0x0 }
  - { offsetInCU: 0xB2C4, offset: 0x2C4CB, size: 0x8, addend: 0x0, symName: __ZL10patchBuf98, symObjAddr: 0x10ABA6, symBinAddr: 0xFC9A6, symSize: 0x0 }
  - { offsetInCU: 0xB2E3, offset: 0x2C4EA, size: 0x8, addend: 0x0, symName: __ZL11platforms74, symObjAddr: 0x1B2380, symBinAddr: 0x1A4210, symSize: 0x0 }
  - { offsetInCU: 0xB302, offset: 0x2C509, size: 0x8, addend: 0x0, symName: __ZL7file794, symObjAddr: 0x10ABB0, symBinAddr: 0xFC9B0, symSize: 0x0 }
  - { offsetInCU: 0xB321, offset: 0x2C528, size: 0x8, addend: 0x0, symName: __ZL7file795, symObjAddr: 0x10ACF0, symBinAddr: 0xFCAF0, symSize: 0x0 }
  - { offsetInCU: 0xB340, offset: 0x2C547, size: 0x8, addend: 0x0, symName: __ZL7file796, symObjAddr: 0x10AE60, symBinAddr: 0xFCC60, symSize: 0x0 }
  - { offsetInCU: 0xB374, offset: 0x2C57B, size: 0x8, addend: 0x0, symName: __ZL7file797, symObjAddr: 0x10AFF0, symBinAddr: 0xFCDF0, symSize: 0x0 }
  - { offsetInCU: 0xB393, offset: 0x2C59A, size: 0x8, addend: 0x0, symName: __ZL7file798, symObjAddr: 0x10B130, symBinAddr: 0xFCF30, symSize: 0x0 }
  - { offsetInCU: 0xB3B2, offset: 0x2C5B9, size: 0x8, addend: 0x0, symName: __ZL7file799, symObjAddr: 0x10B270, symBinAddr: 0xFD070, symSize: 0x0 }
  - { offsetInCU: 0xB3D1, offset: 0x2C5D8, size: 0x8, addend: 0x0, symName: __ZL7file800, symObjAddr: 0x10B3F0, symBinAddr: 0xFD1F0, symSize: 0x0 }
  - { offsetInCU: 0xB405, offset: 0x2C60C, size: 0x8, addend: 0x0, symName: __ZL7file801, symObjAddr: 0x10B550, symBinAddr: 0xFD350, symSize: 0x0 }
  - { offsetInCU: 0xB424, offset: 0x2C62B, size: 0x8, addend: 0x0, symName: __ZL7file802, symObjAddr: 0x10B6C0, symBinAddr: 0xFD4C0, symSize: 0x0 }
  - { offsetInCU: 0xB443, offset: 0x2C64A, size: 0x8, addend: 0x0, symName: __ZL7file803, symObjAddr: 0x10B810, symBinAddr: 0xFD610, symSize: 0x0 }
  - { offsetInCU: 0xB462, offset: 0x2C669, size: 0x8, addend: 0x0, symName: __ZL7file804, symObjAddr: 0x10B960, symBinAddr: 0xFD760, symSize: 0x0 }
  - { offsetInCU: 0xB481, offset: 0x2C688, size: 0x8, addend: 0x0, symName: __ZL9layouts74, symObjAddr: 0x1B2490, symBinAddr: 0x1A4320, symSize: 0x0 }
  - { offsetInCU: 0xB4A0, offset: 0x2C6A7, size: 0x8, addend: 0x0, symName: __ZL7file805, symObjAddr: 0x10BAB0, symBinAddr: 0xFD8B0, symSize: 0x0 }
  - { offsetInCU: 0xB4BF, offset: 0x2C6C6, size: 0x8, addend: 0x0, symName: __ZL7file806, symObjAddr: 0x10C0E0, symBinAddr: 0xFDEE0, symSize: 0x0 }
  - { offsetInCU: 0xB4DE, offset: 0x2C6E5, size: 0x8, addend: 0x0, symName: __ZL7file807, symObjAddr: 0x10C700, symBinAddr: 0xFE500, symSize: 0x0 }
  - { offsetInCU: 0xB512, offset: 0x2C719, size: 0x8, addend: 0x0, symName: __ZL7file808, symObjAddr: 0x10CD60, symBinAddr: 0xFEB60, symSize: 0x0 }
  - { offsetInCU: 0xB531, offset: 0x2C738, size: 0x8, addend: 0x0, symName: __ZL7file809, symObjAddr: 0x10D390, symBinAddr: 0xFF190, symSize: 0x0 }
  - { offsetInCU: 0xB550, offset: 0x2C757, size: 0x8, addend: 0x0, symName: __ZL7file810, symObjAddr: 0x10D9C0, symBinAddr: 0xFF7C0, symSize: 0x0 }
  - { offsetInCU: 0xB584, offset: 0x2C78B, size: 0x8, addend: 0x0, symName: __ZL7file811, symObjAddr: 0x10E900, symBinAddr: 0x100700, symSize: 0x0 }
  - { offsetInCU: 0xB5A3, offset: 0x2C7AA, size: 0x8, addend: 0x0, symName: __ZL7file812, symObjAddr: 0x10EF30, symBinAddr: 0x100D30, symSize: 0x0 }
  - { offsetInCU: 0xB5C2, offset: 0x2C7C9, size: 0x8, addend: 0x0, symName: __ZL7file813, symObjAddr: 0x10F550, symBinAddr: 0x101350, symSize: 0x0 }
  - { offsetInCU: 0xB5E1, offset: 0x2C7E8, size: 0x8, addend: 0x0, symName: __ZL7file814, symObjAddr: 0x10FB80, symBinAddr: 0x101980, symSize: 0x0 }
  - { offsetInCU: 0xB615, offset: 0x2C81C, size: 0x8, addend: 0x0, symName: __ZL7file815, symObjAddr: 0x110140, symBinAddr: 0x101F40, symSize: 0x0 }
  - { offsetInCU: 0xB649, offset: 0x2C850, size: 0x8, addend: 0x0, symName: __ZL9patches74, symObjAddr: 0xF520, symBinAddr: 0x1B1ED0, symSize: 0x0 }
  - { offsetInCU: 0xB668, offset: 0x2C86F, size: 0x8, addend: 0x0, symName: __ZL10patchBuf99, symObjAddr: 0x110765, symBinAddr: 0x102565, symSize: 0x0 }
  - { offsetInCU: 0xB687, offset: 0x2C88E, size: 0x8, addend: 0x0, symName: __ZL11revisions31, symObjAddr: 0x5AA3C, symBinAddr: 0x4C83C, symSize: 0x0 }
  - { offsetInCU: 0xB6A6, offset: 0x2C8AD, size: 0x8, addend: 0x0, symName: __ZL11platforms75, symObjAddr: 0x1B25A0, symBinAddr: 0x1A4430, symSize: 0x0 }
  - { offsetInCU: 0xB6D9, offset: 0x2C8E0, size: 0x8, addend: 0x0, symName: __ZL7file816, symObjAddr: 0x110770, symBinAddr: 0x102570, symSize: 0x0 }
  - { offsetInCU: 0xB6F8, offset: 0x2C8FF, size: 0x8, addend: 0x0, symName: __ZL7file817, symObjAddr: 0x1109B0, symBinAddr: 0x1027B0, symSize: 0x0 }
  - { offsetInCU: 0xB72C, offset: 0x2C933, size: 0x8, addend: 0x0, symName: __ZL7file818, symObjAddr: 0x110B90, symBinAddr: 0x102990, symSize: 0x0 }
  - { offsetInCU: 0xB760, offset: 0x2C967, size: 0x8, addend: 0x0, symName: __ZL7file819, symObjAddr: 0x1117C0, symBinAddr: 0x1035C0, symSize: 0x0 }
  - { offsetInCU: 0xB77F, offset: 0x2C986, size: 0x8, addend: 0x0, symName: __ZL7file820, symObjAddr: 0x111940, symBinAddr: 0x103740, symSize: 0x0 }
  - { offsetInCU: 0xB79E, offset: 0x2C9A5, size: 0x8, addend: 0x0, symName: __ZL7file821, symObjAddr: 0x111AD0, symBinAddr: 0x1038D0, symSize: 0x0 }
  - { offsetInCU: 0xB7BD, offset: 0x2C9C4, size: 0x8, addend: 0x0, symName: __ZL7file822, symObjAddr: 0x111C10, symBinAddr: 0x103A10, symSize: 0x0 }
  - { offsetInCU: 0xB7DC, offset: 0x2C9E3, size: 0x8, addend: 0x0, symName: __ZL7file823, symObjAddr: 0x111D70, symBinAddr: 0x103B70, symSize: 0x0 }
  - { offsetInCU: 0xB7FB, offset: 0x2CA02, size: 0x8, addend: 0x0, symName: __ZL7file824, symObjAddr: 0x111ED0, symBinAddr: 0x103CD0, symSize: 0x0 }
  - { offsetInCU: 0xB82F, offset: 0x2CA36, size: 0x8, addend: 0x0, symName: __ZL7file825, symObjAddr: 0x112090, symBinAddr: 0x103E90, symSize: 0x0 }
  - { offsetInCU: 0xB84E, offset: 0x2CA55, size: 0x8, addend: 0x0, symName: __ZL7file826, symObjAddr: 0x112200, symBinAddr: 0x104000, symSize: 0x0 }
  - { offsetInCU: 0xB86D, offset: 0x2CA74, size: 0x8, addend: 0x0, symName: __ZL9layouts75, symObjAddr: 0x1B2750, symBinAddr: 0x1A45E0, symSize: 0x0 }
  - { offsetInCU: 0xB88C, offset: 0x2CA93, size: 0x8, addend: 0x0, symName: __ZL7file827, symObjAddr: 0x112390, symBinAddr: 0x104190, symSize: 0x0 }
  - { offsetInCU: 0xB8AB, offset: 0x2CAB2, size: 0x8, addend: 0x0, symName: __ZL7file828, symObjAddr: 0x112790, symBinAddr: 0x104590, symSize: 0x0 }
  - { offsetInCU: 0xB8CA, offset: 0x2CAD1, size: 0x8, addend: 0x0, symName: __ZL7file829, symObjAddr: 0x112B90, symBinAddr: 0x104990, symSize: 0x0 }
  - { offsetInCU: 0xB8E9, offset: 0x2CAF0, size: 0x8, addend: 0x0, symName: __ZL7file830, symObjAddr: 0x112F90, symBinAddr: 0x104D90, symSize: 0x0 }
  - { offsetInCU: 0xB91D, offset: 0x2CB24, size: 0x8, addend: 0x0, symName: __ZL7file831, symObjAddr: 0x1132D0, symBinAddr: 0x1050D0, symSize: 0x0 }
  - { offsetInCU: 0xB951, offset: 0x2CB58, size: 0x8, addend: 0x0, symName: __ZL7file832, symObjAddr: 0x113610, symBinAddr: 0x105410, symSize: 0x0 }
  - { offsetInCU: 0xB970, offset: 0x2CB77, size: 0x8, addend: 0x0, symName: __ZL7file833, symObjAddr: 0x1139B0, symBinAddr: 0x1057B0, symSize: 0x0 }
  - { offsetInCU: 0xB98F, offset: 0x2CB96, size: 0x8, addend: 0x0, symName: __ZL7file834, symObjAddr: 0x113DB0, symBinAddr: 0x105BB0, symSize: 0x0 }
  - { offsetInCU: 0xB9C3, offset: 0x2CBCA, size: 0x8, addend: 0x0, symName: __ZL7file835, symObjAddr: 0x114190, symBinAddr: 0x105F90, symSize: 0x0 }
  - { offsetInCU: 0xB9E2, offset: 0x2CBE9, size: 0x8, addend: 0x0, symName: __ZL7file836, symObjAddr: 0x1144B0, symBinAddr: 0x1062B0, symSize: 0x0 }
  - { offsetInCU: 0xBA16, offset: 0x2CC1D, size: 0x8, addend: 0x0, symName: __ZL7file837, symObjAddr: 0x114820, symBinAddr: 0x106620, symSize: 0x0 }
  - { offsetInCU: 0xBA4A, offset: 0x2CC51, size: 0x8, addend: 0x0, symName: __ZL7file838, symObjAddr: 0x114B30, symBinAddr: 0x106930, symSize: 0x0 }
  - { offsetInCU: 0xBA69, offset: 0x2CC70, size: 0x8, addend: 0x0, symName: __ZL7file839, symObjAddr: 0x114C00, symBinAddr: 0x106A00, symSize: 0x0 }
  - { offsetInCU: 0xBA9D, offset: 0x2CCA4, size: 0x8, addend: 0x0, symName: __ZL7file840, symObjAddr: 0x114F00, symBinAddr: 0x106D00, symSize: 0x0 }
  - { offsetInCU: 0xBAD1, offset: 0x2CCD8, size: 0x8, addend: 0x0, symName: __ZL7file841, symObjAddr: 0x115260, symBinAddr: 0x107060, symSize: 0x0 }
  - { offsetInCU: 0xBB05, offset: 0x2CD0C, size: 0x8, addend: 0x0, symName: __ZL7file842, symObjAddr: 0x1156F0, symBinAddr: 0x1074F0, symSize: 0x0 }
  - { offsetInCU: 0xBB39, offset: 0x2CD40, size: 0x8, addend: 0x0, symName: __ZL7file843, symObjAddr: 0x115B80, symBinAddr: 0x107980, symSize: 0x0 }
  - { offsetInCU: 0xBB58, offset: 0x2CD5F, size: 0x8, addend: 0x0, symName: __ZL7file844, symObjAddr: 0x115EC0, symBinAddr: 0x107CC0, symSize: 0x0 }
  - { offsetInCU: 0xBB8C, offset: 0x2CD93, size: 0x8, addend: 0x0, symName: __ZL9patches75, symObjAddr: 0xF670, symBinAddr: 0x1B2020, symSize: 0x0 }
  - { offsetInCU: 0xBBAB, offset: 0x2CDB2, size: 0x8, addend: 0x0, symName: __ZL11patchBuf100, symObjAddr: 0x1162C0, symBinAddr: 0x1080C0, symSize: 0x0 }
  - { offsetInCU: 0xBBCA, offset: 0x2CDD1, size: 0x8, addend: 0x0, symName: __ZL11platforms76, symObjAddr: 0x1B2900, symBinAddr: 0x1A4790, symSize: 0x0 }
  - { offsetInCU: 0xBBE9, offset: 0x2CDF0, size: 0x8, addend: 0x0, symName: __ZL7file845, symObjAddr: 0x1162D0, symBinAddr: 0x1080D0, symSize: 0x0 }
  - { offsetInCU: 0xBC08, offset: 0x2CE0F, size: 0x8, addend: 0x0, symName: __ZL7file846, symObjAddr: 0x116410, symBinAddr: 0x108210, symSize: 0x0 }
  - { offsetInCU: 0xBC27, offset: 0x2CE2E, size: 0x8, addend: 0x0, symName: __ZL7file847, symObjAddr: 0x116550, symBinAddr: 0x108350, symSize: 0x0 }
  - { offsetInCU: 0xBC46, offset: 0x2CE4D, size: 0x8, addend: 0x0, symName: __ZL9layouts76, symObjAddr: 0x1B2950, symBinAddr: 0x1A47E0, symSize: 0x0 }
  - { offsetInCU: 0xBC65, offset: 0x2CE6C, size: 0x8, addend: 0x0, symName: __ZL7file848, symObjAddr: 0x116690, symBinAddr: 0x108490, symSize: 0x0 }
  - { offsetInCU: 0xBC99, offset: 0x2CEA0, size: 0x8, addend: 0x0, symName: __ZL7file849, symObjAddr: 0x116D60, symBinAddr: 0x108B60, symSize: 0x0 }
  - { offsetInCU: 0xBCCD, offset: 0x2CED4, size: 0x8, addend: 0x0, symName: __ZL7file850, symObjAddr: 0x117D40, symBinAddr: 0x109B40, symSize: 0x0 }
  - { offsetInCU: 0xBD01, offset: 0x2CF08, size: 0x8, addend: 0x0, symName: __ZL9patches76, symObjAddr: 0xF7C0, symBinAddr: 0x1B2170, symSize: 0x0 }
  - { offsetInCU: 0xBD20, offset: 0x2CF27, size: 0x8, addend: 0x0, symName: __ZL11patchBuf101, symObjAddr: 0x118D1A, symBinAddr: 0x10AB1A, symSize: 0x0 }
  - { offsetInCU: 0xBD3F, offset: 0x2CF46, size: 0x8, addend: 0x0, symName: __ZL11platforms77, symObjAddr: 0x1B29A0, symBinAddr: 0x1A4830, symSize: 0x0 }
  - { offsetInCU: 0xBD5E, offset: 0x2CF65, size: 0x8, addend: 0x0, symName: __ZL7file851, symObjAddr: 0x118D20, symBinAddr: 0x10AB20, symSize: 0x0 }
  - { offsetInCU: 0xBD7D, offset: 0x2CF84, size: 0x8, addend: 0x0, symName: __ZL7file852, symObjAddr: 0x118E80, symBinAddr: 0x10AC80, symSize: 0x0 }
  - { offsetInCU: 0xBD9C, offset: 0x2CFA3, size: 0x8, addend: 0x0, symName: __ZL9layouts77, symObjAddr: 0x1B29D0, symBinAddr: 0x1A4860, symSize: 0x0 }
  - { offsetInCU: 0xBDBB, offset: 0x2CFC2, size: 0x8, addend: 0x0, symName: __ZL7file853, symObjAddr: 0x118FE0, symBinAddr: 0x10ADE0, symSize: 0x0 }
  - { offsetInCU: 0xBDEF, offset: 0x2CFF6, size: 0x8, addend: 0x0, symName: __ZL7file854, symObjAddr: 0x119580, symBinAddr: 0x10B380, symSize: 0x0 }
  - { offsetInCU: 0xBE23, offset: 0x2D02A, size: 0x8, addend: 0x0, symName: __ZL9patches77, symObjAddr: 0xF9A0, symBinAddr: 0x1B2350, symSize: 0x0 }
  - { offsetInCU: 0xBE42, offset: 0x2D049, size: 0x8, addend: 0x0, symName: __ZL11patchBuf102, symObjAddr: 0x119BD0, symBinAddr: 0x10B9D0, symSize: 0x0 }
  - { offsetInCU: 0xBE61, offset: 0x2D068, size: 0x8, addend: 0x0, symName: __ZL11platforms78, symObjAddr: 0x1B2A00, symBinAddr: 0x1A4890, symSize: 0x0 }
  - { offsetInCU: 0xBE80, offset: 0x2D087, size: 0x8, addend: 0x0, symName: __ZL7file855, symObjAddr: 0x119BE0, symBinAddr: 0x10B9E0, symSize: 0x0 }
  - { offsetInCU: 0xBE9F, offset: 0x2D0A6, size: 0x8, addend: 0x0, symName: __ZL7file856, symObjAddr: 0x119D20, symBinAddr: 0x10BB20, symSize: 0x0 }
  - { offsetInCU: 0xBEBE, offset: 0x2D0C5, size: 0x8, addend: 0x0, symName: __ZL7file857, symObjAddr: 0x119E70, symBinAddr: 0x10BC70, symSize: 0x0 }
  - { offsetInCU: 0xBEDD, offset: 0x2D0E4, size: 0x8, addend: 0x0, symName: __ZL7file858, symObjAddr: 0x119FF0, symBinAddr: 0x10BDF0, symSize: 0x0 }
  - { offsetInCU: 0xBEFC, offset: 0x2D103, size: 0x8, addend: 0x0, symName: __ZL7file859, symObjAddr: 0x11A130, symBinAddr: 0x10BF30, symSize: 0x0 }
  - { offsetInCU: 0xBF1B, offset: 0x2D122, size: 0x8, addend: 0x0, symName: __ZL9layouts78, symObjAddr: 0x1B2A80, symBinAddr: 0x1A4910, symSize: 0x0 }
  - { offsetInCU: 0xBF3A, offset: 0x2D141, size: 0x8, addend: 0x0, symName: __ZL7file860, symObjAddr: 0x11A270, symBinAddr: 0x10C070, symSize: 0x0 }
  - { offsetInCU: 0xBF59, offset: 0x2D160, size: 0x8, addend: 0x0, symName: __ZL7file861, symObjAddr: 0x11A8C0, symBinAddr: 0x10C6C0, symSize: 0x0 }
  - { offsetInCU: 0xBF8D, offset: 0x2D194, size: 0x8, addend: 0x0, symName: __ZL7file862, symObjAddr: 0x11AFF0, symBinAddr: 0x10CDF0, symSize: 0x0 }
  - { offsetInCU: 0xBFC1, offset: 0x2D1C8, size: 0x8, addend: 0x0, symName: __ZL7file863, symObjAddr: 0x11B900, symBinAddr: 0x10D700, symSize: 0x0 }
  - { offsetInCU: 0xBFF5, offset: 0x2D1FC, size: 0x8, addend: 0x0, symName: __ZL7file864, symObjAddr: 0x11C030, symBinAddr: 0x10DE30, symSize: 0x0 }
  - { offsetInCU: 0xC014, offset: 0x2D21B, size: 0x8, addend: 0x0, symName: __ZL9patches78, symObjAddr: 0xFB20, symBinAddr: 0x1B24D0, symSize: 0x0 }
  - { offsetInCU: 0xC033, offset: 0x2D23A, size: 0x8, addend: 0x0, symName: __ZL11patchBuf103, symObjAddr: 0x11C674, symBinAddr: 0x10E474, symSize: 0x0 }
  - { offsetInCU: 0xC052, offset: 0x2D259, size: 0x8, addend: 0x0, symName: __ZL11revisions32, symObjAddr: 0x5AA44, symBinAddr: 0x4C844, symSize: 0x0 }
  - { offsetInCU: 0xC071, offset: 0x2D278, size: 0x8, addend: 0x0, symName: __ZL11platforms79, symObjAddr: 0x1B2B00, symBinAddr: 0x1A4990, symSize: 0x0 }
  - { offsetInCU: 0xC090, offset: 0x2D297, size: 0x8, addend: 0x0, symName: __ZL7file865, symObjAddr: 0x11C680, symBinAddr: 0x10E480, symSize: 0x0 }
  - { offsetInCU: 0xC0C4, offset: 0x2D2CB, size: 0x8, addend: 0x0, symName: __ZL7file866, symObjAddr: 0x11CBE0, symBinAddr: 0x10E9E0, symSize: 0x0 }
  - { offsetInCU: 0xC0E3, offset: 0x2D2EA, size: 0x8, addend: 0x0, symName: __ZL7file867, symObjAddr: 0x11CD20, symBinAddr: 0x10EB20, symSize: 0x0 }
  - { offsetInCU: 0xC117, offset: 0x2D31E, size: 0x8, addend: 0x0, symName: __ZL7file868, symObjAddr: 0x11CE40, symBinAddr: 0x10EC40, symSize: 0x0 }
  - { offsetInCU: 0xC136, offset: 0x2D33D, size: 0x8, addend: 0x0, symName: __ZL7file869, symObjAddr: 0x11CF80, symBinAddr: 0x10ED80, symSize: 0x0 }
  - { offsetInCU: 0xC155, offset: 0x2D35C, size: 0x8, addend: 0x0, symName: __ZL7file870, symObjAddr: 0x11D0D0, symBinAddr: 0x10EED0, symSize: 0x0 }
  - { offsetInCU: 0xC174, offset: 0x2D37B, size: 0x8, addend: 0x0, symName: __ZL7file871, symObjAddr: 0x11D210, symBinAddr: 0x10F010, symSize: 0x0 }
  - { offsetInCU: 0xC1A8, offset: 0x2D3AF, size: 0x8, addend: 0x0, symName: __ZL7file872, symObjAddr: 0x11D390, symBinAddr: 0x10F190, symSize: 0x0 }
  - { offsetInCU: 0xC1C7, offset: 0x2D3CE, size: 0x8, addend: 0x0, symName: __ZL7file873, symObjAddr: 0x11D4F0, symBinAddr: 0x10F2F0, symSize: 0x0 }
  - { offsetInCU: 0xC1E6, offset: 0x2D3ED, size: 0x8, addend: 0x0, symName: __ZL7file874, symObjAddr: 0x11D640, symBinAddr: 0x10F440, symSize: 0x0 }
  - { offsetInCU: 0xC205, offset: 0x2D40C, size: 0x8, addend: 0x0, symName: __ZL7file875, symObjAddr: 0x11D790, symBinAddr: 0x10F590, symSize: 0x0 }
  - { offsetInCU: 0xC224, offset: 0x2D42B, size: 0x8, addend: 0x0, symName: __ZL9layouts79, symObjAddr: 0x1B2C10, symBinAddr: 0x1A4AA0, symSize: 0x0 }
  - { offsetInCU: 0xC243, offset: 0x2D44A, size: 0x8, addend: 0x0, symName: __ZL7file876, symObjAddr: 0x11D8F0, symBinAddr: 0x10F6F0, symSize: 0x0 }
  - { offsetInCU: 0xC277, offset: 0x2D47E, size: 0x8, addend: 0x0, symName: __ZL7file877, symObjAddr: 0x11DFA0, symBinAddr: 0x10FDA0, symSize: 0x0 }
  - { offsetInCU: 0xC2AA, offset: 0x2D4B1, size: 0x8, addend: 0x0, symName: __ZL7file878, symObjAddr: 0x11E090, symBinAddr: 0x10FE90, symSize: 0x0 }
  - { offsetInCU: 0xC2C9, offset: 0x2D4D0, size: 0x8, addend: 0x0, symName: __ZL7file879, symObjAddr: 0x11E180, symBinAddr: 0x10FF80, symSize: 0x0 }
  - { offsetInCU: 0xC2E8, offset: 0x2D4EF, size: 0x8, addend: 0x0, symName: __ZL7file880, symObjAddr: 0x11E270, symBinAddr: 0x110070, symSize: 0x0 }
  - { offsetInCU: 0xC31B, offset: 0x2D522, size: 0x8, addend: 0x0, symName: __ZL7file881, symObjAddr: 0x11E360, symBinAddr: 0x110160, symSize: 0x0 }
  - { offsetInCU: 0xC34E, offset: 0x2D555, size: 0x8, addend: 0x0, symName: __ZL7file882, symObjAddr: 0x11E440, symBinAddr: 0x110240, symSize: 0x0 }
  - { offsetInCU: 0xC36D, offset: 0x2D574, size: 0x8, addend: 0x0, symName: __ZL7file883, symObjAddr: 0x11E5B0, symBinAddr: 0x1103B0, symSize: 0x0 }
  - { offsetInCU: 0xC38C, offset: 0x2D593, size: 0x8, addend: 0x0, symName: __ZL7file884, symObjAddr: 0x11E720, symBinAddr: 0x110520, symSize: 0x0 }
  - { offsetInCU: 0xC3BF, offset: 0x2D5C6, size: 0x8, addend: 0x0, symName: __ZL7file885, symObjAddr: 0x11E810, symBinAddr: 0x110610, symSize: 0x0 }
  - { offsetInCU: 0xC3F3, offset: 0x2D5FA, size: 0x8, addend: 0x0, symName: __ZL7file886, symObjAddr: 0x11EEB0, symBinAddr: 0x110CB0, symSize: 0x0 }
  - { offsetInCU: 0xC412, offset: 0x2D619, size: 0x8, addend: 0x0, symName: __ZL9patches79, symObjAddr: 0xFC70, symBinAddr: 0x1B2620, symSize: 0x0 }
  - { offsetInCU: 0xC431, offset: 0x2D638, size: 0x8, addend: 0x0, symName: __ZL11patchBuf104, symObjAddr: 0x11F01B, symBinAddr: 0x110E1B, symSize: 0x0 }
  - { offsetInCU: 0xC450, offset: 0x2D657, size: 0x8, addend: 0x0, symName: __ZL11revisions33, symObjAddr: 0x5AA4C, symBinAddr: 0x4C84C, symSize: 0x0 }
  - { offsetInCU: 0xC46F, offset: 0x2D676, size: 0x8, addend: 0x0, symName: __ZL11platforms80, symObjAddr: 0x1B2D20, symBinAddr: 0x1A4BB0, symSize: 0x0 }
  - { offsetInCU: 0xC48E, offset: 0x2D695, size: 0x8, addend: 0x0, symName: __ZL7file887, symObjAddr: 0x11F020, symBinAddr: 0x110E20, symSize: 0x0 }
  - { offsetInCU: 0xC4AD, offset: 0x2D6B4, size: 0x8, addend: 0x0, symName: __ZL7file888, symObjAddr: 0x11F170, symBinAddr: 0x110F70, symSize: 0x0 }
  - { offsetInCU: 0xC4CC, offset: 0x2D6D3, size: 0x8, addend: 0x0, symName: __ZL9layouts80, symObjAddr: 0x1B2D50, symBinAddr: 0x1A4BE0, symSize: 0x0 }
  - { offsetInCU: 0xC4EB, offset: 0x2D6F2, size: 0x8, addend: 0x0, symName: __ZL7file889, symObjAddr: 0x11F2C0, symBinAddr: 0x1110C0, symSize: 0x0 }
  - { offsetInCU: 0xC50A, offset: 0x2D711, size: 0x8, addend: 0x0, symName: __ZL7file890, symObjAddr: 0x11F8F0, symBinAddr: 0x1116F0, symSize: 0x0 }
  - { offsetInCU: 0xC529, offset: 0x2D730, size: 0x8, addend: 0x0, symName: __ZL9patches80, symObjAddr: 0xFDC0, symBinAddr: 0x1B2770, symSize: 0x0 }
  - { offsetInCU: 0xC548, offset: 0x2D74F, size: 0x8, addend: 0x0, symName: __ZL11patchBuf105, symObjAddr: 0x11FF13, symBinAddr: 0x111D13, symSize: 0x0 }
  - { offsetInCU: 0xC567, offset: 0x2D76E, size: 0x8, addend: 0x0, symName: __ZL11platforms81, symObjAddr: 0x1B2D80, symBinAddr: 0x1A4C10, symSize: 0x0 }
  - { offsetInCU: 0xC59A, offset: 0x2D7A1, size: 0x8, addend: 0x0, symName: __ZL7file891, symObjAddr: 0x11FF20, symBinAddr: 0x111D20, symSize: 0x0 }
  - { offsetInCU: 0xC5B9, offset: 0x2D7C0, size: 0x8, addend: 0x0, symName: __ZL7file892, symObjAddr: 0x120060, symBinAddr: 0x111E60, symSize: 0x0 }
  - { offsetInCU: 0xC5D8, offset: 0x2D7DF, size: 0x8, addend: 0x0, symName: __ZL7file893, symObjAddr: 0x1201C0, symBinAddr: 0x111FC0, symSize: 0x0 }
  - { offsetInCU: 0xC5F7, offset: 0x2D7FE, size: 0x8, addend: 0x0, symName: __ZL7file894, symObjAddr: 0x120340, symBinAddr: 0x112140, symSize: 0x0 }
  - { offsetInCU: 0xC616, offset: 0x2D81D, size: 0x8, addend: 0x0, symName: __ZL7file895, symObjAddr: 0x120490, symBinAddr: 0x112290, symSize: 0x0 }
  - { offsetInCU: 0xC635, offset: 0x2D83C, size: 0x8, addend: 0x0, symName: __ZL7file896, symObjAddr: 0x120610, symBinAddr: 0x112410, symSize: 0x0 }
  - { offsetInCU: 0xC654, offset: 0x2D85B, size: 0x8, addend: 0x0, symName: __ZL7file897, symObjAddr: 0x120790, symBinAddr: 0x112590, symSize: 0x0 }
  - { offsetInCU: 0xC673, offset: 0x2D87A, size: 0x8, addend: 0x0, symName: __ZL7file898, symObjAddr: 0x120900, symBinAddr: 0x112700, symSize: 0x0 }
  - { offsetInCU: 0xC692, offset: 0x2D899, size: 0x8, addend: 0x0, symName: __ZL7file899, symObjAddr: 0x120AA0, symBinAddr: 0x1128A0, symSize: 0x0 }
  - { offsetInCU: 0xC6B1, offset: 0x2D8B8, size: 0x8, addend: 0x0, symName: __ZL7file900, symObjAddr: 0x120C30, symBinAddr: 0x112A30, symSize: 0x0 }
  - { offsetInCU: 0xC6D0, offset: 0x2D8D7, size: 0x8, addend: 0x0, symName: __ZL7file901, symObjAddr: 0x120D90, symBinAddr: 0x112B90, symSize: 0x0 }
  - { offsetInCU: 0xC704, offset: 0x2D90B, size: 0x8, addend: 0x0, symName: __ZL7file902, symObjAddr: 0x120F20, symBinAddr: 0x112D20, symSize: 0x0 }
  - { offsetInCU: 0xC723, offset: 0x2D92A, size: 0x8, addend: 0x0, symName: __ZL7file903, symObjAddr: 0x121070, symBinAddr: 0x112E70, symSize: 0x0 }
  - { offsetInCU: 0xC742, offset: 0x2D949, size: 0x8, addend: 0x0, symName: __ZL7file904, symObjAddr: 0x1211E0, symBinAddr: 0x112FE0, symSize: 0x0 }
  - { offsetInCU: 0xC776, offset: 0x2D97D, size: 0x8, addend: 0x0, symName: __ZL7file905, symObjAddr: 0x122610, symBinAddr: 0x114410, symSize: 0x0 }
  - { offsetInCU: 0xC795, offset: 0x2D99C, size: 0x8, addend: 0x0, symName: __ZL7file906, symObjAddr: 0x122780, symBinAddr: 0x114580, symSize: 0x0 }
  - { offsetInCU: 0xC7B4, offset: 0x2D9BB, size: 0x8, addend: 0x0, symName: __ZL7file907, symObjAddr: 0x1228F0, symBinAddr: 0x1146F0, symSize: 0x0 }
  - { offsetInCU: 0xC7D3, offset: 0x2D9DA, size: 0x8, addend: 0x0, symName: __ZL7file908, symObjAddr: 0x122A60, symBinAddr: 0x114860, symSize: 0x0 }
  - { offsetInCU: 0xC7F2, offset: 0x2D9F9, size: 0x8, addend: 0x0, symName: __ZL7file909, symObjAddr: 0x122BE0, symBinAddr: 0x1149E0, symSize: 0x0 }
  - { offsetInCU: 0xC811, offset: 0x2DA18, size: 0x8, addend: 0x0, symName: __ZL7file910, symObjAddr: 0x122D50, symBinAddr: 0x114B50, symSize: 0x0 }
  - { offsetInCU: 0xC830, offset: 0x2DA37, size: 0x8, addend: 0x0, symName: __ZL7file911, symObjAddr: 0x122ED0, symBinAddr: 0x114CD0, symSize: 0x0 }
  - { offsetInCU: 0xC84F, offset: 0x2DA56, size: 0x8, addend: 0x0, symName: __ZL7file912, symObjAddr: 0x123060, symBinAddr: 0x114E60, symSize: 0x0 }
  - { offsetInCU: 0xC86E, offset: 0x2DA75, size: 0x8, addend: 0x0, symName: __ZL7file913, symObjAddr: 0x1231C0, symBinAddr: 0x114FC0, symSize: 0x0 }
  - { offsetInCU: 0xC8A2, offset: 0x2DAA9, size: 0x8, addend: 0x0, symName: __ZL7file914, symObjAddr: 0x123320, symBinAddr: 0x115120, symSize: 0x0 }
  - { offsetInCU: 0xC8C1, offset: 0x2DAC8, size: 0x8, addend: 0x0, symName: __ZL7file915, symObjAddr: 0x123490, symBinAddr: 0x115290, symSize: 0x0 }
  - { offsetInCU: 0xC8E0, offset: 0x2DAE7, size: 0x8, addend: 0x0, symName: __ZL7file916, symObjAddr: 0x123620, symBinAddr: 0x115420, symSize: 0x0 }
  - { offsetInCU: 0xC8FF, offset: 0x2DB06, size: 0x8, addend: 0x0, symName: __ZL9layouts81, symObjAddr: 0x1B3010, symBinAddr: 0x1A4EA0, symSize: 0x0 }
  - { offsetInCU: 0xC91E, offset: 0x2DB25, size: 0x8, addend: 0x0, symName: __ZL7file917, symObjAddr: 0x123760, symBinAddr: 0x115560, symSize: 0x0 }
  - { offsetInCU: 0xC952, offset: 0x2DB59, size: 0x8, addend: 0x0, symName: __ZL7file918, symObjAddr: 0x123D40, symBinAddr: 0x115B40, symSize: 0x0 }
  - { offsetInCU: 0xC986, offset: 0x2DB8D, size: 0x8, addend: 0x0, symName: __ZL7file919, symObjAddr: 0x1240C0, symBinAddr: 0x115EC0, symSize: 0x0 }
  - { offsetInCU: 0xC9BA, offset: 0x2DBC1, size: 0x8, addend: 0x0, symName: __ZL7file920, symObjAddr: 0x124AC0, symBinAddr: 0x1168C0, symSize: 0x0 }
  - { offsetInCU: 0xC9D9, offset: 0x2DBE0, size: 0x8, addend: 0x0, symName: __ZL7file921, symObjAddr: 0x124B80, symBinAddr: 0x116980, symSize: 0x0 }
  - { offsetInCU: 0xCA0D, offset: 0x2DC14, size: 0x8, addend: 0x0, symName: __ZL7file922, symObjAddr: 0x1258E0, symBinAddr: 0x1176E0, symSize: 0x0 }
  - { offsetInCU: 0xCA41, offset: 0x2DC48, size: 0x8, addend: 0x0, symName: __ZL7file923, symObjAddr: 0x126670, symBinAddr: 0x118470, symSize: 0x0 }
  - { offsetInCU: 0xCA75, offset: 0x2DC7C, size: 0x8, addend: 0x0, symName: __ZL7file924, symObjAddr: 0x127400, symBinAddr: 0x119200, symSize: 0x0 }
  - { offsetInCU: 0xCA94, offset: 0x2DC9B, size: 0x8, addend: 0x0, symName: __ZL7file925, symObjAddr: 0x128190, symBinAddr: 0x119F90, symSize: 0x0 }
  - { offsetInCU: 0xCAC8, offset: 0x2DCCF, size: 0x8, addend: 0x0, symName: __ZL7file926, symObjAddr: 0x128790, symBinAddr: 0x11A590, symSize: 0x0 }
  - { offsetInCU: 0xCAFC, offset: 0x2DD03, size: 0x8, addend: 0x0, symName: __ZL7file927, symObjAddr: 0x1299B0, symBinAddr: 0x11B7B0, symSize: 0x0 }
  - { offsetInCU: 0xCB30, offset: 0x2DD37, size: 0x8, addend: 0x0, symName: __ZL7file928, symObjAddr: 0x12A2F0, symBinAddr: 0x11C0F0, symSize: 0x0 }
  - { offsetInCU: 0xCB64, offset: 0x2DD6B, size: 0x8, addend: 0x0, symName: __ZL7file929, symObjAddr: 0x12AC20, symBinAddr: 0x11CA20, symSize: 0x0 }
  - { offsetInCU: 0xCB98, offset: 0x2DD9F, size: 0x8, addend: 0x0, symName: __ZL7file930, symObjAddr: 0x12BE40, symBinAddr: 0x11DC40, symSize: 0x0 }
  - { offsetInCU: 0xCBCC, offset: 0x2DDD3, size: 0x8, addend: 0x0, symName: __ZL7file931, symObjAddr: 0x12CBD0, symBinAddr: 0x11E9D0, symSize: 0x0 }
  - { offsetInCU: 0xCBEB, offset: 0x2DDF2, size: 0x8, addend: 0x0, symName: __ZL7file932, symObjAddr: 0x12D990, symBinAddr: 0x11F790, symSize: 0x0 }
  - { offsetInCU: 0xCC1F, offset: 0x2DE26, size: 0x8, addend: 0x0, symName: __ZL7file933, symObjAddr: 0x12EAC0, symBinAddr: 0x1208C0, symSize: 0x0 }
  - { offsetInCU: 0xCC3E, offset: 0x2DE45, size: 0x8, addend: 0x0, symName: __ZL7file934, symObjAddr: 0x12F850, symBinAddr: 0x121650, symSize: 0x0 }
  - { offsetInCU: 0xCC71, offset: 0x2DE78, size: 0x8, addend: 0x0, symName: __ZL7file935, symObjAddr: 0x12F940, symBinAddr: 0x121740, symSize: 0x0 }
  - { offsetInCU: 0xCCA4, offset: 0x2DEAB, size: 0x8, addend: 0x0, symName: __ZL7file936, symObjAddr: 0x12FA40, symBinAddr: 0x121840, symSize: 0x0 }
  - { offsetInCU: 0xCCD8, offset: 0x2DEDF, size: 0x8, addend: 0x0, symName: __ZL7file937, symObjAddr: 0x130BA0, symBinAddr: 0x1229A0, symSize: 0x0 }
  - { offsetInCU: 0xCCF7, offset: 0x2DEFE, size: 0x8, addend: 0x0, symName: __ZL7file938, symObjAddr: 0x131930, symBinAddr: 0x123730, symSize: 0x0 }
  - { offsetInCU: 0xCD2B, offset: 0x2DF32, size: 0x8, addend: 0x0, symName: __ZL7file939, symObjAddr: 0x1326D0, symBinAddr: 0x1244D0, symSize: 0x0 }
  - { offsetInCU: 0xCD4A, offset: 0x2DF51, size: 0x8, addend: 0x0, symName: __ZL7file940, symObjAddr: 0x1336A0, symBinAddr: 0x1254A0, symSize: 0x0 }
  - { offsetInCU: 0xCD7E, offset: 0x2DF85, size: 0x8, addend: 0x0, symName: __ZL7file941, symObjAddr: 0x134480, symBinAddr: 0x126280, symSize: 0x0 }
  - { offsetInCU: 0xCDB2, offset: 0x2DFB9, size: 0x8, addend: 0x0, symName: __ZL7file942, symObjAddr: 0x1349B0, symBinAddr: 0x1267B0, symSize: 0x0 }
  - { offsetInCU: 0xCDE6, offset: 0x2DFED, size: 0x8, addend: 0x0, symName: __ZL7file943, symObjAddr: 0x1352E0, symBinAddr: 0x1270E0, symSize: 0x0 }
  - { offsetInCU: 0xCE05, offset: 0x2E00C, size: 0x8, addend: 0x0, symName: __ZL9patches81, symObjAddr: 0xFF40, symBinAddr: 0x1B28F0, symSize: 0x0 }
  - { offsetInCU: 0xCE24, offset: 0x2E02B, size: 0x8, addend: 0x0, symName: __ZL11patchBuf106, symObjAddr: 0x135630, symBinAddr: 0x127430, symSize: 0x0 }
  - { offsetInCU: 0xCE43, offset: 0x2E04A, size: 0x8, addend: 0x0, symName: __ZL11platforms82, symObjAddr: 0x1B32A0, symBinAddr: 0x1A5130, symSize: 0x0 }
  - { offsetInCU: 0xCE62, offset: 0x2E069, size: 0x8, addend: 0x0, symName: __ZL7file944, symObjAddr: 0x135640, symBinAddr: 0x127440, symSize: 0x0 }
  - { offsetInCU: 0xCE96, offset: 0x2E09D, size: 0x8, addend: 0x0, symName: __ZL9layouts82, symObjAddr: 0x1B32D0, symBinAddr: 0x1A5160, symSize: 0x0 }
  - { offsetInCU: 0xCEB5, offset: 0x2E0BC, size: 0x8, addend: 0x0, symName: __ZL7file945, symObjAddr: 0x1361C0, symBinAddr: 0x127FC0, symSize: 0x0 }
  - { offsetInCU: 0xCED4, offset: 0x2E0DB, size: 0x8, addend: 0x0, symName: __ZL7file946, symObjAddr: 0x136F80, symBinAddr: 0x128D80, symSize: 0x0 }
  - { offsetInCU: 0xCEF3, offset: 0x2E0FA, size: 0x8, addend: 0x0, symName: __ZL9patches82, symObjAddr: 0x10120, symBinAddr: 0x1B2AD0, symSize: 0x0 }
  - { offsetInCU: 0xCF12, offset: 0x2E119, size: 0x8, addend: 0x0, symName: __ZL11patchBuf107, symObjAddr: 0x137D40, symBinAddr: 0x129B40, symSize: 0x0 }
  - { offsetInCU: 0xCF31, offset: 0x2E138, size: 0x8, addend: 0x0, symName: __ZL11revisions34, symObjAddr: 0x5AA50, symBinAddr: 0x4C850, symSize: 0x0 }
  - { offsetInCU: 0xCF50, offset: 0x2E157, size: 0x8, addend: 0x0, symName: __ZL11platforms83, symObjAddr: 0x1B3300, symBinAddr: 0x1A5190, symSize: 0x0 }
  - { offsetInCU: 0xCF6F, offset: 0x2E176, size: 0x8, addend: 0x0, symName: __ZL7file947, symObjAddr: 0x137D50, symBinAddr: 0x129B50, symSize: 0x0 }
  - { offsetInCU: 0xCFA3, offset: 0x2E1AA, size: 0x8, addend: 0x0, symName: __ZL7file948, symObjAddr: 0x137F50, symBinAddr: 0x129D50, symSize: 0x0 }
  - { offsetInCU: 0xCFD7, offset: 0x2E1DE, size: 0x8, addend: 0x0, symName: __ZL7file949, symObjAddr: 0x139060, symBinAddr: 0x12AE60, symSize: 0x0 }
  - { offsetInCU: 0xCFF6, offset: 0x2E1FD, size: 0x8, addend: 0x0, symName: __ZL7file950, symObjAddr: 0x1391B0, symBinAddr: 0x12AFB0, symSize: 0x0 }
  - { offsetInCU: 0xD015, offset: 0x2E21C, size: 0x8, addend: 0x0, symName: __ZL7file951, symObjAddr: 0x1392F0, symBinAddr: 0x12B0F0, symSize: 0x0 }
  - { offsetInCU: 0xD034, offset: 0x2E23B, size: 0x8, addend: 0x0, symName: __ZL7file952, symObjAddr: 0x139440, symBinAddr: 0x12B240, symSize: 0x0 }
  - { offsetInCU: 0xD053, offset: 0x2E25A, size: 0x8, addend: 0x0, symName: __ZL7file953, symObjAddr: 0x1395A0, symBinAddr: 0x12B3A0, symSize: 0x0 }
  - { offsetInCU: 0xD072, offset: 0x2E279, size: 0x8, addend: 0x0, symName: __ZL7file954, symObjAddr: 0x139AF0, symBinAddr: 0x12B8F0, symSize: 0x0 }
  - { offsetInCU: 0xD0A6, offset: 0x2E2AD, size: 0x8, addend: 0x0, symName: __ZL7file955, symObjAddr: 0x139C20, symBinAddr: 0x12BA20, symSize: 0x0 }
  - { offsetInCU: 0xD0C5, offset: 0x2E2CC, size: 0x8, addend: 0x0, symName: __ZL7file956, symObjAddr: 0x139D70, symBinAddr: 0x12BB70, symSize: 0x0 }
  - { offsetInCU: 0xD0E4, offset: 0x2E2EB, size: 0x8, addend: 0x0, symName: __ZL9layouts83, symObjAddr: 0x1B3410, symBinAddr: 0x1A52A0, symSize: 0x0 }
  - { offsetInCU: 0xD103, offset: 0x2E30A, size: 0x8, addend: 0x0, symName: __ZL7file957, symObjAddr: 0x139EC0, symBinAddr: 0x12BCC0, symSize: 0x0 }
  - { offsetInCU: 0xD137, offset: 0x2E33E, size: 0x8, addend: 0x0, symName: __ZL7file958, symObjAddr: 0x13A1E0, symBinAddr: 0x12BFE0, symSize: 0x0 }
  - { offsetInCU: 0xD16B, offset: 0x2E372, size: 0x8, addend: 0x0, symName: __ZL7file959, symObjAddr: 0x13A500, symBinAddr: 0x12C300, symSize: 0x0 }
  - { offsetInCU: 0xD18A, offset: 0x2E391, size: 0x8, addend: 0x0, symName: __ZL7file960, symObjAddr: 0x13A800, symBinAddr: 0x12C600, symSize: 0x0 }
  - { offsetInCU: 0xD1BE, offset: 0x2E3C5, size: 0x8, addend: 0x0, symName: __ZL7file961, symObjAddr: 0x13AB00, symBinAddr: 0x12C900, symSize: 0x0 }
  - { offsetInCU: 0xD1F2, offset: 0x2E3F9, size: 0x8, addend: 0x0, symName: __ZL7file962, symObjAddr: 0x13B030, symBinAddr: 0x12CE30, symSize: 0x0 }
  - { offsetInCU: 0xD211, offset: 0x2E418, size: 0x8, addend: 0x0, symName: __ZL7file963, symObjAddr: 0x13B370, symBinAddr: 0x12D170, symSize: 0x0 }
  - { offsetInCU: 0xD230, offset: 0x2E437, size: 0x8, addend: 0x0, symName: __ZL7file964, symObjAddr: 0x13B6A0, symBinAddr: 0x12D4A0, symSize: 0x0 }
  - { offsetInCU: 0xD24F, offset: 0x2E456, size: 0x8, addend: 0x0, symName: __ZL7file965, symObjAddr: 0x13B9D0, symBinAddr: 0x12D7D0, symSize: 0x0 }
  - { offsetInCU: 0xD26E, offset: 0x2E475, size: 0x8, addend: 0x0, symName: __ZL7file966, symObjAddr: 0x13BD00, symBinAddr: 0x12DB00, symSize: 0x0 }
  - { offsetInCU: 0xD2A2, offset: 0x2E4A9, size: 0x8, addend: 0x0, symName: __ZL7file967, symObjAddr: 0x13C010, symBinAddr: 0x12DE10, symSize: 0x0 }
  - { offsetInCU: 0xD2C1, offset: 0x2E4C8, size: 0x8, addend: 0x0, symName: __ZL9patches83, symObjAddr: 0x102D0, symBinAddr: 0x1B2C80, symSize: 0x0 }
  - { offsetInCU: 0xD2E0, offset: 0x2E4E7, size: 0x8, addend: 0x0, symName: __ZL11patchBuf108, symObjAddr: 0x13C33F, symBinAddr: 0x12E13F, symSize: 0x0 }
  - { offsetInCU: 0xD2FF, offset: 0x2E506, size: 0x8, addend: 0x0, symName: __ZL11platforms84, symObjAddr: 0x1B3520, symBinAddr: 0x1A53B0, symSize: 0x0 }
  - { offsetInCU: 0xD31E, offset: 0x2E525, size: 0x8, addend: 0x0, symName: __ZL7file968, symObjAddr: 0x13C350, symBinAddr: 0x12E150, symSize: 0x0 }
  - { offsetInCU: 0xD33D, offset: 0x2E544, size: 0x8, addend: 0x0, symName: __ZL7file969, symObjAddr: 0x13C4B0, symBinAddr: 0x12E2B0, symSize: 0x0 }
  - { offsetInCU: 0xD35C, offset: 0x2E563, size: 0x8, addend: 0x0, symName: __ZL9layouts84, symObjAddr: 0x1B3550, symBinAddr: 0x1A53E0, symSize: 0x0 }
  - { offsetInCU: 0xD37B, offset: 0x2E582, size: 0x8, addend: 0x0, symName: __ZL7file970, symObjAddr: 0x13C610, symBinAddr: 0x12E410, symSize: 0x0 }
  - { offsetInCU: 0xD3AF, offset: 0x2E5B6, size: 0x8, addend: 0x0, symName: __ZL7file971, symObjAddr: 0x13D5B0, symBinAddr: 0x12F3B0, symSize: 0x0 }
  - { offsetInCU: 0xD3E3, offset: 0x2E5EA, size: 0x8, addend: 0x0, symName: __ZL9patches84, symObjAddr: 0x10450, symBinAddr: 0x1B2E00, symSize: 0x0 }
  - { offsetInCU: 0xD402, offset: 0x2E609, size: 0x8, addend: 0x0, symName: __ZL11patchBuf109, symObjAddr: 0x13E559, symBinAddr: 0x130359, symSize: 0x0 }
  - { offsetInCU: 0xD421, offset: 0x2E628, size: 0x8, addend: 0x0, symName: __ZL11revisions35, symObjAddr: 0x5AA58, symBinAddr: 0x4C858, symSize: 0x0 }
  - { offsetInCU: 0xD440, offset: 0x2E647, size: 0x8, addend: 0x0, symName: __ZL11platforms85, symObjAddr: 0x1B3580, symBinAddr: 0x1A5410, symSize: 0x0 }
  - { offsetInCU: 0xD45F, offset: 0x2E666, size: 0x8, addend: 0x0, symName: __ZL7file972, symObjAddr: 0x13E560, symBinAddr: 0x130360, symSize: 0x0 }
  - { offsetInCU: 0xD493, offset: 0x2E69A, size: 0x8, addend: 0x0, symName: __ZL7file973, symObjAddr: 0x13E6A0, symBinAddr: 0x1304A0, symSize: 0x0 }
  - { offsetInCU: 0xD4C7, offset: 0x2E6CE, size: 0x8, addend: 0x0, symName: __ZL9layouts85, symObjAddr: 0x1B35B0, symBinAddr: 0x1A5440, symSize: 0x0 }
  - { offsetInCU: 0xD4E6, offset: 0x2E6ED, size: 0x8, addend: 0x0, symName: __ZL7file974, symObjAddr: 0x13F200, symBinAddr: 0x131000, symSize: 0x0 }
  - { offsetInCU: 0xD51A, offset: 0x2E721, size: 0x8, addend: 0x0, symName: __ZL7file975, symObjAddr: 0x13F540, symBinAddr: 0x131340, symSize: 0x0 }
  - { offsetInCU: 0xD539, offset: 0x2E740, size: 0x8, addend: 0x0, symName: __ZL9patches85, symObjAddr: 0x10630, symBinAddr: 0x1B2FE0, symSize: 0x0 }
  - { offsetInCU: 0xD558, offset: 0x2E75F, size: 0x8, addend: 0x0, symName: __ZL11patchBuf110, symObjAddr: 0x13F8BC, symBinAddr: 0x1316BC, symSize: 0x0 }
  - { offsetInCU: 0xD577, offset: 0x2E77E, size: 0x8, addend: 0x0, symName: __ZL11revisions36, symObjAddr: 0x5AA5C, symBinAddr: 0x4C85C, symSize: 0x0 }
  - { offsetInCU: 0xD596, offset: 0x2E79D, size: 0x8, addend: 0x0, symName: __ZL11platforms86, symObjAddr: 0x1B35E0, symBinAddr: 0x1A5470, symSize: 0x0 }
  - { offsetInCU: 0xD5C9, offset: 0x2E7D0, size: 0x8, addend: 0x0, symName: __ZL7file976, symObjAddr: 0x13F8C0, symBinAddr: 0x1316C0, symSize: 0x0 }
  - { offsetInCU: 0xD5E8, offset: 0x2E7EF, size: 0x8, addend: 0x0, symName: __ZL7file977, symObjAddr: 0x13FA10, symBinAddr: 0x131810, symSize: 0x0 }
  - { offsetInCU: 0xD607, offset: 0x2E80E, size: 0x8, addend: 0x0, symName: __ZL7file978, symObjAddr: 0x13FB50, symBinAddr: 0x131950, symSize: 0x0 }
  - { offsetInCU: 0xD626, offset: 0x2E82D, size: 0x8, addend: 0x0, symName: __ZL7file979, symObjAddr: 0x13FCE0, symBinAddr: 0x131AE0, symSize: 0x0 }
  - { offsetInCU: 0xD645, offset: 0x2E84C, size: 0x8, addend: 0x0, symName: __ZL7file980, symObjAddr: 0x13FE30, symBinAddr: 0x131C30, symSize: 0x0 }
  - { offsetInCU: 0xD664, offset: 0x2E86B, size: 0x8, addend: 0x0, symName: __ZL7file981, symObjAddr: 0x13FF80, symBinAddr: 0x131D80, symSize: 0x0 }
  - { offsetInCU: 0xD683, offset: 0x2E88A, size: 0x8, addend: 0x0, symName: __ZL7file982, symObjAddr: 0x1400D0, symBinAddr: 0x131ED0, symSize: 0x0 }
  - { offsetInCU: 0xD6A2, offset: 0x2E8A9, size: 0x8, addend: 0x0, symName: __ZL7file983, symObjAddr: 0x140220, symBinAddr: 0x132020, symSize: 0x0 }
  - { offsetInCU: 0xD6C1, offset: 0x2E8C8, size: 0x8, addend: 0x0, symName: __ZL7file984, symObjAddr: 0x140380, symBinAddr: 0x132180, symSize: 0x0 }
  - { offsetInCU: 0xD6E0, offset: 0x2E8E7, size: 0x8, addend: 0x0, symName: __ZL7file985, symObjAddr: 0x1404C0, symBinAddr: 0x1322C0, symSize: 0x0 }
  - { offsetInCU: 0xD6FF, offset: 0x2E906, size: 0x8, addend: 0x0, symName: __ZL7file986, symObjAddr: 0x140610, symBinAddr: 0x132410, symSize: 0x0 }
  - { offsetInCU: 0xD71E, offset: 0x2E925, size: 0x8, addend: 0x0, symName: __ZL7file987, symObjAddr: 0x140760, symBinAddr: 0x132560, symSize: 0x0 }
  - { offsetInCU: 0xD73D, offset: 0x2E944, size: 0x8, addend: 0x0, symName: __ZL7file988, symObjAddr: 0x1408A0, symBinAddr: 0x1326A0, symSize: 0x0 }
  - { offsetInCU: 0xD75C, offset: 0x2E963, size: 0x8, addend: 0x0, symName: __ZL7file989, symObjAddr: 0x140A30, symBinAddr: 0x132830, symSize: 0x0 }
  - { offsetInCU: 0xD77B, offset: 0x2E982, size: 0x8, addend: 0x0, symName: __ZL7file990, symObjAddr: 0x140B70, symBinAddr: 0x132970, symSize: 0x0 }
  - { offsetInCU: 0xD79A, offset: 0x2E9A1, size: 0x8, addend: 0x0, symName: __ZL7file991, symObjAddr: 0x140CF0, symBinAddr: 0x132AF0, symSize: 0x0 }
  - { offsetInCU: 0xD7B9, offset: 0x2E9C0, size: 0x8, addend: 0x0, symName: __ZL7file992, symObjAddr: 0x140E30, symBinAddr: 0x132C30, symSize: 0x0 }
  - { offsetInCU: 0xD7D8, offset: 0x2E9DF, size: 0x8, addend: 0x0, symName: __ZL7file993, symObjAddr: 0x140F70, symBinAddr: 0x132D70, symSize: 0x0 }
  - { offsetInCU: 0xD7F7, offset: 0x2E9FE, size: 0x8, addend: 0x0, symName: __ZL7file994, symObjAddr: 0x1410E0, symBinAddr: 0x132EE0, symSize: 0x0 }
  - { offsetInCU: 0xD82B, offset: 0x2EA32, size: 0x8, addend: 0x0, symName: __ZL7file995, symObjAddr: 0x141260, symBinAddr: 0x133060, symSize: 0x0 }
  - { offsetInCU: 0xD84A, offset: 0x2EA51, size: 0x8, addend: 0x0, symName: __ZL9layouts86, symObjAddr: 0x1B37C0, symBinAddr: 0x1A5650, symSize: 0x0 }
  - { offsetInCU: 0xD869, offset: 0x2EA70, size: 0x8, addend: 0x0, symName: __ZL7file996, symObjAddr: 0x1413F0, symBinAddr: 0x1331F0, symSize: 0x0 }
  - { offsetInCU: 0xD888, offset: 0x2EA8F, size: 0x8, addend: 0x0, symName: __ZL7file997, symObjAddr: 0x1421B0, symBinAddr: 0x133FB0, symSize: 0x0 }
  - { offsetInCU: 0xD8A7, offset: 0x2EAAE, size: 0x8, addend: 0x0, symName: __ZL7file998, symObjAddr: 0x1427E0, symBinAddr: 0x1345E0, symSize: 0x0 }
  - { offsetInCU: 0xD8DB, offset: 0x2EAE2, size: 0x8, addend: 0x0, symName: __ZL7file999, symObjAddr: 0x142DA0, symBinAddr: 0x134BA0, symSize: 0x0 }
  - { offsetInCU: 0xD8FA, offset: 0x2EB01, size: 0x8, addend: 0x0, symName: __ZL8file1000, symObjAddr: 0x143B70, symBinAddr: 0x135970, symSize: 0x0 }
  - { offsetInCU: 0xD919, offset: 0x2EB20, size: 0x8, addend: 0x0, symName: __ZL8file1001, symObjAddr: 0x144930, symBinAddr: 0x136730, symSize: 0x0 }
  - { offsetInCU: 0xD94D, offset: 0x2EB54, size: 0x8, addend: 0x0, symName: __ZL8file1002, symObjAddr: 0x144F40, symBinAddr: 0x136D40, symSize: 0x0 }
  - { offsetInCU: 0xD96C, offset: 0x2EB73, size: 0x8, addend: 0x0, symName: __ZL8file1003, symObjAddr: 0x145D10, symBinAddr: 0x137B10, symSize: 0x0 }
  - { offsetInCU: 0xD98B, offset: 0x2EB92, size: 0x8, addend: 0x0, symName: __ZL8file1004, symObjAddr: 0x146AD0, symBinAddr: 0x1388D0, symSize: 0x0 }
  - { offsetInCU: 0xD9BF, offset: 0x2EBC6, size: 0x8, addend: 0x0, symName: __ZL8file1005, symObjAddr: 0x147850, symBinAddr: 0x139650, symSize: 0x0 }
  - { offsetInCU: 0xD9F3, offset: 0x2EBFA, size: 0x8, addend: 0x0, symName: __ZL8file1006, symObjAddr: 0x148610, symBinAddr: 0x13A410, symSize: 0x0 }
  - { offsetInCU: 0xDA12, offset: 0x2EC19, size: 0x8, addend: 0x0, symName: __ZL8file1007, symObjAddr: 0x1493E0, symBinAddr: 0x13B1E0, symSize: 0x0 }
  - { offsetInCU: 0xDA46, offset: 0x2EC4D, size: 0x8, addend: 0x0, symName: __ZL8file1008, symObjAddr: 0x14A150, symBinAddr: 0x13BF50, symSize: 0x0 }
  - { offsetInCU: 0xDA7A, offset: 0x2EC81, size: 0x8, addend: 0x0, symName: __ZL8file1009, symObjAddr: 0x14AF20, symBinAddr: 0x13CD20, symSize: 0x0 }
  - { offsetInCU: 0xDA99, offset: 0x2ECA0, size: 0x8, addend: 0x0, symName: __ZL8file1010, symObjAddr: 0x14B550, symBinAddr: 0x13D350, symSize: 0x0 }
  - { offsetInCU: 0xDAB8, offset: 0x2ECBF, size: 0x8, addend: 0x0, symName: __ZL8file1011, symObjAddr: 0x14C320, symBinAddr: 0x13E120, symSize: 0x0 }
  - { offsetInCU: 0xDAD7, offset: 0x2ECDE, size: 0x8, addend: 0x0, symName: __ZL8file1012, symObjAddr: 0x14C950, symBinAddr: 0x13E750, symSize: 0x0 }
  - { offsetInCU: 0xDB0B, offset: 0x2ED12, size: 0x8, addend: 0x0, symName: __ZL8file1013, symObjAddr: 0x14CEE0, symBinAddr: 0x13ECE0, symSize: 0x0 }
  - { offsetInCU: 0xDB3F, offset: 0x2ED46, size: 0x8, addend: 0x0, symName: __ZL8file1014, symObjAddr: 0x14DDA0, symBinAddr: 0x13FBA0, symSize: 0x0 }
  - { offsetInCU: 0xDB5E, offset: 0x2ED65, size: 0x8, addend: 0x0, symName: __ZL8file1015, symObjAddr: 0x14EB70, symBinAddr: 0x140970, symSize: 0x0 }
  - { offsetInCU: 0xDB92, offset: 0x2ED99, size: 0x8, addend: 0x0, symName: __ZL9patches86, symObjAddr: 0x10780, symBinAddr: 0x1B3130, symSize: 0x0 }
  - { offsetInCU: 0xDBB1, offset: 0x2EDB8, size: 0x8, addend: 0x0, symName: __ZL11patchBuf111, symObjAddr: 0x14F329, symBinAddr: 0x141129, symSize: 0x0 }
  - { offsetInCU: 0xDBD0, offset: 0x2EDD7, size: 0x8, addend: 0x0, symName: __ZL11revisions37, symObjAddr: 0x5AA64, symBinAddr: 0x4C864, symSize: 0x0 }
  - { offsetInCU: 0xDBEF, offset: 0x2EDF6, size: 0x8, addend: 0x0, symName: __ZL11platforms87, symObjAddr: 0x1B39A0, symBinAddr: 0x1A5830, symSize: 0x0 }
  - { offsetInCU: 0xDC22, offset: 0x2EE29, size: 0x8, addend: 0x0, symName: __ZL8file1016, symObjAddr: 0x14F330, symBinAddr: 0x141130, symSize: 0x0 }
  - { offsetInCU: 0xDC41, offset: 0x2EE48, size: 0x8, addend: 0x0, symName: __ZL8file1017, symObjAddr: 0x14F740, symBinAddr: 0x141540, symSize: 0x0 }
  - { offsetInCU: 0xDC60, offset: 0x2EE67, size: 0x8, addend: 0x0, symName: __ZL8file1018, symObjAddr: 0x14F900, symBinAddr: 0x141700, symSize: 0x0 }
  - { offsetInCU: 0xDC94, offset: 0x2EE9B, size: 0x8, addend: 0x0, symName: __ZL8file1019, symObjAddr: 0x1507A0, symBinAddr: 0x1425A0, symSize: 0x0 }
  - { offsetInCU: 0xDCB3, offset: 0x2EEBA, size: 0x8, addend: 0x0, symName: __ZL8file1020, symObjAddr: 0x150B40, symBinAddr: 0x142940, symSize: 0x0 }
  - { offsetInCU: 0xDCE7, offset: 0x2EEEE, size: 0x8, addend: 0x0, symName: __ZL8file1021, symObjAddr: 0x151670, symBinAddr: 0x143470, symSize: 0x0 }
  - { offsetInCU: 0xDD06, offset: 0x2EF0D, size: 0x8, addend: 0x0, symName: __ZL8file1022, symObjAddr: 0x1517E0, symBinAddr: 0x1435E0, symSize: 0x0 }
  - { offsetInCU: 0xDD25, offset: 0x2EF2C, size: 0x8, addend: 0x0, symName: __ZL8file1023, symObjAddr: 0x151950, symBinAddr: 0x143750, symSize: 0x0 }
  - { offsetInCU: 0xDD44, offset: 0x2EF4B, size: 0x8, addend: 0x0, symName: __ZL8file1024, symObjAddr: 0x151AD0, symBinAddr: 0x1438D0, symSize: 0x0 }
  - { offsetInCU: 0xDD63, offset: 0x2EF6A, size: 0x8, addend: 0x0, symName: __ZL8file1025, symObjAddr: 0x151C20, symBinAddr: 0x143A20, symSize: 0x0 }
  - { offsetInCU: 0xDD82, offset: 0x2EF89, size: 0x8, addend: 0x0, symName: __ZL9layouts87, symObjAddr: 0x1B3AE0, symBinAddr: 0x1A5970, symSize: 0x0 }
  - { offsetInCU: 0xDDA1, offset: 0x2EFA8, size: 0x8, addend: 0x0, symName: __ZL8file1026, symObjAddr: 0x151D90, symBinAddr: 0x143B90, symSize: 0x0 }
  - { offsetInCU: 0xDDC0, offset: 0x2EFC7, size: 0x8, addend: 0x0, symName: __ZL8file1027, symObjAddr: 0x1520E0, symBinAddr: 0x143EE0, symSize: 0x0 }
  - { offsetInCU: 0xDDDF, offset: 0x2EFE6, size: 0x8, addend: 0x0, symName: __ZL8file1028, symObjAddr: 0x152430, symBinAddr: 0x144230, symSize: 0x0 }
  - { offsetInCU: 0xDDFE, offset: 0x2F005, size: 0x8, addend: 0x0, symName: __ZL8file1029, symObjAddr: 0x152750, symBinAddr: 0x144550, symSize: 0x0 }
  - { offsetInCU: 0xDE1D, offset: 0x2F024, size: 0x8, addend: 0x0, symName: __ZL8file1030, symObjAddr: 0x152AA0, symBinAddr: 0x1448A0, symSize: 0x0 }
  - { offsetInCU: 0xDE3C, offset: 0x2F043, size: 0x8, addend: 0x0, symName: __ZL8file1031, symObjAddr: 0x152DF0, symBinAddr: 0x144BF0, symSize: 0x0 }
  - { offsetInCU: 0xDE70, offset: 0x2F077, size: 0x8, addend: 0x0, symName: __ZL8file1032, symObjAddr: 0x154160, symBinAddr: 0x145F60, symSize: 0x0 }
  - { offsetInCU: 0xDE8F, offset: 0x2F096, size: 0x8, addend: 0x0, symName: __ZL8file1033, symObjAddr: 0x1545D0, symBinAddr: 0x1463D0, symSize: 0x0 }
  - { offsetInCU: 0xDEC3, offset: 0x2F0CA, size: 0x8, addend: 0x0, symName: __ZL8file1034, symObjAddr: 0x154950, symBinAddr: 0x146750, symSize: 0x0 }
  - { offsetInCU: 0xDEE2, offset: 0x2F0E9, size: 0x8, addend: 0x0, symName: __ZL8file1035, symObjAddr: 0x154CD0, symBinAddr: 0x146AD0, symSize: 0x0 }
  - { offsetInCU: 0xDF16, offset: 0x2F11D, size: 0x8, addend: 0x0, symName: __ZL8file1036, symObjAddr: 0x1551A0, symBinAddr: 0x146FA0, symSize: 0x0 }
  - { offsetInCU: 0xDF4A, offset: 0x2F151, size: 0x8, addend: 0x0, symName: __ZL8file1037, symObjAddr: 0x155600, symBinAddr: 0x147400, symSize: 0x0 }
  - { offsetInCU: 0xDF69, offset: 0x2F170, size: 0x8, addend: 0x0, symName: __ZL9patches87, symObjAddr: 0x10900, symBinAddr: 0x1B32B0, symSize: 0x0 }
  - { offsetInCU: 0xDF88, offset: 0x2F18F, size: 0x8, addend: 0x0, symName: __ZL11patchBuf112, symObjAddr: 0x155951, symBinAddr: 0x147751, symSize: 0x0 }
  - { offsetInCU: 0xDFA7, offset: 0x2F1AE, size: 0x8, addend: 0x0, symName: __ZL11revisions38, symObjAddr: 0x5AA68, symBinAddr: 0x4C868, symSize: 0x0 }
  - { offsetInCU: 0xDFC6, offset: 0x2F1CD, size: 0x8, addend: 0x0, symName: __ZL11platforms88, symObjAddr: 0x1B3C20, symBinAddr: 0x1A5AB0, symSize: 0x0 }
  - { offsetInCU: 0xDFE5, offset: 0x2F1EC, size: 0x8, addend: 0x0, symName: __ZL8file1038, symObjAddr: 0x155960, symBinAddr: 0x147760, symSize: 0x0 }
  - { offsetInCU: 0xE004, offset: 0x2F20B, size: 0x8, addend: 0x0, symName: __ZL8file1039, symObjAddr: 0x155AB0, symBinAddr: 0x1478B0, symSize: 0x0 }
  - { offsetInCU: 0xE023, offset: 0x2F22A, size: 0x8, addend: 0x0, symName: __ZL8file1040, symObjAddr: 0x155C00, symBinAddr: 0x147A00, symSize: 0x0 }
  - { offsetInCU: 0xE042, offset: 0x2F249, size: 0x8, addend: 0x0, symName: __ZL8file1041, symObjAddr: 0x155D50, symBinAddr: 0x147B50, symSize: 0x0 }
  - { offsetInCU: 0xE061, offset: 0x2F268, size: 0x8, addend: 0x0, symName: __ZL8file1042, symObjAddr: 0x155EB0, symBinAddr: 0x147CB0, symSize: 0x0 }
  - { offsetInCU: 0xE080, offset: 0x2F287, size: 0x8, addend: 0x0, symName: __ZL8file1043, symObjAddr: 0x156000, symBinAddr: 0x147E00, symSize: 0x0 }
  - { offsetInCU: 0xE09F, offset: 0x2F2A6, size: 0x8, addend: 0x0, symName: __ZL8file1044, symObjAddr: 0x156150, symBinAddr: 0x147F50, symSize: 0x0 }
  - { offsetInCU: 0xE0BE, offset: 0x2F2C5, size: 0x8, addend: 0x0, symName: __ZL9layouts88, symObjAddr: 0x1B3CD0, symBinAddr: 0x1A5B60, symSize: 0x0 }
  - { offsetInCU: 0xE0DD, offset: 0x2F2E4, size: 0x8, addend: 0x0, symName: __ZL8file1045, symObjAddr: 0x1562B0, symBinAddr: 0x1480B0, symSize: 0x0 }
  - { offsetInCU: 0xE0FC, offset: 0x2F303, size: 0x8, addend: 0x0, symName: __ZL8file1046, symObjAddr: 0x156970, symBinAddr: 0x148770, symSize: 0x0 }
  - { offsetInCU: 0xE130, offset: 0x2F337, size: 0x8, addend: 0x0, symName: __ZL8file1047, symObjAddr: 0x157040, symBinAddr: 0x148E40, symSize: 0x0 }
  - { offsetInCU: 0xE163, offset: 0x2F36A, size: 0x8, addend: 0x0, symName: __ZL8file1048, symObjAddr: 0x157130, symBinAddr: 0x148F30, symSize: 0x0 }
  - { offsetInCU: 0xE182, offset: 0x2F389, size: 0x8, addend: 0x0, symName: __ZL8file1049, symObjAddr: 0x157460, symBinAddr: 0x149260, symSize: 0x0 }
  - { offsetInCU: 0xE1B6, offset: 0x2F3BD, size: 0x8, addend: 0x0, symName: __ZL8file1050, symObjAddr: 0x157920, symBinAddr: 0x149720, symSize: 0x0 }
  - { offsetInCU: 0xE1EA, offset: 0x2F3F1, size: 0x8, addend: 0x0, symName: __ZL8file1051, symObjAddr: 0x157E10, symBinAddr: 0x149C10, symSize: 0x0 }
  - { offsetInCU: 0xE209, offset: 0x2F410, size: 0x8, addend: 0x0, symName: __ZL9patches88, symObjAddr: 0x10A50, symBinAddr: 0x1B3400, symSize: 0x0 }
  - { offsetInCU: 0xE228, offset: 0x2F42F, size: 0x8, addend: 0x0, symName: __ZL11revisions39, symObjAddr: 0x5AA74, symBinAddr: 0x4C874, symSize: 0x0 }
  - { offsetInCU: 0xE247, offset: 0x2F44E, size: 0x8, addend: 0x0, symName: __ZL11platforms89, symObjAddr: 0x1B3D80, symBinAddr: 0x1A5C10, symSize: 0x0 }
  - { offsetInCU: 0xE27A, offset: 0x2F481, size: 0x8, addend: 0x0, symName: __ZL8file1052, symObjAddr: 0x1584D0, symBinAddr: 0x14A2D0, symSize: 0x0 }
  - { offsetInCU: 0xE299, offset: 0x2F4A0, size: 0x8, addend: 0x0, symName: __ZL8file1053, symObjAddr: 0x158620, symBinAddr: 0x14A420, symSize: 0x0 }
  - { offsetInCU: 0xE2CD, offset: 0x2F4D4, size: 0x8, addend: 0x0, symName: __ZL8file1054, symObjAddr: 0x158750, symBinAddr: 0x14A550, symSize: 0x0 }
  - { offsetInCU: 0xE2EC, offset: 0x2F4F3, size: 0x8, addend: 0x0, symName: __ZL8file1055, symObjAddr: 0x159280, symBinAddr: 0x14B080, symSize: 0x0 }
  - { offsetInCU: 0xE30B, offset: 0x2F512, size: 0x8, addend: 0x0, symName: __ZL8file1056, symObjAddr: 0x159DB0, symBinAddr: 0x14BBB0, symSize: 0x0 }
  - { offsetInCU: 0xE32A, offset: 0x2F531, size: 0x8, addend: 0x0, symName: __ZL8file1057, symObjAddr: 0x159EF0, symBinAddr: 0x14BCF0, symSize: 0x0 }
  - { offsetInCU: 0xE349, offset: 0x2F550, size: 0x8, addend: 0x0, symName: __ZL8file1058, symObjAddr: 0x15A030, symBinAddr: 0x14BE30, symSize: 0x0 }
  - { offsetInCU: 0xE368, offset: 0x2F56F, size: 0x8, addend: 0x0, symName: __ZL8file1059, symObjAddr: 0x15A170, symBinAddr: 0x14BF70, symSize: 0x0 }
  - { offsetInCU: 0xE387, offset: 0x2F58E, size: 0x8, addend: 0x0, symName: __ZL8file1060, symObjAddr: 0x15A2D0, symBinAddr: 0x14C0D0, symSize: 0x0 }
  - { offsetInCU: 0xE3A6, offset: 0x2F5AD, size: 0x8, addend: 0x0, symName: __ZL8file1061, symObjAddr: 0x15A420, symBinAddr: 0x14C220, symSize: 0x0 }
  - { offsetInCU: 0xE3C5, offset: 0x2F5CC, size: 0x8, addend: 0x0, symName: __ZL8file1062, symObjAddr: 0x15A560, symBinAddr: 0x14C360, symSize: 0x0 }
  - { offsetInCU: 0xE3E4, offset: 0x2F5EB, size: 0x8, addend: 0x0, symName: __ZL8file1063, symObjAddr: 0x15A6B0, symBinAddr: 0x14C4B0, symSize: 0x0 }
  - { offsetInCU: 0xE403, offset: 0x2F60A, size: 0x8, addend: 0x0, symName: __ZL8file1064, symObjAddr: 0x15A7F0, symBinAddr: 0x14C5F0, symSize: 0x0 }
  - { offsetInCU: 0xE422, offset: 0x2F629, size: 0x8, addend: 0x0, symName: __ZL8file1065, symObjAddr: 0x15A940, symBinAddr: 0x14C740, symSize: 0x0 }
  - { offsetInCU: 0xE441, offset: 0x2F648, size: 0x8, addend: 0x0, symName: __ZL8file1066, symObjAddr: 0x15AA90, symBinAddr: 0x14C890, symSize: 0x0 }
  - { offsetInCU: 0xE460, offset: 0x2F667, size: 0x8, addend: 0x0, symName: __ZL8file1067, symObjAddr: 0x15ABF0, symBinAddr: 0x14C9F0, symSize: 0x0 }
  - { offsetInCU: 0xE47F, offset: 0x2F686, size: 0x8, addend: 0x0, symName: __ZL8file1068, symObjAddr: 0x15AD60, symBinAddr: 0x14CB60, symSize: 0x0 }
  - { offsetInCU: 0xE49E, offset: 0x2F6A5, size: 0x8, addend: 0x0, symName: __ZL9layouts89, symObjAddr: 0x1B3F20, symBinAddr: 0x1A5DB0, symSize: 0x0 }
  - { offsetInCU: 0xE4BD, offset: 0x2F6C4, size: 0x8, addend: 0x0, symName: __ZL8file1069, symObjAddr: 0x15AEB0, symBinAddr: 0x14CCB0, symSize: 0x0 }
  - { offsetInCU: 0xE4F1, offset: 0x2F6F8, size: 0x8, addend: 0x0, symName: __ZL8file1070, symObjAddr: 0x15B540, symBinAddr: 0x14D340, symSize: 0x0 }
  - { offsetInCU: 0xE525, offset: 0x2F72C, size: 0x8, addend: 0x0, symName: __ZL8file1071, symObjAddr: 0x15B820, symBinAddr: 0x14D620, symSize: 0x0 }
  - { offsetInCU: 0xE559, offset: 0x2F760, size: 0x8, addend: 0x0, symName: __ZL8file1072, symObjAddr: 0x15C7F0, symBinAddr: 0x14E5F0, symSize: 0x0 }
  - { offsetInCU: 0xE578, offset: 0x2F77F, size: 0x8, addend: 0x0, symName: __ZL8file1073, symObjAddr: 0x15D7C0, symBinAddr: 0x14F5C0, symSize: 0x0 }
  - { offsetInCU: 0xE597, offset: 0x2F79E, size: 0x8, addend: 0x0, symName: __ZL8file1074, symObjAddr: 0x15DE60, symBinAddr: 0x14FC60, symSize: 0x0 }
  - { offsetInCU: 0xE5B6, offset: 0x2F7BD, size: 0x8, addend: 0x0, symName: __ZL8file1075, symObjAddr: 0x15E500, symBinAddr: 0x150300, symSize: 0x0 }
  - { offsetInCU: 0xE5D5, offset: 0x2F7DC, size: 0x8, addend: 0x0, symName: __ZL8file1076, symObjAddr: 0x15EBA0, symBinAddr: 0x1509A0, symSize: 0x0 }
  - { offsetInCU: 0xE609, offset: 0x2F810, size: 0x8, addend: 0x0, symName: __ZL8file1077, symObjAddr: 0x15FBF0, symBinAddr: 0x1519F0, symSize: 0x0 }
  - { offsetInCU: 0xE63D, offset: 0x2F844, size: 0x8, addend: 0x0, symName: __ZL8file1078, symObjAddr: 0x160280, symBinAddr: 0x152080, symSize: 0x0 }
  - { offsetInCU: 0xE671, offset: 0x2F878, size: 0x8, addend: 0x0, symName: __ZL8file1079, symObjAddr: 0x160920, symBinAddr: 0x152720, symSize: 0x0 }
  - { offsetInCU: 0xE6A5, offset: 0x2F8AC, size: 0x8, addend: 0x0, symName: __ZL8file1080, symObjAddr: 0x160FB0, symBinAddr: 0x152DB0, symSize: 0x0 }
  - { offsetInCU: 0xE6D9, offset: 0x2F8E0, size: 0x8, addend: 0x0, symName: __ZL8file1081, symObjAddr: 0x161650, symBinAddr: 0x153450, symSize: 0x0 }
  - { offsetInCU: 0xE6F8, offset: 0x2F8FF, size: 0x8, addend: 0x0, symName: __ZL8file1082, symObjAddr: 0x161CE0, symBinAddr: 0x153AE0, symSize: 0x0 }
  - { offsetInCU: 0xE717, offset: 0x2F91E, size: 0x8, addend: 0x0, symName: __ZL8file1083, symObjAddr: 0x162030, symBinAddr: 0x153E30, symSize: 0x0 }
  - { offsetInCU: 0xE736, offset: 0x2F93D, size: 0x8, addend: 0x0, symName: __ZL8file1084, symObjAddr: 0x162660, symBinAddr: 0x154460, symSize: 0x0 }
  - { offsetInCU: 0xE755, offset: 0x2F95C, size: 0x8, addend: 0x0, symName: __ZL8file1085, symObjAddr: 0x162C80, symBinAddr: 0x154A80, symSize: 0x0 }
  - { offsetInCU: 0xE789, offset: 0x2F990, size: 0x8, addend: 0x0, symName: __ZL9patches89, symObjAddr: 0x10BD0, symBinAddr: 0x1B3580, symSize: 0x0 }
  - { offsetInCU: 0xE7A8, offset: 0x2F9AF, size: 0x8, addend: 0x0, symName: __ZL11patchBuf113, symObjAddr: 0x16330C, symBinAddr: 0x15510C, symSize: 0x0 }
  - { offsetInCU: 0xE7C7, offset: 0x2F9CE, size: 0x8, addend: 0x0, symName: __ZL11revisions40, symObjAddr: 0x5AA7C, symBinAddr: 0x4C87C, symSize: 0x0 }
  - { offsetInCU: 0xE7E6, offset: 0x2F9ED, size: 0x8, addend: 0x0, symName: __ZL11platforms90, symObjAddr: 0x1B40C0, symBinAddr: 0x1A5F50, symSize: 0x0 }
  - { offsetInCU: 0xE805, offset: 0x2FA0C, size: 0x8, addend: 0x0, symName: __ZL8file1086, symObjAddr: 0x163310, symBinAddr: 0x155110, symSize: 0x0 }
  - { offsetInCU: 0xE824, offset: 0x2FA2B, size: 0x8, addend: 0x0, symName: __ZL8file1087, symObjAddr: 0x163450, symBinAddr: 0x155250, symSize: 0x0 }
  - { offsetInCU: 0xE843, offset: 0x2FA4A, size: 0x8, addend: 0x0, symName: __ZL8file1088, symObjAddr: 0x1635A0, symBinAddr: 0x1553A0, symSize: 0x0 }
  - { offsetInCU: 0xE862, offset: 0x2FA69, size: 0x8, addend: 0x0, symName: __ZL8file1089, symObjAddr: 0x1636F0, symBinAddr: 0x1554F0, symSize: 0x0 }
  - { offsetInCU: 0xE881, offset: 0x2FA88, size: 0x8, addend: 0x0, symName: __ZL8file1090, symObjAddr: 0x163860, symBinAddr: 0x155660, symSize: 0x0 }
  - { offsetInCU: 0xE8B5, offset: 0x2FABC, size: 0x8, addend: 0x0, symName: __ZL9layouts90, symObjAddr: 0x1B4140, symBinAddr: 0x1A5FD0, symSize: 0x0 }
  - { offsetInCU: 0xE8D4, offset: 0x2FADB, size: 0x8, addend: 0x0, symName: __ZL8file1091, symObjAddr: 0x1643A0, symBinAddr: 0x1561A0, symSize: 0x0 }
  - { offsetInCU: 0xE908, offset: 0x2FB0F, size: 0x8, addend: 0x0, symName: __ZL8file1092, symObjAddr: 0x164970, symBinAddr: 0x156770, symSize: 0x0 }
  - { offsetInCU: 0xE927, offset: 0x2FB2E, size: 0x8, addend: 0x0, symName: __ZL8file1093, symObjAddr: 0x164F40, symBinAddr: 0x156D40, symSize: 0x0 }
  - { offsetInCU: 0xE95B, offset: 0x2FB62, size: 0x8, addend: 0x0, symName: __ZL8file1094, symObjAddr: 0x1655C0, symBinAddr: 0x1573C0, symSize: 0x0 }
  - { offsetInCU: 0xE97A, offset: 0x2FB81, size: 0x8, addend: 0x0, symName: __ZL8file1095, symObjAddr: 0x165C00, symBinAddr: 0x157A00, symSize: 0x0 }
  - { offsetInCU: 0xE999, offset: 0x2FBA0, size: 0x8, addend: 0x0, symName: __ZL9patches90, symObjAddr: 0x10DE0, symBinAddr: 0x1B3790, symSize: 0x0 }
  - { offsetInCU: 0xE9B8, offset: 0x2FBBF, size: 0x8, addend: 0x0, symName: __ZL11patchBuf114, symObjAddr: 0x166235, symBinAddr: 0x158035, symSize: 0x0 }
  - { offsetInCU: 0xE9D7, offset: 0x2FBDE, size: 0x8, addend: 0x0, symName: __ZL11revisions41, symObjAddr: 0x5AA84, symBinAddr: 0x4C884, symSize: 0x0 }
  - { offsetInCU: 0xE9F6, offset: 0x2FBFD, size: 0x8, addend: 0x0, symName: __ZL11platforms91, symObjAddr: 0x1B41C0, symBinAddr: 0x1A6050, symSize: 0x0 }
  - { offsetInCU: 0xEA15, offset: 0x2FC1C, size: 0x8, addend: 0x0, symName: __ZL8file1096, symObjAddr: 0x166240, symBinAddr: 0x158040, symSize: 0x0 }
  - { offsetInCU: 0xEA34, offset: 0x2FC3B, size: 0x8, addend: 0x0, symName: __ZL8file1097, symObjAddr: 0x166480, symBinAddr: 0x158280, symSize: 0x0 }
  - { offsetInCU: 0xEA53, offset: 0x2FC5A, size: 0x8, addend: 0x0, symName: __ZL8file1098, symObjAddr: 0x166610, symBinAddr: 0x158410, symSize: 0x0 }
  - { offsetInCU: 0xEA87, offset: 0x2FC8E, size: 0x8, addend: 0x0, symName: __ZL8file1099, symObjAddr: 0x1667C0, symBinAddr: 0x1585C0, symSize: 0x0 }
  - { offsetInCU: 0xEAA6, offset: 0x2FCAD, size: 0x8, addend: 0x0, symName: __ZL8file1100, symObjAddr: 0x166930, symBinAddr: 0x158730, symSize: 0x0 }
  - { offsetInCU: 0xEAC5, offset: 0x2FCCC, size: 0x8, addend: 0x0, symName: __ZL8file1101, symObjAddr: 0x166AB0, symBinAddr: 0x1588B0, symSize: 0x0 }
  - { offsetInCU: 0xEAE4, offset: 0x2FCEB, size: 0x8, addend: 0x0, symName: __ZL8file1102, symObjAddr: 0x166C30, symBinAddr: 0x158A30, symSize: 0x0 }
  - { offsetInCU: 0xEB03, offset: 0x2FD0A, size: 0x8, addend: 0x0, symName: __ZL8file1103, symObjAddr: 0x166D80, symBinAddr: 0x158B80, symSize: 0x0 }
  - { offsetInCU: 0xEB22, offset: 0x2FD29, size: 0x8, addend: 0x0, symName: __ZL9layouts91, symObjAddr: 0x1B42E0, symBinAddr: 0x1A6170, symSize: 0x0 }
  - { offsetInCU: 0xEB41, offset: 0x2FD48, size: 0x8, addend: 0x0, symName: __ZL8file1104, symObjAddr: 0x166EF0, symBinAddr: 0x158CF0, symSize: 0x0 }
  - { offsetInCU: 0xEB60, offset: 0x2FD67, size: 0x8, addend: 0x0, symName: __ZL8file1105, symObjAddr: 0x167240, symBinAddr: 0x159040, symSize: 0x0 }
  - { offsetInCU: 0xEB7F, offset: 0x2FD86, size: 0x8, addend: 0x0, symName: __ZL8file1106, symObjAddr: 0x167590, symBinAddr: 0x159390, symSize: 0x0 }
  - { offsetInCU: 0xEB9E, offset: 0x2FDA5, size: 0x8, addend: 0x0, symName: __ZL8file1107, symObjAddr: 0x1678E0, symBinAddr: 0x1596E0, symSize: 0x0 }
  - { offsetInCU: 0xEBD2, offset: 0x2FDD9, size: 0x8, addend: 0x0, symName: __ZL8file1108, symObjAddr: 0x167C10, symBinAddr: 0x159A10, symSize: 0x0 }
  - { offsetInCU: 0xEBF1, offset: 0x2FDF8, size: 0x8, addend: 0x0, symName: __ZL8file1109, symObjAddr: 0x167F40, symBinAddr: 0x159D40, symSize: 0x0 }
  - { offsetInCU: 0xEC10, offset: 0x2FE17, size: 0x8, addend: 0x0, symName: __ZL8file1110, symObjAddr: 0x1682B0, symBinAddr: 0x15A0B0, symSize: 0x0 }
  - { offsetInCU: 0xEC2F, offset: 0x2FE36, size: 0x8, addend: 0x0, symName: __ZL8file1111, symObjAddr: 0x168620, symBinAddr: 0x15A420, symSize: 0x0 }
  - { offsetInCU: 0xEC4E, offset: 0x2FE55, size: 0x8, addend: 0x0, symName: __ZL8file1112, symObjAddr: 0x1689A0, symBinAddr: 0x15A7A0, symSize: 0x0 }
  - { offsetInCU: 0xEC82, offset: 0x2FE89, size: 0x8, addend: 0x0, symName: __ZL8file1113, symObjAddr: 0x168EF0, symBinAddr: 0x15ACF0, symSize: 0x0 }
  - { offsetInCU: 0xECB6, offset: 0x2FEBD, size: 0x8, addend: 0x0, symName: __ZL8file1114, symObjAddr: 0x169430, symBinAddr: 0x15B230, symSize: 0x0 }
  - { offsetInCU: 0xECEA, offset: 0x2FEF1, size: 0x8, addend: 0x0, symName: __ZL8file1115, symObjAddr: 0x1697B0, symBinAddr: 0x15B5B0, symSize: 0x0 }
  - { offsetInCU: 0xED09, offset: 0x2FF10, size: 0x8, addend: 0x0, symName: __ZL9patches91, symObjAddr: 0x10F90, symBinAddr: 0x1B3940, symSize: 0x0 }
  - { offsetInCU: 0xED28, offset: 0x2FF2F, size: 0x8, addend: 0x0, symName: __ZL11patchBuf115, symObjAddr: 0x169AE1, symBinAddr: 0x15B8E1, symSize: 0x0 }
  - { offsetInCU: 0xED47, offset: 0x2FF4E, size: 0x8, addend: 0x0, symName: __ZL11revisions42, symObjAddr: 0x5AA8C, symBinAddr: 0x4C88C, symSize: 0x0 }
  - { offsetInCU: 0xED66, offset: 0x2FF6D, size: 0x8, addend: 0x0, symName: __ZL11platforms92, symObjAddr: 0x1B4400, symBinAddr: 0x1A6290, symSize: 0x0 }
  - { offsetInCU: 0xED85, offset: 0x2FF8C, size: 0x8, addend: 0x0, symName: __ZL8file1116, symObjAddr: 0x169AF0, symBinAddr: 0x15B8F0, symSize: 0x0 }
  - { offsetInCU: 0xEDB9, offset: 0x2FFC0, size: 0x8, addend: 0x0, symName: __ZL8file1117, symObjAddr: 0x169C20, symBinAddr: 0x15BA20, symSize: 0x0 }
  - { offsetInCU: 0xEDD8, offset: 0x2FFDF, size: 0x8, addend: 0x0, symName: __ZL9layouts92, symObjAddr: 0x1B4430, symBinAddr: 0x1A62C0, symSize: 0x0 }
  - { offsetInCU: 0xEDF7, offset: 0x2FFFE, size: 0x8, addend: 0x0, symName: __ZL8file1118, symObjAddr: 0x169D60, symBinAddr: 0x15BB60, symSize: 0x0 }
  - { offsetInCU: 0xEE16, offset: 0x3001D, size: 0x8, addend: 0x0, symName: __ZL8file1119, symObjAddr: 0x169E40, symBinAddr: 0x15BC40, symSize: 0x0 }
  - { offsetInCU: 0xEE4A, offset: 0x30051, size: 0x8, addend: 0x0, symName: __ZL9patches92, symObjAddr: 0x110E0, symBinAddr: 0x1B3A90, symSize: 0x0 }
  - { offsetInCU: 0xEE69, offset: 0x30070, size: 0x8, addend: 0x0, symName: __ZL11patchBuf116, symObjAddr: 0x16A4DB, symBinAddr: 0x15C2DB, symSize: 0x0 }
  - { offsetInCU: 0xEE88, offset: 0x3008F, size: 0x8, addend: 0x0, symName: __ZL11platforms93, symObjAddr: 0x1B4460, symBinAddr: 0x1A62F0, symSize: 0x0 }
  - { offsetInCU: 0xEEA7, offset: 0x300AE, size: 0x8, addend: 0x0, symName: __ZL8file1120, symObjAddr: 0x16A4E0, symBinAddr: 0x15C2E0, symSize: 0x0 }
  - { offsetInCU: 0xEEC6, offset: 0x300CD, size: 0x8, addend: 0x0, symName: __ZL8file1121, symObjAddr: 0x16A620, symBinAddr: 0x15C420, symSize: 0x0 }
  - { offsetInCU: 0xEEE5, offset: 0x300EC, size: 0x8, addend: 0x0, symName: __ZL8file1122, symObjAddr: 0x16A760, symBinAddr: 0x15C560, symSize: 0x0 }
  - { offsetInCU: 0xEF04, offset: 0x3010B, size: 0x8, addend: 0x0, symName: __ZL8file1123, symObjAddr: 0x16A8A0, symBinAddr: 0x15C6A0, symSize: 0x0 }
  - { offsetInCU: 0xEF23, offset: 0x3012A, size: 0x8, addend: 0x0, symName: __ZL9layouts93, symObjAddr: 0x1B44C0, symBinAddr: 0x1A6350, symSize: 0x0 }
  - { offsetInCU: 0xEF42, offset: 0x30149, size: 0x8, addend: 0x0, symName: __ZL8file1124, symObjAddr: 0x16A9E0, symBinAddr: 0x15C7E0, symSize: 0x0 }
  - { offsetInCU: 0xEF61, offset: 0x30168, size: 0x8, addend: 0x0, symName: __ZL8file1125, symObjAddr: 0x16B7B0, symBinAddr: 0x15D5B0, symSize: 0x0 }
  - { offsetInCU: 0xEF80, offset: 0x30187, size: 0x8, addend: 0x0, symName: __ZL8file1126, symObjAddr: 0x16C580, symBinAddr: 0x15E380, symSize: 0x0 }
  - { offsetInCU: 0xEFB4, offset: 0x301BB, size: 0x8, addend: 0x0, symName: __ZL8file1127, symObjAddr: 0x16D540, symBinAddr: 0x15F340, symSize: 0x0 }
  - { offsetInCU: 0xEFD3, offset: 0x301DA, size: 0x8, addend: 0x0, symName: __ZL9patches93, symObjAddr: 0x112C0, symBinAddr: 0x1B3C70, symSize: 0x0 }
  - { offsetInCU: 0xEFF2, offset: 0x301F9, size: 0x8, addend: 0x0, symName: __ZL11patchBuf117, symObjAddr: 0x16E302, symBinAddr: 0x160102, symSize: 0x0 }
  - { offsetInCU: 0xF011, offset: 0x30218, size: 0x8, addend: 0x0, symName: __ZL11revisions43, symObjAddr: 0x5AA90, symBinAddr: 0x4C890, symSize: 0x0 }
  - { offsetInCU: 0xF030, offset: 0x30237, size: 0x8, addend: 0x0, symName: __ZL11platforms94, symObjAddr: 0x1B4520, symBinAddr: 0x1A63B0, symSize: 0x0 }
  - { offsetInCU: 0xF04F, offset: 0x30256, size: 0x8, addend: 0x0, symName: __ZL8file1128, symObjAddr: 0x16E310, symBinAddr: 0x160110, symSize: 0x0 }
  - { offsetInCU: 0xF06E, offset: 0x30275, size: 0x8, addend: 0x0, symName: __ZL9layouts94, symObjAddr: 0x1B4540, symBinAddr: 0x1A63D0, symSize: 0x0 }
  - { offsetInCU: 0xF08D, offset: 0x30294, size: 0x8, addend: 0x0, symName: __ZL8file1129, symObjAddr: 0x16E470, symBinAddr: 0x160270, symSize: 0x0 }
  - { offsetInCU: 0xF0C1, offset: 0x302C8, size: 0x8, addend: 0x0, symName: __ZL9patches94, symObjAddr: 0x11470, symBinAddr: 0x1B3E20, symSize: 0x0 }
  - { offsetInCU: 0xF0E0, offset: 0x302E7, size: 0x8, addend: 0x0, symName: __ZL11patchBuf118, symObjAddr: 0x16EB34, symBinAddr: 0x160934, symSize: 0x0 }
  - { offsetInCU: 0xF0FF, offset: 0x30306, size: 0x8, addend: 0x0, symName: __ZL11revisions44, symObjAddr: 0x5AA94, symBinAddr: 0x4C894, symSize: 0x0 }
  - { offsetInCU: 0xF11E, offset: 0x30325, size: 0x8, addend: 0x0, symName: __ZL11platforms95, symObjAddr: 0x1B4560, symBinAddr: 0x1A63F0, symSize: 0x0 }
  - { offsetInCU: 0xF13D, offset: 0x30344, size: 0x8, addend: 0x0, symName: __ZL8file1130, symObjAddr: 0x16EB40, symBinAddr: 0x160940, symSize: 0x0 }
  - { offsetInCU: 0xF15C, offset: 0x30363, size: 0x8, addend: 0x0, symName: __ZL8file1131, symObjAddr: 0x16EF50, symBinAddr: 0x160D50, symSize: 0x0 }
  - { offsetInCU: 0xF17B, offset: 0x30382, size: 0x8, addend: 0x0, symName: __ZL8file1132, symObjAddr: 0x16F360, symBinAddr: 0x161160, symSize: 0x0 }
  - { offsetInCU: 0xF19A, offset: 0x303A1, size: 0x8, addend: 0x0, symName: __ZL8file1133, symObjAddr: 0x16F4F0, symBinAddr: 0x1612F0, symSize: 0x0 }
  - { offsetInCU: 0xF1D0, offset: 0x303D7, size: 0x8, addend: 0x0, symName: __ZL9layouts95, symObjAddr: 0x1B4710, symBinAddr: 0x1A65A0, symSize: 0x0 }
  - { offsetInCU: 0xF1EF, offset: 0x303F6, size: 0x8, addend: 0x0, symName: __ZL8file1134, symObjAddr: 0x170290, symBinAddr: 0x162090, symSize: 0x0 }
  - { offsetInCU: 0xF223, offset: 0x3042A, size: 0x8, addend: 0x0, symName: __ZL8file1135, symObjAddr: 0x170830, symBinAddr: 0x162630, symSize: 0x0 }
  - { offsetInCU: 0xF242, offset: 0x30449, size: 0x8, addend: 0x0, symName: __ZL8file1136, symObjAddr: 0x170DD0, symBinAddr: 0x162BD0, symSize: 0x0 }
  - { offsetInCU: 0xF276, offset: 0x3047D, size: 0x8, addend: 0x0, symName: __ZL8file1137, symObjAddr: 0x1713C0, symBinAddr: 0x1631C0, symSize: 0x0 }
  - { offsetInCU: 0xF2AA, offset: 0x304B1, size: 0x8, addend: 0x0, symName: __ZL8file1138, symObjAddr: 0x172750, symBinAddr: 0x164550, symSize: 0x0 }
  - { offsetInCU: 0xF2DE, offset: 0x304E5, size: 0x8, addend: 0x0, symName: __ZL8file1139, symObjAddr: 0x172FC0, symBinAddr: 0x164DC0, symSize: 0x0 }
  - { offsetInCU: 0xF312, offset: 0x30519, size: 0x8, addend: 0x0, symName: __ZL8file1140, symObjAddr: 0x173A20, symBinAddr: 0x165820, symSize: 0x0 }
  - { offsetInCU: 0xF346, offset: 0x3054D, size: 0x8, addend: 0x0, symName: __ZL8file1141, symObjAddr: 0x174290, symBinAddr: 0x166090, symSize: 0x0 }
  - { offsetInCU: 0xF37A, offset: 0x30581, size: 0x8, addend: 0x0, symName: __ZL8file1142, symObjAddr: 0x174BC0, symBinAddr: 0x1669C0, symSize: 0x0 }
  - { offsetInCU: 0xF3B0, offset: 0x305B7, size: 0x8, addend: 0x0, symName: __ZL8file1143, symObjAddr: 0x176280, symBinAddr: 0x168080, symSize: 0x0 }
  - { offsetInCU: 0xF3E6, offset: 0x305ED, size: 0x8, addend: 0x0, symName: __ZL8file1144, symObjAddr: 0x177C10, symBinAddr: 0x169A10, symSize: 0x0 }
  - { offsetInCU: 0xF41C, offset: 0x30623, size: 0x8, addend: 0x0, symName: __ZL8file1145, symObjAddr: 0x178230, symBinAddr: 0x16A030, symSize: 0x0 }
  - { offsetInCU: 0xF452, offset: 0x30659, size: 0x8, addend: 0x0, symName: __ZL8file1146, symObjAddr: 0x1788D0, symBinAddr: 0x16A6D0, symSize: 0x0 }
  - { offsetInCU: 0xF488, offset: 0x3068F, size: 0x8, addend: 0x0, symName: __ZL8file1147, symObjAddr: 0x178F80, symBinAddr: 0x16AD80, symSize: 0x0 }
  - { offsetInCU: 0xF4BE, offset: 0x306C5, size: 0x8, addend: 0x0, symName: __ZL8file1148, symObjAddr: 0x179880, symBinAddr: 0x16B680, symSize: 0x0 }
  - { offsetInCU: 0xF4F4, offset: 0x306FB, size: 0x8, addend: 0x0, symName: __ZL8file1149, symObjAddr: 0x17A190, symBinAddr: 0x16BF90, symSize: 0x0 }
  - { offsetInCU: 0xF52A, offset: 0x30731, size: 0x8, addend: 0x0, symName: __ZL8file1150, symObjAddr: 0x17B0B0, symBinAddr: 0x16CEB0, symSize: 0x0 }
  - { offsetInCU: 0xF560, offset: 0x30767, size: 0x8, addend: 0x0, symName: __ZL8file1151, symObjAddr: 0x17BCC0, symBinAddr: 0x16DAC0, symSize: 0x0 }
  - { offsetInCU: 0xF596, offset: 0x3079D, size: 0x8, addend: 0x0, symName: __ZL9patches95, symObjAddr: 0x115F0, symBinAddr: 0x1B3FA0, symSize: 0x0 }
  - { offsetInCU: 0xF5B7, offset: 0x307BE, size: 0x8, addend: 0x0, symName: __ZL11platforms96, symObjAddr: 0x1B48C0, symBinAddr: 0x1A6750, symSize: 0x0 }
  - { offsetInCU: 0xF5D8, offset: 0x307DF, size: 0x8, addend: 0x0, symName: __ZL8file1152, symObjAddr: 0x17BFF0, symBinAddr: 0x16DDF0, symSize: 0x0 }
  - { offsetInCU: 0xF5F9, offset: 0x30800, size: 0x8, addend: 0x0, symName: __ZL9layouts96, symObjAddr: 0x1B48F0, symBinAddr: 0x1A6780, symSize: 0x0 }
  - { offsetInCU: 0xF61A, offset: 0x30821, size: 0x8, addend: 0x0, symName: __ZL8file1153, symObjAddr: 0x17C1B0, symBinAddr: 0x16DFB0, symSize: 0x0 }
  - { offsetInCU: 0xF650, offset: 0x30857, size: 0x8, addend: 0x0, symName: __ZL8file1154, symObjAddr: 0x17C4D0, symBinAddr: 0x16E2D0, symSize: 0x0 }
  - { offsetInCU: 0xF671, offset: 0x30878, size: 0x8, addend: 0x0, symName: __ZL9patches96, symObjAddr: 0x11740, symBinAddr: 0x1B40F0, symSize: 0x0 }
  - { offsetInCU: 0xF692, offset: 0x30899, size: 0x8, addend: 0x0, symName: __ZL11patchBuf119, symObjAddr: 0x17C7EE, symBinAddr: 0x16E5EE, symSize: 0x0 }
  - { offsetInCU: 0xF6B3, offset: 0x308BA, size: 0x8, addend: 0x0, symName: __ZL11revisions45, symObjAddr: 0x5AA9C, symBinAddr: 0x4C89C, symSize: 0x0 }
  - { offsetInCU: 0xF6D4, offset: 0x308DB, size: 0x8, addend: 0x0, symName: __ZL11platforms97, symObjAddr: 0x1B4920, symBinAddr: 0x1A67B0, symSize: 0x0 }
  - { offsetInCU: 0xF6F5, offset: 0x308FC, size: 0x8, addend: 0x0, symName: __ZL8file1155, symObjAddr: 0x17C800, symBinAddr: 0x16E600, symSize: 0x0 }
  - { offsetInCU: 0xF716, offset: 0x3091D, size: 0x8, addend: 0x0, symName: __ZL8file1156, symObjAddr: 0x17CD40, symBinAddr: 0x16EB40, symSize: 0x0 }
  - { offsetInCU: 0xF74C, offset: 0x30953, size: 0x8, addend: 0x0, symName: __ZL9layouts97, symObjAddr: 0x1B4950, symBinAddr: 0x1A67E0, symSize: 0x0 }
  - { offsetInCU: 0xF76D, offset: 0x30974, size: 0x8, addend: 0x0, symName: __ZL8file1157, symObjAddr: 0x17D290, symBinAddr: 0x16F090, symSize: 0x0 }
  - { offsetInCU: 0xF78E, offset: 0x30995, size: 0x8, addend: 0x0, symName: __ZL8file1158, symObjAddr: 0x17D5C0, symBinAddr: 0x16F3C0, symSize: 0x0 }
  - { offsetInCU: 0xF7AF, offset: 0x309B6, size: 0x8, addend: 0x0, symName: __ZL9patches97, symObjAddr: 0x118C0, symBinAddr: 0x1B4270, symSize: 0x0 }
  - { offsetInCU: 0xF7D0, offset: 0x309D7, size: 0x8, addend: 0x0, symName: __ZL11patchBuf120, symObjAddr: 0x17D8EF, symBinAddr: 0x16F6EF, symSize: 0x0 }
  - { offsetInCU: 0xF7F1, offset: 0x309F8, size: 0x8, addend: 0x0, symName: __ZL11platforms98, symObjAddr: 0x1B4980, symBinAddr: 0x1A6810, symSize: 0x0 }
  - { offsetInCU: 0xF812, offset: 0x30A19, size: 0x8, addend: 0x0, symName: __ZL8file1159, symObjAddr: 0x17D900, symBinAddr: 0x16F700, symSize: 0x0 }
  - { offsetInCU: 0xF833, offset: 0x30A3A, size: 0x8, addend: 0x0, symName: __ZL8file1160, symObjAddr: 0x17DA40, symBinAddr: 0x16F840, symSize: 0x0 }
  - { offsetInCU: 0xF854, offset: 0x30A5B, size: 0x8, addend: 0x0, symName: __ZL8file1161, symObjAddr: 0x17DB80, symBinAddr: 0x16F980, symSize: 0x0 }
  - { offsetInCU: 0xF875, offset: 0x30A7C, size: 0x8, addend: 0x0, symName: __ZL8file1162, symObjAddr: 0x17DCC0, symBinAddr: 0x16FAC0, symSize: 0x0 }
  - { offsetInCU: 0xF896, offset: 0x30A9D, size: 0x8, addend: 0x0, symName: __ZL8file1163, symObjAddr: 0x17DE40, symBinAddr: 0x16FC40, symSize: 0x0 }
  - { offsetInCU: 0xF8B7, offset: 0x30ABE, size: 0x8, addend: 0x0, symName: __ZL8file1164, symObjAddr: 0x17DF80, symBinAddr: 0x16FD80, symSize: 0x0 }
  - { offsetInCU: 0xF8D8, offset: 0x30ADF, size: 0x8, addend: 0x0, symName: __ZL8file1165, symObjAddr: 0x17E0B0, symBinAddr: 0x16FEB0, symSize: 0x0 }
  - { offsetInCU: 0xF8F9, offset: 0x30B00, size: 0x8, addend: 0x0, symName: __ZL8file1166, symObjAddr: 0x17E1F0, symBinAddr: 0x16FFF0, symSize: 0x0 }
  - { offsetInCU: 0xF91A, offset: 0x30B21, size: 0x8, addend: 0x0, symName: __ZL8file1167, symObjAddr: 0x17E350, symBinAddr: 0x170150, symSize: 0x0 }
  - { offsetInCU: 0xF950, offset: 0x30B57, size: 0x8, addend: 0x0, symName: __ZL9layouts98, symObjAddr: 0x1B4A60, symBinAddr: 0x1A68F0, symSize: 0x0 }
  - { offsetInCU: 0xF971, offset: 0x30B78, size: 0x8, addend: 0x0, symName: __ZL8file1168, symObjAddr: 0x17E480, symBinAddr: 0x170280, symSize: 0x0 }
  - { offsetInCU: 0xF9A7, offset: 0x30BAE, size: 0x8, addend: 0x0, symName: __ZL8file1169, symObjAddr: 0x17EA60, symBinAddr: 0x170860, symSize: 0x0 }
  - { offsetInCU: 0xF9C8, offset: 0x30BCF, size: 0x8, addend: 0x0, symName: __ZL8file1170, symObjAddr: 0x17F040, symBinAddr: 0x170E40, symSize: 0x0 }
  - { offsetInCU: 0xF9E9, offset: 0x30BF0, size: 0x8, addend: 0x0, symName: __ZL8file1171, symObjAddr: 0x17F620, symBinAddr: 0x171420, symSize: 0x0 }
  - { offsetInCU: 0xFA0A, offset: 0x30C11, size: 0x8, addend: 0x0, symName: __ZL8file1172, symObjAddr: 0x180560, symBinAddr: 0x172360, symSize: 0x0 }
  - { offsetInCU: 0xFA2B, offset: 0x30C32, size: 0x8, addend: 0x0, symName: __ZL8file1173, symObjAddr: 0x180B40, symBinAddr: 0x172940, symSize: 0x0 }
  - { offsetInCU: 0xFA61, offset: 0x30C68, size: 0x8, addend: 0x0, symName: __ZL8file1174, symObjAddr: 0x181120, symBinAddr: 0x172F20, symSize: 0x0 }
  - { offsetInCU: 0xFA82, offset: 0x30C89, size: 0x8, addend: 0x0, symName: __ZL8file1175, symObjAddr: 0x1816F0, symBinAddr: 0x1734F0, symSize: 0x0 }
  - { offsetInCU: 0xFAA3, offset: 0x30CAA, size: 0x8, addend: 0x0, symName: __ZL8file1176, symObjAddr: 0x181CD0, symBinAddr: 0x173AD0, symSize: 0x0 }
  - { offsetInCU: 0xFAD9, offset: 0x30CE0, size: 0x8, addend: 0x0, symName: __ZL9patches98, symObjAddr: 0x11A40, symBinAddr: 0x1B43F0, symSize: 0x0 }
  - { offsetInCU: 0xFAFA, offset: 0x30D01, size: 0x8, addend: 0x0, symName: __ZL11patchBuf121, symObjAddr: 0x1822A1, symBinAddr: 0x1740A1, symSize: 0x0 }
  - { offsetInCU: 0xFB1B, offset: 0x30D22, size: 0x8, addend: 0x0, symName: __ZL11revisions46, symObjAddr: 0x5AAA0, symBinAddr: 0x4C8A0, symSize: 0x0 }
  - { offsetInCU: 0xFB3C, offset: 0x30D43, size: 0x8, addend: 0x0, symName: __ZL11platforms99, symObjAddr: 0x1B4B40, symBinAddr: 0x1A69D0, symSize: 0x0 }
  - { offsetInCU: 0xFB71, offset: 0x30D78, size: 0x8, addend: 0x0, symName: __ZL8file1177, symObjAddr: 0x1822B0, symBinAddr: 0x1740B0, symSize: 0x0 }
  - { offsetInCU: 0xFB92, offset: 0x30D99, size: 0x8, addend: 0x0, symName: __ZL8file1178, symObjAddr: 0x182410, symBinAddr: 0x174210, symSize: 0x0 }
  - { offsetInCU: 0xFBB3, offset: 0x30DBA, size: 0x8, addend: 0x0, symName: __ZL8file1179, symObjAddr: 0x182550, symBinAddr: 0x174350, symSize: 0x0 }
  - { offsetInCU: 0xFBD4, offset: 0x30DDB, size: 0x8, addend: 0x0, symName: __ZL8file1180, symObjAddr: 0x1826A0, symBinAddr: 0x1744A0, symSize: 0x0 }
  - { offsetInCU: 0xFBF5, offset: 0x30DFC, size: 0x8, addend: 0x0, symName: __ZL8file1181, symObjAddr: 0x1827E0, symBinAddr: 0x1745E0, symSize: 0x0 }
  - { offsetInCU: 0xFC2B, offset: 0x30E32, size: 0x8, addend: 0x0, symName: __ZL8file1182, symObjAddr: 0x182900, symBinAddr: 0x174700, symSize: 0x0 }
  - { offsetInCU: 0xFC61, offset: 0x30E68, size: 0x8, addend: 0x0, symName: __ZL8file1183, symObjAddr: 0x183480, symBinAddr: 0x175280, symSize: 0x0 }
  - { offsetInCU: 0xFC97, offset: 0x30E9E, size: 0x8, addend: 0x0, symName: __ZL8file1184, symObjAddr: 0x183ED0, symBinAddr: 0x175CD0, symSize: 0x0 }
  - { offsetInCU: 0xFCB8, offset: 0x30EBF, size: 0x8, addend: 0x0, symName: __ZL8file1185, symObjAddr: 0x184010, symBinAddr: 0x175E10, symSize: 0x0 }
  - { offsetInCU: 0xFCEE, offset: 0x30EF5, size: 0x8, addend: 0x0, symName: __ZL8file1186, symObjAddr: 0x185BD0, symBinAddr: 0x1779D0, symSize: 0x0 }
  - { offsetInCU: 0xFD0F, offset: 0x30F16, size: 0x8, addend: 0x0, symName: __ZL8file1187, symObjAddr: 0x185D10, symBinAddr: 0x177B10, symSize: 0x0 }
  - { offsetInCU: 0xFD30, offset: 0x30F37, size: 0x8, addend: 0x0, symName: __ZL8file1188, symObjAddr: 0x186860, symBinAddr: 0x178660, symSize: 0x0 }
  - { offsetInCU: 0xFD51, offset: 0x30F58, size: 0x8, addend: 0x0, symName: __ZL8file1189, symObjAddr: 0x1873B0, symBinAddr: 0x1791B0, symSize: 0x0 }
  - { offsetInCU: 0xFD87, offset: 0x30F8E, size: 0x8, addend: 0x0, symName: __ZL8file1190, symObjAddr: 0x187F00, symBinAddr: 0x179D00, symSize: 0x0 }
  - { offsetInCU: 0xFDA8, offset: 0x30FAF, size: 0x8, addend: 0x0, symName: __ZL8file1191, symObjAddr: 0x188050, symBinAddr: 0x179E50, symSize: 0x0 }
  - { offsetInCU: 0xFDC9, offset: 0x30FD0, size: 0x8, addend: 0x0, symName: __ZL8file1192, symObjAddr: 0x188190, symBinAddr: 0x179F90, symSize: 0x0 }
  - { offsetInCU: 0xFDEA, offset: 0x30FF1, size: 0x8, addend: 0x0, symName: __ZL9layouts99, symObjAddr: 0x1B4CC0, symBinAddr: 0x1A6B50, symSize: 0x0 }
  - { offsetInCU: 0xFE0B, offset: 0x31012, size: 0x8, addend: 0x0, symName: __ZL8file1193, symObjAddr: 0x1882D0, symBinAddr: 0x17A0D0, symSize: 0x0 }
  - { offsetInCU: 0xFE41, offset: 0x31048, size: 0x8, addend: 0x0, symName: __ZL8file1194, symObjAddr: 0x189290, symBinAddr: 0x17B090, symSize: 0x0 }
  - { offsetInCU: 0xFE62, offset: 0x31069, size: 0x8, addend: 0x0, symName: __ZL8file1195, symObjAddr: 0x1898C0, symBinAddr: 0x17B6C0, symSize: 0x0 }
  - { offsetInCU: 0xFE98, offset: 0x3109F, size: 0x8, addend: 0x0, symName: __ZL8file1196, symObjAddr: 0x189EA0, symBinAddr: 0x17BCA0, symSize: 0x0 }
  - { offsetInCU: 0xFEB9, offset: 0x310C0, size: 0x8, addend: 0x0, symName: __ZL8file1197, symObjAddr: 0x18A480, symBinAddr: 0x17C280, symSize: 0x0 }
  - { offsetInCU: 0xFEDA, offset: 0x310E1, size: 0x8, addend: 0x0, symName: __ZL8file1198, symObjAddr: 0x18AAA0, symBinAddr: 0x17C8A0, symSize: 0x0 }
  - { offsetInCU: 0xFF10, offset: 0x31117, size: 0x8, addend: 0x0, symName: __ZL8file1199, symObjAddr: 0x18BA30, symBinAddr: 0x17D830, symSize: 0x0 }
  - { offsetInCU: 0xFF31, offset: 0x31138, size: 0x8, addend: 0x0, symName: __ZL8file1200, symObjAddr: 0x18C7F0, symBinAddr: 0x17E5F0, symSize: 0x0 }
  - { offsetInCU: 0xFF52, offset: 0x31159, size: 0x8, addend: 0x0, symName: __ZL8file1201, symObjAddr: 0x18CD50, symBinAddr: 0x17EB50, symSize: 0x0 }
  - { offsetInCU: 0xFF73, offset: 0x3117A, size: 0x8, addend: 0x0, symName: __ZL8file1202, symObjAddr: 0x18DB10, symBinAddr: 0x17F910, symSize: 0x0 }
  - { offsetInCU: 0xFFA9, offset: 0x311B0, size: 0x8, addend: 0x0, symName: __ZL8file1203, symObjAddr: 0x18E070, symBinAddr: 0x17FE70, symSize: 0x0 }
  - { offsetInCU: 0xFFDF, offset: 0x311E6, size: 0x8, addend: 0x0, symName: __ZL8file1204, symObjAddr: 0x18F040, symBinAddr: 0x180E40, symSize: 0x0 }
  - { offsetInCU: 0x10000, offset: 0x31207, size: 0x8, addend: 0x0, symName: __ZL8file1205, symObjAddr: 0x190010, symBinAddr: 0x181E10, symSize: 0x0 }
  - { offsetInCU: 0x10036, offset: 0x3123D, size: 0x8, addend: 0x0, symName: __ZL8file1206, symObjAddr: 0x190FE0, symBinAddr: 0x182DE0, symSize: 0x0 }
  - { offsetInCU: 0x1006C, offset: 0x31273, size: 0x8, addend: 0x0, symName: __ZL8file1207, symObjAddr: 0x191970, symBinAddr: 0x183770, symSize: 0x0 }
  - { offsetInCU: 0x100A2, offset: 0x312A9, size: 0x8, addend: 0x0, symName: __ZL8file1208, symObjAddr: 0x1923E0, symBinAddr: 0x1841E0, symSize: 0x0 }
  - { offsetInCU: 0x100C3, offset: 0x312CA, size: 0x8, addend: 0x0, symName: __ZL9patches99, symObjAddr: 0x11B90, symBinAddr: 0x1B4540, symSize: 0x0 }
  - { offsetInCU: 0x100E4, offset: 0x312EB, size: 0x8, addend: 0x0, symName: __ZL11patchBuf122, symObjAddr: 0x1931A4, symBinAddr: 0x184FA4, symSize: 0x0 }
  - { offsetInCU: 0x10105, offset: 0x3130C, size: 0x8, addend: 0x0, symName: __ZL11revisions47, symObjAddr: 0x5AAA4, symBinAddr: 0x4C8A4, symSize: 0x0 }
  - { offsetInCU: 0x10126, offset: 0x3132D, size: 0x8, addend: 0x0, symName: __ZL12platforms100, symObjAddr: 0x1B4E40, symBinAddr: 0x1A6CD0, symSize: 0x0 }
  - { offsetInCU: 0x10147, offset: 0x3134E, size: 0x8, addend: 0x0, symName: __ZL8file1209, symObjAddr: 0x1931B0, symBinAddr: 0x184FB0, symSize: 0x0 }
  - { offsetInCU: 0x10168, offset: 0x3136F, size: 0x8, addend: 0x0, symName: __ZL10layouts100, symObjAddr: 0x1B4E60, symBinAddr: 0x1A6CF0, symSize: 0x0 }
  - { offsetInCU: 0x10189, offset: 0x31390, size: 0x8, addend: 0x0, symName: __ZL8file1210, symObjAddr: 0x1932F0, symBinAddr: 0x1850F0, symSize: 0x0 }
  - { offsetInCU: 0x101AA, offset: 0x313B1, size: 0x8, addend: 0x0, symName: __ZL10patches100, symObjAddr: 0x11D10, symBinAddr: 0x1B46C0, symSize: 0x0 }
  - { offsetInCU: 0x101CB, offset: 0x313D2, size: 0x8, addend: 0x0, symName: __ZL11patchBuf123, symObjAddr: 0x19397B, symBinAddr: 0x18577B, symSize: 0x0 }
  - { offsetInCU: 0x101EC, offset: 0x313F3, size: 0x8, addend: 0x0, symName: __ZL12platforms101, symObjAddr: 0x1B4E80, symBinAddr: 0x1A6D10, symSize: 0x0 }
  - { offsetInCU: 0x1020D, offset: 0x31414, size: 0x8, addend: 0x0, symName: __ZL8file1211, symObjAddr: 0x193980, symBinAddr: 0x185780, symSize: 0x0 }
  - { offsetInCU: 0x10243, offset: 0x3144A, size: 0x8, addend: 0x0, symName: __ZL8file1212, symObjAddr: 0x193AA0, symBinAddr: 0x1858A0, symSize: 0x0 }
  - { offsetInCU: 0x10264, offset: 0x3146B, size: 0x8, addend: 0x0, symName: __ZL8file1213, symObjAddr: 0x193BF0, symBinAddr: 0x1859F0, symSize: 0x0 }
  - { offsetInCU: 0x10285, offset: 0x3148C, size: 0x8, addend: 0x0, symName: __ZL8file1214, symObjAddr: 0x193D40, symBinAddr: 0x185B40, symSize: 0x0 }
  - { offsetInCU: 0x102A6, offset: 0x314AD, size: 0x8, addend: 0x0, symName: __ZL10layouts101, symObjAddr: 0x1B4EE0, symBinAddr: 0x1A6D70, symSize: 0x0 }
  - { offsetInCU: 0x102C7, offset: 0x314CE, size: 0x8, addend: 0x0, symName: __ZL8file1215, symObjAddr: 0x193E90, symBinAddr: 0x185C90, symSize: 0x0 }
  - { offsetInCU: 0x102FC, offset: 0x31503, size: 0x8, addend: 0x0, symName: __ZL8file1216, symObjAddr: 0x193F40, symBinAddr: 0x185D40, symSize: 0x0 }
  - { offsetInCU: 0x1031D, offset: 0x31524, size: 0x8, addend: 0x0, symName: __ZL8file1217, symObjAddr: 0x194470, symBinAddr: 0x186270, symSize: 0x0 }
  - { offsetInCU: 0x1033E, offset: 0x31545, size: 0x8, addend: 0x0, symName: __ZL8file1218, symObjAddr: 0x1949A0, symBinAddr: 0x1867A0, symSize: 0x0 }
  - { offsetInCU: 0x10374, offset: 0x3157B, size: 0x8, addend: 0x0, symName: __ZL10patches101, symObjAddr: 0x11F20, symBinAddr: 0x1B48D0, symSize: 0x0 }
  - { offsetInCU: 0x10395, offset: 0x3159C, size: 0x8, addend: 0x0, symName: __ZL11patchBuf124, symObjAddr: 0x194D9D, symBinAddr: 0x186B9D, symSize: 0x0 }
  - { offsetInCU: 0x103B6, offset: 0x315BD, size: 0x8, addend: 0x0, symName: __ZL12platforms102, symObjAddr: 0x1B4F40, symBinAddr: 0x1A6DD0, symSize: 0x0 }
  - { offsetInCU: 0x103D7, offset: 0x315DE, size: 0x8, addend: 0x0, symName: __ZL8file1219, symObjAddr: 0x194DB0, symBinAddr: 0x186BB0, symSize: 0x0 }
  - { offsetInCU: 0x1040D, offset: 0x31614, size: 0x8, addend: 0x0, symName: __ZL8file1220, symObjAddr: 0x1958D0, symBinAddr: 0x1876D0, symSize: 0x0 }
  - { offsetInCU: 0x1042E, offset: 0x31635, size: 0x8, addend: 0x0, symName: __ZL8file1221, symObjAddr: 0x195A10, symBinAddr: 0x187810, symSize: 0x0 }
  - { offsetInCU: 0x10464, offset: 0x3166B, size: 0x8, addend: 0x0, symName: __ZL8file1222, symObjAddr: 0x196530, symBinAddr: 0x188330, symSize: 0x0 }
  - { offsetInCU: 0x1049A, offset: 0x316A1, size: 0x8, addend: 0x0, symName: __ZL8file1223, symObjAddr: 0x197060, symBinAddr: 0x188E60, symSize: 0x0 }
  - { offsetInCU: 0x104BB, offset: 0x316C2, size: 0x8, addend: 0x0, symName: __ZL8file1224, symObjAddr: 0x1971A0, symBinAddr: 0x188FA0, symSize: 0x0 }
  - { offsetInCU: 0x104DC, offset: 0x316E3, size: 0x8, addend: 0x0, symName: __ZL8file1225, symObjAddr: 0x197300, symBinAddr: 0x189100, symSize: 0x0 }
  - { offsetInCU: 0x104FD, offset: 0x31704, size: 0x8, addend: 0x0, symName: __ZL8file1226, symObjAddr: 0x197450, symBinAddr: 0x189250, symSize: 0x0 }
  - { offsetInCU: 0x1051E, offset: 0x31725, size: 0x8, addend: 0x0, symName: __ZL8file1227, symObjAddr: 0x197590, symBinAddr: 0x189390, symSize: 0x0 }
  - { offsetInCU: 0x1053F, offset: 0x31746, size: 0x8, addend: 0x0, symName: __ZL8file1228, symObjAddr: 0x1976E0, symBinAddr: 0x1894E0, symSize: 0x0 }
  - { offsetInCU: 0x10560, offset: 0x31767, size: 0x8, addend: 0x0, symName: __ZL8file1229, symObjAddr: 0x197820, symBinAddr: 0x189620, symSize: 0x0 }
  - { offsetInCU: 0x10581, offset: 0x31788, size: 0x8, addend: 0x0, symName: __ZL10layouts102, symObjAddr: 0x1B5050, symBinAddr: 0x1A6EE0, symSize: 0x0 }
  - { offsetInCU: 0x105A2, offset: 0x317A9, size: 0x8, addend: 0x0, symName: __ZL8file1230, symObjAddr: 0x197970, symBinAddr: 0x189770, symSize: 0x0 }
  - { offsetInCU: 0x105C3, offset: 0x317CA, size: 0x8, addend: 0x0, symName: __ZL8file1231, symObjAddr: 0x197F50, symBinAddr: 0x189D50, symSize: 0x0 }
  - { offsetInCU: 0x105E4, offset: 0x317EB, size: 0x8, addend: 0x0, symName: __ZL8file1232, symObjAddr: 0x198580, symBinAddr: 0x18A380, symSize: 0x0 }
  - { offsetInCU: 0x1061A, offset: 0x31821, size: 0x8, addend: 0x0, symName: __ZL8file1233, symObjAddr: 0x198B50, symBinAddr: 0x18A950, symSize: 0x0 }
  - { offsetInCU: 0x1063B, offset: 0x31842, size: 0x8, addend: 0x0, symName: __ZL8file1234, symObjAddr: 0x199180, symBinAddr: 0x18AF80, symSize: 0x0 }
  - { offsetInCU: 0x1065C, offset: 0x31863, size: 0x8, addend: 0x0, symName: __ZL8file1235, symObjAddr: 0x1997B0, symBinAddr: 0x18B5B0, symSize: 0x0 }
  - { offsetInCU: 0x1067D, offset: 0x31884, size: 0x8, addend: 0x0, symName: __ZL8file1236, symObjAddr: 0x199DE0, symBinAddr: 0x18BBE0, symSize: 0x0 }
  - { offsetInCU: 0x106B3, offset: 0x318BA, size: 0x8, addend: 0x0, symName: __ZL8file1237, symObjAddr: 0x19A6A0, symBinAddr: 0x18C4A0, symSize: 0x0 }
  - { offsetInCU: 0x106E9, offset: 0x318F0, size: 0x8, addend: 0x0, symName: __ZL8file1238, symObjAddr: 0x19ACD0, symBinAddr: 0x18CAD0, symSize: 0x0 }
  - { offsetInCU: 0x1070A, offset: 0x31911, size: 0x8, addend: 0x0, symName: __ZL8file1239, symObjAddr: 0x19B300, symBinAddr: 0x18D100, symSize: 0x0 }
  - { offsetInCU: 0x1072B, offset: 0x31932, size: 0x8, addend: 0x0, symName: __ZL8file1240, symObjAddr: 0x19B930, symBinAddr: 0x18D730, symSize: 0x0 }
  - { offsetInCU: 0x1074C, offset: 0x31953, size: 0x8, addend: 0x0, symName: __ZL10patches102, symObjAddr: 0x120A0, symBinAddr: 0x1B4A50, symSize: 0x0 }
  - { offsetInCU: 0x1076D, offset: 0x31974, size: 0x8, addend: 0x0, symName: __ZL11patchBuf125, symObjAddr: 0x19BF52, symBinAddr: 0x18DD52, symSize: 0x0 }
  - { offsetInCU: 0x1078E, offset: 0x31995, size: 0x8, addend: 0x0, symName: __ZL12platforms103, symObjAddr: 0x1B5160, symBinAddr: 0x1A6FF0, symSize: 0x0 }
  - { offsetInCU: 0x107AF, offset: 0x319B6, size: 0x8, addend: 0x0, symName: __ZL8file1241, symObjAddr: 0x19BF60, symBinAddr: 0x18DD60, symSize: 0x0 }
  - { offsetInCU: 0x107D0, offset: 0x319D7, size: 0x8, addend: 0x0, symName: __ZL10layouts103, symObjAddr: 0x1B5180, symBinAddr: 0x1A7010, symSize: 0x0 }
  - { offsetInCU: 0x107F1, offset: 0x319F8, size: 0x8, addend: 0x0, symName: __ZL8file1242, symObjAddr: 0x19C0B0, symBinAddr: 0x18DEB0, symSize: 0x0 }
  - { offsetInCU: 0x10827, offset: 0x31A2E, size: 0x8, addend: 0x0, symName: __ZL10patches103, symObjAddr: 0x121F0, symBinAddr: 0x1B4BA0, symSize: 0x0 }
  - { offsetInCU: 0x10848, offset: 0x31A4F, size: 0x8, addend: 0x0, symName: __ZL11patchBuf126, symObjAddr: 0x19C68A, symBinAddr: 0x18E48A, symSize: 0x0 }
  - { offsetInCU: 0x10869, offset: 0x31A70, size: 0x8, addend: 0x0, symName: __ZL11revisions48, symObjAddr: 0x5AAA8, symBinAddr: 0x4C8A8, symSize: 0x0 }
  - { offsetInCU: 0x1088A, offset: 0x31A91, size: 0x8, addend: 0x0, symName: __ZL12platforms104, symObjAddr: 0x1B51A0, symBinAddr: 0x1A7030, symSize: 0x0 }
  - { offsetInCU: 0x108AB, offset: 0x31AB2, size: 0x8, addend: 0x0, symName: __ZL8file1243, symObjAddr: 0x19C690, symBinAddr: 0x18E490, symSize: 0x0 }
  - { offsetInCU: 0x108CC, offset: 0x31AD3, size: 0x8, addend: 0x0, symName: __ZL8file1244, symObjAddr: 0x19C7D0, symBinAddr: 0x18E5D0, symSize: 0x0 }
  - { offsetInCU: 0x108ED, offset: 0x31AF4, size: 0x8, addend: 0x0, symName: __ZL8file1245, symObjAddr: 0x19C910, symBinAddr: 0x18E710, symSize: 0x0 }
  - { offsetInCU: 0x1090E, offset: 0x31B15, size: 0x8, addend: 0x0, symName: __ZL8file1246, symObjAddr: 0x19CA60, symBinAddr: 0x18E860, symSize: 0x0 }
  - { offsetInCU: 0x1092F, offset: 0x31B36, size: 0x8, addend: 0x0, symName: __ZL8file1247, symObjAddr: 0x19CBB0, symBinAddr: 0x18E9B0, symSize: 0x0 }
  - { offsetInCU: 0x10950, offset: 0x31B57, size: 0x8, addend: 0x0, symName: __ZL10layouts104, symObjAddr: 0x1B5220, symBinAddr: 0x1A70B0, symSize: 0x0 }
  - { offsetInCU: 0x10971, offset: 0x31B78, size: 0x8, addend: 0x0, symName: __ZL8file1248, symObjAddr: 0x19CD00, symBinAddr: 0x18EB00, symSize: 0x0 }
  - { offsetInCU: 0x109A7, offset: 0x31BAE, size: 0x8, addend: 0x0, symName: __ZL8file1249, symObjAddr: 0x19D000, symBinAddr: 0x18EE00, symSize: 0x0 }
  - { offsetInCU: 0x109C8, offset: 0x31BCF, size: 0x8, addend: 0x0, symName: __ZL8file1250, symObjAddr: 0x19D300, symBinAddr: 0x18F100, symSize: 0x0 }
  - { offsetInCU: 0x109FE, offset: 0x31C05, size: 0x8, addend: 0x0, symName: __ZL8file1251, symObjAddr: 0x19D960, symBinAddr: 0x18F760, symSize: 0x0 }
  - { offsetInCU: 0x10A1F, offset: 0x31C26, size: 0x8, addend: 0x0, symName: __ZL8file1252, symObjAddr: 0x19E000, symBinAddr: 0x18FE00, symSize: 0x0 }
  - { offsetInCU: 0x10A55, offset: 0x31C5C, size: 0x8, addend: 0x0, symName: __ZL10patches104, symObjAddr: 0x12340, symBinAddr: 0x1B4CF0, symSize: 0x0 }
  - { offsetInCU: 0x10A76, offset: 0x31C7D, size: 0x8, addend: 0x0, symName: __ZL11patchBuf127, symObjAddr: 0x19E65E, symBinAddr: 0x19045E, symSize: 0x0 }
  - { offsetInCU: 0x10A97, offset: 0x31C9E, size: 0x8, addend: 0x0, symName: __ZL11revisions49, symObjAddr: 0x5AAAC, symBinAddr: 0x4C8AC, symSize: 0x0 }
  - { offsetInCU: 0x10AB8, offset: 0x31CBF, size: 0x8, addend: 0x0, symName: __ZL12platforms105, symObjAddr: 0x1B52A0, symBinAddr: 0x1A7130, symSize: 0x0 }
  - { offsetInCU: 0x10AD9, offset: 0x31CE0, size: 0x8, addend: 0x0, symName: __ZL8file1253, symObjAddr: 0x19E670, symBinAddr: 0x190470, symSize: 0x0 }
  - { offsetInCU: 0x10B0F, offset: 0x31D16, size: 0x8, addend: 0x0, symName: __ZL8file1254, symObjAddr: 0x19F170, symBinAddr: 0x190F70, symSize: 0x0 }
  - { offsetInCU: 0x10B30, offset: 0x31D37, size: 0x8, addend: 0x0, symName: __ZL10layouts105, symObjAddr: 0x1B52D0, symBinAddr: 0x1A7160, symSize: 0x0 }
  - { offsetInCU: 0x10B51, offset: 0x31D58, size: 0x8, addend: 0x0, symName: __ZL8file1255, symObjAddr: 0x19F2C0, symBinAddr: 0x1910C0, symSize: 0x0 }
  - { offsetInCU: 0x10B72, offset: 0x31D79, size: 0x8, addend: 0x0, symName: __ZL8file1256, symObjAddr: 0x19F3D0, symBinAddr: 0x1911D0, symSize: 0x0 }
  - { offsetInCU: 0x10B93, offset: 0x31D9A, size: 0x8, addend: 0x0, symName: __ZL10patches105, symObjAddr: 0x124F0, symBinAddr: 0x1B4EA0, symSize: 0x0 }
  - { offsetInCU: 0x10BB4, offset: 0x31DBB, size: 0x8, addend: 0x0, symName: __ZL11patchBuf128, symObjAddr: 0x19FA2F, symBinAddr: 0x19182F, symSize: 0x0 }
  - { offsetInCU: 0x10BD5, offset: 0x31DDC, size: 0x8, addend: 0x0, symName: __ZL14codecModAMDZEN, symObjAddr: 0x2300E0, symBinAddr: 0x1B5E10, symSize: 0x0 }
  - { offsetInCU: 0x10BF6, offset: 0x31DFD, size: 0x8, addend: 0x0, symName: __ZL16codecModCreative, symObjAddr: 0x6F20, symBinAddr: 0x1A98D0, symSize: 0x0 }
  - { offsetInCU: 0x10C2B, offset: 0x31E32, size: 0x8, addend: 0x0, symName: __ZL11revisions50, symObjAddr: 0x19FA34, symBinAddr: 0x191834, symSize: 0x0 }
  - { offsetInCU: 0x10C4C, offset: 0x31E53, size: 0x8, addend: 0x0, symName: __ZL12platforms106, symObjAddr: 0x1B5300, symBinAddr: 0x1A7190, symSize: 0x0 }
  - { offsetInCU: 0x10C6D, offset: 0x31E74, size: 0x8, addend: 0x0, symName: __ZL8file1257, symObjAddr: 0x19FA40, symBinAddr: 0x191840, symSize: 0x0 }
  - { offsetInCU: 0x10CA3, offset: 0x31EAA, size: 0x8, addend: 0x0, symName: __ZL8file1258, symObjAddr: 0x1A0B70, symBinAddr: 0x192970, symSize: 0x0 }
  - { offsetInCU: 0x10CD9, offset: 0x31EE0, size: 0x8, addend: 0x0, symName: __ZL8file1259, symObjAddr: 0x1A1BE0, symBinAddr: 0x1939E0, symSize: 0x0 }
  - { offsetInCU: 0x10D0F, offset: 0x31F16, size: 0x8, addend: 0x0, symName: __ZL8file1260, symObjAddr: 0x1A2C50, symBinAddr: 0x194A50, symSize: 0x0 }
  - { offsetInCU: 0x10D30, offset: 0x31F37, size: 0x8, addend: 0x0, symName: __ZL8file1261, symObjAddr: 0x1A3CC0, symBinAddr: 0x195AC0, symSize: 0x0 }
  - { offsetInCU: 0x10D51, offset: 0x31F58, size: 0x8, addend: 0x0, symName: __ZL8file1262, symObjAddr: 0x1A4D30, symBinAddr: 0x196B30, symSize: 0x0 }
  - { offsetInCU: 0x10D87, offset: 0x31F8E, size: 0x8, addend: 0x0, symName: __ZL10layouts106, symObjAddr: 0x1B5440, symBinAddr: 0x1A72D0, symSize: 0x0 }
  - { offsetInCU: 0x10DA8, offset: 0x31FAF, size: 0x8, addend: 0x0, symName: __ZL8file1263, symObjAddr: 0x1A5D80, symBinAddr: 0x197B80, symSize: 0x0 }
  - { offsetInCU: 0x10DDE, offset: 0x31FE5, size: 0x8, addend: 0x0, symName: __ZL8file1264, symObjAddr: 0x1A60F0, symBinAddr: 0x197EF0, symSize: 0x0 }
  - { offsetInCU: 0x10DFF, offset: 0x32006, size: 0x8, addend: 0x0, symName: __ZL8file1265, symObjAddr: 0x1A6480, symBinAddr: 0x198280, symSize: 0x0 }
  - { offsetInCU: 0x10E35, offset: 0x3203C, size: 0x8, addend: 0x0, symName: __ZL8file1266, symObjAddr: 0x1A6A70, symBinAddr: 0x198870, symSize: 0x0 }
  - { offsetInCU: 0x10E6B, offset: 0x32072, size: 0x8, addend: 0x0, symName: __ZL8file1267, symObjAddr: 0x1A7050, symBinAddr: 0x198E50, symSize: 0x0 }
  - { offsetInCU: 0x10E8C, offset: 0x32093, size: 0x8, addend: 0x0, symName: __ZL8file1268, symObjAddr: 0x1A7630, symBinAddr: 0x199430, symSize: 0x0 }
  - { offsetInCU: 0x10EAD, offset: 0x320B4, size: 0x8, addend: 0x0, symName: __ZL8file1269, symObjAddr: 0x1A7C10, symBinAddr: 0x199A10, symSize: 0x0 }
  - { offsetInCU: 0x10ECE, offset: 0x320D5, size: 0x8, addend: 0x0, symName: __ZL8file1270, symObjAddr: 0x1A81F0, symBinAddr: 0x199FF0, symSize: 0x0 }
  - { offsetInCU: 0x10EEF, offset: 0x320F6, size: 0x8, addend: 0x0, symName: __ZL8file1271, symObjAddr: 0x1A87D0, symBinAddr: 0x19A5D0, symSize: 0x0 }
  - { offsetInCU: 0x10F10, offset: 0x32117, size: 0x8, addend: 0x0, symName: __ZL8file1272, symObjAddr: 0x1A8DB0, symBinAddr: 0x19ABB0, symSize: 0x0 }
  - { offsetInCU: 0x10F31, offset: 0x32138, size: 0x8, addend: 0x0, symName: __ZL8file1273, symObjAddr: 0x1A9390, symBinAddr: 0x19B190, symSize: 0x0 }
  - { offsetInCU: 0x10F52, offset: 0x32159, size: 0x8, addend: 0x0, symName: __ZL8file1274, symObjAddr: 0x1A9970, symBinAddr: 0x19B770, symSize: 0x0 }
  - { offsetInCU: 0x10F88, offset: 0x3218F, size: 0x8, addend: 0x0, symName: __ZL8file1275, symObjAddr: 0x1A9F30, symBinAddr: 0x19BD30, symSize: 0x0 }
  - { offsetInCU: 0x10FBE, offset: 0x321C5, size: 0x8, addend: 0x0, symName: __ZL10patches106, symObjAddr: 0x126A0, symBinAddr: 0x1B5050, symSize: 0x0 }
  - { offsetInCU: 0x10FDF, offset: 0x321E6, size: 0x8, addend: 0x0, symName: __ZL11patchBuf129, symObjAddr: 0x1AA51F, symBinAddr: 0x19C31F, symSize: 0x0 }
  - { offsetInCU: 0x11000, offset: 0x32207, size: 0x8, addend: 0x0, symName: __ZL11patchBuf130, symObjAddr: 0x1AA523, symBinAddr: 0x19C323, symSize: 0x0 }
  - { offsetInCU: 0x11021, offset: 0x32228, size: 0x8, addend: 0x0, symName: __ZL11patchBuf131, symObjAddr: 0x1AA527, symBinAddr: 0x19C327, symSize: 0x0 }
  - { offsetInCU: 0x11042, offset: 0x32249, size: 0x8, addend: 0x0, symName: __ZL11patchBuf132, symObjAddr: 0x1AA52B, symBinAddr: 0x19C32B, symSize: 0x0 }
  - { offsetInCU: 0x11063, offset: 0x3226A, size: 0x8, addend: 0x0, symName: __ZL11patchBuf133, symObjAddr: 0x1AA52F, symBinAddr: 0x19C32F, symSize: 0x0 }
  - { offsetInCU: 0x11084, offset: 0x3228B, size: 0x8, addend: 0x0, symName: __ZL16codecModSigmaTel, symObjAddr: 0x6F70, symBinAddr: 0x1A9920, symSize: 0x0 }
  - { offsetInCU: 0x110A5, offset: 0x322AC, size: 0x8, addend: 0x0, symName: __ZL11revisions51, symObjAddr: 0x1AA534, symBinAddr: 0x19C334, symSize: 0x0 }
  - { offsetInCU: 0x110C6, offset: 0x322CD, size: 0x8, addend: 0x0, symName: __ZL12platforms107, symObjAddr: 0x1B5580, symBinAddr: 0x1A7410, symSize: 0x0 }
  - { offsetInCU: 0x110E7, offset: 0x322EE, size: 0x8, addend: 0x0, symName: __ZL8file1276, symObjAddr: 0x1AA540, symBinAddr: 0x19C340, symSize: 0x0 }
  - { offsetInCU: 0x11108, offset: 0x3230F, size: 0x8, addend: 0x0, symName: __ZL10layouts107, symObjAddr: 0x1B55A0, symBinAddr: 0x1A7430, symSize: 0x0 }
  - { offsetInCU: 0x11129, offset: 0x32330, size: 0x8, addend: 0x0, symName: __ZL8file1277, symObjAddr: 0x1AA680, symBinAddr: 0x19C480, symSize: 0x0 }
  - { offsetInCU: 0x1115F, offset: 0x32366, size: 0x8, addend: 0x0, symName: __ZL10patches107, symObjAddr: 0x12850, symBinAddr: 0x1B5200, symSize: 0x0 }
  - { offsetInCU: 0x11194, offset: 0x3239B, size: 0x8, addend: 0x0, symName: __ZL11patchBuf134, symObjAddr: 0x1AA8DF, symBinAddr: 0x19C6DF, symSize: 0x0 }
  - { offsetInCU: 0x111B5, offset: 0x323BC, size: 0x8, addend: 0x0, symName: __ZL11patchBuf135, symObjAddr: 0x1AA8E3, symBinAddr: 0x19C6E3, symSize: 0x0 }
  - { offsetInCU: 0x111D6, offset: 0x323DD, size: 0x8, addend: 0x0, symName: __ZL11patchBuf136, symObjAddr: 0x1AA8E7, symBinAddr: 0x19C6E7, symSize: 0x0 }
  - { offsetInCU: 0x111F7, offset: 0x323FE, size: 0x8, addend: 0x0, symName: __ZL11patchBuf137, symObjAddr: 0x1AA8EB, symBinAddr: 0x19C6EB, symSize: 0x0 }
  - { offsetInCU: 0x11218, offset: 0x3241F, size: 0x8, addend: 0x0, symName: __ZL11patchBuf138, symObjAddr: 0x1AA8EF, symBinAddr: 0x19C6EF, symSize: 0x0 }
  - { offsetInCU: 0x11239, offset: 0x32440, size: 0x8, addend: 0x0, symName: __ZL11patchBuf139, symObjAddr: 0x1AA8F3, symBinAddr: 0x19C6F3, symSize: 0x0 }
  - { offsetInCU: 0x1125A, offset: 0x32461, size: 0x8, addend: 0x0, symName: __ZL11patchBuf140, symObjAddr: 0x1AA8F7, symBinAddr: 0x19C6F7, symSize: 0x0 }
  - { offsetInCU: 0x1127B, offset: 0x32482, size: 0x8, addend: 0x0, symName: __ZL11patchBuf141, symObjAddr: 0x1AA8FB, symBinAddr: 0x19C6FB, symSize: 0x0 }
  - { offsetInCU: 0x1129C, offset: 0x324A3, size: 0x8, addend: 0x0, symName: __ZL11patchBuf142, symObjAddr: 0x1AA8FF, symBinAddr: 0x19C6FF, symSize: 0x0 }
  - { offsetInCU: 0x112BD, offset: 0x324C4, size: 0x8, addend: 0x0, symName: __ZL11patchBuf143, symObjAddr: 0x1AA903, symBinAddr: 0x19C703, symSize: 0x0 }
  - { offsetInCU: 0x112DE, offset: 0x324E5, size: 0x8, addend: 0x0, symName: __ZL11revisions52, symObjAddr: 0x1AA538, symBinAddr: 0x19C338, symSize: 0x0 }
  - { offsetInCU: 0x112FF, offset: 0x32506, size: 0x8, addend: 0x0, symName: __ZL12platforms108, symObjAddr: 0x1B55C0, symBinAddr: 0x1A7450, symSize: 0x0 }
  - { offsetInCU: 0x11320, offset: 0x32527, size: 0x8, addend: 0x0, symName: __ZL8file1278, symObjAddr: 0x1AA910, symBinAddr: 0x19C710, symSize: 0x0 }
  - { offsetInCU: 0x11341, offset: 0x32548, size: 0x8, addend: 0x0, symName: __ZL10layouts108, symObjAddr: 0x1B55E0, symBinAddr: 0x1A7470, symSize: 0x0 }
  - { offsetInCU: 0x11362, offset: 0x32569, size: 0x8, addend: 0x0, symName: __ZL8file1279, symObjAddr: 0x1AAA50, symBinAddr: 0x19C850, symSize: 0x0 }
  - { offsetInCU: 0x11398, offset: 0x3259F, size: 0x8, addend: 0x0, symName: __ZL10patches108, symObjAddr: 0x12C40, symBinAddr: 0x1B55F0, symSize: 0x0 }
  - { offsetInCU: 0x113CD, offset: 0x325D4, size: 0x8, addend: 0x0, symName: __ZL11patchBuf144, symObjAddr: 0x1AACC1, symBinAddr: 0x19CAC1, symSize: 0x0 }
  - { offsetInCU: 0x113EE, offset: 0x325F5, size: 0x8, addend: 0x0, symName: __ZL11revisions53, symObjAddr: 0x1AA53C, symBinAddr: 0x19C33C, symSize: 0x0 }
  - { offsetInCU: 0x1140F, offset: 0x32616, size: 0x8, addend: 0x0, symName: __ZL12platforms109, symObjAddr: 0x1B5600, symBinAddr: 0x1A7490, symSize: 0x0 }
  - { offsetInCU: 0x11430, offset: 0x32637, size: 0x8, addend: 0x0, symName: __ZL8file1280, symObjAddr: 0x1AACD0, symBinAddr: 0x19CAD0, symSize: 0x0 }
  - { offsetInCU: 0x11451, offset: 0x32658, size: 0x8, addend: 0x0, symName: __ZL10layouts109, symObjAddr: 0x1B5620, symBinAddr: 0x1A74B0, symSize: 0x0 }
  - { offsetInCU: 0x11472, offset: 0x32679, size: 0x8, addend: 0x0, symName: __ZL8file1281, symObjAddr: 0x1AAE20, symBinAddr: 0x19CC20, symSize: 0x0 }
  - { offsetInCU: 0x11493, offset: 0x3269A, size: 0x8, addend: 0x0, symName: __ZL10patches109, symObjAddr: 0x12FD0, symBinAddr: 0x1B5980, symSize: 0x0 }
  - { offsetInCU: 0x114B4, offset: 0x326BB, size: 0x8, addend: 0x0, symName: __ZL11patchBuf145, symObjAddr: 0x1AB07F, symBinAddr: 0x19CE7F, symSize: 0x0 }
  - { offsetInCU: 0x114D5, offset: 0x326DC, size: 0x8, addend: 0x0, symName: __ZL14codecModNVIDIA, symObjAddr: 0x2300E8, symBinAddr: 0x1B5E18, symSize: 0x0 }
  - { offsetInCU: 0x114F6, offset: 0x326FD, size: 0x8, addend: 0x0, symName: __ZL10patches110, symObjAddr: 0x7060, symBinAddr: 0x1A9A10, symSize: 0x0 }
  - { offsetInCU: 0x1152B, offset: 0x32732, size: 0x8, addend: 0x0, symName: __ZL11patchBuf146, symObjAddr: 0x1AB083, symBinAddr: 0x19CE83, symSize: 0x0 }
  - { offsetInCU: 0x1154C, offset: 0x32753, size: 0x8, addend: 0x0, symName: __ZL11patchBuf147, symObjAddr: 0x1AB087, symBinAddr: 0x19CE87, symSize: 0x0 }
  - { offsetInCU: 0x1156D, offset: 0x32774, size: 0x8, addend: 0x0, symName: __ZL10patches111, symObjAddr: 0x7090, symBinAddr: 0x1A9A40, symSize: 0x0 }
  - { offsetInCU: 0x1158E, offset: 0x32795, size: 0x8, addend: 0x0, symName: __ZL11patchBuf148, symObjAddr: 0x1AB08B, symBinAddr: 0x19CE8B, symSize: 0x0 }
  - { offsetInCU: 0x115AF, offset: 0x327B6, size: 0x8, addend: 0x0, symName: __ZL10patches112, symObjAddr: 0x70C0, symBinAddr: 0x1A9A70, symSize: 0x0 }
  - { offsetInCU: 0x115D0, offset: 0x327D7, size: 0x8, addend: 0x0, symName: __ZL11patchBuf149, symObjAddr: 0x1AB08F, symBinAddr: 0x19CE8F, symSize: 0x0 }
  - { offsetInCU: 0x115F1, offset: 0x327F8, size: 0x8, addend: 0x0, symName: __ZL10patches113, symObjAddr: 0x70F0, symBinAddr: 0x1A9AA0, symSize: 0x0 }
  - { offsetInCU: 0x11612, offset: 0x32819, size: 0x8, addend: 0x0, symName: __ZL11patchBuf150, symObjAddr: 0x1AB093, symBinAddr: 0x19CE93, symSize: 0x0 }
  - { offsetInCU: 0x11633, offset: 0x3283A, size: 0x8, addend: 0x0, symName: __ZL10patches114, symObjAddr: 0x7120, symBinAddr: 0x1A9AD0, symSize: 0x0 }
  - { offsetInCU: 0x11654, offset: 0x3285B, size: 0x8, addend: 0x0, symName: __ZL11patchBuf151, symObjAddr: 0x1AB097, symBinAddr: 0x19CE97, symSize: 0x0 }
  - { offsetInCU: 0x11675, offset: 0x3287C, size: 0x8, addend: 0x0, symName: __ZL10patches115, symObjAddr: 0x7150, symBinAddr: 0x1A9B00, symSize: 0x0 }
  - { offsetInCU: 0x11696, offset: 0x3289D, size: 0x8, addend: 0x0, symName: __ZL11patchBuf152, symObjAddr: 0x1AB09B, symBinAddr: 0x19CE9B, symSize: 0x0 }
  - { offsetInCU: 0x116B7, offset: 0x328BE, size: 0x8, addend: 0x0, symName: __ZL10patches116, symObjAddr: 0x7180, symBinAddr: 0x1A9B30, symSize: 0x0 }
  - { offsetInCU: 0x116D8, offset: 0x328DF, size: 0x8, addend: 0x0, symName: __ZL11patchBuf153, symObjAddr: 0x1AB09F, symBinAddr: 0x19CE9F, symSize: 0x0 }
  - { offsetInCU: 0x116F9, offset: 0x32900, size: 0x8, addend: 0x0, symName: __ZL10patches117, symObjAddr: 0x71B0, symBinAddr: 0x1A9B60, symSize: 0x0 }
  - { offsetInCU: 0x1171A, offset: 0x32921, size: 0x8, addend: 0x0, symName: __ZL11patchBuf154, symObjAddr: 0x1AB0A3, symBinAddr: 0x19CEA3, symSize: 0x0 }
  - { offsetInCU: 0x1173B, offset: 0x32942, size: 0x8, addend: 0x0, symName: __ZL11patchBuf155, symObjAddr: 0x1AB0A7, symBinAddr: 0x19CEA7, symSize: 0x0 }
  - { offsetInCU: 0x1175C, offset: 0x32963, size: 0x8, addend: 0x0, symName: __ZL10patches118, symObjAddr: 0x71E0, symBinAddr: 0x1A9B90, symSize: 0x0 }
  - { offsetInCU: 0x1177D, offset: 0x32984, size: 0x8, addend: 0x0, symName: __ZL11patchBuf156, symObjAddr: 0x1AB0AB, symBinAddr: 0x19CEAB, symSize: 0x0 }
  - { offsetInCU: 0x1179E, offset: 0x329A5, size: 0x8, addend: 0x0, symName: __ZL10patches119, symObjAddr: 0x7210, symBinAddr: 0x1A9BC0, symSize: 0x0 }
  - { offsetInCU: 0x117BF, offset: 0x329C6, size: 0x8, addend: 0x0, symName: __ZL11patchBuf157, symObjAddr: 0x1AB0AF, symBinAddr: 0x19CEAF, symSize: 0x0 }
  - { offsetInCU: 0x117E0, offset: 0x329E7, size: 0x8, addend: 0x0, symName: __ZL10patches120, symObjAddr: 0x7240, symBinAddr: 0x1A9BF0, symSize: 0x0 }
  - { offsetInCU: 0x11801, offset: 0x32A08, size: 0x8, addend: 0x0, symName: __ZL11patchBuf158, symObjAddr: 0x1AB0B3, symBinAddr: 0x19CEB3, symSize: 0x0 }
  - { offsetInCU: 0x11822, offset: 0x32A29, size: 0x8, addend: 0x0, symName: __ZL10patches121, symObjAddr: 0x7270, symBinAddr: 0x1A9C20, symSize: 0x0 }
  - { offsetInCU: 0x11857, offset: 0x32A5E, size: 0x8, addend: 0x0, symName: __ZL11patchBuf159, symObjAddr: 0x1AB0B7, symBinAddr: 0x19CEB7, symSize: 0x0 }
  - { offsetInCU: 0x11878, offset: 0x32A7F, size: 0x8, addend: 0x0, symName: __ZL11patchBuf160, symObjAddr: 0x1AB0BB, symBinAddr: 0x19CEBB, symSize: 0x0 }
  - { offsetInCU: 0x11899, offset: 0x32AA0, size: 0x8, addend: 0x0, symName: __ZL11patchBuf161, symObjAddr: 0x1AB0BF, symBinAddr: 0x19CEBF, symSize: 0x0 }
  - { offsetInCU: 0x118BA, offset: 0x32AC1, size: 0x8, addend: 0x0, symName: __ZL11patchBuf162, symObjAddr: 0x1AB0C3, symBinAddr: 0x19CEC3, symSize: 0x0 }
  - { offsetInCU: 0x118DB, offset: 0x32AE2, size: 0x8, addend: 0x0, symName: __ZL11patchBuf163, symObjAddr: 0x1AB0C7, symBinAddr: 0x19CEC7, symSize: 0x0 }
  - { offsetInCU: 0x118FC, offset: 0x32B03, size: 0x8, addend: 0x0, symName: __ZL11patchBuf164, symObjAddr: 0x1AB0CB, symBinAddr: 0x19CECB, symSize: 0x0 }
  - { offsetInCU: 0x1191D, offset: 0x32B24, size: 0x8, addend: 0x0, symName: __ZL11patchBuf165, symObjAddr: 0x1AB0CF, symBinAddr: 0x19CECF, symSize: 0x0 }
  - { offsetInCU: 0x1193E, offset: 0x32B45, size: 0x8, addend: 0x0, symName: __ZL11patchBuf166, symObjAddr: 0x1AB0D3, symBinAddr: 0x19CED3, symSize: 0x0 }
  - { offsetInCU: 0x1195F, offset: 0x32B66, size: 0x8, addend: 0x0, symName: __ZL10patches122, symObjAddr: 0x7330, symBinAddr: 0x1A9CE0, symSize: 0x0 }
  - { offsetInCU: 0x11980, offset: 0x32B87, size: 0x8, addend: 0x0, symName: __ZL11patchBuf167, symObjAddr: 0x1AB0D7, symBinAddr: 0x19CED7, symSize: 0x0 }
  - { offsetInCU: 0x119A1, offset: 0x32BA8, size: 0x8, addend: 0x0, symName: __ZL11patchBuf168, symObjAddr: 0x1AB0DB, symBinAddr: 0x19CEDB, symSize: 0x0 }
  - { offsetInCU: 0x119C2, offset: 0x32BC9, size: 0x8, addend: 0x0, symName: __ZL11patchBuf169, symObjAddr: 0x1AB0DF, symBinAddr: 0x19CEDF, symSize: 0x0 }
  - { offsetInCU: 0x119E3, offset: 0x32BEA, size: 0x8, addend: 0x0, symName: __ZL10patches123, symObjAddr: 0x73F0, symBinAddr: 0x1A9DA0, symSize: 0x0 }
  - { offsetInCU: 0x11A18, offset: 0x32C1F, size: 0x8, addend: 0x0, symName: __ZL11patchBuf170, symObjAddr: 0x1AB0E3, symBinAddr: 0x19CEE3, symSize: 0x0 }
  - { offsetInCU: 0x11A39, offset: 0x32C40, size: 0x8, addend: 0x0, symName: __ZL11patchBuf171, symObjAddr: 0x1AB0E7, symBinAddr: 0x19CEE7, symSize: 0x0 }
  - { offsetInCU: 0x11A5A, offset: 0x32C61, size: 0x8, addend: 0x0, symName: __ZL10patches124, symObjAddr: 0x7450, symBinAddr: 0x1A9E00, symSize: 0x0 }
  - { offsetInCU: 0x11A8F, offset: 0x32C96, size: 0x8, addend: 0x0, symName: __ZL11patchBuf172, symObjAddr: 0x1AB0EB, symBinAddr: 0x19CEEB, symSize: 0x0 }
  - { offsetInCU: 0x11AB0, offset: 0x32CB7, size: 0x8, addend: 0x0, symName: __ZL11patchBuf173, symObjAddr: 0x1AB0EF, symBinAddr: 0x19CEEF, symSize: 0x0 }
  - { offsetInCU: 0x11AD1, offset: 0x32CD8, size: 0x8, addend: 0x0, symName: __ZL11patchBuf174, symObjAddr: 0x1AB0F3, symBinAddr: 0x19CEF3, symSize: 0x0 }
  - { offsetInCU: 0x11AF2, offset: 0x32CF9, size: 0x8, addend: 0x0, symName: __ZL11patchBuf175, symObjAddr: 0x1AB0F9, symBinAddr: 0x19CEF9, symSize: 0x0 }
  - { offsetInCU: 0x11B13, offset: 0x32D1A, size: 0x8, addend: 0x0, symName: __ZL11patchBuf176, symObjAddr: 0x1AB0FF, symBinAddr: 0x19CEFF, symSize: 0x0 }
  - { offsetInCU: 0x11B48, offset: 0x32D4F, size: 0x8, addend: 0x0, symName: __ZL11patchBuf177, symObjAddr: 0x1AB10B, symBinAddr: 0x19CF0B, symSize: 0x0 }
  - { offsetInCU: 0x11B69, offset: 0x32D70, size: 0x8, addend: 0x0, symName: __ZL11patchBuf178, symObjAddr: 0x1AB117, symBinAddr: 0x19CF17, symSize: 0x0 }
  - { offsetInCU: 0x11B8A, offset: 0x32D91, size: 0x8, addend: 0x0, symName: __ZL11patchBuf179, symObjAddr: 0x1AB11C, symBinAddr: 0x19CF1C, symSize: 0x0 }
  - { offsetInCU: 0x11BAB, offset: 0x32DB2, size: 0x8, addend: 0x0, symName: __ZL11patchBuf180, symObjAddr: 0x1AB121, symBinAddr: 0x19CF21, symSize: 0x0 }
  - { offsetInCU: 0x11BE0, offset: 0x32DE7, size: 0x8, addend: 0x0, symName: __ZL11patchBuf181, symObjAddr: 0x1AB128, symBinAddr: 0x19CF28, symSize: 0x0 }
  - { offsetInCU: 0x11C01, offset: 0x32E08, size: 0x8, addend: 0x0, symName: __ZL10patches125, symObjAddr: 0x7540, symBinAddr: 0x1A9EF0, symSize: 0x0 }
  - { offsetInCU: 0x11C22, offset: 0x32E29, size: 0x8, addend: 0x0, symName: __ZL11patchBuf182, symObjAddr: 0x1AB12F, symBinAddr: 0x19CF2F, symSize: 0x0 }
  - { offsetInCU: 0x11C43, offset: 0x32E4A, size: 0x8, addend: 0x0, symName: __ZL10patches126, symObjAddr: 0x7630, symBinAddr: 0x1A9FE0, symSize: 0x0 }
  - { offsetInCU: 0x11C64, offset: 0x32E6B, size: 0x8, addend: 0x0, symName: __ZL11patchBuf183, symObjAddr: 0x1AB133, symBinAddr: 0x19CF33, symSize: 0x0 }
  - { offsetInCU: 0x11C85, offset: 0x32E8C, size: 0x8, addend: 0x0, symName: __ZL10patches127, symObjAddr: 0x7720, symBinAddr: 0x1AA0D0, symSize: 0x0 }
  - { offsetInCU: 0x11CA6, offset: 0x32EAD, size: 0x8, addend: 0x0, symName: __ZL11patchBuf184, symObjAddr: 0x1AB137, symBinAddr: 0x19CF37, symSize: 0x0 }
  - { offsetInCU: 0x11CC7, offset: 0x32ECE, size: 0x8, addend: 0x0, symName: __ZL11patchBuf185, symObjAddr: 0x1AB13B, symBinAddr: 0x19CF3B, symSize: 0x0 }
  - { offsetInCU: 0x11CE8, offset: 0x32EEF, size: 0x8, addend: 0x0, symName: __ZL11patchBuf186, symObjAddr: 0x1AB13F, symBinAddr: 0x19CF3F, symSize: 0x0 }
  - { offsetInCU: 0x11D09, offset: 0x32F10, size: 0x8, addend: 0x0, symName: __ZL11patchBuf187, symObjAddr: 0x1AB143, symBinAddr: 0x19CF43, symSize: 0x0 }
  - { offsetInCU: 0x11D2A, offset: 0x32F31, size: 0x8, addend: 0x0, symName: __ZL11patchBuf188, symObjAddr: 0x1AB148, symBinAddr: 0x19CF48, symSize: 0x0 }
  - { offsetInCU: 0x11D4B, offset: 0x32F52, size: 0x8, addend: 0x0, symName: __ZL11patchBuf189, symObjAddr: 0x1AB14D, symBinAddr: 0x19CF4D, symSize: 0x0 }
  - { offsetInCU: 0x11D6C, offset: 0x32F73, size: 0x8, addend: 0x0, symName: __ZL10patches128, symObjAddr: 0x7810, symBinAddr: 0x1AA1C0, symSize: 0x0 }
  - { offsetInCU: 0x11D8D, offset: 0x32F94, size: 0x8, addend: 0x0, symName: __ZL11patchBuf190, symObjAddr: 0x1AB152, symBinAddr: 0x19CF52, symSize: 0x0 }
  - { offsetInCU: 0x11DAE, offset: 0x32FB5, size: 0x8, addend: 0x0, symName: __ZL11patchBuf191, symObjAddr: 0x1AB156, symBinAddr: 0x19CF56, symSize: 0x0 }
  - { offsetInCU: 0x11DCF, offset: 0x32FD6, size: 0x8, addend: 0x0, symName: __ZL11patchBuf192, symObjAddr: 0x1AB15A, symBinAddr: 0x19CF5A, symSize: 0x0 }
  - { offsetInCU: 0x11DF0, offset: 0x32FF7, size: 0x8, addend: 0x0, symName: __ZL10patches129, symObjAddr: 0x7870, symBinAddr: 0x1AA220, symSize: 0x0 }
  - { offsetInCU: 0x11E11, offset: 0x33018, size: 0x8, addend: 0x0, symName: __ZL11patchBuf193, symObjAddr: 0x1AB15E, symBinAddr: 0x19CF5E, symSize: 0x0 }
  - { offsetInCU: 0x11E32, offset: 0x33039, size: 0x8, addend: 0x0, symName: __ZL11patchBuf194, symObjAddr: 0x1AB162, symBinAddr: 0x19CF62, symSize: 0x0 }
  - { offsetInCU: 0x11E53, offset: 0x3305A, size: 0x8, addend: 0x0, symName: __ZL11patchBuf195, symObjAddr: 0x1AB166, symBinAddr: 0x19CF66, symSize: 0x0 }
  - { offsetInCU: 0x11E74, offset: 0x3307B, size: 0x8, addend: 0x0, symName: __ZL11patchBuf196, symObjAddr: 0x1AB16A, symBinAddr: 0x19CF6A, symSize: 0x0 }
  - { offsetInCU: 0x11E95, offset: 0x3309C, size: 0x8, addend: 0x0, symName: __ZL10patches130, symObjAddr: 0x78D0, symBinAddr: 0x1AA280, symSize: 0x0 }
  - { offsetInCU: 0x11EB6, offset: 0x330BD, size: 0x8, addend: 0x0, symName: __ZL11patchBuf197, symObjAddr: 0x1AB16E, symBinAddr: 0x19CF6E, symSize: 0x0 }
  - { offsetInCU: 0x11ED7, offset: 0x330DE, size: 0x8, addend: 0x0, symName: __ZL11patchBuf198, symObjAddr: 0x1AB172, symBinAddr: 0x19CF72, symSize: 0x0 }
  - { offsetInCU: 0x11EF8, offset: 0x330FF, size: 0x8, addend: 0x0, symName: __ZL10patches131, symObjAddr: 0x7990, symBinAddr: 0x1AA340, symSize: 0x0 }
  - { offsetInCU: 0x11F19, offset: 0x33120, size: 0x8, addend: 0x0, symName: __ZL11patchBuf199, symObjAddr: 0x1AB176, symBinAddr: 0x19CF76, symSize: 0x0 }
  - { offsetInCU: 0x11F3A, offset: 0x33141, size: 0x8, addend: 0x0, symName: __ZL11patchBuf200, symObjAddr: 0x1AB17A, symBinAddr: 0x19CF7A, symSize: 0x0 }
  - { offsetInCU: 0x11F5B, offset: 0x33162, size: 0x8, addend: 0x0, symName: __ZL10patches132, symObjAddr: 0x79F0, symBinAddr: 0x1AA3A0, symSize: 0x0 }
  - { offsetInCU: 0x11F7C, offset: 0x33183, size: 0x8, addend: 0x0, symName: __ZL11patchBuf201, symObjAddr: 0x1AB17E, symBinAddr: 0x19CF7E, symSize: 0x0 }
  - { offsetInCU: 0x11FB1, offset: 0x331B8, size: 0x8, addend: 0x0, symName: __ZL11patchBuf202, symObjAddr: 0x1AB189, symBinAddr: 0x19CF89, symSize: 0x0 }
  - { offsetInCU: 0x11FD2, offset: 0x331D9, size: 0x8, addend: 0x0, symName: __ZL10patches133, symObjAddr: 0x7A20, symBinAddr: 0x1AA3D0, symSize: 0x0 }
  - { offsetInCU: 0x11FF3, offset: 0x331FA, size: 0x8, addend: 0x0, symName: __ZL11patchBuf203, symObjAddr: 0x1AB194, symBinAddr: 0x19CF94, symSize: 0x0 }
  - { offsetInCU: 0x12014, offset: 0x3321B, size: 0x8, addend: 0x0, symName: __ZL11patchBuf204, symObjAddr: 0x1AB198, symBinAddr: 0x19CF98, symSize: 0x0 }
  - { offsetInCU: 0x12035, offset: 0x3323C, size: 0x8, addend: 0x0, symName: __ZL11patchBuf205, symObjAddr: 0x1AB19C, symBinAddr: 0x19CF9C, symSize: 0x0 }
  - { offsetInCU: 0x12056, offset: 0x3325D, size: 0x8, addend: 0x0, symName: __ZL11patchBuf206, symObjAddr: 0x1AB1A0, symBinAddr: 0x19CFA0, symSize: 0x0 }
  - { offsetInCU: 0x12077, offset: 0x3327E, size: 0x8, addend: 0x0, symName: __ZL11patchBuf207, symObjAddr: 0x1AB1A4, symBinAddr: 0x19CFA4, symSize: 0x0 }
  - { offsetInCU: 0x12098, offset: 0x3329F, size: 0x8, addend: 0x0, symName: __ZL11patchBuf208, symObjAddr: 0x1AB1A8, symBinAddr: 0x19CFA8, symSize: 0x0 }
  - { offsetInCU: 0x120B9, offset: 0x332C0, size: 0x8, addend: 0x0, symName: __ZL10patches134, symObjAddr: 0x7AE0, symBinAddr: 0x1AA490, symSize: 0x0 }
  - { offsetInCU: 0x120DA, offset: 0x332E1, size: 0x8, addend: 0x0, symName: __ZL10patches135, symObjAddr: 0x7B40, symBinAddr: 0x1AA4F0, symSize: 0x0 }
  - { offsetInCU: 0x120FB, offset: 0x33302, size: 0x8, addend: 0x0, symName: __ZL10patches136, symObjAddr: 0x7B70, symBinAddr: 0x1AA520, symSize: 0x0 }
  - { offsetInCU: 0x1211C, offset: 0x33323, size: 0x8, addend: 0x0, symName: __ZL11patchBuf209, symObjAddr: 0x1AB1AC, symBinAddr: 0x19CFAC, symSize: 0x0 }
  - { offsetInCU: 0x1213D, offset: 0x33344, size: 0x8, addend: 0x0, symName: __ZL11patchBuf210, symObjAddr: 0x1AB1B0, symBinAddr: 0x19CFB0, symSize: 0x0 }
  - { offsetInCU: 0x1215E, offset: 0x33365, size: 0x8, addend: 0x0, symName: __ZL11patchBuf211, symObjAddr: 0x1AB1B4, symBinAddr: 0x19CFB4, symSize: 0x0 }
  - { offsetInCU: 0x1217F, offset: 0x33386, size: 0x8, addend: 0x0, symName: __ZL11patchBuf212, symObjAddr: 0x1AB1B8, symBinAddr: 0x19CFB8, symSize: 0x0 }
  - { offsetInCU: 0x121A0, offset: 0x333A7, size: 0x8, addend: 0x0, symName: __ZL10patches137, symObjAddr: 0x7BD0, symBinAddr: 0x1AA580, symSize: 0x0 }
  - { offsetInCU: 0x121C1, offset: 0x333C8, size: 0x8, addend: 0x0, symName: __ZL11patchBuf213, symObjAddr: 0x1AB1BC, symBinAddr: 0x19CFBC, symSize: 0x0 }
  - { offsetInCU: 0x121E2, offset: 0x333E9, size: 0x8, addend: 0x0, symName: __ZL10patches138, symObjAddr: 0x7C00, symBinAddr: 0x1AA5B0, symSize: 0x0 }
  - { offsetInCU: 0x12203, offset: 0x3340A, size: 0x8, addend: 0x0, symName: __ZL10patches139, symObjAddr: 0x7C60, symBinAddr: 0x1AA610, symSize: 0x0 }
  - { offsetInCU: 0x12224, offset: 0x3342B, size: 0x8, addend: 0x0, symName: __ZL11patchBuf214, symObjAddr: 0x1AB1C7, symBinAddr: 0x19CFC7, symSize: 0x0 }
  - { offsetInCU: 0x12245, offset: 0x3344C, size: 0x8, addend: 0x0, symName: __ZL11patchBuf215, symObjAddr: 0x1AB1CB, symBinAddr: 0x19CFCB, symSize: 0x0 }
  - { offsetInCU: 0x12266, offset: 0x3346D, size: 0x8, addend: 0x0, symName: __ZL10patches140, symObjAddr: 0x7CC0, symBinAddr: 0x1AA670, symSize: 0x0 }
  - { offsetInCU: 0x12287, offset: 0x3348E, size: 0x8, addend: 0x0, symName: __ZL11patchBuf216, symObjAddr: 0x1AB1CF, symBinAddr: 0x19CFCF, symSize: 0x0 }
  - { offsetInCU: 0x122A8, offset: 0x334AF, size: 0x8, addend: 0x0, symName: __ZL11patchBuf217, symObjAddr: 0x1AB1D3, symBinAddr: 0x19CFD3, symSize: 0x0 }
  - { offsetInCU: 0x122C9, offset: 0x334D0, size: 0x8, addend: 0x0, symName: __ZL10patches141, symObjAddr: 0x7D20, symBinAddr: 0x1AA6D0, symSize: 0x0 }
  - { offsetInCU: 0x122EA, offset: 0x334F1, size: 0x8, addend: 0x0, symName: __ZL10patches142, symObjAddr: 0x7D50, symBinAddr: 0x1AA700, symSize: 0x0 }
  - { offsetInCU: 0x1230B, offset: 0x33512, size: 0x8, addend: 0x0, symName: __ZL11patchBuf218, symObjAddr: 0x1AB1D7, symBinAddr: 0x19CFD7, symSize: 0x0 }
  - { offsetInCU: 0x1232C, offset: 0x33533, size: 0x8, addend: 0x0, symName: __ZL11patchBuf219, symObjAddr: 0x1AB1DB, symBinAddr: 0x19CFDB, symSize: 0x0 }
  - { offsetInCU: 0x1234D, offset: 0x33554, size: 0x8, addend: 0x0, symName: __ZL10patches143, symObjAddr: 0x7DB0, symBinAddr: 0x1AA760, symSize: 0x0 }
  - { offsetInCU: 0x1236E, offset: 0x33575, size: 0x8, addend: 0x0, symName: __ZL10patches144, symObjAddr: 0x7DE0, symBinAddr: 0x1AA790, symSize: 0x0 }
  - { offsetInCU: 0x1238F, offset: 0x33596, size: 0x8, addend: 0x0, symName: __ZL10patches145, symObjAddr: 0x7E10, symBinAddr: 0x1AA7C0, symSize: 0x0 }
  - { offsetInCU: 0x123B0, offset: 0x335B7, size: 0x8, addend: 0x0, symName: __ZL10patches146, symObjAddr: 0x7E40, symBinAddr: 0x1AA7F0, symSize: 0x0 }
  - { offsetInCU: 0x123D1, offset: 0x335D8, size: 0x8, addend: 0x0, symName: __ZL11patchBuf220, symObjAddr: 0x1AB1DF, symBinAddr: 0x19CFDF, symSize: 0x0 }
  - { offsetInCU: 0x123F2, offset: 0x335F9, size: 0x8, addend: 0x0, symName: __ZL11patchBuf221, symObjAddr: 0x1AB1E3, symBinAddr: 0x19CFE3, symSize: 0x0 }
  - { offsetInCU: 0x12413, offset: 0x3361A, size: 0x8, addend: 0x0, symName: __ZL10patches147, symObjAddr: 0x7EA0, symBinAddr: 0x1AA850, symSize: 0x0 }
  - { offsetInCU: 0x12434, offset: 0x3363B, size: 0x8, addend: 0x0, symName: __ZL10patches148, symObjAddr: 0x7ED0, symBinAddr: 0x1AA880, symSize: 0x0 }
  - { offsetInCU: 0x12455, offset: 0x3365C, size: 0x8, addend: 0x0, symName: __ZL11patchBuf222, symObjAddr: 0x1AB1E7, symBinAddr: 0x19CFE7, symSize: 0x0 }
  - { offsetInCU: 0x12476, offset: 0x3367D, size: 0x8, addend: 0x0, symName: __ZL10patches149, symObjAddr: 0x7F00, symBinAddr: 0x1AA8B0, symSize: 0x0 }
  - { offsetInCU: 0x12497, offset: 0x3369E, size: 0x8, addend: 0x0, symName: __ZL10patches150, symObjAddr: 0x7F30, symBinAddr: 0x1AA8E0, symSize: 0x0 }
  - { offsetInCU: 0x124B8, offset: 0x336BF, size: 0x8, addend: 0x0, symName: __ZL10patches151, symObjAddr: 0x7F60, symBinAddr: 0x1AA910, symSize: 0x0 }
...
