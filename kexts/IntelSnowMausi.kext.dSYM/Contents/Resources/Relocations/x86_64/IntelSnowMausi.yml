---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/work/IntelMausi/IntelMausi/build/Release/IntelSnowMausi.kext/Contents/MacOS/IntelSnowMausi'
relocations:
  - { offsetInCU: 0x36, offset: 0x431CC, size: 0x8, addend: 0x0, symName: _kmod_info, symObjAddr: 0x0, symBinAddr: 0x13880, symSize: 0x0 }
  - { offsetInCU: 0x217, offset: 0x433AD, size: 0x8, addend: 0x0, symName: __realmain, symObjAddr: 0xAE8, symBinAddr: 0x13A10, symSize: 0x0 }
  - { offsetInCU: 0x22E, offset: 0x433C4, size: 0x8, addend: 0x0, symName: __antimain, symObjAddr: 0xAF0, symBinAddr: 0x13A18, symSize: 0x0 }
  - { offsetInCU: 0x245, offset: 0x433DB, size: 0x8, addend: 0x0, symName: __kext_apple_cc, symObjAddr: 0xC4, symBinAddr: 0x13944, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x4340C, size: 0x8, addend: 0x0, symName: ___ew32_prepare, symObjAddr: 0x0, symBinAddr: 0x89C, symSize: 0x4C }
  - { offsetInCU: 0x28F, offset: 0x43674, size: 0x8, addend: 0x0, symName: ___ew32_prepare, symObjAddr: 0x0, symBinAddr: 0x89C, symSize: 0x4C }
  - { offsetInCU: 0x297, offset: 0x4367C, size: 0x8, addend: 0x0, symName: ___ew32, symObjAddr: 0x4C, symBinAddr: 0x8E8, symSize: 0x39 }
  - { offsetInCU: 0x318, offset: 0x436FD, size: 0x8, addend: 0x0, symName: ___ew32, symObjAddr: 0x4C, symBinAddr: 0x8E8, symSize: 0x39 }
  - { offsetInCU: 0x320, offset: 0x43705, size: 0x8, addend: 0x0, symName: _e1000e_get_hw_control, symObjAddr: 0x85, symBinAddr: 0x921, symSize: 0x95 }
  - { offsetInCU: 0x1C1D, offset: 0x45002, size: 0x8, addend: 0x0, symName: _e1000e_get_hw_control, symObjAddr: 0x85, symBinAddr: 0x921, symSize: 0x95 }
  - { offsetInCU: 0x1C25, offset: 0x4500A, size: 0x8, addend: 0x0, symName: _e1000e_release_hw_control, symObjAddr: 0x11A, symBinAddr: 0x9B6, symSize: 0x95 }
  - { offsetInCU: 0x1D37, offset: 0x4511C, size: 0x8, addend: 0x0, symName: _e1000e_release_hw_control, symObjAddr: 0x11A, symBinAddr: 0x9B6, symSize: 0x95 }
  - { offsetInCU: 0x1D3F, offset: 0x45124, size: 0x8, addend: 0x0, symName: _e1000e_power_up_phy, symObjAddr: 0x1AF, symBinAddr: 0xA4B, symSize: 0x35 }
  - { offsetInCU: 0x1E51, offset: 0x45236, size: 0x8, addend: 0x0, symName: _e1000e_power_up_phy, symObjAddr: 0x1AF, symBinAddr: 0xA4B, symSize: 0x35 }
  - { offsetInCU: 0x1E59, offset: 0x4523E, size: 0x8, addend: 0x0, symName: _e1000e_update_phy_stats, symObjAddr: 0x1E4, symBinAddr: 0xA80, symSize: 0x1F5 }
  - { offsetInCU: 0x1E81, offset: 0x45266, size: 0x8, addend: 0x0, symName: _e1000e_update_phy_stats, symObjAddr: 0x1E4, symBinAddr: 0xA80, symSize: 0x1F5 }
  - { offsetInCU: 0x27, offset: 0x45309, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0xC76, symSize: 0x32 }
  - { offsetInCU: 0x3F, offset: 0x45321, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10gMetaClassE, symObjAddr: 0x18AA0, symBinAddr: 0x13A20, symSize: 0x0 }
  - { offsetInCU: 0x3C9F, offset: 0x48F81, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9metaClassE, symObjAddr: 0x3220, symBinAddr: 0x12070, symSize: 0x0 }
  - { offsetInCU: 0x3CB7, offset: 0x48F99, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10superClassE, symObjAddr: 0x3228, symBinAddr: 0x12078, symSize: 0x0 }
  - { offsetInCU: 0x3CE5, offset: 0x48FC7, size: 0x8, addend: 0x0, symName: __ZL15powerStateArray, symObjAddr: 0x4EA0, symBinAddr: 0x13950, symSize: 0x0 }
  - { offsetInCU: 0x3D18, offset: 0x48FFA, size: 0x8, addend: 0x0, symName: __ZL11deviceTable, symObjAddr: 0x3F80, symBinAddr: 0x12DD0, symSize: 0x0 }
  - { offsetInCU: 0x4002, offset: 0x492E4, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0xC76, symSize: 0x32 }
  - { offsetInCU: 0x400A, offset: 0x492EC, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9MetaClassD1Ev, symObjAddr: 0x32, symBinAddr: 0xCA8, symSize: 0xA }
  - { offsetInCU: 0x4083, offset: 0x49365, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9MetaClassD1Ev, symObjAddr: 0x32, symBinAddr: 0xCA8, symSize: 0xA }
  - { offsetInCU: 0x408B, offset: 0x4936D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiC2EPK11OSMetaClass, symObjAddr: 0x3C, symBinAddr: 0xCB2, symSize: 0x20 }
  - { offsetInCU: 0x40BB, offset: 0x4939D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiC2EPK11OSMetaClass, symObjAddr: 0x3C, symBinAddr: 0xCB2, symSize: 0x20 }
  - { offsetInCU: 0x40DB, offset: 0x493BD, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiC2EPK11OSMetaClass, symObjAddr: 0x3C, symBinAddr: 0xCB2, symSize: 0x20 }
  - { offsetInCU: 0x4156, offset: 0x49438, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiC1EPK11OSMetaClass, symObjAddr: 0x5C, symBinAddr: 0xCD2, symSize: 0x20 }
  - { offsetInCU: 0x41DE, offset: 0x494C0, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiD2Ev, symObjAddr: 0x7C, symBinAddr: 0xCF2, symSize: 0xA }
  - { offsetInCU: 0x41E6, offset: 0x494C8, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiD1Ev, symObjAddr: 0x86, symBinAddr: 0xCFC, symSize: 0xA }
  - { offsetInCU: 0x4230, offset: 0x49512, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiD1Ev, symObjAddr: 0x86, symBinAddr: 0xCFC, symSize: 0xA }
  - { offsetInCU: 0x4238, offset: 0x4951A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiD0Ev, symObjAddr: 0x90, symBinAddr: 0xD06, symSize: 0x22 }
  - { offsetInCU: 0x4268, offset: 0x4954A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiD0Ev, symObjAddr: 0x90, symBinAddr: 0xD06, symSize: 0x22 }
  - { offsetInCU: 0x42AC, offset: 0x4958E, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiD0Ev, symObjAddr: 0x90, symBinAddr: 0xD06, symSize: 0x22 }
  - { offsetInCU: 0x433A, offset: 0x4961C, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi12getMetaClassEv, symObjAddr: 0xB2, symBinAddr: 0xD28, symSize: 0xE }
  - { offsetInCU: 0x436C, offset: 0x4964E, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9MetaClassC2Ev, symObjAddr: 0xC0, symBinAddr: 0xD36, symSize: 0x32 }
  - { offsetInCU: 0x4374, offset: 0x49656, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi9MetaClass5allocEv, symObjAddr: 0xF2, symBinAddr: 0xD68, symSize: 0x40 }
  - { offsetInCU: 0x43E5, offset: 0x496C7, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi9MetaClass5allocEv, symObjAddr: 0xF2, symBinAddr: 0xD68, symSize: 0x40 }
  - { offsetInCU: 0x43ED, offset: 0x496CF, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiC1Ev, symObjAddr: 0x132, symBinAddr: 0xDA8, symSize: 0x30 }
  - { offsetInCU: 0x446B, offset: 0x4974D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiC1Ev, symObjAddr: 0x132, symBinAddr: 0xDA8, symSize: 0x30 }
  - { offsetInCU: 0x44C6, offset: 0x497A8, size: 0x8, addend: 0x0, symName: __ZN10IntelMausiC2Ev, symObjAddr: 0x162, symBinAddr: 0xDD8, symSize: 0x30 }
  - { offsetInCU: 0x44F5, offset: 0x497D7, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi4initEP12OSDictionary, symObjAddr: 0x192, symBinAddr: 0xE08, symSize: 0x126 }
  - { offsetInCU: 0x4563, offset: 0x49845, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi4freeEv, symObjAddr: 0x2B8, symBinAddr: 0xF2E, symSize: 0x1D2 }
  - { offsetInCU: 0x45AD, offset: 0x4988F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi5startEP9IOService, symObjAddr: 0x48A, symBinAddr: 0x1100, symSize: 0x288 }
  - { offsetInCU: 0x45B5, offset: 0x49897, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10intelStartEv, symObjAddr: 0x712, symBinAddr: 0x1388, symSize: 0x3DE }
  - { offsetInCU: 0x483C, offset: 0x49B1E, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10intelStartEv, symObjAddr: 0x712, symBinAddr: 0x1388, symSize: 0x3DE }
  - { offsetInCU: 0x4844, offset: 0x49B26, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi4stopEP9IOService, symObjAddr: 0xAF0, symBinAddr: 0x1766, symSize: 0x20E }
  - { offsetInCU: 0x4AC1, offset: 0x49DA3, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi4stopEP9IOService, symObjAddr: 0xAF0, symBinAddr: 0x1766, symSize: 0x20E }
  - { offsetInCU: 0x4AC9, offset: 0x49DAB, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi23registerWithPolicyMakerEP9IOService, symObjAddr: 0xCFE, symBinAddr: 0x1974, symSize: 0x32 }
  - { offsetInCU: 0x4B22, offset: 0x49E04, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi23registerWithPolicyMakerEP9IOService, symObjAddr: 0xCFE, symBinAddr: 0x1974, symSize: 0x32 }
  - { offsetInCU: 0x4B2A, offset: 0x49E0C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi13setPowerStateEmP9IOService, symObjAddr: 0xD30, symBinAddr: 0x19A6, symSize: 0x54 }
  - { offsetInCU: 0x4B6E, offset: 0x49E50, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi13setPowerStateEmP9IOService, symObjAddr: 0xD30, symBinAddr: 0x19A6, symSize: 0x54 }
  - { offsetInCU: 0x4BEE, offset: 0x49ED0, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18systemWillShutdownEj, symObjAddr: 0xD84, symBinAddr: 0x19FA, symSize: 0x6C }
  - { offsetInCU: 0x4BF6, offset: 0x49ED8, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi6enableEP16IOKernelDebugger, symObjAddr: 0xDF0, symBinAddr: 0x1A66, symSize: 0xA }
  - { offsetInCU: 0x4C3A, offset: 0x49F1C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi6enableEP16IOKernelDebugger, symObjAddr: 0xDF0, symBinAddr: 0x1A66, symSize: 0xA }
  - { offsetInCU: 0x4C42, offset: 0x49F24, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12driverEnableEv, symObjAddr: 0xDFA, symBinAddr: 0x1A70, symSize: 0xDE }
  - { offsetInCU: 0x4C80, offset: 0x49F62, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12driverEnableEv, symObjAddr: 0xDFA, symBinAddr: 0x1A70, symSize: 0xDE }
  - { offsetInCU: 0x4C88, offset: 0x49F6A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi7disableEP16IOKernelDebugger, symObjAddr: 0xED8, symBinAddr: 0x1B4E, symSize: 0xE }
  - { offsetInCU: 0x4CDD, offset: 0x49FBF, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi7disableEP16IOKernelDebugger, symObjAddr: 0xED8, symBinAddr: 0x1B4E, symSize: 0xE }
  - { offsetInCU: 0x4D25, offset: 0x4A007, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi13driverDisableEv, symObjAddr: 0xEE6, symBinAddr: 0x1B5C, symSize: 0x104 }
  - { offsetInCU: 0x4D7F, offset: 0x4A061, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi6enableEP18IONetworkInterface, symObjAddr: 0xFEA, symBinAddr: 0x1C60, symSize: 0xA }
  - { offsetInCU: 0x4D87, offset: 0x4A069, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi7disableEP18IONetworkInterface, symObjAddr: 0xFF4, symBinAddr: 0x1C6A, symSize: 0xE }
  - { offsetInCU: 0x4DC5, offset: 0x4A0A7, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi7disableEP18IONetworkInterface, symObjAddr: 0xFF4, symBinAddr: 0x1C6A, symSize: 0xE }
  - { offsetInCU: 0x4E0D, offset: 0x4A0EF, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12freePacketExEP6__mbufj, symObjAddr: 0x1002, symBinAddr: 0x1C78, symSize: 0x90 }
  - { offsetInCU: 0x4E15, offset: 0x4A0F7, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10kdpStartupEv, symObjAddr: 0x1092, symBinAddr: 0x1D08, symSize: 0x10A }
  - { offsetInCU: 0x4E83, offset: 0x4A165, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10kdpStartupEv, symObjAddr: 0x1092, symBinAddr: 0x1D08, symSize: 0x10A }
  - { offsetInCU: 0x4EE4, offset: 0x4A1C6, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11isKdpPacketEPhj, symObjAddr: 0x119C, symBinAddr: 0x1E12, symSize: 0x8A }
  - { offsetInCU: 0x4EEC, offset: 0x4A1CE, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi13receivePacketEPvPjj, symObjAddr: 0x1226, symBinAddr: 0x1E9C, symSize: 0x226 }
  - { offsetInCU: 0x4FF5, offset: 0x4A2D7, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi13receivePacketEPvPjj, symObjAddr: 0x1226, symBinAddr: 0x1E9C, symSize: 0x226 }
  - { offsetInCU: 0x51A2, offset: 0x4A484, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10sendPacketEPvj, symObjAddr: 0x144C, symBinAddr: 0x20C2, symSize: 0x57A }
  - { offsetInCU: 0x51AA, offset: 0x4A48C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11txInterruptEj, symObjAddr: 0x19C6, symBinAddr: 0x263C, symSize: 0x104 }
  - { offsetInCU: 0x5392, offset: 0x4A674, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11txInterruptEj, symObjAddr: 0x19C6, symBinAddr: 0x263C, symSize: 0x104 }
  - { offsetInCU: 0x539A, offset: 0x4A67C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12outputPacketEP6__mbufPv, symObjAddr: 0x1ACA, symBinAddr: 0x2740, symSize: 0x514 }
  - { offsetInCU: 0x5419, offset: 0x4A6FB, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12outputPacketEP6__mbufPv, symObjAddr: 0x1ACA, symBinAddr: 0x2740, symSize: 0x514 }
  - { offsetInCU: 0x5421, offset: 0x4A703, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi26getPacketBufferConstraintsEP25IOPacketBufferConstraints, symObjAddr: 0x1FDE, symBinAddr: 0x2C54, symSize: 0x14 }
  - { offsetInCU: 0x5627, offset: 0x4A909, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi26getPacketBufferConstraintsEP25IOPacketBufferConstraints, symObjAddr: 0x1FDE, symBinAddr: 0x2C54, symSize: 0x14 }
  - { offsetInCU: 0x566D, offset: 0x4A94F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17createOutputQueueEv, symObjAddr: 0x1FF2, symBinAddr: 0x2C68, symSize: 0xC }
  - { offsetInCU: 0x5675, offset: 0x4A957, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi15newVendorStringEv, symObjAddr: 0x1FFE, symBinAddr: 0x2C74, symSize: 0x12 }
  - { offsetInCU: 0x56A2, offset: 0x4A984, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi15newVendorStringEv, symObjAddr: 0x1FFE, symBinAddr: 0x2C74, symSize: 0x12 }
  - { offsetInCU: 0x56D5, offset: 0x4A9B7, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi14newModelStringEv, symObjAddr: 0x2010, symBinAddr: 0x2C86, symSize: 0x20 }
  - { offsetInCU: 0x56DD, offset: 0x4A9BF, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18configureInterfaceEP18IONetworkInterface, symObjAddr: 0x2030, symBinAddr: 0x2CA6, symSize: 0x13A }
  - { offsetInCU: 0x570C, offset: 0x4A9EE, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18configureInterfaceEP18IONetworkInterface, symObjAddr: 0x2030, symBinAddr: 0x2CA6, symSize: 0x13A }
  - { offsetInCU: 0x57A8, offset: 0x4AA8A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14createWorkLoopEv, symObjAddr: 0x216A, symBinAddr: 0x2DE0, symSize: 0x22 }
  - { offsetInCU: 0x57B0, offset: 0x4AA92, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi11getWorkLoopEv, symObjAddr: 0x218C, symBinAddr: 0x2E02, symSize: 0xE }
  - { offsetInCU: 0x57DF, offset: 0x4AAC1, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi11getWorkLoopEv, symObjAddr: 0x218C, symBinAddr: 0x2E02, symSize: 0xE }
  - { offsetInCU: 0x5814, offset: 0x4AAF6, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18setPromiscuousModeEb, symObjAddr: 0x219A, symBinAddr: 0x2E10, symSize: 0x70 }
  - { offsetInCU: 0x581C, offset: 0x4AAFE, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16setMulticastModeEb, symObjAddr: 0x220A, symBinAddr: 0x2E80, symSize: 0x6C }
  - { offsetInCU: 0x58F0, offset: 0x4ABD2, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16setMulticastModeEb, symObjAddr: 0x220A, symBinAddr: 0x2E80, symSize: 0x6C }
  - { offsetInCU: 0x59E0, offset: 0x4ACC2, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16setMulticastListEP17IOEthernetAddressj, symObjAddr: 0x2276, symBinAddr: 0x2EEC, symSize: 0xF0 }
  - { offsetInCU: 0x5A95, offset: 0x4AD77, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18getChecksumSupportEPjjb, symObjAddr: 0x2366, symBinAddr: 0x2FDC, symSize: 0x36 }
  - { offsetInCU: 0x5A9D, offset: 0x4AD7F, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi11getFeaturesEv, symObjAddr: 0x239C, symBinAddr: 0x3012, symSize: 0xC }
  - { offsetInCU: 0x5B16, offset: 0x4ADF8, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi11getFeaturesEv, symObjAddr: 0x239C, symBinAddr: 0x3012, symSize: 0xC }
  - { offsetInCU: 0x5B5B, offset: 0x4AE3D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi20setWakeOnMagicPacketEb, symObjAddr: 0x23A8, symBinAddr: 0x301E, symSize: 0x20 }
  - { offsetInCU: 0x5BBA, offset: 0x4AE9C, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi16getPacketFiltersEPK8OSSymbolPj, symObjAddr: 0x23C8, symBinAddr: 0x303E, symSize: 0x42 }
  - { offsetInCU: 0x5BC2, offset: 0x4AEA4, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18setHardwareAddressEPK17IOEthernetAddress, symObjAddr: 0x240A, symBinAddr: 0x3080, symSize: 0x64 }
  - { offsetInCU: 0x5C27, offset: 0x4AF09, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18setHardwareAddressEPK17IOEthernetAddress, symObjAddr: 0x240A, symBinAddr: 0x3080, symSize: 0x64 }
  - { offsetInCU: 0x5CF3, offset: 0x4AFD5, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18getHardwareAddressEP17IOEthernetAddress, symObjAddr: 0x246E, symBinAddr: 0x30E4, symSize: 0x50 }
  - { offsetInCU: 0x5CFB, offset: 0x4AFDD, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12selectMediumEPK15IONetworkMedium, symObjAddr: 0x24BE, symBinAddr: 0x3134, symSize: 0x50 }
  - { offsetInCU: 0x5DAA, offset: 0x4B08C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12selectMediumEPK15IONetworkMedium, symObjAddr: 0x24BE, symBinAddr: 0x3134, symSize: 0x50 }
  - { offsetInCU: 0x5DB2, offset: 0x4B094, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16updateStatisticsEP13e1000_adapter, symObjAddr: 0x250E, symBinAddr: 0x3184, symSize: 0x2B8 }
  - { offsetInCU: 0x5E19, offset: 0x4B0FB, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16updateStatisticsEP13e1000_adapter, symObjAddr: 0x250E, symBinAddr: 0x3184, symSize: 0x2B8 }
  - { offsetInCU: 0x5E21, offset: 0x4B103, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi16getMaxPacketSizeEPj, symObjAddr: 0x27C6, symBinAddr: 0x343C, symSize: 0xE }
  - { offsetInCU: 0x64AE, offset: 0x4B790, size: 0x8, addend: 0x0, symName: __ZNK10IntelMausi16getMaxPacketSizeEPj, symObjAddr: 0x27C6, symBinAddr: 0x343C, symSize: 0xE }
  - { offsetInCU: 0x64B6, offset: 0x4B798, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16setMaxPacketSizeEj, symObjAddr: 0x27D4, symBinAddr: 0x344A, symSize: 0x5C }
  - { offsetInCU: 0x64F4, offset: 0x4B7D6, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16setMaxPacketSizeEj, symObjAddr: 0x27D4, symBinAddr: 0x344A, symSize: 0x5C }
  - { offsetInCU: 0x64FC, offset: 0x4B7DE, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11setLinkDownEv, symObjAddr: 0x2830, symBinAddr: 0x34A6, symSize: 0x96 }
  - { offsetInCU: 0x6584, offset: 0x4B866, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11setLinkDownEv, symObjAddr: 0x2830, symBinAddr: 0x34A6, symSize: 0x96 }
  - { offsetInCU: 0x665A, offset: 0x4B93C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11rxInterruptEv, symObjAddr: 0x28C6, symBinAddr: 0x353C, symSize: 0x2C8 }
  - { offsetInCU: 0x688B, offset: 0x4BB6D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi15checkLinkStatusEv, symObjAddr: 0x2B8E, symBinAddr: 0x3804, symSize: 0xF0 }
  - { offsetInCU: 0x696D, offset: 0x4BC4F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9setLinkUpEv, symObjAddr: 0x2C7E, symBinAddr: 0x38F4, symSize: 0x218 }
  - { offsetInCU: 0x6975, offset: 0x4BC57, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17interruptOccurredEP8OSObjectP22IOInterruptEventSourcei, symObjAddr: 0x2E96, symBinAddr: 0x3B0C, symSize: 0xA8 }
  - { offsetInCU: 0x6C86, offset: 0x4BF68, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17interruptOccurredEP8OSObjectP22IOInterruptEventSourcei, symObjAddr: 0x2E96, symBinAddr: 0x3B0C, symSize: 0xA8 }
  - { offsetInCU: 0x6DFF, offset: 0x4C0E1, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17intelIdentifyChipEv, symObjAddr: 0x2F3E, symBinAddr: 0x3BB4, symSize: 0xCA }
  - { offsetInCU: 0x6EC2, offset: 0x4C1A4, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11timerActionEP18IOTimerEventSource, symObjAddr: 0x3008, symBinAddr: 0x3C7E, symSize: 0xAC }
  - { offsetInCU: 0x6F5F, offset: 0x4C241, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16checkForDeadlockEv, symObjAddr: 0x30B4, symBinAddr: 0x3D2A, symSize: 0x11C }
  - { offsetInCU: 0x6F67, offset: 0x4C249, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9MetaClassD0Ev, symObjAddr: 0x31D0, symBinAddr: 0x3E46, symSize: 0xA }
  - { offsetInCU: 0x707B, offset: 0x4C35D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9MetaClassD0Ev, symObjAddr: 0x31D0, symBinAddr: 0x3E46, symSize: 0xA }
  - { offsetInCU: 0x7083, offset: 0x4C365, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_IntelMausiEthernet.cpp, symObjAddr: 0x31DA, symBinAddr: 0x3E50, symSize: 0x33 }
  - { offsetInCU: 0x70BF, offset: 0x4C3A1, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_IntelMausiEthernet.cpp, symObjAddr: 0x31DA, symBinAddr: 0x3E50, symSize: 0x33 }
  - { offsetInCU: 0x70E9, offset: 0x4C3CB, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_IntelMausiEthernet.cpp, symObjAddr: 0x31DA, symBinAddr: 0x3E50, symSize: 0x33 }
  - { offsetInCU: 0x7138, offset: 0x4C41A, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_IntelMausiEthernet.cpp, symObjAddr: 0x31DA, symBinAddr: 0x3E50, symSize: 0x33 }
  - { offsetInCU: 0x7140, offset: 0x4C422, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x320D, symBinAddr: 0x3E83, symSize: 0x11 }
  - { offsetInCU: 0x715E, offset: 0x4C440, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x320D, symBinAddr: 0x3E83, symSize: 0x11 }
  - { offsetInCU: 0x717A, offset: 0x4C45C, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x320D, symBinAddr: 0x3E83, symSize: 0x11 }
  - { offsetInCU: 0x71A5, offset: 0x4C487, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x320D, symBinAddr: 0x3E83, symSize: 0x11 }
  - { offsetInCU: 0x71C8, offset: 0x4C4AA, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x320D, symBinAddr: 0x3E83, symSize: 0x11 }
  - { offsetInCU: 0x27, offset: 0x4C835, size: 0x8, addend: 0x0, symName: _e1000e_get_bus_info_pcie, symObjAddr: 0x0, symBinAddr: 0x3E94, symSize: 0x8 }
  - { offsetInCU: 0x29D, offset: 0x4CAAB, size: 0x8, addend: 0x0, symName: _e1000e_get_bus_info_pcie, symObjAddr: 0x0, symBinAddr: 0x3E94, symSize: 0x8 }
  - { offsetInCU: 0x2A5, offset: 0x4CAB3, size: 0x8, addend: 0x0, symName: _e1000_set_lan_id_multi_port_pcie, symObjAddr: 0x8, symBinAddr: 0x3E9C, symSize: 0x1A }
  - { offsetInCU: 0x30D, offset: 0x4CB1B, size: 0x8, addend: 0x0, symName: _e1000_set_lan_id_multi_port_pcie, symObjAddr: 0x8, symBinAddr: 0x3E9C, symSize: 0x1A }
  - { offsetInCU: 0x315, offset: 0x4CB23, size: 0x8, addend: 0x0, symName: _e1000_set_lan_id_single_port, symObjAddr: 0x22, symBinAddr: 0x3EB6, symSize: 0xF }
  - { offsetInCU: 0x383, offset: 0x4CB91, size: 0x8, addend: 0x0, symName: _e1000_set_lan_id_single_port, symObjAddr: 0x22, symBinAddr: 0x3EB6, symSize: 0xF }
  - { offsetInCU: 0x38B, offset: 0x4CB99, size: 0x8, addend: 0x0, symName: _e1000_clear_vfta_generic, symObjAddr: 0x31, symBinAddr: 0x3EC5, symSize: 0x27 }
  - { offsetInCU: 0x3F9, offset: 0x4CC07, size: 0x8, addend: 0x0, symName: _e1000_clear_vfta_generic, symObjAddr: 0x31, symBinAddr: 0x3EC5, symSize: 0x27 }
  - { offsetInCU: 0x401, offset: 0x4CC0F, size: 0x8, addend: 0x0, symName: _e1000_write_vfta_generic, symObjAddr: 0x58, symBinAddr: 0x3EEC, symSize: 0x17 }
  - { offsetInCU: 0x491, offset: 0x4CC9F, size: 0x8, addend: 0x0, symName: _e1000_write_vfta_generic, symObjAddr: 0x58, symBinAddr: 0x3EEC, symSize: 0x17 }
  - { offsetInCU: 0x499, offset: 0x4CCA7, size: 0x8, addend: 0x0, symName: _e1000e_init_rx_addrs, symObjAddr: 0x6F, symBinAddr: 0x3F03, symSize: 0x65 }
  - { offsetInCU: 0x53C, offset: 0x4CD4A, size: 0x8, addend: 0x0, symName: _e1000e_init_rx_addrs, symObjAddr: 0x6F, symBinAddr: 0x3F03, symSize: 0x65 }
  - { offsetInCU: 0x544, offset: 0x4CD52, size: 0x8, addend: 0x0, symName: _e1000_check_alt_mac_addr_generic, symObjAddr: 0xD4, symBinAddr: 0x3F68, symSize: 0x115 }
  - { offsetInCU: 0x1E11, offset: 0x4E61F, size: 0x8, addend: 0x0, symName: _e1000_check_alt_mac_addr_generic, symObjAddr: 0xD4, symBinAddr: 0x3F68, symSize: 0x115 }
  - { offsetInCU: 0x1E19, offset: 0x4E627, size: 0x8, addend: 0x0, symName: _e1000e_rar_get_count_generic, symObjAddr: 0x1E9, symBinAddr: 0x407D, symSize: 0xD }
  - { offsetInCU: 0x1F55, offset: 0x4E763, size: 0x8, addend: 0x0, symName: _e1000e_rar_get_count_generic, symObjAddr: 0x1E9, symBinAddr: 0x407D, symSize: 0xD }
  - { offsetInCU: 0x1F5D, offset: 0x4E76B, size: 0x8, addend: 0x0, symName: _e1000e_rar_set_generic, symObjAddr: 0x1F6, symBinAddr: 0x408A, symSize: 0x79 }
  - { offsetInCU: 0x1F89, offset: 0x4E797, size: 0x8, addend: 0x0, symName: _e1000e_rar_set_generic, symObjAddr: 0x1F6, symBinAddr: 0x408A, symSize: 0x79 }
  - { offsetInCU: 0x1F91, offset: 0x4E79F, size: 0x8, addend: 0x0, symName: _e1000e_update_mc_addr_list_generic, symObjAddr: 0x26F, symBinAddr: 0x4103, symSize: 0xF9 }
  - { offsetInCU: 0x20BB, offset: 0x4E8C9, size: 0x8, addend: 0x0, symName: _e1000e_update_mc_addr_list_generic, symObjAddr: 0x26F, symBinAddr: 0x4103, symSize: 0xF9 }
  - { offsetInCU: 0x20C3, offset: 0x4E8D1, size: 0x8, addend: 0x0, symName: _e1000e_clear_hw_cntrs_base, symObjAddr: 0x368, symBinAddr: 0x41FC, symSize: 0xE8 }
  - { offsetInCU: 0x21FD, offset: 0x4EA0B, size: 0x8, addend: 0x0, symName: _e1000e_clear_hw_cntrs_base, symObjAddr: 0x368, symBinAddr: 0x41FC, symSize: 0xE8 }
  - { offsetInCU: 0x2205, offset: 0x4EA13, size: 0x8, addend: 0x0, symName: _e1000e_check_for_copper_link, symObjAddr: 0x450, symBinAddr: 0x42E4, symSize: 0x74 }
  - { offsetInCU: 0x2887, offset: 0x4F095, size: 0x8, addend: 0x0, symName: _e1000e_check_for_copper_link, symObjAddr: 0x450, symBinAddr: 0x42E4, symSize: 0x74 }
  - { offsetInCU: 0x288F, offset: 0x4F09D, size: 0x8, addend: 0x0, symName: _e1000e_config_fc_after_link_up, symObjAddr: 0x4C4, symBinAddr: 0x4358, symSize: 0x278 }
  - { offsetInCU: 0x2943, offset: 0x4F151, size: 0x8, addend: 0x0, symName: _e1000e_config_fc_after_link_up, symObjAddr: 0x4C4, symBinAddr: 0x4358, symSize: 0x278 }
  - { offsetInCU: 0x294B, offset: 0x4F159, size: 0x8, addend: 0x0, symName: _e1000e_check_for_fiber_link, symObjAddr: 0x73C, symBinAddr: 0x45D0, symSize: 0xB6 }
  - { offsetInCU: 0x2B72, offset: 0x4F380, size: 0x8, addend: 0x0, symName: _e1000e_check_for_fiber_link, symObjAddr: 0x73C, symBinAddr: 0x45D0, symSize: 0xB6 }
  - { offsetInCU: 0x2B7A, offset: 0x4F388, size: 0x8, addend: 0x0, symName: _e1000e_check_for_serdes_link, symObjAddr: 0x7F2, symBinAddr: 0x4686, symSize: 0x138 }
  - { offsetInCU: 0x2CBA, offset: 0x4F4C8, size: 0x8, addend: 0x0, symName: _e1000e_check_for_serdes_link, symObjAddr: 0x7F2, symBinAddr: 0x4686, symSize: 0x138 }
  - { offsetInCU: 0x2CC2, offset: 0x4F4D0, size: 0x8, addend: 0x0, symName: _e1000e_setup_link_generic, symObjAddr: 0x92A, symBinAddr: 0x47BE, symSize: 0xE8 }
  - { offsetInCU: 0x2E9A, offset: 0x4F6A8, size: 0x8, addend: 0x0, symName: _e1000e_setup_link_generic, symObjAddr: 0x92A, symBinAddr: 0x47BE, symSize: 0xE8 }
  - { offsetInCU: 0x2EA2, offset: 0x4F6B0, size: 0x8, addend: 0x0, symName: _e1000e_set_fc_watermarks, symObjAddr: 0xA12, symBinAddr: 0x48A6, symSize: 0x5E }
  - { offsetInCU: 0x2F55, offset: 0x4F763, size: 0x8, addend: 0x0, symName: _e1000e_set_fc_watermarks, symObjAddr: 0xA12, symBinAddr: 0x48A6, symSize: 0x5E }
  - { offsetInCU: 0x2F5D, offset: 0x4F76B, size: 0x8, addend: 0x0, symName: _e1000e_setup_fiber_serdes_link, symObjAddr: 0xA70, symBinAddr: 0x4904, symSize: 0xDC }
  - { offsetInCU: 0x305E, offset: 0x4F86C, size: 0x8, addend: 0x0, symName: _e1000e_setup_fiber_serdes_link, symObjAddr: 0xA70, symBinAddr: 0x4904, symSize: 0xDC }
  - { offsetInCU: 0x3066, offset: 0x4F874, size: 0x8, addend: 0x0, symName: _e1000e_config_collision_dist_generic, symObjAddr: 0xB4C, symBinAddr: 0x49E0, symSize: 0x36 }
  - { offsetInCU: 0x3172, offset: 0x4F980, size: 0x8, addend: 0x0, symName: _e1000e_config_collision_dist_generic, symObjAddr: 0xB4C, symBinAddr: 0x49E0, symSize: 0x36 }
  - { offsetInCU: 0x317A, offset: 0x4F988, size: 0x8, addend: 0x0, symName: _e1000e_force_mac_fc, symObjAddr: 0xB82, symBinAddr: 0x4A16, symSize: 0x7E }
  - { offsetInCU: 0x31E1, offset: 0x4F9EF, size: 0x8, addend: 0x0, symName: _e1000e_force_mac_fc, symObjAddr: 0xB82, symBinAddr: 0x4A16, symSize: 0x7E }
  - { offsetInCU: 0x3258, offset: 0x4FA66, size: 0x8, addend: 0x0, symName: _e1000e_get_speed_and_duplex_copper, symObjAddr: 0xC00, symBinAddr: 0x4A94, symSize: 0x33 }
  - { offsetInCU: 0x3260, offset: 0x4FA6E, size: 0x8, addend: 0x0, symName: _e1000e_get_speed_and_duplex_fiber_serdes, symObjAddr: 0xC33, symBinAddr: 0x4AC7, symSize: 0x12 }
  - { offsetInCU: 0x32ED, offset: 0x4FAFB, size: 0x8, addend: 0x0, symName: _e1000e_get_speed_and_duplex_fiber_serdes, symObjAddr: 0xC33, symBinAddr: 0x4AC7, symSize: 0x12 }
  - { offsetInCU: 0x32F5, offset: 0x4FB03, size: 0x8, addend: 0x0, symName: _e1000e_get_hw_semaphore, symObjAddr: 0xC45, symBinAddr: 0x4AD9, symSize: 0xB2 }
  - { offsetInCU: 0x336D, offset: 0x4FB7B, size: 0x8, addend: 0x0, symName: _e1000e_get_hw_semaphore, symObjAddr: 0xC45, symBinAddr: 0x4AD9, symSize: 0xB2 }
  - { offsetInCU: 0x3375, offset: 0x4FB83, size: 0x8, addend: 0x0, symName: _e1000e_put_hw_semaphore, symObjAddr: 0xCF7, symBinAddr: 0x4B8B, symSize: 0x1C }
  - { offsetInCU: 0x3469, offset: 0x4FC77, size: 0x8, addend: 0x0, symName: _e1000e_put_hw_semaphore, symObjAddr: 0xCF7, symBinAddr: 0x4B8B, symSize: 0x1C }
  - { offsetInCU: 0x3471, offset: 0x4FC7F, size: 0x8, addend: 0x0, symName: _e1000e_get_auto_rd_done, symObjAddr: 0xD13, symBinAddr: 0x4BA7, symSize: 0x3D }
  - { offsetInCU: 0x34C0, offset: 0x4FCCE, size: 0x8, addend: 0x0, symName: _e1000e_get_auto_rd_done, symObjAddr: 0xD13, symBinAddr: 0x4BA7, symSize: 0x3D }
  - { offsetInCU: 0x34C8, offset: 0x4FCD6, size: 0x8, addend: 0x0, symName: _e1000e_valid_led_default, symObjAddr: 0xD50, symBinAddr: 0x4BE4, symSize: 0x3B }
  - { offsetInCU: 0x3509, offset: 0x4FD17, size: 0x8, addend: 0x0, symName: _e1000e_valid_led_default, symObjAddr: 0xD50, symBinAddr: 0x4BE4, symSize: 0x3B }
  - { offsetInCU: 0x3511, offset: 0x4FD1F, size: 0x8, addend: 0x0, symName: _e1000e_id_led_init_generic, symObjAddr: 0xD8B, symBinAddr: 0x4C1F, symSize: 0x161 }
  - { offsetInCU: 0x35A2, offset: 0x4FDB0, size: 0x8, addend: 0x0, symName: _e1000e_id_led_init_generic, symObjAddr: 0xD8B, symBinAddr: 0x4C1F, symSize: 0x161 }
  - { offsetInCU: 0x36A2, offset: 0x4FEB0, size: 0x8, addend: 0x0, symName: _e1000e_setup_led_generic, symObjAddr: 0xEEC, symBinAddr: 0x4D80, symSize: 0x61 }
  - { offsetInCU: 0x36AA, offset: 0x4FEB8, size: 0x8, addend: 0x0, symName: _e1000e_cleanup_led_generic, symObjAddr: 0xF4D, symBinAddr: 0x4DE1, symSize: 0x18 }
  - { offsetInCU: 0x371A, offset: 0x4FF28, size: 0x8, addend: 0x0, symName: _e1000e_cleanup_led_generic, symObjAddr: 0xF4D, symBinAddr: 0x4DE1, symSize: 0x18 }
  - { offsetInCU: 0x3722, offset: 0x4FF30, size: 0x8, addend: 0x0, symName: _e1000e_blink_led_generic, symObjAddr: 0xF65, symBinAddr: 0x4DF9, symSize: 0x7E }
  - { offsetInCU: 0x3752, offset: 0x4FF60, size: 0x8, addend: 0x0, symName: _e1000e_blink_led_generic, symObjAddr: 0xF65, symBinAddr: 0x4DF9, symSize: 0x7E }
  - { offsetInCU: 0x375A, offset: 0x4FF68, size: 0x8, addend: 0x0, symName: _e1000e_led_on_generic, symObjAddr: 0xFE3, symBinAddr: 0x4E77, symSize: 0x3D }
  - { offsetInCU: 0x37F1, offset: 0x4FFFF, size: 0x8, addend: 0x0, symName: _e1000e_led_on_generic, symObjAddr: 0xFE3, symBinAddr: 0x4E77, symSize: 0x3D }
  - { offsetInCU: 0x37F9, offset: 0x50007, size: 0x8, addend: 0x0, symName: _e1000e_led_off_generic, symObjAddr: 0x1020, symBinAddr: 0x4EB4, symSize: 0x37 }
  - { offsetInCU: 0x383E, offset: 0x5004C, size: 0x8, addend: 0x0, symName: _e1000e_led_off_generic, symObjAddr: 0x1020, symBinAddr: 0x4EB4, symSize: 0x37 }
  - { offsetInCU: 0x3846, offset: 0x50054, size: 0x8, addend: 0x0, symName: _e1000e_set_pcie_no_snoop, symObjAddr: 0x1057, symBinAddr: 0x4EEB, symSize: 0x23 }
  - { offsetInCU: 0x388B, offset: 0x50099, size: 0x8, addend: 0x0, symName: _e1000e_set_pcie_no_snoop, symObjAddr: 0x1057, symBinAddr: 0x4EEB, symSize: 0x23 }
  - { offsetInCU: 0x3893, offset: 0x500A1, size: 0x8, addend: 0x0, symName: _e1000e_disable_pcie_master, symObjAddr: 0x107A, symBinAddr: 0x4F0E, symSize: 0x51 }
  - { offsetInCU: 0x390F, offset: 0x5011D, size: 0x8, addend: 0x0, symName: _e1000e_disable_pcie_master, symObjAddr: 0x107A, symBinAddr: 0x4F0E, symSize: 0x51 }
  - { offsetInCU: 0x3917, offset: 0x50125, size: 0x8, addend: 0x0, symName: _e1000e_reset_adaptive, symObjAddr: 0x10CB, symBinAddr: 0x4F5F, symSize: 0x40 }
  - { offsetInCU: 0x399B, offset: 0x501A9, size: 0x8, addend: 0x0, symName: _e1000e_reset_adaptive, symObjAddr: 0x10CB, symBinAddr: 0x4F5F, symSize: 0x40 }
  - { offsetInCU: 0x39A3, offset: 0x501B1, size: 0x8, addend: 0x0, symName: _e1000e_update_adaptive, symObjAddr: 0x110B, symBinAddr: 0x4F9F, symSize: 0x93 }
  - { offsetInCU: 0x39DA, offset: 0x501E8, size: 0x8, addend: 0x0, symName: _e1000e_update_adaptive, symObjAddr: 0x110B, symBinAddr: 0x4F9F, symSize: 0x93 }
  - { offsetInCU: 0x27, offset: 0x50262, size: 0x8, addend: 0x0, symName: _e1000e_check_reset_block_generic, symObjAddr: 0x0, symBinAddr: 0x5034, symSize: 0x19 }
  - { offsetInCU: 0x2F, offset: 0x5026A, size: 0x8, addend: 0x0, symName: _e1000_m88_cable_length_table, symObjAddr: 0x2920, symBinAddr: 0x11E50, symSize: 0x0 }
  - { offsetInCU: 0x39, offset: 0x50274, size: 0x8, addend: 0x0, symName: _e1000e_get_cable_length_igp_2, symObjAddr: 0x1500, symBinAddr: 0x6534, symSize: 0x113 }
  - { offsetInCU: 0x41, offset: 0x5027C, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_info_m88, symObjAddr: 0x1613, symBinAddr: 0x6647, symSize: 0x134 }
  - { offsetInCU: 0x6C, offset: 0x502A7, size: 0x8, addend: 0x0, symName: _e1000e_get_cable_length_igp_2.agc_reg_array, symObjAddr: 0x292E, symBinAddr: 0x11E5E, symSize: 0x0 }
  - { offsetInCU: 0x189, offset: 0x503C4, size: 0x8, addend: 0x0, symName: _e1000_m88_cable_length_table, symObjAddr: 0x2920, symBinAddr: 0x11E50, symSize: 0x0 }
  - { offsetInCU: 0x1AB, offset: 0x503E6, size: 0x8, addend: 0x0, symName: _e1000_igp_2_cable_length_table, symObjAddr: 0x2940, symBinAddr: 0x11E70, symSize: 0x0 }
  - { offsetInCU: 0x453, offset: 0x5068E, size: 0x8, addend: 0x0, symName: _e1000e_check_reset_block_generic, symObjAddr: 0x0, symBinAddr: 0x5034, symSize: 0x19 }
  - { offsetInCU: 0x45B, offset: 0x50696, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_id, symObjAddr: 0x19, symBinAddr: 0x504D, symSize: 0xAB }
  - { offsetInCU: 0x1D2A, offset: 0x51F65, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_id, symObjAddr: 0x19, symBinAddr: 0x504D, symSize: 0xAB }
  - { offsetInCU: 0x1D32, offset: 0x51F6D, size: 0x8, addend: 0x0, symName: _e1000e_phy_reset_dsp, symObjAddr: 0xC4, symBinAddr: 0x50F8, symSize: 0x3D }
  - { offsetInCU: 0x1E4A, offset: 0x52085, size: 0x8, addend: 0x0, symName: _e1000e_phy_reset_dsp, symObjAddr: 0xC4, symBinAddr: 0x50F8, symSize: 0x3D }
  - { offsetInCU: 0x1E52, offset: 0x5208D, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_mdic, symObjAddr: 0x101, symBinAddr: 0x5135, symSize: 0xB4 }
  - { offsetInCU: 0x1EB3, offset: 0x520EE, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_mdic, symObjAddr: 0x101, symBinAddr: 0x5135, symSize: 0xB4 }
  - { offsetInCU: 0x1EDA, offset: 0x52115, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_mdic, symObjAddr: 0x101, symBinAddr: 0x5135, symSize: 0xB4 }
  - { offsetInCU: 0x1EE2, offset: 0x5211D, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_mdic, symObjAddr: 0x1B5, symBinAddr: 0x51E9, symSize: 0xB1 }
  - { offsetInCU: 0x1F97, offset: 0x521D2, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_mdic, symObjAddr: 0x1B5, symBinAddr: 0x51E9, symSize: 0xB1 }
  - { offsetInCU: 0x1F9F, offset: 0x521DA, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_m88, symObjAddr: 0x266, symBinAddr: 0x529A, symSize: 0x49 }
  - { offsetInCU: 0x2058, offset: 0x52293, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_m88, symObjAddr: 0x266, symBinAddr: 0x529A, symSize: 0x49 }
  - { offsetInCU: 0x2060, offset: 0x5229B, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_m88, symObjAddr: 0x2AF, symBinAddr: 0x52E3, symSize: 0x4A }
  - { offsetInCU: 0x20C6, offset: 0x52301, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_m88, symObjAddr: 0x2AF, symBinAddr: 0x52E3, symSize: 0x4A }
  - { offsetInCU: 0x20CE, offset: 0x52309, size: 0x8, addend: 0x0, symName: _e1000_set_page_igp, symObjAddr: 0x2F9, symBinAddr: 0x532D, symSize: 0x1B }
  - { offsetInCU: 0x213D, offset: 0x52378, size: 0x8, addend: 0x0, symName: _e1000_set_page_igp, symObjAddr: 0x2F9, symBinAddr: 0x532D, symSize: 0x1B }
  - { offsetInCU: 0x2145, offset: 0x52380, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_igp, symObjAddr: 0x314, symBinAddr: 0x5348, symSize: 0xC }
  - { offsetInCU: 0x2168, offset: 0x523A3, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_igp, symObjAddr: 0x314, symBinAddr: 0x5348, symSize: 0xC }
  - { offsetInCU: 0x2170, offset: 0x523AB, size: 0x8, addend: 0x0, symName: ___e1000e_read_phy_reg_igp, symObjAddr: 0x320, symBinAddr: 0x5354, symSize: 0x90 }
  - { offsetInCU: 0x21C0, offset: 0x523FB, size: 0x8, addend: 0x0, symName: ___e1000e_read_phy_reg_igp, symObjAddr: 0x320, symBinAddr: 0x5354, symSize: 0x90 }
  - { offsetInCU: 0x21C8, offset: 0x52403, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_igp_locked, symObjAddr: 0x3B0, symBinAddr: 0x53E4, symSize: 0xF }
  - { offsetInCU: 0x223F, offset: 0x5247A, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_igp_locked, symObjAddr: 0x3B0, symBinAddr: 0x53E4, symSize: 0xF }
  - { offsetInCU: 0x2247, offset: 0x52482, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_igp, symObjAddr: 0x3BF, symBinAddr: 0x53F3, symSize: 0xC }
  - { offsetInCU: 0x2297, offset: 0x524D2, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_igp, symObjAddr: 0x3BF, symBinAddr: 0x53F3, symSize: 0xC }
  - { offsetInCU: 0x229F, offset: 0x524DA, size: 0x8, addend: 0x0, symName: ___e1000e_write_phy_reg_igp, symObjAddr: 0x3CB, symBinAddr: 0x53FF, symSize: 0x91 }
  - { offsetInCU: 0x22F3, offset: 0x5252E, size: 0x8, addend: 0x0, symName: ___e1000e_write_phy_reg_igp, symObjAddr: 0x3CB, symBinAddr: 0x53FF, symSize: 0x91 }
  - { offsetInCU: 0x22FB, offset: 0x52536, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_igp_locked, symObjAddr: 0x45C, symBinAddr: 0x5490, symSize: 0xF }
  - { offsetInCU: 0x2376, offset: 0x525B1, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_igp_locked, symObjAddr: 0x45C, symBinAddr: 0x5490, symSize: 0xF }
  - { offsetInCU: 0x237E, offset: 0x525B9, size: 0x8, addend: 0x0, symName: _e1000e_read_kmrn_reg, symObjAddr: 0x46B, symBinAddr: 0x549F, symSize: 0xC }
  - { offsetInCU: 0x23D2, offset: 0x5260D, size: 0x8, addend: 0x0, symName: _e1000e_read_kmrn_reg, symObjAddr: 0x46B, symBinAddr: 0x549F, symSize: 0xC }
  - { offsetInCU: 0x23DA, offset: 0x52615, size: 0x8, addend: 0x0, symName: ___e1000_read_kmrn_reg, symObjAddr: 0x477, symBinAddr: 0x54AB, symSize: 0x9B }
  - { offsetInCU: 0x242A, offset: 0x52665, size: 0x8, addend: 0x0, symName: ___e1000_read_kmrn_reg, symObjAddr: 0x477, symBinAddr: 0x54AB, symSize: 0x9B }
  - { offsetInCU: 0x2432, offset: 0x5266D, size: 0x8, addend: 0x0, symName: _e1000e_read_kmrn_reg_locked, symObjAddr: 0x512, symBinAddr: 0x5546, symSize: 0xF }
  - { offsetInCU: 0x2525, offset: 0x52760, size: 0x8, addend: 0x0, symName: _e1000e_read_kmrn_reg_locked, symObjAddr: 0x512, symBinAddr: 0x5546, symSize: 0xF }
  - { offsetInCU: 0x252D, offset: 0x52768, size: 0x8, addend: 0x0, symName: _e1000e_write_kmrn_reg, symObjAddr: 0x521, symBinAddr: 0x5555, symSize: 0xC }
  - { offsetInCU: 0x257D, offset: 0x527B8, size: 0x8, addend: 0x0, symName: _e1000e_write_kmrn_reg, symObjAddr: 0x521, symBinAddr: 0x5555, symSize: 0xC }
  - { offsetInCU: 0x2585, offset: 0x527C0, size: 0x8, addend: 0x0, symName: ___e1000_write_kmrn_reg, symObjAddr: 0x52D, symBinAddr: 0x5561, symSize: 0xA0 }
  - { offsetInCU: 0x25D9, offset: 0x52814, size: 0x8, addend: 0x0, symName: ___e1000_write_kmrn_reg, symObjAddr: 0x52D, symBinAddr: 0x5561, symSize: 0xA0 }
  - { offsetInCU: 0x25E1, offset: 0x5281C, size: 0x8, addend: 0x0, symName: _e1000e_write_kmrn_reg_locked, symObjAddr: 0x5CD, symBinAddr: 0x5601, symSize: 0x35 }
  - { offsetInCU: 0x2707, offset: 0x52942, size: 0x8, addend: 0x0, symName: _e1000e_write_kmrn_reg_locked, symObjAddr: 0x5CD, symBinAddr: 0x5601, symSize: 0x35 }
  - { offsetInCU: 0x270F, offset: 0x5294A, size: 0x8, addend: 0x0, symName: _e1000_copper_link_setup_82577, symObjAddr: 0x602, symBinAddr: 0x5636, symSize: 0xAD }
  - { offsetInCU: 0x27DB, offset: 0x52A16, size: 0x8, addend: 0x0, symName: _e1000_copper_link_setup_82577, symObjAddr: 0x602, symBinAddr: 0x5636, symSize: 0xAD }
  - { offsetInCU: 0x27E3, offset: 0x52A1E, size: 0x8, addend: 0x0, symName: _e1000_set_master_slave_mode, symObjAddr: 0x6AF, symBinAddr: 0x56E3, symSize: 0x8C }
  - { offsetInCU: 0x28F7, offset: 0x52B32, size: 0x8, addend: 0x0, symName: _e1000_set_master_slave_mode, symObjAddr: 0x6AF, symBinAddr: 0x56E3, symSize: 0x8C }
  - { offsetInCU: 0x28FF, offset: 0x52B3A, size: 0x8, addend: 0x0, symName: _e1000e_copper_link_setup_m88, symObjAddr: 0x73B, symBinAddr: 0x576F, symSize: 0x237 }
  - { offsetInCU: 0x29BA, offset: 0x52BF5, size: 0x8, addend: 0x0, symName: _e1000e_copper_link_setup_m88, symObjAddr: 0x73B, symBinAddr: 0x576F, symSize: 0x237 }
  - { offsetInCU: 0x29C2, offset: 0x52BFD, size: 0x8, addend: 0x0, symName: _e1000e_copper_link_setup_igp, symObjAddr: 0x972, symBinAddr: 0x59A6, symSize: 0x148 }
  - { offsetInCU: 0x2C0B, offset: 0x52E46, size: 0x8, addend: 0x0, symName: _e1000e_copper_link_setup_igp, symObjAddr: 0x972, symBinAddr: 0x59A6, symSize: 0x148 }
  - { offsetInCU: 0x2C13, offset: 0x52E4E, size: 0x8, addend: 0x0, symName: _e1000e_setup_copper_link, symObjAddr: 0xABA, symBinAddr: 0x5AEE, symSize: 0x23E }
  - { offsetInCU: 0x2EBC, offset: 0x530F7, size: 0x8, addend: 0x0, symName: _e1000e_setup_copper_link, symObjAddr: 0xABA, symBinAddr: 0x5AEE, symSize: 0x23E }
  - { offsetInCU: 0x313F, offset: 0x5337A, size: 0x8, addend: 0x0, symName: _e1000e_phy_has_link_generic, symObjAddr: 0xCF8, symBinAddr: 0x5D2C, symSize: 0xD8 }
  - { offsetInCU: 0x3147, offset: 0x53382, size: 0x8, addend: 0x0, symName: _e1000e_phy_force_speed_duplex_igp, symObjAddr: 0xDD0, symBinAddr: 0x5E04, symSize: 0xCD }
  - { offsetInCU: 0x3251, offset: 0x5348C, size: 0x8, addend: 0x0, symName: _e1000e_phy_force_speed_duplex_igp, symObjAddr: 0xDD0, symBinAddr: 0x5E04, symSize: 0xCD }
  - { offsetInCU: 0x3259, offset: 0x53494, size: 0x8, addend: 0x0, symName: _e1000e_phy_force_speed_duplex_setup, symObjAddr: 0xE9D, symBinAddr: 0x5ED1, symSize: 0x81 }
  - { offsetInCU: 0x3383, offset: 0x535BE, size: 0x8, addend: 0x0, symName: _e1000e_phy_force_speed_duplex_setup, symObjAddr: 0xE9D, symBinAddr: 0x5ED1, symSize: 0x81 }
  - { offsetInCU: 0x338B, offset: 0x535C6, size: 0x8, addend: 0x0, symName: _e1000e_phy_force_speed_duplex_m88, symObjAddr: 0xF1E, symBinAddr: 0x5F52, symSize: 0x1BD }
  - { offsetInCU: 0x341B, offset: 0x53656, size: 0x8, addend: 0x0, symName: _e1000e_phy_force_speed_duplex_m88, symObjAddr: 0xF1E, symBinAddr: 0x5F52, symSize: 0x1BD }
  - { offsetInCU: 0x3423, offset: 0x5365E, size: 0x8, addend: 0x0, symName: _e1000_phy_force_speed_duplex_ife, symObjAddr: 0x10DB, symBinAddr: 0x610F, symSize: 0xD1 }
  - { offsetInCU: 0x36BC, offset: 0x538F7, size: 0x8, addend: 0x0, symName: _e1000_phy_force_speed_duplex_ife, symObjAddr: 0x10DB, symBinAddr: 0x610F, symSize: 0xD1 }
  - { offsetInCU: 0x36C4, offset: 0x538FF, size: 0x8, addend: 0x0, symName: _e1000e_set_d3_lplu_state, symObjAddr: 0x11AC, symBinAddr: 0x61E0, symSize: 0x156 }
  - { offsetInCU: 0x37EE, offset: 0x53A29, size: 0x8, addend: 0x0, symName: _e1000e_set_d3_lplu_state, symObjAddr: 0x11AC, symBinAddr: 0x61E0, symSize: 0x156 }
  - { offsetInCU: 0x37F6, offset: 0x53A31, size: 0x8, addend: 0x0, symName: _e1000e_check_downshift, symObjAddr: 0x1302, symBinAddr: 0x6336, symSize: 0x76 }
  - { offsetInCU: 0x3A07, offset: 0x53C42, size: 0x8, addend: 0x0, symName: _e1000e_check_downshift, symObjAddr: 0x1302, symBinAddr: 0x6336, symSize: 0x76 }
  - { offsetInCU: 0x3A0F, offset: 0x53C4A, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_m88, symObjAddr: 0x1378, symBinAddr: 0x63AC, symSize: 0x37 }
  - { offsetInCU: 0x3AD1, offset: 0x53D0C, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_m88, symObjAddr: 0x1378, symBinAddr: 0x63AC, symSize: 0x37 }
  - { offsetInCU: 0x3AD9, offset: 0x53D14, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_igp, symObjAddr: 0x13AF, symBinAddr: 0x63E3, symSize: 0x87 }
  - { offsetInCU: 0x3B3E, offset: 0x53D79, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_igp, symObjAddr: 0x13AF, symBinAddr: 0x63E3, symSize: 0x87 }
  - { offsetInCU: 0x3B46, offset: 0x53D81, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_ife, symObjAddr: 0x1436, symBinAddr: 0x646A, symSize: 0x5F }
  - { offsetInCU: 0x3C38, offset: 0x53E73, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_ife, symObjAddr: 0x1436, symBinAddr: 0x646A, symSize: 0x5F }
  - { offsetInCU: 0x3C40, offset: 0x53E7B, size: 0x8, addend: 0x0, symName: _e1000e_get_cable_length_m88, symObjAddr: 0x1495, symBinAddr: 0x64C9, symSize: 0x6B }
  - { offsetInCU: 0x3D02, offset: 0x53F3D, size: 0x8, addend: 0x0, symName: _e1000e_get_cable_length_m88, symObjAddr: 0x1495, symBinAddr: 0x64C9, symSize: 0x6B }
  - { offsetInCU: 0x3D0A, offset: 0x53F45, size: 0x8, addend: 0x0, symName: _e1000e_get_cable_length_igp_2, symObjAddr: 0x1500, symBinAddr: 0x6534, symSize: 0x113 }
  - { offsetInCU: 0x3E08, offset: 0x54043, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_info_m88, symObjAddr: 0x1613, symBinAddr: 0x6647, symSize: 0x134 }
  - { offsetInCU: 0x3E10, offset: 0x5404B, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_info_igp, symObjAddr: 0x1747, symBinAddr: 0x677B, symSize: 0xE1 }
  - { offsetInCU: 0x3F85, offset: 0x541C0, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_info_igp, symObjAddr: 0x1747, symBinAddr: 0x677B, symSize: 0xE1 }
  - { offsetInCU: 0x3F8D, offset: 0x541C8, size: 0x8, addend: 0x0, symName: _e1000_get_phy_info_ife, symObjAddr: 0x1828, symBinAddr: 0x685C, symSize: 0xCA }
  - { offsetInCU: 0x4065, offset: 0x542A0, size: 0x8, addend: 0x0, symName: _e1000_get_phy_info_ife, symObjAddr: 0x1828, symBinAddr: 0x685C, symSize: 0xCA }
  - { offsetInCU: 0x406D, offset: 0x542A8, size: 0x8, addend: 0x0, symName: _e1000e_phy_sw_reset, symObjAddr: 0x18F2, symBinAddr: 0x6926, symSize: 0x4E }
  - { offsetInCU: 0x4145, offset: 0x54380, size: 0x8, addend: 0x0, symName: _e1000e_phy_sw_reset, symObjAddr: 0x18F2, symBinAddr: 0x6926, symSize: 0x4E }
  - { offsetInCU: 0x414D, offset: 0x54388, size: 0x8, addend: 0x0, symName: _e1000e_phy_hw_reset_generic, symObjAddr: 0x1940, symBinAddr: 0x6974, symSize: 0x94 }
  - { offsetInCU: 0x41FF, offset: 0x5443A, size: 0x8, addend: 0x0, symName: _e1000e_phy_hw_reset_generic, symObjAddr: 0x1940, symBinAddr: 0x6974, symSize: 0x94 }
  - { offsetInCU: 0x4207, offset: 0x54442, size: 0x8, addend: 0x0, symName: _e1000e_get_cfg_done_generic, symObjAddr: 0x19D4, symBinAddr: 0x6A08, symSize: 0x12 }
  - { offsetInCU: 0x42ED, offset: 0x54528, size: 0x8, addend: 0x0, symName: _e1000e_get_cfg_done_generic, symObjAddr: 0x19D4, symBinAddr: 0x6A08, symSize: 0x12 }
  - { offsetInCU: 0x42F5, offset: 0x54530, size: 0x8, addend: 0x0, symName: _e1000e_phy_init_script_igp3, symObjAddr: 0x19E6, symBinAddr: 0x6A1A, symSize: 0x263 }
  - { offsetInCU: 0x4325, offset: 0x54560, size: 0x8, addend: 0x0, symName: _e1000e_phy_init_script_igp3, symObjAddr: 0x19E6, symBinAddr: 0x6A1A, symSize: 0x263 }
  - { offsetInCU: 0x432D, offset: 0x54568, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_type_from_id, symObjAddr: 0x1C49, symBinAddr: 0x6C7D, symSize: 0xF2 }
  - { offsetInCU: 0x49EE, offset: 0x54C29, size: 0x8, addend: 0x0, symName: _e1000e_get_phy_type_from_id, symObjAddr: 0x1C49, symBinAddr: 0x6C7D, symSize: 0xF2 }
  - { offsetInCU: 0x49F6, offset: 0x54C31, size: 0x8, addend: 0x0, symName: _e1000e_determine_phy_address, symObjAddr: 0x1D3B, symBinAddr: 0x6D6F, symSize: 0x6A }
  - { offsetInCU: 0x4A35, offset: 0x54C70, size: 0x8, addend: 0x0, symName: _e1000e_determine_phy_address, symObjAddr: 0x1D3B, symBinAddr: 0x6D6F, symSize: 0x6A }
  - { offsetInCU: 0x4A3D, offset: 0x54C78, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_bm, symObjAddr: 0x1DA5, symBinAddr: 0x6DD9, symSize: 0xD8 }
  - { offsetInCU: 0x4AF0, offset: 0x54D2B, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_bm, symObjAddr: 0x1DA5, symBinAddr: 0x6DD9, symSize: 0xD8 }
  - { offsetInCU: 0x4AF8, offset: 0x54D33, size: 0x8, addend: 0x0, symName: _e1000_access_phy_wakeup_reg_bm, symObjAddr: 0x1E7D, symBinAddr: 0x6EB1, symSize: 0xAA }
  - { offsetInCU: 0x4BF8, offset: 0x54E33, size: 0x8, addend: 0x0, symName: _e1000_access_phy_wakeup_reg_bm, symObjAddr: 0x1E7D, symBinAddr: 0x6EB1, symSize: 0xAA }
  - { offsetInCU: 0x4C00, offset: 0x54E3B, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_bm, symObjAddr: 0x1F27, symBinAddr: 0x6F5B, symSize: 0xCC }
  - { offsetInCU: 0x4CAE, offset: 0x54EE9, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_bm, symObjAddr: 0x1F27, symBinAddr: 0x6F5B, symSize: 0xCC }
  - { offsetInCU: 0x4CB6, offset: 0x54EF1, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_bm2, symObjAddr: 0x1FF3, symBinAddr: 0x7027, symSize: 0x9F }
  - { offsetInCU: 0x4DB2, offset: 0x54FED, size: 0x8, addend: 0x0, symName: _e1000e_read_phy_reg_bm2, symObjAddr: 0x1FF3, symBinAddr: 0x7027, symSize: 0x9F }
  - { offsetInCU: 0x4DBA, offset: 0x54FF5, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_bm2, symObjAddr: 0x2092, symBinAddr: 0x70C6, symSize: 0xAB }
  - { offsetInCU: 0x4E4B, offset: 0x55086, size: 0x8, addend: 0x0, symName: _e1000e_write_phy_reg_bm2, symObjAddr: 0x2092, symBinAddr: 0x70C6, symSize: 0xAB }
  - { offsetInCU: 0x4E53, offset: 0x5508E, size: 0x8, addend: 0x0, symName: _e1000_enable_phy_wakeup_reg_access_bm, symObjAddr: 0x213D, symBinAddr: 0x7171, symSize: 0x81 }
  - { offsetInCU: 0x4F1C, offset: 0x55157, size: 0x8, addend: 0x0, symName: _e1000_enable_phy_wakeup_reg_access_bm, symObjAddr: 0x213D, symBinAddr: 0x7171, symSize: 0x81 }
  - { offsetInCU: 0x4F24, offset: 0x5515F, size: 0x8, addend: 0x0, symName: _e1000_disable_phy_wakeup_reg_access_bm, symObjAddr: 0x21BE, symBinAddr: 0x71F2, symSize: 0x44 }
  - { offsetInCU: 0x4FC6, offset: 0x55201, size: 0x8, addend: 0x0, symName: _e1000_disable_phy_wakeup_reg_access_bm, symObjAddr: 0x21BE, symBinAddr: 0x71F2, symSize: 0x44 }
  - { offsetInCU: 0x4FE9, offset: 0x55224, size: 0x8, addend: 0x0, symName: _e1000_disable_phy_wakeup_reg_access_bm, symObjAddr: 0x21BE, symBinAddr: 0x71F2, symSize: 0x44 }
  - { offsetInCU: 0x4FF1, offset: 0x5522C, size: 0x8, addend: 0x0, symName: _e1000_power_up_phy_copper, symObjAddr: 0x2202, symBinAddr: 0x7236, symSize: 0x45 }
  - { offsetInCU: 0x5073, offset: 0x552AE, size: 0x8, addend: 0x0, symName: _e1000_power_up_phy_copper, symObjAddr: 0x2202, symBinAddr: 0x7236, symSize: 0x45 }
  - { offsetInCU: 0x507B, offset: 0x552B6, size: 0x8, addend: 0x0, symName: _e1000_power_down_phy_copper, symObjAddr: 0x2247, symBinAddr: 0x727B, symSize: 0x4F }
  - { offsetInCU: 0x5110, offset: 0x5534B, size: 0x8, addend: 0x0, symName: _e1000_power_down_phy_copper, symObjAddr: 0x2247, symBinAddr: 0x727B, symSize: 0x4F }
  - { offsetInCU: 0x5118, offset: 0x55353, size: 0x8, addend: 0x0, symName: _e1000_read_phy_reg_hv, symObjAddr: 0x2296, symBinAddr: 0x72CA, symSize: 0xF }
  - { offsetInCU: 0x51AD, offset: 0x553E8, size: 0x8, addend: 0x0, symName: _e1000_read_phy_reg_hv, symObjAddr: 0x2296, symBinAddr: 0x72CA, symSize: 0xF }
  - { offsetInCU: 0x51B5, offset: 0x553F0, size: 0x8, addend: 0x0, symName: ___e1000_read_phy_reg_hv, symObjAddr: 0x22A5, symBinAddr: 0x72D9, symSize: 0x11F }
  - { offsetInCU: 0x523C, offset: 0x55477, size: 0x8, addend: 0x0, symName: ___e1000_read_phy_reg_hv, symObjAddr: 0x22A5, symBinAddr: 0x72D9, symSize: 0x11F }
  - { offsetInCU: 0x5244, offset: 0x5547F, size: 0x8, addend: 0x0, symName: _e1000_read_phy_reg_hv_locked, symObjAddr: 0x23C4, symBinAddr: 0x73F8, symSize: 0x12 }
  - { offsetInCU: 0x536E, offset: 0x555A9, size: 0x8, addend: 0x0, symName: _e1000_read_phy_reg_hv_locked, symObjAddr: 0x23C4, symBinAddr: 0x73F8, symSize: 0x12 }
  - { offsetInCU: 0x5376, offset: 0x555B1, size: 0x8, addend: 0x0, symName: _e1000_read_phy_reg_page_hv, symObjAddr: 0x23D6, symBinAddr: 0x740A, symSize: 0x15 }
  - { offsetInCU: 0x53C6, offset: 0x55601, size: 0x8, addend: 0x0, symName: _e1000_read_phy_reg_page_hv, symObjAddr: 0x23D6, symBinAddr: 0x740A, symSize: 0x15 }
  - { offsetInCU: 0x53CE, offset: 0x55609, size: 0x8, addend: 0x0, symName: _e1000_write_phy_reg_hv, symObjAddr: 0x23EB, symBinAddr: 0x741F, symSize: 0xF }
  - { offsetInCU: 0x541E, offset: 0x55659, size: 0x8, addend: 0x0, symName: _e1000_write_phy_reg_hv, symObjAddr: 0x23EB, symBinAddr: 0x741F, symSize: 0xF }
  - { offsetInCU: 0x5426, offset: 0x55661, size: 0x8, addend: 0x0, symName: ___e1000_write_phy_reg_hv, symObjAddr: 0x23FA, symBinAddr: 0x742E, symSize: 0x19B }
  - { offsetInCU: 0x547A, offset: 0x556B5, size: 0x8, addend: 0x0, symName: ___e1000_write_phy_reg_hv, symObjAddr: 0x23FA, symBinAddr: 0x742E, symSize: 0x19B }
  - { offsetInCU: 0x5482, offset: 0x556BD, size: 0x8, addend: 0x0, symName: _e1000_write_phy_reg_hv_locked, symObjAddr: 0x2595, symBinAddr: 0x75C9, symSize: 0x12 }
  - { offsetInCU: 0x55D8, offset: 0x55813, size: 0x8, addend: 0x0, symName: _e1000_write_phy_reg_hv_locked, symObjAddr: 0x2595, symBinAddr: 0x75C9, symSize: 0x12 }
  - { offsetInCU: 0x55E0, offset: 0x5581B, size: 0x8, addend: 0x0, symName: _e1000_write_phy_reg_page_hv, symObjAddr: 0x25A7, symBinAddr: 0x75DB, symSize: 0x15 }
  - { offsetInCU: 0x5634, offset: 0x5586F, size: 0x8, addend: 0x0, symName: _e1000_write_phy_reg_page_hv, symObjAddr: 0x25A7, symBinAddr: 0x75DB, symSize: 0x15 }
  - { offsetInCU: 0x563C, offset: 0x55877, size: 0x8, addend: 0x0, symName: _e1000_link_stall_workaround_hv, symObjAddr: 0x25BC, symBinAddr: 0x75F0, symSize: 0xB7 }
  - { offsetInCU: 0x5690, offset: 0x558CB, size: 0x8, addend: 0x0, symName: _e1000_link_stall_workaround_hv, symObjAddr: 0x25BC, symBinAddr: 0x75F0, symSize: 0xB7 }
  - { offsetInCU: 0x5698, offset: 0x558D3, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_82577, symObjAddr: 0x2673, symBinAddr: 0x76A7, symSize: 0x38 }
  - { offsetInCU: 0x57BA, offset: 0x559F5, size: 0x8, addend: 0x0, symName: _e1000_check_polarity_82577, symObjAddr: 0x2673, symBinAddr: 0x76A7, symSize: 0x38 }
  - { offsetInCU: 0x57C2, offset: 0x559FD, size: 0x8, addend: 0x0, symName: _e1000_phy_force_speed_duplex_82577, symObjAddr: 0x26AB, symBinAddr: 0x76DF, symSize: 0x98 }
  - { offsetInCU: 0x5827, offset: 0x55A62, size: 0x8, addend: 0x0, symName: _e1000_phy_force_speed_duplex_82577, symObjAddr: 0x26AB, symBinAddr: 0x76DF, symSize: 0x98 }
  - { offsetInCU: 0x582F, offset: 0x55A6A, size: 0x8, addend: 0x0, symName: _e1000_get_phy_info_82577, symObjAddr: 0x2743, symBinAddr: 0x7777, symSize: 0x113 }
  - { offsetInCU: 0x5953, offset: 0x55B8E, size: 0x8, addend: 0x0, symName: _e1000_get_phy_info_82577, symObjAddr: 0x2743, symBinAddr: 0x7777, symSize: 0x113 }
  - { offsetInCU: 0x595B, offset: 0x55B96, size: 0x8, addend: 0x0, symName: _e1000_get_cable_length_82577, symObjAddr: 0x2856, symBinAddr: 0x788A, symSize: 0x48 }
  - { offsetInCU: 0x5AA0, offset: 0x55CDB, size: 0x8, addend: 0x0, symName: _e1000_get_cable_length_82577, symObjAddr: 0x2856, symBinAddr: 0x788A, symSize: 0x48 }
  - { offsetInCU: 0x5AA8, offset: 0x55CE3, size: 0x8, addend: 0x0, symName: _e1000_access_phy_debug_regs_hv, symObjAddr: 0x289E, symBinAddr: 0x78D2, symSize: 0x82 }
  - { offsetInCU: 0x5B54, offset: 0x55D8F, size: 0x8, addend: 0x0, symName: _e1000_access_phy_debug_regs_hv, symObjAddr: 0x289E, symBinAddr: 0x78D2, symSize: 0x82 }
  - { offsetInCU: 0x5B5C, offset: 0x55D97, size: 0x8, addend: 0x0, symName: _e1000_m88_cable_length_table, symObjAddr: 0x2920, symBinAddr: 0x11E50, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x55E69, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18initPCIConfigSpaceEP11IOPCIDevice, symObjAddr: 0x0, symBinAddr: 0x7954, symSize: 0x236 }
  - { offsetInCU: 0x142, offset: 0x55F84, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18initPCIConfigSpaceEP11IOPCIDevice, symObjAddr: 0x0, symBinAddr: 0x7954, symSize: 0x236 }
  - { offsetInCU: 0x14A, offset: 0x55F8C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21initPCIPowerManagmentEP11IOPCIDevicePK10e1000_info, symObjAddr: 0x236, symBinAddr: 0x7B8A, symSize: 0x100 }
  - { offsetInCU: 0x22C, offset: 0x5606E, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21initPCIPowerManagmentEP11IOPCIDevicePK10e1000_info, symObjAddr: 0x236, symBinAddr: 0x7B8A, symSize: 0x100 }
  - { offsetInCU: 0x234, offset: 0x56076, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi23setPowerStateWakeActionEP8OSObjectPvS2_S2_S2_, symObjAddr: 0x336, symBinAddr: 0x7C8A, symSize: 0x86 }
  - { offsetInCU: 0x2DA, offset: 0x5611C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi23setPowerStateWakeActionEP8OSObjectPvS2_S2_S2_, symObjAddr: 0x336, symBinAddr: 0x7C8A, symSize: 0x86 }
  - { offsetInCU: 0x3E5, offset: 0x56227, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi24setPowerStateSleepActionEP8OSObjectPvS2_S2_S2_, symObjAddr: 0x3BC, symBinAddr: 0x7D10, symSize: 0x7A }
  - { offsetInCU: 0x3ED, offset: 0x5622F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17intelEEPROMChecksEP13e1000_adapter, symObjAddr: 0x436, symBinAddr: 0x7D8A, symSize: 0x54 }
  - { offsetInCU: 0x514, offset: 0x56356, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17intelEEPROMChecksEP13e1000_adapter, symObjAddr: 0x436, symBinAddr: 0x7D8A, symSize: 0x54 }
  - { offsetInCU: 0x62D, offset: 0x5646F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14intelEnableIRQEP13e1000_adapter, symObjAddr: 0x48A, symBinAddr: 0x7DDE, symSize: 0x2A }
  - { offsetInCU: 0x635, offset: 0x56477, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi15intelDisableIRQEv, symObjAddr: 0x4B4, symBinAddr: 0x7E08, symSize: 0x1A }
  - { offsetInCU: 0x6DA, offset: 0x5651C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi15intelDisableIRQEv, symObjAddr: 0x4B4, symBinAddr: 0x7E08, symSize: 0x1A }
  - { offsetInCU: 0x6E2, offset: 0x56524, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11intelEnableEv, symObjAddr: 0x4CE, symBinAddr: 0x7E22, symSize: 0x228 }
  - { offsetInCU: 0x8A1, offset: 0x566E3, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi11intelEnableEv, symObjAddr: 0x4CE, symBinAddr: 0x7E22, symSize: 0x228 }
  - { offsetInCU: 0xADC, offset: 0x5691E, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi22intelSetupAdvForMediumEPK15IONetworkMedium, symObjAddr: 0x6F6, symBinAddr: 0x804A, symSize: 0x18E }
  - { offsetInCU: 0xBC8, offset: 0x56A0A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi10intelResetEP13e1000_adapter, symObjAddr: 0x884, symBinAddr: 0x81D8, symSize: 0x326 }
  - { offsetInCU: 0x10BD, offset: 0x56EFF, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi24intelInitManageabilityPtEP13e1000_adapter, symObjAddr: 0xBAA, symBinAddr: 0x84FE, symSize: 0xF6 }
  - { offsetInCU: 0x1305, offset: 0x57147, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14intelConfigureEP13e1000_adapter, symObjAddr: 0xCA0, symBinAddr: 0x85F4, symSize: 0x60 }
  - { offsetInCU: 0x130D, offset: 0x5714F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12intelDisableEv, symObjAddr: 0xD00, symBinAddr: 0x8654, symSize: 0x26A }
  - { offsetInCU: 0x14D9, offset: 0x5731B, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12intelDisableEv, symObjAddr: 0xD00, symBinAddr: 0x8654, symSize: 0x26A }
  - { offsetInCU: 0x1899, offset: 0x576DB, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14intelFlushLPICEv, symObjAddr: 0xF6A, symBinAddr: 0x88BE, symSize: 0x36 }
  - { offsetInCU: 0x18A1, offset: 0x576E3, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9intelDownEP13e1000_adapterb, symObjAddr: 0xFA0, symBinAddr: 0x88F4, symSize: 0x140 }
  - { offsetInCU: 0x1957, offset: 0x57799, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9intelDownEP13e1000_adapterb, symObjAddr: 0xFA0, symBinAddr: 0x88F4, symSize: 0x140 }
  - { offsetInCU: 0x1D3B, offset: 0x57B7D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi19intelSetupRxControlEP13e1000_adapter, symObjAddr: 0x10E0, symBinAddr: 0x8A34, symSize: 0x1C0 }
  - { offsetInCU: 0x2053, offset: 0x57E95, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18intelInitPhyWakeupEjP13IntelAddrData, symObjAddr: 0x12A0, symBinAddr: 0x8BF4, symSize: 0x544 }
  - { offsetInCU: 0x205B, offset: 0x57E9D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18intelInitMacWakeupEjP13IntelAddrData, symObjAddr: 0x17E4, symBinAddr: 0x9138, symSize: 0xCC }
  - { offsetInCU: 0x227D, offset: 0x580BF, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18intelInitMacWakeupEjP13IntelAddrData, symObjAddr: 0x17E4, symBinAddr: 0x9138, symSize: 0xCC }
  - { offsetInCU: 0x243F, offset: 0x58281, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17intelPowerDownPhyEP13e1000_adapter, symObjAddr: 0x18B0, symBinAddr: 0x9204, symSize: 0x20 }
  - { offsetInCU: 0x247C, offset: 0x582BE, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16intelConfigureTxEP13e1000_adapter, symObjAddr: 0x18D0, symBinAddr: 0x9224, symSize: 0x138 }
  - { offsetInCU: 0x28E6, offset: 0x58728, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi17intelSetupRssHashEP13e1000_adapter, symObjAddr: 0x1A08, symBinAddr: 0x935C, symSize: 0xAA }
  - { offsetInCU: 0x28EE, offset: 0x58730, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi20intelVlanStripEnableEP13e1000_adapter, symObjAddr: 0x1AB2, symBinAddr: 0x9406, symSize: 0x14 }
  - { offsetInCU: 0x2A82, offset: 0x588C4, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi20intelVlanStripEnableEP13e1000_adapter, symObjAddr: 0x1AB2, symBinAddr: 0x9406, symSize: 0x14 }
  - { offsetInCU: 0x2AFF, offset: 0x58941, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16intelConfigureRxEP13e1000_adapter, symObjAddr: 0x1AC6, symBinAddr: 0x941A, symSize: 0x13C }
  - { offsetInCU: 0x2B07, offset: 0x58949, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21intelUpdateTxDescTailEj, symObjAddr: 0x1C02, symBinAddr: 0x9556, symSize: 0x7E }
  - { offsetInCU: 0x302A, offset: 0x58E6C, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21intelUpdateTxDescTailEj, symObjAddr: 0x1C02, symBinAddr: 0x9556, symSize: 0x7E }
  - { offsetInCU: 0x3032, offset: 0x58E74, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21intelUpdateRxDescTailEj, symObjAddr: 0x1C80, symBinAddr: 0x95D4, symSize: 0x58 }
  - { offsetInCU: 0x31CB, offset: 0x5900D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21intelUpdateRxDescTailEj, symObjAddr: 0x1C80, symBinAddr: 0x95D4, symSize: 0x58 }
  - { offsetInCU: 0x3316, offset: 0x59158, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21intelFlushDescriptorsEv, symObjAddr: 0x1CD8, symBinAddr: 0x962C, symSize: 0x4E }
  - { offsetInCU: 0x331E, offset: 0x59160, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi19intelFlushDescRingsEP13e1000_adapter, symObjAddr: 0x1D26, symBinAddr: 0x967A, symSize: 0x8A }
  - { offsetInCU: 0x3497, offset: 0x592D9, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi19intelFlushDescRingsEP13e1000_adapter, symObjAddr: 0x1D26, symBinAddr: 0x967A, symSize: 0x8A }
  - { offsetInCU: 0x349F, offset: 0x592E1, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18intelResetAdaptiveEP8e1000_hw, symObjAddr: 0x1DB0, symBinAddr: 0x9704, symSize: 0x44 }
  - { offsetInCU: 0x356E, offset: 0x593B0, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18intelResetAdaptiveEP8e1000_hw, symObjAddr: 0x1DB0, symBinAddr: 0x9704, symSize: 0x44 }
  - { offsetInCU: 0x3576, offset: 0x593B8, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi22intelEnableMngPassThruEP8e1000_hw, symObjAddr: 0x1DF4, symBinAddr: 0x9748, symSize: 0x4C }
  - { offsetInCU: 0x35F7, offset: 0x59439, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi22intelEnableMngPassThruEP8e1000_hw, symObjAddr: 0x1DF4, symBinAddr: 0x9748, symSize: 0x4C }
  - { offsetInCU: 0x35FF, offset: 0x59441, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi19intelUpdateAdaptiveEP8e1000_hw, symObjAddr: 0x1E40, symBinAddr: 0x9794, symSize: 0x9A }
  - { offsetInCU: 0x36EE, offset: 0x59530, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi19intelUpdateAdaptiveEP8e1000_hw, symObjAddr: 0x1E40, symBinAddr: 0x9794, symSize: 0x9A }
  - { offsetInCU: 0x3748, offset: 0x5958A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21intelVlanStripDisableEP13e1000_adapter, symObjAddr: 0x1EDA, symBinAddr: 0x982E, symSize: 0x14 }
  - { offsetInCU: 0x37DB, offset: 0x5961D, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi15intelRssKeyFillEPvm, symObjAddr: 0x1EEE, symBinAddr: 0x9842, symSize: 0x66 }
  - { offsetInCU: 0x37E3, offset: 0x59625, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12intelRestartEv, symObjAddr: 0x1F54, symBinAddr: 0x98A8, symSize: 0xEE }
  - { offsetInCU: 0x383C, offset: 0x5967E, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi12intelRestartEv, symObjAddr: 0x1F54, symBinAddr: 0x98A8, symSize: 0xEE }
  - { offsetInCU: 0x39C0, offset: 0x59802, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16intelFlushTxRingEP13e1000_adapter, symObjAddr: 0x2042, symBinAddr: 0x9996, symSize: 0xAA }
  - { offsetInCU: 0x39C8, offset: 0x5980A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16intelFlushRxRingEP13e1000_adapter, symObjAddr: 0x20EC, symBinAddr: 0x9A40, symSize: 0x86 }
  - { offsetInCU: 0x3B56, offset: 0x59998, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16intelFlushRxRingEP13e1000_adapter, symObjAddr: 0x20EC, symBinAddr: 0x9A40, symSize: 0x86 }
  - { offsetInCU: 0x3D77, offset: 0x59BB9, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14intelCheckLinkEP13e1000_adapter, symObjAddr: 0x2172, symBinAddr: 0x9AC6, symSize: 0xBE }
  - { offsetInCU: 0x3E38, offset: 0x59C7A, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18intelPhyReadStatusEP13e1000_adapter, symObjAddr: 0x2230, symBinAddr: 0x9B84, symSize: 0x158 }
  - { offsetInCU: 0x40F4, offset: 0x59F36, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi13setMaxLatencyEj, symObjAddr: 0x2388, symBinAddr: 0x9CDC, symSize: 0x4C }
  - { offsetInCU: 0x40FC, offset: 0x59F3E, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16intelSupportsEEEEP13e1000_adapter, symObjAddr: 0x23D4, symBinAddr: 0x9D28, symSize: 0xC8 }
  - { offsetInCU: 0x41B0, offset: 0x59FF2, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16intelSupportsEEEEP13e1000_adapter, symObjAddr: 0x23D4, symBinAddr: 0x9D28, symSize: 0xC8 }
  - { offsetInCU: 0x41B8, offset: 0x59FFA, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14intelEnableEEEEP8e1000_hwt, symObjAddr: 0x249C, symBinAddr: 0x9DF0, symSize: 0xFE }
  - { offsetInCU: 0x42B1, offset: 0x5A0F3, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14intelEnableEEEEP8e1000_hwt, symObjAddr: 0x249C, symBinAddr: 0x9DF0, symSize: 0xFE }
  - { offsetInCU: 0x27, offset: 0x5A29B, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9getParamsEv, symObjAddr: 0x0, symBinAddr: 0x9EEE, symSize: 0x3E4 }
  - { offsetInCU: 0x53, offset: 0x5A2C7, size: 0x8, addend: 0x0, symName: __ZL15mediumTypeArray, symObjAddr: 0x14B0, symBinAddr: 0x11FA0, symSize: 0x0 }
  - { offsetInCU: 0x8D, offset: 0x5A301, size: 0x8, addend: 0x0, symName: __ZL16mediumSpeedArray, symObjAddr: 0x14E0, symBinAddr: 0x11FD0, symSize: 0x0 }
  - { offsetInCU: 0x13D, offset: 0x5A3B1, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi9getParamsEv, symObjAddr: 0x0, symBinAddr: 0x9EEE, symSize: 0x3E4 }
  - { offsetInCU: 0x213, offset: 0x5A487, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi15setupMediumDictEv, symObjAddr: 0x3E4, symBinAddr: 0xA2D2, symSize: 0x140 }
  - { offsetInCU: 0x21B, offset: 0x5A48F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16initEventSourcesEP9IOService, symObjAddr: 0x524, symBinAddr: 0xA412, symSize: 0x18C }
  - { offsetInCU: 0x2BB, offset: 0x5A52F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16initEventSourcesEP9IOService, symObjAddr: 0x524, symBinAddr: 0xA412, symSize: 0x18C }
  - { offsetInCU: 0x2C3, offset: 0x5A537, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi19setupDMADescriptorsEv, symObjAddr: 0x6B0, symBinAddr: 0xA59E, symSize: 0x614 }
  - { offsetInCU: 0x3F2, offset: 0x5A666, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi19setupDMADescriptorsEv, symObjAddr: 0x6B0, symBinAddr: 0xA59E, symSize: 0x614 }
  - { offsetInCU: 0x3FA, offset: 0x5A66E, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18freeDMADescriptorsEv, symObjAddr: 0xCC4, symBinAddr: 0xABB2, symSize: 0x150 }
  - { offsetInCU: 0x62B, offset: 0x5A89F, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi18freeDMADescriptorsEv, symObjAddr: 0xCC4, symBinAddr: 0xABB2, symSize: 0x150 }
  - { offsetInCU: 0x633, offset: 0x5A8A7, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16clearDescriptorsEv, symObjAddr: 0xE14, symBinAddr: 0xAD02, symSize: 0xE2 }
  - { offsetInCU: 0x6B3, offset: 0x5A927, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi16clearDescriptorsEv, symObjAddr: 0xE14, symBinAddr: 0xAD02, symSize: 0xE2 }
  - { offsetInCU: 0x745, offset: 0x5A9B9, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi21discardPacketFragmentEb, symObjAddr: 0xEF6, symBinAddr: 0xADE4, symSize: 0x56 }
  - { offsetInCU: 0x74D, offset: 0x5A9C1, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14getAddressListEP13IntelAddrData, symObjAddr: 0xF4C, symBinAddr: 0xAE3A, symSize: 0x185 }
  - { offsetInCU: 0x77E, offset: 0x5A9F2, size: 0x8, addend: 0x0, symName: __ZN10IntelMausi14getAddressListEP13IntelAddrData, symObjAddr: 0xF4C, symBinAddr: 0xAE3A, symSize: 0x185 }
  - { offsetInCU: 0x27, offset: 0x5AB2A, size: 0x8, addend: 0x0, symName: _e1000e_poll_eerd_eewr_done, symObjAddr: 0x0, symBinAddr: 0xAFBF, symSize: 0x55 }
  - { offsetInCU: 0x29D, offset: 0x5ADA0, size: 0x8, addend: 0x0, symName: _e1000e_poll_eerd_eewr_done, symObjAddr: 0x0, symBinAddr: 0xAFBF, symSize: 0x55 }
  - { offsetInCU: 0x2A5, offset: 0x5ADA8, size: 0x8, addend: 0x0, symName: _e1000e_acquire_nvm, symObjAddr: 0x55, symBinAddr: 0xB014, symSize: 0x88 }
  - { offsetInCU: 0x323, offset: 0x5AE26, size: 0x8, addend: 0x0, symName: _e1000e_acquire_nvm, symObjAddr: 0x55, symBinAddr: 0xB014, symSize: 0x88 }
  - { offsetInCU: 0x32B, offset: 0x5AE2E, size: 0x8, addend: 0x0, symName: _e1000e_release_nvm, symObjAddr: 0xDD, symBinAddr: 0xB09C, symSize: 0x5F }
  - { offsetInCU: 0x1C23, offset: 0x5C726, size: 0x8, addend: 0x0, symName: _e1000e_release_nvm, symObjAddr: 0xDD, symBinAddr: 0xB09C, symSize: 0x5F }
  - { offsetInCU: 0x1C2B, offset: 0x5C72E, size: 0x8, addend: 0x0, symName: _e1000e_read_nvm_eerd, symObjAddr: 0x13C, symBinAddr: 0xB0FB, symSize: 0xA2 }
  - { offsetInCU: 0x1D85, offset: 0x5C888, size: 0x8, addend: 0x0, symName: _e1000e_read_nvm_eerd, symObjAddr: 0x13C, symBinAddr: 0xB0FB, symSize: 0xA2 }
  - { offsetInCU: 0x1D8D, offset: 0x5C890, size: 0x8, addend: 0x0, symName: _e1000e_write_nvm_spi, symObjAddr: 0x1DE, symBinAddr: 0xB19D, symSize: 0x279 }
  - { offsetInCU: 0x1F90, offset: 0x5CA93, size: 0x8, addend: 0x0, symName: _e1000e_write_nvm_spi, symObjAddr: 0x1DE, symBinAddr: 0xB19D, symSize: 0x279 }
  - { offsetInCU: 0x1F98, offset: 0x5CA9B, size: 0x8, addend: 0x0, symName: _e1000_standby_nvm, symObjAddr: 0x457, symBinAddr: 0xB416, symSize: 0x6E }
  - { offsetInCU: 0x225E, offset: 0x5CD61, size: 0x8, addend: 0x0, symName: _e1000_standby_nvm, symObjAddr: 0x457, symBinAddr: 0xB416, symSize: 0x6E }
  - { offsetInCU: 0x2266, offset: 0x5CD69, size: 0x8, addend: 0x0, symName: _e1000_shift_out_eec_bits, symObjAddr: 0x4C5, symBinAddr: 0xB484, symSize: 0xF4 }
  - { offsetInCU: 0x232A, offset: 0x5CE2D, size: 0x8, addend: 0x0, symName: _e1000_shift_out_eec_bits, symObjAddr: 0x4C5, symBinAddr: 0xB484, symSize: 0xF4 }
  - { offsetInCU: 0x2332, offset: 0x5CE35, size: 0x8, addend: 0x0, symName: _e1000_read_pba_string_generic, symObjAddr: 0x5B9, symBinAddr: 0xB578, symSize: 0x1BB }
  - { offsetInCU: 0x24F2, offset: 0x5CFF5, size: 0x8, addend: 0x0, symName: _e1000_read_pba_string_generic, symObjAddr: 0x5B9, symBinAddr: 0xB578, symSize: 0x1BB }
  - { offsetInCU: 0x24FA, offset: 0x5CFFD, size: 0x8, addend: 0x0, symName: _e1000_read_mac_addr_generic, symObjAddr: 0x774, symBinAddr: 0xB733, symSize: 0x58 }
  - { offsetInCU: 0x2687, offset: 0x5D18A, size: 0x8, addend: 0x0, symName: _e1000_read_mac_addr_generic, symObjAddr: 0x774, symBinAddr: 0xB733, symSize: 0x58 }
  - { offsetInCU: 0x268F, offset: 0x5D192, size: 0x8, addend: 0x0, symName: _e1000e_validate_nvm_checksum_generic, symObjAddr: 0x7CC, symBinAddr: 0xB78B, symSize: 0x68 }
  - { offsetInCU: 0x2754, offset: 0x5D257, size: 0x8, addend: 0x0, symName: _e1000e_validate_nvm_checksum_generic, symObjAddr: 0x7CC, symBinAddr: 0xB78B, symSize: 0x68 }
  - { offsetInCU: 0x275C, offset: 0x5D25F, size: 0x8, addend: 0x0, symName: _e1000e_update_nvm_checksum_generic, symObjAddr: 0x834, symBinAddr: 0xB7F3, symSize: 0x79 }
  - { offsetInCU: 0x2869, offset: 0x5D36C, size: 0x8, addend: 0x0, symName: _e1000e_update_nvm_checksum_generic, symObjAddr: 0x834, symBinAddr: 0xB7F3, symSize: 0x79 }
  - { offsetInCU: 0x2871, offset: 0x5D374, size: 0x8, addend: 0x0, symName: _e1000e_reload_nvm_generic, symObjAddr: 0x8AD, symBinAddr: 0xB86C, symSize: 0x3A }
  - { offsetInCU: 0x2964, offset: 0x5D467, size: 0x8, addend: 0x0, symName: _e1000e_reload_nvm_generic, symObjAddr: 0x8AD, symBinAddr: 0xB86C, symSize: 0x3A }
  - { offsetInCU: 0x27, offset: 0x5D4FD, size: 0x8, addend: 0x0, symName: _e1000e_check_mng_mode_generic, symObjAddr: 0x0, symBinAddr: 0xB8A6, symSize: 0x19 }
  - { offsetInCU: 0x2E4, offset: 0x5D7BA, size: 0x8, addend: 0x0, symName: _e1000e_check_mng_mode_generic, symObjAddr: 0x0, symBinAddr: 0xB8A6, symSize: 0x19 }
  - { offsetInCU: 0x2EC, offset: 0x5D7C2, size: 0x8, addend: 0x0, symName: _e1000e_enable_tx_pkt_filtering, symObjAddr: 0x19, symBinAddr: 0xB8BF, symSize: 0xA2 }
  - { offsetInCU: 0x3A3, offset: 0x5D879, size: 0x8, addend: 0x0, symName: _e1000e_enable_tx_pkt_filtering, symObjAddr: 0x19, symBinAddr: 0xB8BF, symSize: 0xA2 }
  - { offsetInCU: 0x3AB, offset: 0x5D881, size: 0x8, addend: 0x0, symName: _e1000_mng_enable_host_if, symObjAddr: 0xBB, symBinAddr: 0xB961, symSize: 0x5D }
  - { offsetInCU: 0x4C8, offset: 0x5D99E, size: 0x8, addend: 0x0, symName: _e1000_mng_enable_host_if, symObjAddr: 0xBB, symBinAddr: 0xB961, symSize: 0x5D }
  - { offsetInCU: 0x4D0, offset: 0x5D9A6, size: 0x8, addend: 0x0, symName: _e1000e_mng_write_dhcp_info, symObjAddr: 0x118, symBinAddr: 0xB9BE, symSize: 0x179 }
  - { offsetInCU: 0x1EFE, offset: 0x5F3D4, size: 0x8, addend: 0x0, symName: _e1000e_mng_write_dhcp_info, symObjAddr: 0x118, symBinAddr: 0xB9BE, symSize: 0x179 }
  - { offsetInCU: 0x27, offset: 0x5F663, size: 0x8, addend: 0x0, symName: _e1000_read_emi_reg_locked, symObjAddr: 0x0, symBinAddr: 0xBB38, symSize: 0x3B }
  - { offsetInCU: 0x47, offset: 0x5F683, size: 0x8, addend: 0x0, symName: _e1000_ich8_info, symObjAddr: 0x5508, symBinAddr: 0x13680, symSize: 0x0 }
  - { offsetInCU: 0x1AE9, offset: 0x61125, size: 0x8, addend: 0x0, symName: _e1000_ich9_info, symObjAddr: 0x5540, symBinAddr: 0x136B8, symSize: 0x0 }
  - { offsetInCU: 0x1B01, offset: 0x6113D, size: 0x8, addend: 0x0, symName: _e1000_ich10_info, symObjAddr: 0x5578, symBinAddr: 0x136F0, symSize: 0x0 }
  - { offsetInCU: 0x1B19, offset: 0x61155, size: 0x8, addend: 0x0, symName: _e1000_pch_info, symObjAddr: 0x55B0, symBinAddr: 0x13728, symSize: 0x0 }
  - { offsetInCU: 0x1B31, offset: 0x6116D, size: 0x8, addend: 0x0, symName: _e1000_pch2_info, symObjAddr: 0x55E8, symBinAddr: 0x13760, symSize: 0x0 }
  - { offsetInCU: 0x1B49, offset: 0x61185, size: 0x8, addend: 0x0, symName: _e1000_pch_lpt_info, symObjAddr: 0x5620, symBinAddr: 0x13798, symSize: 0x0 }
  - { offsetInCU: 0x1B61, offset: 0x6119D, size: 0x8, addend: 0x0, symName: _e1000_pch_spt_info, symObjAddr: 0x5698, symBinAddr: 0x13810, symSize: 0x0 }
  - { offsetInCU: 0x1B79, offset: 0x611B5, size: 0x8, addend: 0x0, symName: _e1000_pch_cnp_info, symObjAddr: 0x56D0, symBinAddr: 0x13848, symSize: 0x0 }
  - { offsetInCU: 0x1B90, offset: 0x611CC, size: 0x8, addend: 0x0, symName: _ich8_mac_ops, symObjAddr: 0x5360, symBinAddr: 0x134D8, symSize: 0x0 }
  - { offsetInCU: 0x1BA7, offset: 0x611E3, size: 0x8, addend: 0x0, symName: _ich8_phy_ops, symObjAddr: 0x5418, symBinAddr: 0x13590, symSize: 0x0 }
  - { offsetInCU: 0x1BBE, offset: 0x611FA, size: 0x8, addend: 0x0, symName: _ich8_nvm_ops, symObjAddr: 0x54C8, symBinAddr: 0x13640, symSize: 0x0 }
  - { offsetInCU: 0x1BD5, offset: 0x61211, size: 0x8, addend: 0x0, symName: _spt_nvm_ops, symObjAddr: 0x5658, symBinAddr: 0x137D0, symSize: 0x0 }
  - { offsetInCU: 0x1CC1, offset: 0x612FD, size: 0x8, addend: 0x0, symName: _e1000_read_emi_reg_locked, symObjAddr: 0x0, symBinAddr: 0xBB38, symSize: 0x3B }
  - { offsetInCU: 0x1CC9, offset: 0x61305, size: 0x8, addend: 0x0, symName: _e1000_write_emi_reg_locked, symObjAddr: 0x3B, symBinAddr: 0xBB73, symSize: 0x3C }
  - { offsetInCU: 0x1D77, offset: 0x613B3, size: 0x8, addend: 0x0, symName: _e1000_write_emi_reg_locked, symObjAddr: 0x3B, symBinAddr: 0xBB73, symSize: 0x3C }
  - { offsetInCU: 0x1DA3, offset: 0x613DF, size: 0x8, addend: 0x0, symName: _e1000_write_emi_reg_locked, symObjAddr: 0x3B, symBinAddr: 0xBB73, symSize: 0x3C }
  - { offsetInCU: 0x1DAB, offset: 0x613E7, size: 0x8, addend: 0x0, symName: _e1000_set_eee_pchlan, symObjAddr: 0x77, symBinAddr: 0xBBAF, symSize: 0x22F }
  - { offsetInCU: 0x1E4F, offset: 0x6148B, size: 0x8, addend: 0x0, symName: _e1000_set_eee_pchlan, symObjAddr: 0x77, symBinAddr: 0xBBAF, symSize: 0x22F }
  - { offsetInCU: 0x1F01, offset: 0x6153D, size: 0x8, addend: 0x0, symName: _e1000_set_eee_pchlan, symObjAddr: 0x77, symBinAddr: 0xBBAF, symSize: 0x22F }
  - { offsetInCU: 0x1F09, offset: 0x61545, size: 0x8, addend: 0x0, symName: _e1000_enable_ulp_lpt_lp, symObjAddr: 0x2A6, symBinAddr: 0xBDDE, symSize: 0x29B }
  - { offsetInCU: 0x24DA, offset: 0x61B16, size: 0x8, addend: 0x0, symName: _e1000_enable_ulp_lpt_lp, symObjAddr: 0x2A6, symBinAddr: 0xBDDE, symSize: 0x29B }
  - { offsetInCU: 0x24E2, offset: 0x61B1E, size: 0x8, addend: 0x0, symName: _e1000_configure_k1_ich8lan, symObjAddr: 0x541, symBinAddr: 0xC079, symSize: 0xF6 }
  - { offsetInCU: 0x25E4, offset: 0x61C20, size: 0x8, addend: 0x0, symName: _e1000_configure_k1_ich8lan, symObjAddr: 0x541, symBinAddr: 0xC079, symSize: 0xF6 }
  - { offsetInCU: 0x25EC, offset: 0x61C28, size: 0x8, addend: 0x0, symName: _e1000_copy_rx_addrs_to_phy_ich8lan, symObjAddr: 0x637, symBinAddr: 0xC16F, symSize: 0x1A2 }
  - { offsetInCU: 0x273A, offset: 0x61D76, size: 0x8, addend: 0x0, symName: _e1000_copy_rx_addrs_to_phy_ich8lan, symObjAddr: 0x637, symBinAddr: 0xC16F, symSize: 0x1A2 }
  - { offsetInCU: 0x2742, offset: 0x61D7E, size: 0x8, addend: 0x0, symName: _e1000_lv_jumbo_workaround_ich8lan, symObjAddr: 0x7D9, symBinAddr: 0xC311, symSize: 0x4E1 }
  - { offsetInCU: 0x28EF, offset: 0x61F2B, size: 0x8, addend: 0x0, symName: _e1000_lv_jumbo_workaround_ich8lan, symObjAddr: 0x7D9, symBinAddr: 0xC311, symSize: 0x4E1 }
  - { offsetInCU: 0x28F7, offset: 0x61F33, size: 0x8, addend: 0x0, symName: _e1000e_write_protect_nvm_ich8lan, symObjAddr: 0xCBA, symBinAddr: 0xC7F2, symSize: 0x49 }
  - { offsetInCU: 0x2E7C, offset: 0x624B8, size: 0x8, addend: 0x0, symName: _e1000e_write_protect_nvm_ich8lan, symObjAddr: 0xCBA, symBinAddr: 0xC7F2, symSize: 0x49 }
  - { offsetInCU: 0x2F05, offset: 0x62541, size: 0x8, addend: 0x0, symName: _e1000e_write_protect_nvm_ich8lan, symObjAddr: 0xCBA, symBinAddr: 0xC7F2, symSize: 0x49 }
  - { offsetInCU: 0x2F0D, offset: 0x62549, size: 0x8, addend: 0x0, symName: _e1000e_set_kmrn_lock_loss_workaround_ich8lan, symObjAddr: 0xD03, symBinAddr: 0xC83B, symSize: 0x16 }
  - { offsetInCU: 0x3007, offset: 0x62643, size: 0x8, addend: 0x0, symName: _e1000e_set_kmrn_lock_loss_workaround_ich8lan, symObjAddr: 0xD03, symBinAddr: 0xC83B, symSize: 0x16 }
  - { offsetInCU: 0x300F, offset: 0x6264B, size: 0x8, addend: 0x0, symName: _e1000e_igp3_phy_powerdown_workaround_ich8lan, symObjAddr: 0xD19, symBinAddr: 0xC851, symSize: 0xE9 }
  - { offsetInCU: 0x303C, offset: 0x62678, size: 0x8, addend: 0x0, symName: _e1000e_igp3_phy_powerdown_workaround_ich8lan, symObjAddr: 0xD19, symBinAddr: 0xC851, symSize: 0xE9 }
  - { offsetInCU: 0x3044, offset: 0x62680, size: 0x8, addend: 0x0, symName: _e1000e_gig_downshift_workaround_ich8lan, symObjAddr: 0xE02, symBinAddr: 0xC93A, symSize: 0x77 }
  - { offsetInCU: 0x3166, offset: 0x627A2, size: 0x8, addend: 0x0, symName: _e1000e_gig_downshift_workaround_ich8lan, symObjAddr: 0xE02, symBinAddr: 0xC93A, symSize: 0x77 }
  - { offsetInCU: 0x316E, offset: 0x627AA, size: 0x8, addend: 0x0, symName: _e1000_suspend_workarounds_ich8lan, symObjAddr: 0xE79, symBinAddr: 0xC9B1, symSize: 0x265 }
  - { offsetInCU: 0x31C0, offset: 0x627FC, size: 0x8, addend: 0x0, symName: _e1000_suspend_workarounds_ich8lan, symObjAddr: 0xE79, symBinAddr: 0xC9B1, symSize: 0x265 }
  - { offsetInCU: 0x31C8, offset: 0x62804, size: 0x8, addend: 0x0, symName: _e1000_oem_bits_config_ich8lan, symObjAddr: 0x10DE, symBinAddr: 0xCC16, symSize: 0x12D }
  - { offsetInCU: 0x35A1, offset: 0x62BDD, size: 0x8, addend: 0x0, symName: _e1000_oem_bits_config_ich8lan, symObjAddr: 0x10DE, symBinAddr: 0xCC16, symSize: 0x12D }
  - { offsetInCU: 0x35A9, offset: 0x62BE5, size: 0x8, addend: 0x0, symName: _e1000_write_smbus_addr, symObjAddr: 0x120B, symBinAddr: 0xCD43, symSize: 0x9E }
  - { offsetInCU: 0x36C0, offset: 0x62CFC, size: 0x8, addend: 0x0, symName: _e1000_write_smbus_addr, symObjAddr: 0x120B, symBinAddr: 0xCD43, symSize: 0x9E }
  - { offsetInCU: 0x36C8, offset: 0x62D04, size: 0x8, addend: 0x0, symName: _e1000_resume_workarounds_pchlan, symObjAddr: 0x12A9, symBinAddr: 0xCDE1, symSize: 0x10B }
  - { offsetInCU: 0x3771, offset: 0x62DAD, size: 0x8, addend: 0x0, symName: _e1000_resume_workarounds_pchlan, symObjAddr: 0x12A9, symBinAddr: 0xCDE1, symSize: 0x10B }
  - { offsetInCU: 0x3779, offset: 0x62DB5, size: 0x8, addend: 0x0, symName: _e1000_init_phy_workarounds_pchlan, symObjAddr: 0x13B4, symBinAddr: 0xCEEC, symSize: 0x427 }
  - { offsetInCU: 0x3A12, offset: 0x6304E, size: 0x8, addend: 0x0, symName: _e1000_init_phy_workarounds_pchlan, symObjAddr: 0x13B4, symBinAddr: 0xCEEC, symSize: 0x427 }
  - { offsetInCU: 0x3A1A, offset: 0x63056, size: 0x8, addend: 0x0, symName: _e1000_get_variants_ich8lan, symObjAddr: 0x17DB, symBinAddr: 0xD313, symSize: 0x6EF }
  - { offsetInCU: 0x3D76, offset: 0x633B2, size: 0x8, addend: 0x0, symName: _e1000_get_variants_ich8lan, symObjAddr: 0x17DB, symBinAddr: 0xD313, symSize: 0x6EF }
  - { offsetInCU: 0x3D7E, offset: 0x633BA, size: 0x8, addend: 0x0, symName: _e1000_phy_is_accessible_pchlan, symObjAddr: 0x1ECA, symBinAddr: 0xDA02, symSize: 0x168 }
  - { offsetInCU: 0x3F19, offset: 0x63555, size: 0x8, addend: 0x0, symName: _e1000_phy_is_accessible_pchlan, symObjAddr: 0x1ECA, symBinAddr: 0xDA02, symSize: 0x168 }
  - { offsetInCU: 0x3F21, offset: 0x6355D, size: 0x8, addend: 0x0, symName: _e1000_toggle_lanphypc_pch_lpt, symObjAddr: 0x2032, symBinAddr: 0xDB6A, symSize: 0xAE }
  - { offsetInCU: 0x4072, offset: 0x636AE, size: 0x8, addend: 0x0, symName: _e1000_toggle_lanphypc_pch_lpt, symObjAddr: 0x2032, symBinAddr: 0xDB6A, symSize: 0xAE }
  - { offsetInCU: 0x407A, offset: 0x636B6, size: 0x8, addend: 0x0, symName: _e1000_set_mdio_slow_mode_hv, symObjAddr: 0x20E0, symBinAddr: 0xDC18, symSize: 0x44 }
  - { offsetInCU: 0x412D, offset: 0x63769, size: 0x8, addend: 0x0, symName: _e1000_set_mdio_slow_mode_hv, symObjAddr: 0x20E0, symBinAddr: 0xDC18, symSize: 0x44 }
  - { offsetInCU: 0x4135, offset: 0x63771, size: 0x8, addend: 0x0, symName: _e1000_check_mng_mode_ich8lan, symObjAddr: 0x2124, symBinAddr: 0xDC5C, symSize: 0x1E }
  - { offsetInCU: 0x41EA, offset: 0x63826, size: 0x8, addend: 0x0, symName: _e1000_check_mng_mode_ich8lan, symObjAddr: 0x2124, symBinAddr: 0xDC5C, symSize: 0x1E }
  - { offsetInCU: 0x41F2, offset: 0x6382E, size: 0x8, addend: 0x0, symName: _e1000_cleanup_led_ich8lan, symObjAddr: 0x2142, symBinAddr: 0xDC7A, symSize: 0x32 }
  - { offsetInCU: 0x4230, offset: 0x6386C, size: 0x8, addend: 0x0, symName: _e1000_cleanup_led_ich8lan, symObjAddr: 0x2142, symBinAddr: 0xDC7A, symSize: 0x32 }
  - { offsetInCU: 0x4238, offset: 0x63874, size: 0x8, addend: 0x0, symName: _e1000_led_on_ich8lan, symObjAddr: 0x2174, symBinAddr: 0xDCAC, symSize: 0x35 }
  - { offsetInCU: 0x4298, offset: 0x638D4, size: 0x8, addend: 0x0, symName: _e1000_led_on_ich8lan, symObjAddr: 0x2174, symBinAddr: 0xDCAC, symSize: 0x35 }
  - { offsetInCU: 0x42A0, offset: 0x638DC, size: 0x8, addend: 0x0, symName: _e1000_led_off_ich8lan, symObjAddr: 0x21A9, symBinAddr: 0xDCE1, symSize: 0x35 }
  - { offsetInCU: 0x4300, offset: 0x6393C, size: 0x8, addend: 0x0, symName: _e1000_led_off_ich8lan, symObjAddr: 0x21A9, symBinAddr: 0xDCE1, symSize: 0x35 }
  - { offsetInCU: 0x4308, offset: 0x63944, size: 0x8, addend: 0x0, symName: _e1000_rar_set_pch2lan, symObjAddr: 0x21DE, symBinAddr: 0xDD16, symSize: 0xF9 }
  - { offsetInCU: 0x4368, offset: 0x639A4, size: 0x8, addend: 0x0, symName: _e1000_rar_set_pch2lan, symObjAddr: 0x21DE, symBinAddr: 0xDD16, symSize: 0xF9 }
  - { offsetInCU: 0x4370, offset: 0x639AC, size: 0x8, addend: 0x0, symName: _e1000_check_mng_mode_pchlan, symObjAddr: 0x22D7, symBinAddr: 0xDE0F, symSize: 0x1A }
  - { offsetInCU: 0x451C, offset: 0x63B58, size: 0x8, addend: 0x0, symName: _e1000_check_mng_mode_pchlan, symObjAddr: 0x22D7, symBinAddr: 0xDE0F, symSize: 0x1A }
  - { offsetInCU: 0x4524, offset: 0x63B60, size: 0x8, addend: 0x0, symName: _e1000_id_led_init_pchlan, symObjAddr: 0x22F1, symBinAddr: 0xDE29, symSize: 0x13F }
  - { offsetInCU: 0x458E, offset: 0x63BCA, size: 0x8, addend: 0x0, symName: _e1000_id_led_init_pchlan, symObjAddr: 0x22F1, symBinAddr: 0xDE29, symSize: 0x13F }
  - { offsetInCU: 0x4682, offset: 0x63CBE, size: 0x8, addend: 0x0, symName: _e1000_setup_led_pchlan, symObjAddr: 0x2430, symBinAddr: 0xDF68, symSize: 0x1A }
  - { offsetInCU: 0x468A, offset: 0x63CC6, size: 0x8, addend: 0x0, symName: _e1000_cleanup_led_pchlan, symObjAddr: 0x244A, symBinAddr: 0xDF82, symSize: 0x1A }
  - { offsetInCU: 0x46C2, offset: 0x63CFE, size: 0x8, addend: 0x0, symName: _e1000_cleanup_led_pchlan, symObjAddr: 0x244A, symBinAddr: 0xDF82, symSize: 0x1A }
  - { offsetInCU: 0x46E3, offset: 0x63D1F, size: 0x8, addend: 0x0, symName: _e1000_cleanup_led_pchlan, symObjAddr: 0x244A, symBinAddr: 0xDF82, symSize: 0x1A }
  - { offsetInCU: 0x46EB, offset: 0x63D27, size: 0x8, addend: 0x0, symName: _e1000_led_on_pchlan, symObjAddr: 0x2464, symBinAddr: 0xDF9C, symSize: 0x58 }
  - { offsetInCU: 0x4723, offset: 0x63D5F, size: 0x8, addend: 0x0, symName: _e1000_led_on_pchlan, symObjAddr: 0x2464, symBinAddr: 0xDF9C, symSize: 0x58 }
  - { offsetInCU: 0x4744, offset: 0x63D80, size: 0x8, addend: 0x0, symName: _e1000_led_on_pchlan, symObjAddr: 0x2464, symBinAddr: 0xDF9C, symSize: 0x58 }
  - { offsetInCU: 0x474C, offset: 0x63D88, size: 0x8, addend: 0x0, symName: _e1000_led_off_pchlan, symObjAddr: 0x24BC, symBinAddr: 0xDFF4, symSize: 0x58 }
  - { offsetInCU: 0x47C3, offset: 0x63DFF, size: 0x8, addend: 0x0, symName: _e1000_led_off_pchlan, symObjAddr: 0x24BC, symBinAddr: 0xDFF4, symSize: 0x58 }
  - { offsetInCU: 0x47EC, offset: 0x63E28, size: 0x8, addend: 0x0, symName: _e1000_led_off_pchlan, symObjAddr: 0x24BC, symBinAddr: 0xDFF4, symSize: 0x58 }
  - { offsetInCU: 0x47F4, offset: 0x63E30, size: 0x8, addend: 0x0, symName: _e1000_rar_set_pch_lpt, symObjAddr: 0x2514, symBinAddr: 0xE04C, symSize: 0x11D }
  - { offsetInCU: 0x486B, offset: 0x63EA7, size: 0x8, addend: 0x0, symName: _e1000_rar_set_pch_lpt, symObjAddr: 0x2514, symBinAddr: 0xE04C, symSize: 0x11D }
  - { offsetInCU: 0x4894, offset: 0x63ED0, size: 0x8, addend: 0x0, symName: _e1000_rar_set_pch_lpt, symObjAddr: 0x2514, symBinAddr: 0xE04C, symSize: 0x11D }
  - { offsetInCU: 0x489C, offset: 0x63ED8, size: 0x8, addend: 0x0, symName: _e1000_setup_copper_link_pch_lpt, symObjAddr: 0x2631, symBinAddr: 0xE169, symSize: 0x3F }
  - { offsetInCU: 0x4A89, offset: 0x640C5, size: 0x8, addend: 0x0, symName: _e1000_setup_copper_link_pch_lpt, symObjAddr: 0x2631, symBinAddr: 0xE169, symSize: 0x3F }
  - { offsetInCU: 0x4A91, offset: 0x640CD, size: 0x8, addend: 0x0, symName: _e1000_rar_get_count_pch_lpt, symObjAddr: 0x2670, symBinAddr: 0xE1A8, symSize: 0x2A }
  - { offsetInCU: 0x4AEA, offset: 0x64126, size: 0x8, addend: 0x0, symName: _e1000_rar_get_count_pch_lpt, symObjAddr: 0x2670, symBinAddr: 0xE1A8, symSize: 0x2A }
  - { offsetInCU: 0x4AF2, offset: 0x6412E, size: 0x8, addend: 0x0, symName: _e1000_acquire_swflag_ich8lan, symObjAddr: 0x269A, symBinAddr: 0xE1D2, symSize: 0xC2 }
  - { offsetInCU: 0x4BCC, offset: 0x64208, size: 0x8, addend: 0x0, symName: _e1000_acquire_swflag_ich8lan, symObjAddr: 0x269A, symBinAddr: 0xE1D2, symSize: 0xC2 }
  - { offsetInCU: 0x4BD4, offset: 0x64210, size: 0x8, addend: 0x0, symName: _e1000_release_swflag_ich8lan, symObjAddr: 0x275C, symBinAddr: 0xE294, symSize: 0x3F }
  - { offsetInCU: 0x4D01, offset: 0x6433D, size: 0x8, addend: 0x0, symName: _e1000_release_swflag_ich8lan, symObjAddr: 0x275C, symBinAddr: 0xE294, symSize: 0x3F }
  - { offsetInCU: 0x4D09, offset: 0x64345, size: 0x8, addend: 0x0, symName: _e1000_power_down_phy_copper_ich8lan, symObjAddr: 0x279B, symBinAddr: 0xE2D3, symSize: 0x32 }
  - { offsetInCU: 0x4D7B, offset: 0x643B7, size: 0x8, addend: 0x0, symName: _e1000_power_down_phy_copper_ich8lan, symObjAddr: 0x279B, symBinAddr: 0xE2D3, symSize: 0x32 }
  - { offsetInCU: 0x4D9A, offset: 0x643D6, size: 0x8, addend: 0x0, symName: _e1000_power_down_phy_copper_ich8lan, symObjAddr: 0x279B, symBinAddr: 0xE2D3, symSize: 0x32 }
  - { offsetInCU: 0x4DA2, offset: 0x643DE, size: 0x8, addend: 0x0, symName: _e1000_set_lplu_state_pchlan, symObjAddr: 0x27CD, symBinAddr: 0xE305, symSize: 0x70 }
  - { offsetInCU: 0x4DC9, offset: 0x64405, size: 0x8, addend: 0x0, symName: _e1000_set_lplu_state_pchlan, symObjAddr: 0x27CD, symBinAddr: 0xE305, symSize: 0x70 }
  - { offsetInCU: 0x4DD1, offset: 0x6440D, size: 0x8, addend: 0x0, symName: _e1000_check_for_copper_link_ich8lan, symObjAddr: 0x283D, symBinAddr: 0xE375, symSize: 0x6E6 }
  - { offsetInCU: 0x5023, offset: 0x6465F, size: 0x8, addend: 0x0, symName: _e1000_check_for_copper_link_ich8lan, symObjAddr: 0x283D, symBinAddr: 0xE375, symSize: 0x6E6 }
  - { offsetInCU: 0x502B, offset: 0x64667, size: 0x8, addend: 0x0, symName: _e1000_clear_hw_cntrs_ich8lan, symObjAddr: 0x2F23, symBinAddr: 0xEA5B, symSize: 0x19C }
  - { offsetInCU: 0x56A3, offset: 0x64CDF, size: 0x8, addend: 0x0, symName: _e1000_clear_hw_cntrs_ich8lan, symObjAddr: 0x2F23, symBinAddr: 0xEA5B, symSize: 0x19C }
  - { offsetInCU: 0x56AB, offset: 0x64CE7, size: 0x8, addend: 0x0, symName: _e1000_get_bus_info_ich8lan, symObjAddr: 0x30BF, symBinAddr: 0xEBF7, symSize: 0x28 }
  - { offsetInCU: 0x58F1, offset: 0x64F2D, size: 0x8, addend: 0x0, symName: _e1000_get_bus_info_ich8lan, symObjAddr: 0x30BF, symBinAddr: 0xEBF7, symSize: 0x28 }
  - { offsetInCU: 0x58F9, offset: 0x64F35, size: 0x8, addend: 0x0, symName: _e1000_get_link_up_info_ich8lan, symObjAddr: 0x30E7, symBinAddr: 0xEC1F, symSize: 0x11F }
  - { offsetInCU: 0x59CA, offset: 0x65006, size: 0x8, addend: 0x0, symName: _e1000_get_link_up_info_ich8lan, symObjAddr: 0x30E7, symBinAddr: 0xEC1F, symSize: 0x11F }
  - { offsetInCU: 0x59D2, offset: 0x6500E, size: 0x8, addend: 0x0, symName: _e1000_reset_hw_ich8lan, symObjAddr: 0x3206, symBinAddr: 0xED3E, symSize: 0x1EC }
  - { offsetInCU: 0x5B96, offset: 0x651D2, size: 0x8, addend: 0x0, symName: _e1000_reset_hw_ich8lan, symObjAddr: 0x3206, symBinAddr: 0xED3E, symSize: 0x1EC }
  - { offsetInCU: 0x5B9E, offset: 0x651DA, size: 0x8, addend: 0x0, symName: _e1000_init_hw_ich8lan, symObjAddr: 0x33F2, symBinAddr: 0xEF2A, symSize: 0x28B }
  - { offsetInCU: 0x5D6E, offset: 0x653AA, size: 0x8, addend: 0x0, symName: _e1000_init_hw_ich8lan, symObjAddr: 0x33F2, symBinAddr: 0xEF2A, symSize: 0x28B }
  - { offsetInCU: 0x5D76, offset: 0x653B2, size: 0x8, addend: 0x0, symName: _e1000_setup_link_ich8lan, symObjAddr: 0x367D, symBinAddr: 0xF1B5, symSize: 0xB1 }
  - { offsetInCU: 0x5F21, offset: 0x6555D, size: 0x8, addend: 0x0, symName: _e1000_setup_link_ich8lan, symObjAddr: 0x367D, symBinAddr: 0xF1B5, symSize: 0xB1 }
  - { offsetInCU: 0x5F29, offset: 0x65565, size: 0x8, addend: 0x0, symName: _e1000_setup_copper_link_ich8lan, symObjAddr: 0x372E, symBinAddr: 0xF266, symSize: 0x13A }
  - { offsetInCU: 0x5F99, offset: 0x655D5, size: 0x8, addend: 0x0, symName: _e1000_setup_copper_link_ich8lan, symObjAddr: 0x372E, symBinAddr: 0xF266, symSize: 0x13A }
  - { offsetInCU: 0x6071, offset: 0x656AD, size: 0x8, addend: 0x0, symName: _e1000_k1_gig_workaround_hv, symObjAddr: 0x3868, symBinAddr: 0xF3A0, symSize: 0x121 }
  - { offsetInCU: 0x6079, offset: 0x656B5, size: 0x8, addend: 0x0, symName: _e1000_k1_workaround_lv, symObjAddr: 0x3989, symBinAddr: 0xF4C1, symSize: 0xA8 }
  - { offsetInCU: 0x61CA, offset: 0x65806, size: 0x8, addend: 0x0, symName: _e1000_k1_workaround_lv, symObjAddr: 0x3989, symBinAddr: 0xF4C1, symSize: 0xA8 }
  - { offsetInCU: 0x61D2, offset: 0x6580E, size: 0x8, addend: 0x0, symName: _e1000_post_phy_reset_ich8lan, symObjAddr: 0x3A31, symBinAddr: 0xF569, symSize: 0x527 }
  - { offsetInCU: 0x649C, offset: 0x65AD8, size: 0x8, addend: 0x0, symName: _e1000_post_phy_reset_ich8lan, symObjAddr: 0x3A31, symBinAddr: 0xF569, symSize: 0x527 }
  - { offsetInCU: 0x64A4, offset: 0x65AE0, size: 0x8, addend: 0x0, symName: _e1000_phy_hw_reset_ich8lan, symObjAddr: 0x3F58, symBinAddr: 0xFA90, symSize: 0x59 }
  - { offsetInCU: 0x6BE1, offset: 0x6621D, size: 0x8, addend: 0x0, symName: _e1000_phy_hw_reset_ich8lan, symObjAddr: 0x3F58, symBinAddr: 0xFA90, symSize: 0x59 }
  - { offsetInCU: 0x6BE9, offset: 0x66225, size: 0x8, addend: 0x0, symName: _e1000_check_reset_block_ich8lan, symObjAddr: 0x3FB1, symBinAddr: 0xFAE9, symSize: 0x58 }
  - { offsetInCU: 0x6C62, offset: 0x6629E, size: 0x8, addend: 0x0, symName: _e1000_check_reset_block_ich8lan, symObjAddr: 0x3FB1, symBinAddr: 0xFAE9, symSize: 0x58 }
  - { offsetInCU: 0x6C6A, offset: 0x662A6, size: 0x8, addend: 0x0, symName: _e1000_get_cfg_done_ich8lan, symObjAddr: 0x4009, symBinAddr: 0xFB41, symSize: 0xDC }
  - { offsetInCU: 0x6CF7, offset: 0x66333, size: 0x8, addend: 0x0, symName: _e1000_get_cfg_done_ich8lan, symObjAddr: 0x4009, symBinAddr: 0xFB41, symSize: 0xDC }
  - { offsetInCU: 0x6CFF, offset: 0x6633B, size: 0x8, addend: 0x0, symName: _e1000_set_d0_lplu_state_ich8lan, symObjAddr: 0x40E5, symBinAddr: 0xFC1D, symSize: 0x105 }
  - { offsetInCU: 0x6DCA, offset: 0x66406, size: 0x8, addend: 0x0, symName: _e1000_set_d0_lplu_state_ich8lan, symObjAddr: 0x40E5, symBinAddr: 0xFC1D, symSize: 0x105 }
  - { offsetInCU: 0x6DD2, offset: 0x6640E, size: 0x8, addend: 0x0, symName: _e1000_set_d3_lplu_state_ich8lan, symObjAddr: 0x41EA, symBinAddr: 0xFD22, symSize: 0x14E }
  - { offsetInCU: 0x6EEA, offset: 0x66526, size: 0x8, addend: 0x0, symName: _e1000_set_d3_lplu_state_ich8lan, symObjAddr: 0x41EA, symBinAddr: 0xFD22, symSize: 0x14E }
  - { offsetInCU: 0x6EF2, offset: 0x6652E, size: 0x8, addend: 0x0, symName: _e1000_valid_nvm_bank_detect_ich8lan, symObjAddr: 0x4338, symBinAddr: 0xFE70, symSize: 0x171 }
  - { offsetInCU: 0x7123, offset: 0x6675F, size: 0x8, addend: 0x0, symName: _e1000_valid_nvm_bank_detect_ich8lan, symObjAddr: 0x4338, symBinAddr: 0xFE70, symSize: 0x171 }
  - { offsetInCU: 0x712B, offset: 0x66767, size: 0x8, addend: 0x0, symName: _e1000_read_flash_dword_ich8lan, symObjAddr: 0x44A9, symBinAddr: 0xFFE1, symSize: 0xBD }
  - { offsetInCU: 0x74BE, offset: 0x66AFA, size: 0x8, addend: 0x0, symName: _e1000_read_flash_dword_ich8lan, symObjAddr: 0x44A9, symBinAddr: 0xFFE1, symSize: 0xBD }
  - { offsetInCU: 0x74C6, offset: 0x66B02, size: 0x8, addend: 0x0, symName: _e1000_flash_cycle_init_ich8lan, symObjAddr: 0x4566, symBinAddr: 0x1009E, symSize: 0x92 }
  - { offsetInCU: 0x7662, offset: 0x66C9E, size: 0x8, addend: 0x0, symName: _e1000_flash_cycle_init_ich8lan, symObjAddr: 0x4566, symBinAddr: 0x1009E, symSize: 0x92 }
  - { offsetInCU: 0x766A, offset: 0x66CA6, size: 0x8, addend: 0x0, symName: _e1000_flash_cycle_ich8lan, symObjAddr: 0x45F8, symBinAddr: 0x10130, symSize: 0x64 }
  - { offsetInCU: 0x779B, offset: 0x66DD7, size: 0x8, addend: 0x0, symName: _e1000_flash_cycle_ich8lan, symObjAddr: 0x45F8, symBinAddr: 0x10130, symSize: 0x64 }
  - { offsetInCU: 0x77A3, offset: 0x66DDF, size: 0x8, addend: 0x0, symName: _e1000_read_flash_data_ich8lan, symObjAddr: 0x465C, symBinAddr: 0x10194, symSize: 0xC9 }
  - { offsetInCU: 0x78A5, offset: 0x66EE1, size: 0x8, addend: 0x0, symName: _e1000_read_flash_data_ich8lan, symObjAddr: 0x465C, symBinAddr: 0x10194, symSize: 0xC9 }
  - { offsetInCU: 0x78AD, offset: 0x66EE9, size: 0x8, addend: 0x0, symName: _e1000_acquire_nvm_ich8lan, symObjAddr: 0x4725, symBinAddr: 0x1025D, symSize: 0x8 }
  - { offsetInCU: 0x7A6F, offset: 0x670AB, size: 0x8, addend: 0x0, symName: _e1000_acquire_nvm_ich8lan, symObjAddr: 0x4725, symBinAddr: 0x1025D, symSize: 0x8 }
  - { offsetInCU: 0x7A77, offset: 0x670B3, size: 0x8, addend: 0x0, symName: _e1000_read_nvm_ich8lan, symObjAddr: 0x472D, symBinAddr: 0x10265, symSize: 0x10D }
  - { offsetInCU: 0x7AE2, offset: 0x6711E, size: 0x8, addend: 0x0, symName: _e1000_read_nvm_ich8lan, symObjAddr: 0x472D, symBinAddr: 0x10265, symSize: 0x10D }
  - { offsetInCU: 0x7AEA, offset: 0x67126, size: 0x8, addend: 0x0, symName: _e1000_release_nvm_ich8lan, symObjAddr: 0x483A, symBinAddr: 0x10372, symSize: 0x6 }
  - { offsetInCU: 0x7C1A, offset: 0x67256, size: 0x8, addend: 0x0, symName: _e1000_release_nvm_ich8lan, symObjAddr: 0x483A, symBinAddr: 0x10372, symSize: 0x6 }
  - { offsetInCU: 0x7C22, offset: 0x6725E, size: 0x8, addend: 0x0, symName: _e1000_update_nvm_checksum_ich8lan, symObjAddr: 0x4840, symBinAddr: 0x10378, symSize: 0x22E }
  - { offsetInCU: 0x7C45, offset: 0x67281, size: 0x8, addend: 0x0, symName: _e1000_update_nvm_checksum_ich8lan, symObjAddr: 0x4840, symBinAddr: 0x10378, symSize: 0x22E }
  - { offsetInCU: 0x7C4D, offset: 0x67289, size: 0x8, addend: 0x0, symName: _e1000_valid_led_default_ich8lan, symObjAddr: 0x4A6E, symBinAddr: 0x105A6, symSize: 0x3B }
  - { offsetInCU: 0x7DAD, offset: 0x673E9, size: 0x8, addend: 0x0, symName: _e1000_valid_led_default_ich8lan, symObjAddr: 0x4A6E, symBinAddr: 0x105A6, symSize: 0x3B }
  - { offsetInCU: 0x7DB5, offset: 0x673F1, size: 0x8, addend: 0x0, symName: _e1000_validate_nvm_checksum_ich8lan, symObjAddr: 0x4AA9, symBinAddr: 0x105E1, symSize: 0x9D }
  - { offsetInCU: 0x7EB8, offset: 0x674F4, size: 0x8, addend: 0x0, symName: _e1000_validate_nvm_checksum_ich8lan, symObjAddr: 0x4AA9, symBinAddr: 0x105E1, symSize: 0x9D }
  - { offsetInCU: 0x7EC0, offset: 0x674FC, size: 0x8, addend: 0x0, symName: _e1000_write_nvm_ich8lan, symObjAddr: 0x4B46, symBinAddr: 0x1067E, symSize: 0x77 }
  - { offsetInCU: 0x7FD6, offset: 0x67612, size: 0x8, addend: 0x0, symName: _e1000_write_nvm_ich8lan, symObjAddr: 0x4B46, symBinAddr: 0x1067E, symSize: 0x77 }
  - { offsetInCU: 0x7FDE, offset: 0x6761A, size: 0x8, addend: 0x0, symName: _e1000_erase_flash_bank_ich8lan, symObjAddr: 0x4BBD, symBinAddr: 0x106F5, symSize: 0x157 }
  - { offsetInCU: 0x807F, offset: 0x676BB, size: 0x8, addend: 0x0, symName: _e1000_erase_flash_bank_ich8lan, symObjAddr: 0x4BBD, symBinAddr: 0x106F5, symSize: 0x157 }
  - { offsetInCU: 0x8255, offset: 0x67891, size: 0x8, addend: 0x0, symName: _e1000_retry_write_flash_byte_ich8lan, symObjAddr: 0x4D14, symBinAddr: 0x1084C, symSize: 0x66 }
  - { offsetInCU: 0x825D, offset: 0x67899, size: 0x8, addend: 0x0, symName: _e1000_write_flash_byte_ich8lan, symObjAddr: 0x4D7A, symBinAddr: 0x108B2, symSize: 0xCC }
  - { offsetInCU: 0x8393, offset: 0x679CF, size: 0x8, addend: 0x0, symName: _e1000_write_flash_byte_ich8lan, symObjAddr: 0x4D7A, symBinAddr: 0x108B2, symSize: 0xCC }
  - { offsetInCU: 0x839B, offset: 0x679D7, size: 0x8, addend: 0x0, symName: _e1000_read_nvm_spt, symObjAddr: 0x4E46, symBinAddr: 0x1097E, symSize: 0x1C1 }
  - { offsetInCU: 0x858A, offset: 0x67BC6, size: 0x8, addend: 0x0, symName: _e1000_read_nvm_spt, symObjAddr: 0x4E46, symBinAddr: 0x1097E, symSize: 0x1C1 }
  - { offsetInCU: 0x8592, offset: 0x67BCE, size: 0x8, addend: 0x0, symName: _e1000_update_nvm_checksum_spt, symObjAddr: 0x5007, symBinAddr: 0x10B3F, symSize: 0x219 }
  - { offsetInCU: 0x86A7, offset: 0x67CE3, size: 0x8, addend: 0x0, symName: _e1000_update_nvm_checksum_spt, symObjAddr: 0x5007, symBinAddr: 0x10B3F, symSize: 0x219 }
  - { offsetInCU: 0x86AF, offset: 0x67CEB, size: 0x8, addend: 0x0, symName: _e1000_retry_write_flash_dword_ich8lan, symObjAddr: 0x5220, symBinAddr: 0x10D58, symSize: 0x68 }
  - { offsetInCU: 0x87AD, offset: 0x67DE9, size: 0x8, addend: 0x0, symName: _e1000_retry_write_flash_dword_ich8lan, symObjAddr: 0x5220, symBinAddr: 0x10D58, symSize: 0x68 }
  - { offsetInCU: 0x87B5, offset: 0x67DF1, size: 0x8, addend: 0x0, symName: _e1000_write_flash_data32_ich8lan, symObjAddr: 0x5288, symBinAddr: 0x10DC0, symSize: 0xD7 }
  - { offsetInCU: 0x8838, offset: 0x67E74, size: 0x8, addend: 0x0, symName: _e1000_write_flash_data32_ich8lan, symObjAddr: 0x5288, symBinAddr: 0x10DC0, symSize: 0xD7 }
...
