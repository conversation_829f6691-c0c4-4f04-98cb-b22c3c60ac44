---
triple:          'i386-apple-darwin'
binary-path:     '/Users/<USER>/work/VirtualSMC/VirtualSMC/build/Release/VirtualSMC.kext/Contents/MacOS/VirtualSMC'
relocations:
  - { offsetInCU: 0x35, offset: 0x35, size: 0x4, addend: 0x0, symName: _kmod_info, symObjAddr: 0x0, symBinAddr: 0xCFDC, symSize: 0x0 }
  - { offsetInCU: 0x206, offset: 0x206, size: 0x4, addend: 0x0, symName: __realmain, symObjAddr: 0xA8, symBinAddr: 0xD084, symSize: 0x0 }
  - { offsetInCU: 0x218, offset: 0x218, size: 0x4, addend: 0x0, symName: __antimain, symObjAddr: 0xAC, symBinAddr: 0xD088, symSize: 0x0 }
  - { offsetInCU: 0x22A, offset: 0x22A, size: 0x4, addend: 0x0, symName: __kext_apple_cc, symObjAddr: 0xB0, symBinAddr: 0xD08C, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x256, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO11resetDeviceEv, symObjAddr: 0x0, symBinAddr: 0x0, symSize: 0x50 }
  - { offsetInCU: 0x2B, offset: 0x25A, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO10MemoryInfoE, symObjAddr: 0x6AC, symBinAddr: 0xB3B0, symSize: 0x0 }
  - { offsetInCU: 0x3A, offset: 0x269, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO10MemoryInfoE, symObjAddr: 0x6AC, symBinAddr: 0xB3B0, symSize: 0x0 }
  - { offsetInCU: 0x679, offset: 0x8A8, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO14MemoryInfoSizeE, symObjAddr: 0x6C0, symBinAddr: 0xB3C4, symSize: 0x0 }
  - { offsetInCU: 0x74C, offset: 0x97B, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO11resetDeviceEv, symObjAddr: 0x0, symBinAddr: 0x0, symSize: 0x50 }
  - { offsetInCU: 0x780, offset: 0x9AF, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO11resetBufferEv, symObjAddr: 0x50, symBinAddr: 0x50, symSize: 0x30 }
  - { offsetInCU: 0xF786, offset: 0xF9B5, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO17loadValueInBufferEv, symObjAddr: 0x80, symBinAddr: 0x80, symSize: 0xC0 }
  - { offsetInCU: 0xF7F9, offset: 0xFA28, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO15loadKeyInBufferEv, symObjAddr: 0x140, symBinAddr: 0x140, symSize: 0x80 }
  - { offsetInCU: 0xF846, offset: 0xFA75, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO19saveValueFromBufferEv, symObjAddr: 0x1C0, symBinAddr: 0x1C0, symSize: 0x50 }
  - { offsetInCU: 0xF88A, offset: 0xFAB9, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO19loadKeyInfoInBufferEv, symObjAddr: 0x210, symBinAddr: 0x210, symSize: 0xA0 }
  - { offsetInCU: 0xF979, offset: 0xFBA8, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO9writeDataEh, symObjAddr: 0x2B0, symBinAddr: 0x2B0, symSize: 0x260 }
  - { offsetInCU: 0xFACB, offset: 0xFCFA, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO12writeCommandEh, symObjAddr: 0x510, symBinAddr: 0x510, symSize: 0xF0 }
  - { offsetInCU: 0xFB5C, offset: 0xFD8B, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO8readDataEv, symObjAddr: 0x600, symBinAddr: 0x600, symSize: 0x80 }
  - { offsetInCU: 0xFB8E, offset: 0xFDBD, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO10readStatusEv, symObjAddr: 0x680, symBinAddr: 0x680, symSize: 0x10 }
  - { offsetInCU: 0xFBB1, offset: 0xFDE0, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO10readResultEv, symObjAddr: 0x690, symBinAddr: 0x690, symSize: 0x10 }
  - { offsetInCU: 0xFBD4, offset: 0xFE03, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO12setInterruptEhPKvm, symObjAddr: 0x6A0, symBinAddr: 0x6A0, symSize: 0xC }
  - { offsetInCU: 0xFBD8, offset: 0xFE07, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolPMIO10MemoryInfoE, symObjAddr: 0x6AC, symBinAddr: 0xB3B0, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0xFE81, size: 0x4, addend: 0x0, symName: __ZN23VirtualSMCValueVariable14withDictionaryEP12OSDictionary, symObjAddr: 0x0, symBinAddr: 0x6B0, symSize: 0x50 }
  - { offsetInCU: 0x3637, offset: 0x13491, size: 0x4, addend: 0x0, symName: __ZN23VirtualSMCValueVariable14withDictionaryEP12OSDictionary, symObjAddr: 0x0, symBinAddr: 0x6B0, symSize: 0x50 }
  - { offsetInCU: 0x3696, offset: 0x134F0, size: 0x4, addend: 0x0, symName: __ZN23VirtualSMCValueVariable8withDataEPKhhjh14SerializeLevel, symObjAddr: 0x50, symBinAddr: 0x700, symSize: 0x70 }
  - { offsetInCU: 0x37E0, offset: 0x1363A, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueKEY10readAccessEv, symObjAddr: 0xC0, symBinAddr: 0x770, symSize: 0x30 }
  - { offsetInCU: 0x3851, offset: 0x136AB, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueKEY9withStoreEP18VirtualSMCKeystore, symObjAddr: 0xF0, symBinAddr: 0x7A0, symSize: 0x70 }
  - { offsetInCU: 0x39B7, offset: 0x13811, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueCLKT8readTimeEv, symObjAddr: 0x160, symBinAddr: 0x810, symSize: 0x60 }
  - { offsetInCU: 0x3C7E, offset: 0x13AD8, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueCLKT10readAccessEv, symObjAddr: 0x1C0, symBinAddr: 0x870, symSize: 0x90 }
  - { offsetInCU: 0x3E0F, offset: 0x13C69, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueCLKT6updateEPKh, symObjAddr: 0x250, symBinAddr: 0x900, symSize: 0x70 }
  - { offsetInCU: 0x4001, offset: 0x13E5B, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueCLKT9withDeltaEi, symObjAddr: 0x2C0, symBinAddr: 0x970, symSize: 0x70 }
  - { offsetInCU: 0x41E0, offset: 0x1403A, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueCLWK10readAccessEv, symObjAddr: 0x330, symBinAddr: 0x9E0, symSize: 0x100 }
  - { offsetInCU: 0x4291, offset: 0x140EB, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueCLWK6updateEPKh, symObjAddr: 0x430, symBinAddr: 0xAE0, symSize: 0x40 }
  - { offsetInCU: 0x42F8, offset: 0x14152, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueCLWK12withLastWakeEPy, symObjAddr: 0x470, symBinAddr: 0xB20, symSize: 0x90 }
  - { offsetInCU: 0x438D, offset: 0x141E7, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueKPST12withUnlockedEb, symObjAddr: 0x500, symBinAddr: 0xBB0, symSize: 0x70 }
  - { offsetInCU: 0x4407, offset: 0x14261, size: 0x4, addend: 0x0, symName: __ZNK19VirtualSMCValueKPST8unlockedEv, symObjAddr: 0x570, symBinAddr: 0xC20, symSize: 0x10 }
  - { offsetInCU: 0x442A, offset: 0x14284, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueKPST11setUnlockedEb, symObjAddr: 0x580, symBinAddr: 0xC30, symSize: 0x10 }
  - { offsetInCU: 0x445A, offset: 0x142B4, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueKPPW6updateEPKh, symObjAddr: 0x590, symBinAddr: 0xC40, symSize: 0x50 }
  - { offsetInCU: 0x44C1, offset: 0x1431B, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueKPPW8withKPSTEP19VirtualSMCValueKPSTN7SMCInfo10GenerationE, symObjAddr: 0x5E0, symBinAddr: 0xC90, symSize: 0x90 }
  - { offsetInCU: 0x4591, offset: 0x143EB, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueAdr8withAddrEj, symObjAddr: 0x670, symBinAddr: 0xD20, symSize: 0x70 }
  - { offsetInCU: 0x4611, offset: 0x1446B, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueAdr10setAddressEj, symObjAddr: 0x6E0, symBinAddr: 0xD90, symSize: 0x10 }
  - { offsetInCU: 0x4615, offset: 0x1446F, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueNum10readAccessEv, symObjAddr: 0x6F0, symBinAddr: 0xDA0, symSize: 0x10 }
  - { offsetInCU: 0x4705, offset: 0x1455F, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueNum10readAccessEv, symObjAddr: 0x6F0, symBinAddr: 0xDA0, symSize: 0x10 }
  - { offsetInCU: 0x4728, offset: 0x14582, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueNum6updateEPKh, symObjAddr: 0x700, symBinAddr: 0xDB0, symSize: 0x30 }
  - { offsetInCU: 0x47B1, offset: 0x1460B, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueNum7withAdrEP18VirtualSMCValueAdrh, symObjAddr: 0x730, symBinAddr: 0xDE0, symSize: 0x80 }
  - { offsetInCU: 0x48E1, offset: 0x1473B, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueLDLG6updateEPKh, symObjAddr: 0x7B0, symBinAddr: 0xE60, symSize: 0x70 }
  - { offsetInCU: 0x495C, offset: 0x147B6, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueLDLG13withEasterEggEhPKc, symObjAddr: 0x820, symBinAddr: 0xED0, symSize: 0x70 }
  - { offsetInCU: 0x49D4, offset: 0x1482E, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueHBKP6updateEPKh, symObjAddr: 0x890, symBinAddr: 0xF40, symSize: 0x50 }
  - { offsetInCU: 0x4A79, offset: 0x148D3, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueHBKP8withDumpEb, symObjAddr: 0x8E0, symBinAddr: 0xF90, symSize: 0xD0 }
  - { offsetInCU: 0x4BA6, offset: 0x14A00, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueNTOK6updateEPKh, symObjAddr: 0x9B0, symBinAddr: 0x1060, symSize: 0x20 }
  - { offsetInCU: 0x4BAA, offset: 0x14A04, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueNTOK9withStateEb, symObjAddr: 0x9D0, symBinAddr: 0x1080, symSize: 0x70 }
  - { offsetInCU: 0x4C0D, offset: 0x14A67, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueNTOK9withStateEb, symObjAddr: 0x9D0, symBinAddr: 0x1080, symSize: 0x70 }
  - { offsetInCU: 0x4D76, offset: 0x14BD0, size: 0x4, addend: 0x0, symName: __ZN20VirtualSMCValueTimer10readAccessEv, symObjAddr: 0xA40, symBinAddr: 0x10F0, symSize: 0x110 }
  - { offsetInCU: 0x4E8D, offset: 0x14CE7, size: 0x4, addend: 0x0, symName: __ZN20VirtualSMCValueTimer14startCountdownEv, symObjAddr: 0xB50, symBinAddr: 0x1200, symSize: 0x60 }
  - { offsetInCU: 0x4F6C, offset: 0x14DC6, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueNATi6updateEPKh, symObjAddr: 0xBB0, symBinAddr: 0x1260, symSize: 0x40 }
  - { offsetInCU: 0x4FF6, offset: 0x14E50, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueNATi13withCountdownEt, symObjAddr: 0xBF0, symBinAddr: 0x12A0, symSize: 0x80 }
  - { offsetInCU: 0x516E, offset: 0x14FC8, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueNATJ6updateEPKh, symObjAddr: 0xC70, symBinAddr: 0x1320, symSize: 0x90 }
  - { offsetInCU: 0x5249, offset: 0x150A3, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueNATJ8withNATiEP19VirtualSMCValueNATi, symObjAddr: 0xD00, symBinAddr: 0x13B0, symSize: 0x70 }
  - { offsetInCU: 0x5346, offset: 0x151A0, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueOSWD6updateEPKh, symObjAddr: 0xD70, symBinAddr: 0x1420, symSize: 0xB0 }
  - { offsetInCU: 0x542E, offset: 0x15288, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueOSWD13withCountdownEt, symObjAddr: 0xE20, symBinAddr: 0x14D0, symSize: 0x80 }
  - { offsetInCU: 0x5502, offset: 0x1535C, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueEVRD10withEventsEPh, symObjAddr: 0xEA0, symBinAddr: 0x1550, symSize: 0x60 }
  - { offsetInCU: 0x5604, offset: 0x1545E, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueEVCT6updateEPKh, symObjAddr: 0xF00, symBinAddr: 0x15B0, symSize: 0x10 }
  - { offsetInCU: 0x5664, offset: 0x154BE, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueEVCT10withBufferEP19VirtualSMCValueEVRD, symObjAddr: 0xF10, symBinAddr: 0x15C0, symSize: 0x70 }
  - { offsetInCU: 0x5757, offset: 0x155B1, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueEFBS6updateEPKh, symObjAddr: 0xF80, symBinAddr: 0x1630, symSize: 0x10 }
  - { offsetInCU: 0x57B7, offset: 0x15611, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueEFBS14withBootStatusEh, symObjAddr: 0xF90, symBinAddr: 0x1640, symSize: 0x60 }
  - { offsetInCU: 0x58A5, offset: 0x156FF, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueDUSR6updateEPKh, symObjAddr: 0xFF0, symBinAddr: 0x16A0, symSize: 0x20 }
  - { offsetInCU: 0x5909, offset: 0x15763, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueDUSR6createEv, symObjAddr: 0x1010, symBinAddr: 0x16C0, symSize: 0x60 }
  - { offsetInCU: 0x599B, offset: 0x157F5, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueOSK9withIndexEh, symObjAddr: 0x1070, symBinAddr: 0x1720, symSize: 0x260 }
  - { offsetInCU: 0x5A56, offset: 0x158B0, size: 0x4, addend: 0x0, symName: __ZN15VirtualSMCValue11writeAccessEv, symObjAddr: 0x12D0, symBinAddr: 0x1980, symSize: 0x10 }
  - { offsetInCU: 0x5A78, offset: 0x158D2, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueKEYD1Ev, symObjAddr: 0x12E0, symBinAddr: 0x1990, symSize: 0x10 }
  - { offsetInCU: 0x5AA0, offset: 0x158FA, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueKEYD0Ev, symObjAddr: 0x12F0, symBinAddr: 0x19A0, symSize: 0x10 }
  - { offsetInCU: 0x5AC8, offset: 0x15922, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueCLKTD1Ev, symObjAddr: 0x1300, symBinAddr: 0x19B0, symSize: 0x10 }
  - { offsetInCU: 0x5AF0, offset: 0x1594A, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueCLKTD0Ev, symObjAddr: 0x1310, symBinAddr: 0x19C0, symSize: 0x10 }
  - { offsetInCU: 0x5B18, offset: 0x15972, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueCLWKD1Ev, symObjAddr: 0x1320, symBinAddr: 0x19D0, symSize: 0x10 }
  - { offsetInCU: 0x5B40, offset: 0x1599A, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueCLWKD0Ev, symObjAddr: 0x1330, symBinAddr: 0x19E0, symSize: 0x10 }
  - { offsetInCU: 0x5B68, offset: 0x159C2, size: 0x4, addend: 0x0, symName: __ZN15VirtualSMCValue10readAccessEv, symObjAddr: 0x1340, symBinAddr: 0x19F0, symSize: 0x10 }
  - { offsetInCU: 0x5B8A, offset: 0x159E4, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueKPPWD1Ev, symObjAddr: 0x1350, symBinAddr: 0x1A00, symSize: 0x10 }
  - { offsetInCU: 0x5BB2, offset: 0x15A0C, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueKPPWD0Ev, symObjAddr: 0x1360, symBinAddr: 0x1A10, symSize: 0x10 }
  - { offsetInCU: 0x5BDA, offset: 0x15A34, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueNumD1Ev, symObjAddr: 0x1370, symBinAddr: 0x1A20, symSize: 0x10 }
  - { offsetInCU: 0x5C02, offset: 0x15A5C, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueNumD0Ev, symObjAddr: 0x1380, symBinAddr: 0x1A30, symSize: 0x10 }
  - { offsetInCU: 0x5C2A, offset: 0x15A84, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueLDLGD1Ev, symObjAddr: 0x1390, symBinAddr: 0x1A40, symSize: 0x10 }
  - { offsetInCU: 0x5C52, offset: 0x15AAC, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueLDLGD0Ev, symObjAddr: 0x13A0, symBinAddr: 0x1A50, symSize: 0x10 }
  - { offsetInCU: 0x5C7A, offset: 0x15AD4, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueHBKPD1Ev, symObjAddr: 0x13B0, symBinAddr: 0x1A60, symSize: 0x10 }
  - { offsetInCU: 0x5CA2, offset: 0x15AFC, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueHBKPD0Ev, symObjAddr: 0x13C0, symBinAddr: 0x1A70, symSize: 0x10 }
  - { offsetInCU: 0x5CCA, offset: 0x15B24, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueNTOKD1Ev, symObjAddr: 0x13D0, symBinAddr: 0x1A80, symSize: 0x10 }
  - { offsetInCU: 0x5CF2, offset: 0x15B4C, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueNTOKD0Ev, symObjAddr: 0x13E0, symBinAddr: 0x1A90, symSize: 0x10 }
  - { offsetInCU: 0x5D1A, offset: 0x15B74, size: 0x4, addend: 0x0, symName: __ZN20VirtualSMCValueTimerD1Ev, symObjAddr: 0x13F0, symBinAddr: 0x1AA0, symSize: 0x10 }
  - { offsetInCU: 0x5D42, offset: 0x15B9C, size: 0x4, addend: 0x0, symName: __ZN20VirtualSMCValueTimerD0Ev, symObjAddr: 0x1400, symBinAddr: 0x1AB0, symSize: 0x10 }
  - { offsetInCU: 0x5D6A, offset: 0x15BC4, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueNATiD1Ev, symObjAddr: 0x1410, symBinAddr: 0x1AC0, symSize: 0x10 }
  - { offsetInCU: 0x5D92, offset: 0x15BEC, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueNATiD0Ev, symObjAddr: 0x1420, symBinAddr: 0x1AD0, symSize: 0x10 }
  - { offsetInCU: 0x5DBA, offset: 0x15C14, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueNATJD1Ev, symObjAddr: 0x1430, symBinAddr: 0x1AE0, symSize: 0x10 }
  - { offsetInCU: 0x5DE2, offset: 0x15C3C, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueNATJD0Ev, symObjAddr: 0x1440, symBinAddr: 0x1AF0, symSize: 0x10 }
  - { offsetInCU: 0x5E0A, offset: 0x15C64, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueOSWDD1Ev, symObjAddr: 0x1450, symBinAddr: 0x1B00, symSize: 0x10 }
  - { offsetInCU: 0x5E32, offset: 0x15C8C, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueOSWDD0Ev, symObjAddr: 0x1460, symBinAddr: 0x1B10, symSize: 0x10 }
  - { offsetInCU: 0x5E5A, offset: 0x15CB4, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueEVCTD1Ev, symObjAddr: 0x1470, symBinAddr: 0x1B20, symSize: 0x10 }
  - { offsetInCU: 0x5E82, offset: 0x15CDC, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueEVCTD0Ev, symObjAddr: 0x1480, symBinAddr: 0x1B30, symSize: 0x10 }
  - { offsetInCU: 0x5EAA, offset: 0x15D04, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueEFBSD1Ev, symObjAddr: 0x1490, symBinAddr: 0x1B40, symSize: 0x10 }
  - { offsetInCU: 0x5ED2, offset: 0x15D2C, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueEFBSD0Ev, symObjAddr: 0x14A0, symBinAddr: 0x1B50, symSize: 0x10 }
  - { offsetInCU: 0x5EFA, offset: 0x15D54, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueDUSRD1Ev, symObjAddr: 0x14B0, symBinAddr: 0x1B60, symSize: 0x10 }
  - { offsetInCU: 0x5F22, offset: 0x15D7C, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueDUSRD0Ev, symObjAddr: 0x14C0, symBinAddr: 0x1B70, symSize: 0x10 }
  - { offsetInCU: 0x5F4A, offset: 0x15DA4, size: 0x4, addend: 0x0, symName: __ZN15VirtualSMCValueC2Ev, symObjAddr: 0x14D0, symBinAddr: 0x1B80, symSize: 0x100 }
  - { offsetInCU: 0x5F71, offset: 0x15DCB, size: 0x4, addend: 0x0, symName: __ZN23VirtualSMCValueVariableD1Ev, symObjAddr: 0x15D0, symBinAddr: 0x1C80, symSize: 0x10 }
  - { offsetInCU: 0x5F97, offset: 0x15DF1, size: 0x4, addend: 0x0, symName: __ZN23VirtualSMCValueVariableD0Ev, symObjAddr: 0x15E0, symBinAddr: 0x1C90, symSize: 0x10 }
  - { offsetInCU: 0x5FBD, offset: 0x15E17, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueKPSTD1Ev, symObjAddr: 0x15F0, symBinAddr: 0x1CA0, symSize: 0x10 }
  - { offsetInCU: 0x5FE5, offset: 0x15E3F, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueKPSTD0Ev, symObjAddr: 0x1600, symBinAddr: 0x1CB0, symSize: 0x10 }
  - { offsetInCU: 0x600D, offset: 0x15E67, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueAdrD1Ev, symObjAddr: 0x1610, symBinAddr: 0x1CC0, symSize: 0x10 }
  - { offsetInCU: 0x6035, offset: 0x15E8F, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueAdrD0Ev, symObjAddr: 0x1620, symBinAddr: 0x1CD0, symSize: 0x10 }
  - { offsetInCU: 0x605D, offset: 0x15EB7, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueEVRDD1Ev, symObjAddr: 0x1630, symBinAddr: 0x1CE0, symSize: 0x10 }
  - { offsetInCU: 0x6085, offset: 0x15EDF, size: 0x4, addend: 0x0, symName: __ZN19VirtualSMCValueEVRDD0Ev, symObjAddr: 0x1640, symBinAddr: 0x1CF0, symSize: 0x10 }
  - { offsetInCU: 0x60AD, offset: 0x15F07, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueOSKD1Ev, symObjAddr: 0x1650, symBinAddr: 0x1D00, symSize: 0x10 }
  - { offsetInCU: 0x60D5, offset: 0x15F2F, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCValueOSKD0Ev, symObjAddr: 0x1660, symBinAddr: 0x1D10, symSize: 0x9 }
  - { offsetInCU: 0x27, offset: 0x15FA6, size: 0x4, addend: 0x0, symName: __ZN13VirtualSMCAPI15registerHandlerEPFbPvS0_P9IOServiceP10IONotifierES0_, symObjAddr: 0x0, symBinAddr: 0x1D20, symSize: 0x70 }
  - { offsetInCU: 0x35, offset: 0x15FB4, size: 0x4, addend: 0x0, symName: __ZN13VirtualSMCAPI15registerHandlerEPFbPvS0_P9IOServiceP10IONotifierES0_, symObjAddr: 0x0, symBinAddr: 0x1D20, symSize: 0x70 }
  - { offsetInCU: 0x96, offset: 0x16015, size: 0x4, addend: 0x0, symName: __ZN13VirtualSMCAPI13postInterruptEhPKvj, symObjAddr: 0x70, symBinAddr: 0x1D90, symSize: 0x10 }
  - { offsetInCU: 0xDD, offset: 0x1605C, size: 0x4, addend: 0x0, symName: __ZN13VirtualSMCAPI13getDeviceInfoER7SMCInfo, symObjAddr: 0x80, symBinAddr: 0x1DA0, symSize: 0x40 }
  - { offsetInCU: 0x13D, offset: 0x160BC, size: 0x4, addend: 0x0, symName: __ZN13VirtualSMCAPI6addKeyEjR7evectorIR18VirtualSMCKeyValueXadL_ZNS1_7deleterES2_EEEP15VirtualSMCValue, symObjAddr: 0xC0, symBinAddr: 0x1DE0, symSize: 0xA0 }
  - { offsetInCU: 0x1D4, offset: 0x16153, size: 0x4, addend: 0x0, symName: __ZN13VirtualSMCAPI13valueWithDataEPKhhjP15VirtualSMCValueh14SerializeLevel, symObjAddr: 0x160, symBinAddr: 0x1E80, symSize: 0x1A0 }
  - { offsetInCU: 0x277, offset: 0x161F6, size: 0x4, addend: 0x0, symName: __ZN13VirtualSMCAPI8decodeSpEjt, symObjAddr: 0x300, symBinAddr: 0x2020, symSize: 0x170 }
  - { offsetInCU: 0x318, offset: 0x16297, size: 0x4, addend: 0x0, symName: __ZN13VirtualSMCAPI8encodeSpEjd, symObjAddr: 0x470, symBinAddr: 0x2190, symSize: 0x190 }
  - { offsetInCU: 0x3B3, offset: 0x16332, size: 0x4, addend: 0x0, symName: __ZN13VirtualSMCAPI8decodeFpEjt, symObjAddr: 0x600, symBinAddr: 0x2320, symSize: 0x150 }
  - { offsetInCU: 0x450, offset: 0x163CF, size: 0x4, addend: 0x0, symName: __ZN13VirtualSMCAPI8encodeFpEjd, symObjAddr: 0x750, symBinAddr: 0x2470, symSize: 0x170 }
  - { offsetInCU: 0x4EE, offset: 0x1646D, size: 0x4, addend: 0x0, symName: __ZN13VirtualSMCAPI11decodeIntSpEjt, symObjAddr: 0x8C0, symBinAddr: 0x25E0, symSize: 0x170 }
  - { offsetInCU: 0x574, offset: 0x164F3, size: 0x4, addend: 0x0, symName: __ZN13VirtualSMCAPI11encodeIntSpEjs, symObjAddr: 0xA30, symBinAddr: 0x2750, symSize: 0x170 }
  - { offsetInCU: 0x5F8, offset: 0x16577, size: 0x4, addend: 0x0, symName: __ZN13VirtualSMCAPI11decodeIntFpEjt, symObjAddr: 0xBA0, symBinAddr: 0x28C0, symSize: 0x150 }
  - { offsetInCU: 0x680, offset: 0x165FF, size: 0x4, addend: 0x0, symName: __ZN13VirtualSMCAPI11encodeIntFpEjt, symObjAddr: 0xCF0, symBinAddr: 0x2A10, symSize: 0x13A }
  - { offsetInCU: 0x27, offset: 0x19B1B, size: 0x4, addend: 0x0, symName: _VirtualSMC_kern_start, symObjAddr: 0x0, symBinAddr: 0x2B50, symSize: 0x160 }
  - { offsetInCU: 0x3D, offset: 0x19B31, size: 0x4, addend: 0x0, symName: _VirtualSMC_startSuccess, symObjAddr: 0x16D98, symBinAddr: 0xD0DC, symSize: 0x0 }
  - { offsetInCU: 0x57, offset: 0x19B4B, size: 0x4, addend: 0x0, symName: _VirtualSMC_debugEnabled, symObjAddr: 0x16D99, symBinAddr: 0xD0DD, symSize: 0x0 }
  - { offsetInCU: 0x6A, offset: 0x19B5E, size: 0x4, addend: 0x0, symName: _VirtualSMC_debugPrintDelay, symObjAddr: 0x16D9C, symBinAddr: 0xD0E0, symSize: 0x0 }
  - { offsetInCU: 0x4A49, offset: 0x1E53D, size: 0x4, addend: 0x0, symName: _VirtualSMC_kern_start, symObjAddr: 0x0, symBinAddr: 0x2B50, symSize: 0x160 }
  - { offsetInCU: 0x4B3E, offset: 0x1E632, size: 0x4, addend: 0x0, symName: _VirtualSMC_kern_stop, symObjAddr: 0x160, symBinAddr: 0x2CB0, symSize: 0xF }
  - { offsetInCU: 0x27, offset: 0x1E688, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x2CC0, symSize: 0x30 }
  - { offsetInCU: 0x36, offset: 0x1E697, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC10gMetaClassE, symObjAddr: 0x32E80, symBinAddr: 0xD0E4, symSize: 0x0 }
  - { offsetInCU: 0xA7C1, offset: 0x28E22, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC9metaClassE, symObjAddr: 0x2AB8, symBinAddr: 0xB700, symSize: 0x0 }
  - { offsetInCU: 0xA7D0, offset: 0x28E31, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC10superClassE, symObjAddr: 0x2ABC, symBinAddr: 0xB704, symSize: 0x0 }
  - { offsetInCU: 0xA7DF, offset: 0x28E40, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC8instanceE, symObjAddr: 0x32E98, symBinAddr: 0xD0FC, symSize: 0x0 }
  - { offsetInCU: 0xA7EE, offset: 0x28E4F, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC9mmioReadyE, symObjAddr: 0x32E9C, symBinAddr: 0xD100, symSize: 0x0 }
  - { offsetInCU: 0xA7FD, offset: 0x28E5E, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC14servicingReadyE, symObjAddr: 0x32E9D, symBinAddr: 0xD101, symSize: 0x0 }
  - { offsetInCU: 0xA812, offset: 0x28E73, size: 0x4, addend: 0x0, symName: __ZL11kextVersion, symObjAddr: 0x3154, symBinAddr: 0xBD9C, symSize: 0x0 }
  - { offsetInCU: 0xB69F, offset: 0x29D00, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC9MetaClassC1Ev, symObjAddr: 0x0, symBinAddr: 0x2CC0, symSize: 0x30 }
  - { offsetInCU: 0xB6EF, offset: 0x29D50, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC9MetaClassD1Ev, symObjAddr: 0x30, symBinAddr: 0x2CF0, symSize: 0x10 }
  - { offsetInCU: 0xB7EA, offset: 0x29E4B, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMCC2EPK11OSMetaClass, symObjAddr: 0x40, symBinAddr: 0x2D00, symSize: 0xF0 }
  - { offsetInCU: 0xB8C7, offset: 0x29F28, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMCC1EPK11OSMetaClass, symObjAddr: 0x130, symBinAddr: 0x2DF0, symSize: 0xF0 }
  - { offsetInCU: 0xB9B0, offset: 0x2A011, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMCD2Ev, symObjAddr: 0x220, symBinAddr: 0x2EE0, symSize: 0x10 }
  - { offsetInCU: 0xB9E7, offset: 0x2A048, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMCD1Ev, symObjAddr: 0x230, symBinAddr: 0x2EF0, symSize: 0x10 }
  - { offsetInCU: 0xBA37, offset: 0x2A098, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMCD0Ev, symObjAddr: 0x240, symBinAddr: 0x2F00, symSize: 0x30 }
  - { offsetInCU: 0xBA91, offset: 0x2A0F2, size: 0x4, addend: 0x0, symName: __ZNK10VirtualSMC12getMetaClassEv, symObjAddr: 0x270, symBinAddr: 0x2F30, symSize: 0x10 }
  - { offsetInCU: 0xBAB1, offset: 0x2A112, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC9MetaClassC2Ev, symObjAddr: 0x280, symBinAddr: 0x2F40, symSize: 0x30 }
  - { offsetInCU: 0xBB03, offset: 0x2A164, size: 0x4, addend: 0x0, symName: __ZNK10VirtualSMC9MetaClass5allocEv, symObjAddr: 0x2B0, symBinAddr: 0x2F70, symSize: 0x100 }
  - { offsetInCU: 0xBBE5, offset: 0x2A246, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMCC1Ev, symObjAddr: 0x3B0, symBinAddr: 0x3070, symSize: 0x100 }
  - { offsetInCU: 0xBCAC, offset: 0x2A30D, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMCC2Ev, symObjAddr: 0x4B0, symBinAddr: 0x3170, symSize: 0x100 }
  - { offsetInCU: 0xBD96, offset: 0x2A3F7, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC5probeEP9IOServicePl, symObjAddr: 0x5B0, symBinAddr: 0x3270, symSize: 0x90 }
  - { offsetInCU: 0xC015, offset: 0x2A676, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC5startEP9IOService, symObjAddr: 0x640, symBinAddr: 0x3300, symSize: 0xAE0 }
  - { offsetInCU: 0xC35F, offset: 0x2A9C0, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC14devicesPresentEP9IOService, symObjAddr: 0x1120, symBinAddr: 0x3DE0, symSize: 0x2D0 }
  - { offsetInCU: 0xC464, offset: 0x2AAC5, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC14watchDogActionEP8OSObjectP18IOTimerEventSource, symObjAddr: 0x13F0, symBinAddr: 0x40B0, symSize: 0x120 }
  - { offsetInCU: 0xC548, offset: 0x2ABA9, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC21obtainBooterModelInfoER7SMCInfo, symObjAddr: 0x1510, symBinAddr: 0x41D0, symSize: 0x320 }
  - { offsetInCU: 0xC706, offset: 0x2AD67, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC15obtainModelInfoER7SMCInfoPKcPK12OSDictionaryS6_, symObjAddr: 0x1830, symBinAddr: 0x44F0, symSize: 0x1D0 }
  - { offsetInCU: 0xC950, offset: 0x2AFB1, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC16forcedGenerationEv, symObjAddr: 0x1A00, symBinAddr: 0x46C0, symSize: 0x60 }
  - { offsetInCU: 0xC99F, offset: 0x2B000, size: 0x4, addend: 0x0, symName: '__ZZN10VirtualSMC15obtainModelInfoER7SMCInfoPKcPK12OSDictionaryS6_ENK3$_0clES3_b', symObjAddr: 0x1A60, symBinAddr: 0x4720, symSize: 0x320 }
  - { offsetInCU: 0xCCEB, offset: 0x2B34C, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC4stopEP9IOService, symObjAddr: 0x1D80, symBinAddr: 0x4A40, symSize: 0x30 }
  - { offsetInCU: 0xCD1D, offset: 0x2B37E, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC13setInterruptsEb, symObjAddr: 0x1DB0, symBinAddr: 0x4A70, symSize: 0x40 }
  - { offsetInCU: 0xCD70, offset: 0x2B3D1, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC13postInterruptEhPKvj, symObjAddr: 0x1DF0, symBinAddr: 0x4AB0, symSize: 0x340 }
  - { offsetInCU: 0xCD74, offset: 0x2B3D5, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC12getInterruptEv, symObjAddr: 0x2130, symBinAddr: 0x4DF0, symSize: 0x120 }
  - { offsetInCU: 0xCE8B, offset: 0x2B4EC, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC12getInterruptEv, symObjAddr: 0x2130, symBinAddr: 0x4DF0, symSize: 0x120 }
  - { offsetInCU: 0xCEF8, offset: 0x2B559, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC15postWatchDogJobEhyb, symObjAddr: 0x2250, symBinAddr: 0x4F10, symSize: 0xA0 }
  - { offsetInCU: 0xCF59, offset: 0x2B5BA, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC24mapDeviceMemoryWithIndexEjm, symObjAddr: 0x22F0, symBinAddr: 0x4FB0, symSize: 0x40 }
  - { offsetInCU: 0xCFB4, offset: 0x2B615, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC8ioVerifyERtP11IOMemoryMap, symObjAddr: 0x2330, symBinAddr: 0x4FF0, symSize: 0xD0 }
  - { offsetInCU: 0xCFFF, offset: 0x2B660, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC9ioWrite32EtmP11IOMemoryMap, symObjAddr: 0x2400, symBinAddr: 0x50C0, symSize: 0x30 }
  - { offsetInCU: 0xD04E, offset: 0x2B6AF, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC8ioRead32EtP11IOMemoryMap, symObjAddr: 0x2430, symBinAddr: 0x50F0, symSize: 0x20 }
  - { offsetInCU: 0xD08D, offset: 0x2B6EE, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC9ioWrite16EttP11IOMemoryMap, symObjAddr: 0x2450, symBinAddr: 0x5110, symSize: 0x30 }
  - { offsetInCU: 0xD0DC, offset: 0x2B73D, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC8ioRead16EtP11IOMemoryMap, symObjAddr: 0x2480, symBinAddr: 0x5140, symSize: 0x20 }
  - { offsetInCU: 0xD14E, offset: 0x2B7AF, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC8ioWrite8EthP11IOMemoryMap, symObjAddr: 0x24A0, symBinAddr: 0x5160, symSize: 0x120 }
  - { offsetInCU: 0xD1E9, offset: 0x2B84A, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC7ioRead8EtP11IOMemoryMap, symObjAddr: 0x25C0, symBinAddr: 0x5280, symSize: 0x150 }
  - { offsetInCU: 0xD2A8, offset: 0x2B909, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC17registerInterruptEiP8OSObjectPFvS1_PvP9IOServiceiES2_, symObjAddr: 0x2710, symBinAddr: 0x53D0, symSize: 0x120 }
  - { offsetInCU: 0xD3C9, offset: 0x2BA2A, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC19unregisterInterruptEi, symObjAddr: 0x2830, symBinAddr: 0x54F0, symSize: 0x70 }
  - { offsetInCU: 0xD440, offset: 0x2BAA1, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC16getInterruptTypeEiPi, symObjAddr: 0x28A0, symBinAddr: 0x5560, symSize: 0x20 }
  - { offsetInCU: 0xD444, offset: 0x2BAA5, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC15enableInterruptEi, symObjAddr: 0x28C0, symBinAddr: 0x5580, symSize: 0x40 }
  - { offsetInCU: 0xD49C, offset: 0x2BAFD, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC15enableInterruptEi, symObjAddr: 0x28C0, symBinAddr: 0x5580, symSize: 0x40 }
  - { offsetInCU: 0xD4E9, offset: 0x2BB4A, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC16disableInterruptEi, symObjAddr: 0x2900, symBinAddr: 0x55C0, symSize: 0x40 }
  - { offsetInCU: 0xD536, offset: 0x2BB97, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC14causeInterruptEi, symObjAddr: 0x2940, symBinAddr: 0x5600, symSize: 0x60 }
  - { offsetInCU: 0xD53A, offset: 0x2BB9B, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC13setPowerStateEmP9IOService, symObjAddr: 0x29A0, symBinAddr: 0x5660, symSize: 0x40 }
  - { offsetInCU: 0xD586, offset: 0x2BBE7, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC13setPowerStateEmP9IOService, symObjAddr: 0x29A0, symBinAddr: 0x5660, symSize: 0x40 }
  - { offsetInCU: 0xD5C8, offset: 0x2BC29, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC20callPlatformFunctionEPK8OSSymbolbPvS3_S3_S3_, symObjAddr: 0x29E0, symBinAddr: 0x56A0, symSize: 0x70 }
  - { offsetInCU: 0xD652, offset: 0x2BCB3, size: 0x4, addend: 0x0, symName: __ZN10VirtualSMC9MetaClassD0Ev, symObjAddr: 0x2A50, symBinAddr: 0x5710, symSize: 0x10 }
  - { offsetInCU: 0xD6D1, offset: 0x2BD32, size: 0x4, addend: 0x0, symName: __GLOBAL__sub_I_kern_vsmc.cpp, symObjAddr: 0x2A60, symBinAddr: 0x5720, symSize: 0x40 }
  - { offsetInCU: 0xD723, offset: 0x2BD84, size: 0x4, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x2AA0, symBinAddr: 0x5760, symSize: 0x17 }
  - { offsetInCU: 0x27, offset: 0x2BFE6, size: 0x4, addend: 0x0, symName: __ZN10EfiBackend21detectFirmwareBackendEv, symObjAddr: 0x0, symBinAddr: 0x5780, symSize: 0x280 }
  - { offsetInCU: 0x167, offset: 0x2C126, size: 0x4, addend: 0x0, symName: __ZN10EfiBackend21detectFirmwareBackendEv, symObjAddr: 0x0, symBinAddr: 0x5780, symSize: 0x280 }
  - { offsetInCU: 0x2CB, offset: 0x2C28A, size: 0x4, addend: 0x0, symName: __ZN10EfiBackend19submitEncryptionKeyEPKhb, symObjAddr: 0x280, symBinAddr: 0x5A00, symSize: 0x4E0 }
  - { offsetInCU: 0x5AA, offset: 0x2C569, size: 0x4, addend: 0x0, symName: __ZN10EfiBackend22eraseTempEncryptionKeyEv, symObjAddr: 0x760, symBinAddr: 0x5EE0, symSize: 0x1B0 }
  - { offsetInCU: 0x681, offset: 0x2C640, size: 0x4, addend: 0x0, symName: __ZN10EfiBackend11readSerialsEPhmS0_m, symObjAddr: 0x910, symBinAddr: 0x6090, symSize: 0x16D }
  - { offsetInCU: 0x27, offset: 0x354B0, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolMMIO10submitDataEv, symObjAddr: 0x0, symBinAddr: 0x6200, symSize: 0xB0 }
  - { offsetInCU: 0x2B, offset: 0x354B4, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolMMIO10MemoryInfoE, symObjAddr: 0x568, symBinAddr: 0xBDB0, symSize: 0x0 }
  - { offsetInCU: 0x3A, offset: 0x354C3, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolMMIO10MemoryInfoE, symObjAddr: 0x568, symBinAddr: 0xBDB0, symSize: 0x0 }
  - { offsetInCU: 0x459, offset: 0x358E2, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolMMIO14MemoryInfoSizeE, symObjAddr: 0x5A4, symBinAddr: 0xBDEC, symSize: 0x0 }
  - { offsetInCU: 0x55C, offset: 0x359E5, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolMMIO10submitDataEv, symObjAddr: 0x0, symBinAddr: 0x6200, symSize: 0xB0 }
  - { offsetInCU: 0x667, offset: 0x35AF0, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolMMIO10handleReadEyy, symObjAddr: 0xB0, symBinAddr: 0x62B0, symSize: 0x70 }
  - { offsetInCU: 0x8C9, offset: 0x35D52, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolMMIO11handleWriteEyy, symObjAddr: 0x120, symBinAddr: 0x6320, symSize: 0x1E0 }
  - { offsetInCU: 0xAD9, offset: 0x35F62, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolMMIO9readValueEv, symObjAddr: 0x300, symBinAddr: 0x6500, symSize: 0x80 }
  - { offsetInCU: 0xB47, offset: 0x35FD0, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolMMIO10writeValueEv, symObjAddr: 0x380, symBinAddr: 0x6580, symSize: 0x40 }
  - { offsetInCU: 0xB85, offset: 0x3600E, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolMMIO15getKeyFromIndexEv, symObjAddr: 0x3C0, symBinAddr: 0x65C0, symSize: 0x70 }
  - { offsetInCU: 0xBE3, offset: 0x3606C, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolMMIO10getKeyInfoEv, symObjAddr: 0x430, symBinAddr: 0x6630, symSize: 0x80 }
  - { offsetInCU: 0xC7D, offset: 0x36106, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolMMIO5resetEv, symObjAddr: 0x4B0, symBinAddr: 0x66B0, symSize: 0x10 }
  - { offsetInCU: 0xCCB, offset: 0x36154, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolMMIO12setInterruptEhPKvm, symObjAddr: 0x4C0, symBinAddr: 0x66C0, symSize: 0xA8 }
  - { offsetInCU: 0xCCF, offset: 0x36158, size: 0x4, addend: 0x0, symName: __ZN15SMCProtocolMMIO10MemoryInfoE, symObjAddr: 0x568, symBinAddr: 0xBDB0, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x361F8, size: 0x4, addend: 0x0, symName: '__ZN3$_08__invokeEv', symObjAddr: 0x0, symBinAddr: 0x6770, symSize: 0x51 }
  - { offsetInCU: 0x3D, offset: 0x3620E, size: 0x4, addend: 0x0, symName: _VirtualSMC_config, symObjAddr: 0x84, symBinAddr: 0xD0AC, symSize: 0x0 }
  - { offsetInCU: 0x12B, offset: 0x362FC, size: 0x4, addend: 0x0, symName: __ZL10bootargOff, symObjAddr: 0x78, symBinAddr: 0xD0A0, symSize: 0x0 }
  - { offsetInCU: 0x154, offset: 0x36325, size: 0x4, addend: 0x0, symName: __ZL12bootargDebug, symObjAddr: 0x7C, symBinAddr: 0xD0A4, symSize: 0x0 }
  - { offsetInCU: 0x16A, offset: 0x3633B, size: 0x4, addend: 0x0, symName: __ZL11bootargBeta, symObjAddr: 0x80, symBinAddr: 0xD0A8, symSize: 0x0 }
  - { offsetInCU: 0x32A, offset: 0x364FB, size: 0x4, addend: 0x0, symName: '__ZN3$_08__invokeEv', symObjAddr: 0x0, symBinAddr: 0x6770, symSize: 0x51 }
  - { offsetInCU: 0x27, offset: 0x36584, size: 0x4, addend: 0x0, symName: __ZNK18VirtualSMCKeyValue12serializableEb, symObjAddr: 0x0, symBinAddr: 0x67D0, symSize: 0x30 }
  - { offsetInCU: 0xB8, offset: 0x36615, size: 0x4, addend: 0x0, symName: __ZNK18VirtualSMCKeyValue12serializableEb, symObjAddr: 0x0, symBinAddr: 0x67D0, symSize: 0x30 }
  - { offsetInCU: 0x104, offset: 0x36661, size: 0x4, addend: 0x0, symName: __ZNK18VirtualSMCKeyValue14serializedSizeEv, symObjAddr: 0x30, symBinAddr: 0x6800, symSize: 0x20 }
  - { offsetInCU: 0x127, offset: 0x36684, size: 0x4, addend: 0x0, symName: __ZNK18VirtualSMCKeyValue9serializeERPh, symObjAddr: 0x50, symBinAddr: 0x6820, symSize: 0x70 }
  - { offsetInCU: 0x158, offset: 0x366B5, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeyValue11deserializeERPKhRjS3_PhRh, symObjAddr: 0xC0, symBinAddr: 0x6890, symSize: 0x86 }
  - { offsetInCU: 0x27, offset: 0x36739, size: 0x4, addend: 0x0, symName: __ZN15VirtualSMCValue4initEPKhhjh14SerializeLevel, symObjAddr: 0x0, symBinAddr: 0x6920, symSize: 0x60 }
  - { offsetInCU: 0x16CB, offset: 0x37DDD, size: 0x4, addend: 0x0, symName: __ZN15VirtualSMCValue4initEPKhhjh14SerializeLevel, symObjAddr: 0x0, symBinAddr: 0x6920, symSize: 0x60 }
  - { offsetInCU: 0x185A, offset: 0x37F6C, size: 0x4, addend: 0x0, symName: __ZN15VirtualSMCValue4initEPK12OSDictionary, symObjAddr: 0x60, symBinAddr: 0x6980, symSize: 0x260 }
  - { offsetInCU: 0x19A5, offset: 0x380B7, size: 0x4, addend: 0x0, symName: __ZNK15VirtualSMCValue3getERh, symObjAddr: 0x2C0, symBinAddr: 0x6BE0, symSize: 0x20 }
  - { offsetInCU: 0x19D6, offset: 0x380E8, size: 0x4, addend: 0x0, symName: __ZN15VirtualSMCValue6updateEPKh, symObjAddr: 0x2E0, symBinAddr: 0x6C00, symSize: 0x30 }
  - { offsetInCU: 0x1A07, offset: 0x38119, size: 0x4, addend: 0x0, symName: __ZN15VirtualSMCValue10readAccessEv, symObjAddr: 0x310, symBinAddr: 0x6C30, symSize: 0x10 }
  - { offsetInCU: 0x1A29, offset: 0x3813B, size: 0x4, addend: 0x0, symName: __ZN15VirtualSMCValue11writeAccessEv, symObjAddr: 0x320, symBinAddr: 0x6C40, symSize: 0x10 }
  - { offsetInCU: 0x1A4B, offset: 0x3815D, size: 0x4, addend: 0x0, symName: __ZN15VirtualSMCValueD1Ev, symObjAddr: 0x330, symBinAddr: 0x6C50, symSize: 0x10 }
  - { offsetInCU: 0x1A71, offset: 0x38183, size: 0x4, addend: 0x0, symName: __ZN15VirtualSMCValueD0Ev, symObjAddr: 0x340, symBinAddr: 0x6C60, symSize: 0x9 }
  - { offsetInCU: 0x27, offset: 0x381D5, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCProvider4initEv, symObjAddr: 0x0, symBinAddr: 0x6C70, symSize: 0x183 }
  - { offsetInCU: 0x36, offset: 0x381E4, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCProvider8instanceE, symObjAddr: 0x229E8, symBinAddr: 0xD104, symSize: 0x0 }
  - { offsetInCU: 0x97, offset: 0x38245, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCProvider15firstGenerationE, symObjAddr: 0x229EC, symBinAddr: 0xD108, symSize: 0x0 }
  - { offsetInCU: 0xA0, offset: 0x3824E, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCProvider4initEv, symObjAddr: 0x0, symBinAddr: 0x6C70, symSize: 0x183 }
  - { offsetInCU: 0x27, offset: 0x382EB, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore4initEPK12OSDictionaryS2_RK7SMCInfoPKcib, symObjAddr: 0x0, symBinAddr: 0x6E00, symSize: 0x240 }
  - { offsetInCU: 0x2FA0, offset: 0x3B264, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore4initEPK12OSDictionaryS2_RK7SMCInfoPKcib, symObjAddr: 0x0, symBinAddr: 0x6E00, symSize: 0x240 }
  - { offsetInCU: 0x3134, offset: 0x3B3F8, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore6addKeyEjP15VirtualSMCValueb, symObjAddr: 0x240, symBinAddr: 0x7040, symSize: 0xB0 }
  - { offsetInCU: 0x3229, offset: 0x3B4ED, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore15mergePredefinedEPKci, symObjAddr: 0x2F0, symBinAddr: 0x70F0, symSize: 0x2380 }
  - { offsetInCU: 0x5CE9, offset: 0x3DFAD, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore13mergeProviderEPK12OSDictionaryPKci, symObjAddr: 0x2670, symBinAddr: 0x9470, symSize: 0x160 }
  - { offsetInCU: 0x5EE8, offset: 0x3E1AC, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeyValue7compareEPKvS1_, symObjAddr: 0x27D0, symBinAddr: 0x95D0, symSize: 0x30 }
  - { offsetInCU: 0x6054, offset: 0x3E318, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore14findAccessKeysEv, symObjAddr: 0x2800, symBinAddr: 0x9600, symSize: 0x240 }
  - { offsetInCU: 0x6423, offset: 0x3E6E7, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore5mergeEPK7OSArray, symObjAddr: 0x2A40, symBinAddr: 0x9840, symSize: 0x1F0 }
  - { offsetInCU: 0x657A, offset: 0x3E83E, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore14handlePowerOffEv, symObjAddr: 0x2C30, symBinAddr: 0x9A30, symSize: 0x50 }
  - { offsetInCU: 0x65B7, offset: 0x3E87B, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore13handlePowerOnEv, symObjAddr: 0x2C80, symBinAddr: 0x9A80, symSize: 0x50 }
  - { offsetInCU: 0x6750, offset: 0x3EA14, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore10loadPluginEPN13VirtualSMCAPI6PluginE, symObjAddr: 0x2CD0, symBinAddr: 0x9AD0, symSize: 0x870 }
  - { offsetInCU: 0x6B92, offset: 0x3EE56, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore9getByNameER7evectorIR18VirtualSMCKeyValueXadL_ZNS1_7deleterES2_EEEjRPS1_, symObjAddr: 0x3540, symBinAddr: 0xA340, symSize: 0x80 }
  - { offsetInCU: 0x6C38, offset: 0x3EEFC, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore18getPublicKeyAmountEv, symObjAddr: 0x35C0, symBinAddr: 0xA3C0, symSize: 0xC0 }
  - { offsetInCU: 0x6C9C, offset: 0x3EF60, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore11deserializeEPKhj, symObjAddr: 0x3680, symBinAddr: 0xA480, symSize: 0xC0 }
  - { offsetInCU: 0x6D53, offset: 0x3F017, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore9getByNameEjRP18VirtualSMCKeyValueb, symObjAddr: 0x3740, symBinAddr: 0xA540, symSize: 0x1C0 }
  - { offsetInCU: 0x6EDB, offset: 0x3F19F, size: 0x4, addend: 0x0, symName: __ZNK18VirtualSMCKeystore9serializeERm, symObjAddr: 0x3900, symBinAddr: 0xA700, symSize: 0x1E0 }
  - { offsetInCU: 0x71E0, offset: 0x3F4A4, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore10getByIndexEjRP18VirtualSMCKeyValue, symObjAddr: 0x3AE0, symBinAddr: 0xA8E0, symSize: 0x190 }
  - { offsetInCU: 0x72A6, offset: 0x3F56A, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore15readValueByNameEjRPK15VirtualSMCValue, symObjAddr: 0x3C70, symBinAddr: 0xAA70, symSize: 0xE0 }
  - { offsetInCU: 0x7329, offset: 0x3F5ED, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore15readNameByIndexEjRj, symObjAddr: 0x3D50, symBinAddr: 0xAB50, symSize: 0x50 }
  - { offsetInCU: 0x7390, offset: 0x3F654, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore16writeValueByNameEjPKh, symObjAddr: 0x3DA0, symBinAddr: 0xABA0, symSize: 0xF0 }
  - { offsetInCU: 0x7413, offset: 0x3F6D7, size: 0x4, addend: 0x0, symName: __ZN18VirtualSMCKeystore13getInfoByNameEjRhRjS0_, symObjAddr: 0x3E90, symBinAddr: 0xAC90, symSize: 0xB7 }
...
