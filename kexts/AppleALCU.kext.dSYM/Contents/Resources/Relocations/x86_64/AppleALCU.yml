---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/work/AppleALC/AppleALC/build/Release/AppleALCU.kext/Contents/MacOS/AppleALCU'
relocations:
  - { offsetInCU: 0x35, offset: 0x35, size: 0x8, addend: 0x0, symName: _kmod_info, symObjAddr: 0x26B0, symBinAddr: 0x5DE0, symSize: 0x0 }
  - { offsetInCU: 0x206, offset: 0x206, size: 0x8, addend: 0x0, symName: __realmain, symObjAddr: 0x2778, symBinAddr: 0x5EA8, symSize: 0x0 }
  - { offsetInCU: 0x21C, offset: 0x21C, size: 0x8, addend: 0x0, symName: __antimain, symObjAddr: 0x2780, symBinAddr: 0x5EB0, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x24C, size: 0x8, addend: 0x0, symName: '__ZZN10AlcEnabler4initEvEN3$_08__invokeEPvR13KernelPatcher', symObjAddr: 0x0, symBinAddr: 0xFE0, symSize: 0xCF0 }
  - { offsetInCU: 0x3E, offset: 0x263, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler11callbackAlcE, symObjAddr: 0x43F08, symBinAddr: 0x7948, symSize: 0x0 }
  - { offsetInCU: 0x1255C, offset: 0x12781, size: 0x8, addend: 0x0, symName: __ZL10alcEnabler, symObjAddr: 0x2788, symBinAddr: 0x5EB8, symSize: 0x0 }
  - { offsetInCU: 0x169B5, offset: 0x16BDA, size: 0x8, addend: 0x0, symName: '__ZZN10AlcEnabler4initEvEN3$_08__invokeEPvR13KernelPatcher', symObjAddr: 0x0, symBinAddr: 0xFE0, symSize: 0xCF0 }
  - { offsetInCU: 0x174DF, offset: 0x17704, size: 0x8, addend: 0x0, symName: '__ZZN10AlcEnabler4initEvEN3$_18__invokeEPvR13KernelPatchermym', symObjAddr: 0xCF0, symBinAddr: 0x1CD0, symSize: 0x6D0 }
  - { offsetInCU: 0x17CA3, offset: 0x17EC8, size: 0x8, addend: 0x0, symName: '__ZZN10AlcEnabler4initEvEN3$_28__invokeEPvP4taskPKcRP8OSObject', symObjAddr: 0x13C0, symBinAddr: 0x23A0, symSize: 0x50 }
  - { offsetInCU: 0x17D7A, offset: 0x17F9F, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler28IOHDACodecDevice_executeVerbEPvtttPjb, symObjAddr: 0x1410, symBinAddr: 0x23F0, symSize: 0x10 }
  - { offsetInCU: 0x17DDD, offset: 0x18002, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler24AppleHDAController_startEP9IOServiceS1_, symObjAddr: 0x1420, symBinAddr: 0x2400, symSize: 0x120 }
  - { offsetInCU: 0x18121, offset: 0x18346, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler16insertControllerEjjjbjjP15IORegistryEntry, symObjAddr: 0x1540, symBinAddr: 0x2520, symSize: 0x120 }
  - { offsetInCU: 0x1840B, offset: 0x18630, size: 0x8, addend: 0x0, symName: __ZN10AlcEnabler22updateDevicePropertiesEP15IORegistryEntryP10DeviceInfoPKcb, symObjAddr: 0x1660, symBinAddr: 0x2640, symSize: 0x100 }
  - { offsetInCU: 0x27, offset: 0x1893D, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderD1Ev, symObjAddr: 0x1760, symBinAddr: 0x2740, symSize: 0x10 }
  - { offsetInCU: 0x3E, offset: 0x18954, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider10gMetaClassE, symObjAddr: 0x43E80, symBinAddr: 0x78C0, symSize: 0x0 }
  - { offsetInCU: 0x305, offset: 0x18C1B, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider9metaClassE, symObjAddr: 0x5F28, symBinAddr: 0x5478, symSize: 0x0 }
  - { offsetInCU: 0x31C, offset: 0x18C32, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider10superClassE, symObjAddr: 0x4B40, symBinAddr: 0x4090, symSize: 0x0 }
  - { offsetInCU: 0x35A, offset: 0x18C70, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderD1Ev, symObjAddr: 0x1760, symBinAddr: 0x2740, symSize: 0x10 }
  - { offsetInCU: 0x3D6, offset: 0x18CEC, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderD0Ev, symObjAddr: 0x1770, symBinAddr: 0x2750, symSize: 0x30 }
  - { offsetInCU: 0x464, offset: 0x18D7A, size: 0x8, addend: 0x0, symName: __ZNK21ALCUserClientProvider12getMetaClassEv, symObjAddr: 0x17A0, symBinAddr: 0x2780, symSize: 0x10 }
  - { offsetInCU: 0x54D, offset: 0x18E63, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider5probeEP9IOServicePi, symObjAddr: 0x17B0, symBinAddr: 0x2790, symSize: 0x100 }
  - { offsetInCU: 0x67D, offset: 0x18F93, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider5startEP9IOService, symObjAddr: 0x18B0, symBinAddr: 0x2890, symSize: 0x60 }
  - { offsetInCU: 0x6C7, offset: 0x18FDD, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider4stopEP9IOService, symObjAddr: 0x1910, symBinAddr: 0x28F0, symSize: 0x20 }
  - { offsetInCU: 0x70D, offset: 0x19023, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider14sendHdaCommandEttt, symObjAddr: 0x1930, symBinAddr: 0x2910, symSize: 0x60 }
  - { offsetInCU: 0x83A, offset: 0x19150, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassD1Ev, symObjAddr: 0x1990, symBinAddr: 0x2970, symSize: 0x10 }
  - { offsetInCU: 0x8B6, offset: 0x191CC, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassD0Ev, symObjAddr: 0x19A0, symBinAddr: 0x2980, symSize: 0x10 }
  - { offsetInCU: 0x98C, offset: 0x192A2, size: 0x8, addend: 0x0, symName: __ZNK21ALCUserClientProvider9MetaClass5allocEv, symObjAddr: 0x19B0, symBinAddr: 0x2990, symSize: 0x60 }
  - { offsetInCU: 0xA65, offset: 0x1937B, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_ALCUserClientProvider.cpp, symObjAddr: 0x1A10, symBinAddr: 0x29F0, symSize: 0x40 }
  - { offsetInCU: 0xAF5, offset: 0x1940B, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a, symObjAddr: 0x1A50, symBinAddr: 0x2A30, symSize: 0x20 }
  - { offsetInCU: 0xB67, offset: 0x1947D, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassC1Ev, symObjAddr: 0x1A70, symBinAddr: 0x2A50, symSize: 0x40 }
  - { offsetInCU: 0xBC2, offset: 0x194D8, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderC2EPK11OSMetaClass, symObjAddr: 0x1AB0, symBinAddr: 0x2A90, symSize: 0x40 }
  - { offsetInCU: 0xC33, offset: 0x19549, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderC1EPK11OSMetaClass, symObjAddr: 0x1AF0, symBinAddr: 0x2AD0, symSize: 0x40 }
  - { offsetInCU: 0xCBB, offset: 0x195D1, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderD2Ev, symObjAddr: 0x1B30, symBinAddr: 0x2B10, symSize: 0x10 }
  - { offsetInCU: 0xCE8, offset: 0x195FE, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProvider9MetaClassC2Ev, symObjAddr: 0x1B40, symBinAddr: 0x2B20, symSize: 0x40 }
  - { offsetInCU: 0xD17, offset: 0x1962D, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderC1Ev, symObjAddr: 0x1B80, symBinAddr: 0x2B60, symSize: 0x50 }
  - { offsetInCU: 0xD72, offset: 0x19688, size: 0x8, addend: 0x0, symName: __ZN21ALCUserClientProviderC2Ev, symObjAddr: 0x1BD0, symBinAddr: 0x2BB0, symSize: 0x50 }
  - { offsetInCU: 0x27, offset: 0x196EF, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientD1Ev, symObjAddr: 0x1C20, symBinAddr: 0x2C00, symSize: 0x10 }
  - { offsetInCU: 0x3E, offset: 0x19706, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient10gMetaClassE, symObjAddr: 0x43EA8, symBinAddr: 0x78E8, symSize: 0x0 }
  - { offsetInCU: 0x39A, offset: 0x19A62, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient9metaClassE, symObjAddr: 0x54A0, symBinAddr: 0x49F0, symSize: 0x0 }
  - { offsetInCU: 0x3B1, offset: 0x19A79, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient10superClassE, symObjAddr: 0x54A8, symBinAddr: 0x49F8, symSize: 0x0 }
  - { offsetInCU: 0x3C8, offset: 0x19A90, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient8sMethodsE, symObjAddr: 0x5F10, symBinAddr: 0x5460, symSize: 0x0 }
  - { offsetInCU: 0x408, offset: 0x19AD0, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientD1Ev, symObjAddr: 0x1C20, symBinAddr: 0x2C00, symSize: 0x10 }
  - { offsetInCU: 0x484, offset: 0x19B4C, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientD0Ev, symObjAddr: 0x1C30, symBinAddr: 0x2C10, symSize: 0x30 }
  - { offsetInCU: 0x512, offset: 0x19BDA, size: 0x8, addend: 0x0, symName: __ZNK13ALCUserClient12getMetaClassEv, symObjAddr: 0x1C60, symBinAddr: 0x2C40, symSize: 0x10 }
  - { offsetInCU: 0x544, offset: 0x19C0C, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient5startEP9IOService, symObjAddr: 0x1C70, symBinAddr: 0x2C50, symSize: 0x50 }
  - { offsetInCU: 0x5A2, offset: 0x19C6A, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient14externalMethodEjP25IOExternalMethodArgumentsP24IOExternalMethodDispatchP8OSObjectPv, symObjAddr: 0x1CC0, symBinAddr: 0x2CA0, symSize: 0x30 }
  - { offsetInCU: 0x5AA, offset: 0x19C72, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient12initWithTaskEP4taskPvjP12OSDictionary, symObjAddr: 0x1CF0, symBinAddr: 0x2CD0, symSize: 0x40 }
  - { offsetInCU: 0x638, offset: 0x19D00, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient12initWithTaskEP4taskPvjP12OSDictionary, symObjAddr: 0x1CF0, symBinAddr: 0x2CD0, symSize: 0x40 }
  - { offsetInCU: 0x640, offset: 0x19D08, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient11clientCloseEv, symObjAddr: 0x1D30, symBinAddr: 0x2D10, symSize: 0x30 }
  - { offsetInCU: 0x6B9, offset: 0x19D81, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient11clientCloseEv, symObjAddr: 0x1D30, symBinAddr: 0x2D10, symSize: 0x30 }
  - { offsetInCU: 0x6EF, offset: 0x19DB7, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient17methodExecuteVerbEP21ALCUserClientProviderPvP25IOExternalMethodArguments, symObjAddr: 0x1D60, symBinAddr: 0x2D40, symSize: 0x40 }
  - { offsetInCU: 0x7AF, offset: 0x19E77, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassD1Ev, symObjAddr: 0x1DA0, symBinAddr: 0x2D80, symSize: 0x10 }
  - { offsetInCU: 0x82B, offset: 0x19EF3, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassD0Ev, symObjAddr: 0x1DB0, symBinAddr: 0x2D90, symSize: 0x10 }
  - { offsetInCU: 0x901, offset: 0x19FC9, size: 0x8, addend: 0x0, symName: __ZNK13ALCUserClient9MetaClass5allocEv, symObjAddr: 0x1DC0, symBinAddr: 0x2DA0, symSize: 0x60 }
  - { offsetInCU: 0x9DA, offset: 0x1A0A2, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_ALCUserClient.cpp, symObjAddr: 0x1E20, symBinAddr: 0x2E00, symSize: 0x40 }
  - { offsetInCU: 0xA6A, offset: 0x1A132, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a.45, symObjAddr: 0x1E60, symBinAddr: 0x2E40, symSize: 0x20 }
  - { offsetInCU: 0xADC, offset: 0x1A1A4, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassC1Ev, symObjAddr: 0x1E80, symBinAddr: 0x2E60, symSize: 0x40 }
  - { offsetInCU: 0xB37, offset: 0x1A1FF, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientC2EPK11OSMetaClass, symObjAddr: 0x1EC0, symBinAddr: 0x2EA0, symSize: 0x40 }
  - { offsetInCU: 0xBA8, offset: 0x1A270, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientC1EPK11OSMetaClass, symObjAddr: 0x1F00, symBinAddr: 0x2EE0, symSize: 0x40 }
  - { offsetInCU: 0xC30, offset: 0x1A2F8, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientD2Ev, symObjAddr: 0x1F40, symBinAddr: 0x2F20, symSize: 0x10 }
  - { offsetInCU: 0xC5D, offset: 0x1A325, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClient9MetaClassC2Ev, symObjAddr: 0x1F50, symBinAddr: 0x2F30, symSize: 0x40 }
  - { offsetInCU: 0xC8C, offset: 0x1A354, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientC1Ev, symObjAddr: 0x1F90, symBinAddr: 0x2F70, symSize: 0x50 }
  - { offsetInCU: 0xCE7, offset: 0x1A3AF, size: 0x8, addend: 0x0, symName: __ZN13ALCUserClientC2Ev, symObjAddr: 0x1FE0, symBinAddr: 0x2FC0, symSize: 0x50 }
  - { offsetInCU: 0x27, offset: 0x1A416, size: 0x8, addend: 0x0, symName: __ZN9AppleALCUD1Ev, symObjAddr: 0x2030, symBinAddr: 0x3010, symSize: 0x10 }
  - { offsetInCU: 0x2F, offset: 0x1A41E, size: 0x8, addend: 0x0, symName: _kmod_info, symObjAddr: 0x26B0, symBinAddr: 0x5DE0, symSize: 0x0 }
  - { offsetInCU: 0x4A, offset: 0x1A439, size: 0x8, addend: 0x0, symName: _AppleALCU_startSuccess, symObjAddr: 0x43EF8, symBinAddr: 0x7938, symSize: 0x0 }
  - { offsetInCU: 0x6B, offset: 0x1A45A, size: 0x8, addend: 0x0, symName: _AppleALCU_debugEnabled, symObjAddr: 0x43EF9, symBinAddr: 0x7939, symSize: 0x0 }
  - { offsetInCU: 0x86, offset: 0x1A475, size: 0x8, addend: 0x0, symName: _AppleALCU_debugPrintDelay, symObjAddr: 0x43EFC, symBinAddr: 0x793C, symSize: 0x0 }
  - { offsetInCU: 0x95, offset: 0x1A484, size: 0x8, addend: 0x0, symName: __ZN9AppleALCU10gMetaClassE, symObjAddr: 0x43ED0, symBinAddr: 0x7910, symSize: 0x0 }
  - { offsetInCU: 0x2EA, offset: 0x1A6D9, size: 0x8, addend: 0x0, symName: __ZN9AppleALCU9metaClassE, symObjAddr: 0x5F30, symBinAddr: 0x5480, symSize: 0x0 }
  - { offsetInCU: 0x301, offset: 0x1A6F0, size: 0x8, addend: 0x0, symName: __ZN9AppleALCU10superClassE, symObjAddr: 0x5F38, symBinAddr: 0x5488, symSize: 0x0 }
  - { offsetInCU: 0x31F, offset: 0x1A70E, size: 0x8, addend: 0x0, symName: _AppleALCU_selfInstance, symObjAddr: 0x43F00, symBinAddr: 0x7940, symSize: 0x0 }
  - { offsetInCU: 0x34A, offset: 0x1A739, size: 0x8, addend: 0x0, symName: __ZL11kextVersion, symObjAddr: 0x4980, symBinAddr: 0x3E70, symSize: 0x0 }
  - { offsetInCU: 0x394, offset: 0x1A783, size: 0x8, addend: 0x0, symName: __ZN9AppleALCUD1Ev, symObjAddr: 0x2030, symBinAddr: 0x3010, symSize: 0x10 }
  - { offsetInCU: 0x410, offset: 0x1A7FF, size: 0x8, addend: 0x0, symName: __ZN9AppleALCUD0Ev, symObjAddr: 0x2040, symBinAddr: 0x3020, symSize: 0x30 }
  - { offsetInCU: 0x49E, offset: 0x1A88D, size: 0x8, addend: 0x0, symName: __ZNK9AppleALCU12getMetaClassEv, symObjAddr: 0x2070, symBinAddr: 0x3050, symSize: 0x10 }
  - { offsetInCU: 0x4D0, offset: 0x1A8BF, size: 0x8, addend: 0x0, symName: __ZN9AppleALCU5probeEP9IOServicePi, symObjAddr: 0x2080, symBinAddr: 0x3060, symSize: 0x60 }
  - { offsetInCU: 0x542, offset: 0x1A931, size: 0x8, addend: 0x0, symName: __ZN9AppleALCU5startEP9IOService, symObjAddr: 0x20E0, symBinAddr: 0x30C0, symSize: 0x60 }
  - { offsetInCU: 0x58C, offset: 0x1A97B, size: 0x8, addend: 0x0, symName: __ZN9AppleALCU4stopEP9IOService, symObjAddr: 0x2140, symBinAddr: 0x3120, symSize: 0x20 }
  - { offsetInCU: 0x5FF, offset: 0x1A9EE, size: 0x8, addend: 0x0, symName: __ZN9AppleALCU9MetaClassD1Ev, symObjAddr: 0x2160, symBinAddr: 0x3140, symSize: 0x10 }
  - { offsetInCU: 0x67B, offset: 0x1AA6A, size: 0x8, addend: 0x0, symName: __ZN9AppleALCU9MetaClassD0Ev, symObjAddr: 0x2170, symBinAddr: 0x3150, symSize: 0x10 }
  - { offsetInCU: 0x751, offset: 0x1AB40, size: 0x8, addend: 0x0, symName: __ZNK9AppleALCU9MetaClass5allocEv, symObjAddr: 0x2180, symBinAddr: 0x3160, symSize: 0x40 }
  - { offsetInCU: 0x759, offset: 0x1AB48, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_plugin_start.cpp, symObjAddr: 0x21C0, symBinAddr: 0x31A0, symSize: 0x40 }
  - { offsetInCU: 0x82A, offset: 0x1AC19, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_plugin_start.cpp, symObjAddr: 0x21C0, symBinAddr: 0x31A0, symSize: 0x40 }
  - { offsetInCU: 0x8BA, offset: 0x1ACA9, size: 0x8, addend: 0x0, symName: __GLOBAL__D_a.54, symObjAddr: 0x2200, symBinAddr: 0x31E0, symSize: 0x20 }
  - { offsetInCU: 0x92C, offset: 0x1AD1B, size: 0x8, addend: 0x0, symName: __ZN9AppleALCU9MetaClassC1Ev, symObjAddr: 0x2220, symBinAddr: 0x3200, symSize: 0x40 }
  - { offsetInCU: 0x987, offset: 0x1AD76, size: 0x8, addend: 0x0, symName: __ZN9AppleALCUC2EPK11OSMetaClass, symObjAddr: 0x2260, symBinAddr: 0x3240, symSize: 0x20 }
  - { offsetInCU: 0x9F8, offset: 0x1ADE7, size: 0x8, addend: 0x0, symName: __ZN9AppleALCUC1EPK11OSMetaClass, symObjAddr: 0x2280, symBinAddr: 0x3260, symSize: 0x20 }
  - { offsetInCU: 0xA80, offset: 0x1AE6F, size: 0x8, addend: 0x0, symName: __ZN9AppleALCUD2Ev, symObjAddr: 0x22A0, symBinAddr: 0x3280, symSize: 0x10 }
  - { offsetInCU: 0xAAD, offset: 0x1AE9C, size: 0x8, addend: 0x0, symName: __ZN9AppleALCU9MetaClassC2Ev, symObjAddr: 0x22B0, symBinAddr: 0x3290, symSize: 0x40 }
  - { offsetInCU: 0xADC, offset: 0x1AECB, size: 0x8, addend: 0x0, symName: __ZN9AppleALCUC1Ev, symObjAddr: 0x22F0, symBinAddr: 0x32D0, symSize: 0x30 }
  - { offsetInCU: 0xB37, offset: 0x1AF26, size: 0x8, addend: 0x0, symName: __ZN9AppleALCUC2Ev, symObjAddr: 0x2320, symBinAddr: 0x3300, symSize: 0x30 }
  - { offsetInCU: 0xBA0, offset: 0x1AF8F, size: 0x8, addend: 0x0, symName: _AppleALCU_kern_start, symObjAddr: 0x2350, symBinAddr: 0x3330, symSize: 0x350 }
  - { offsetInCU: 0xEAA, offset: 0x1B299, size: 0x8, addend: 0x0, symName: _AppleALCU_kern_stop, symObjAddr: 0x26A0, symBinAddr: 0x3680, symSize: 0x10 }
  - { offsetInCU: 0xEB2, offset: 0x1B2A1, size: 0x8, addend: 0x0, symName: _kmod_info, symObjAddr: 0x26B0, symBinAddr: 0x5DE0, symSize: 0x0 }
  - { offsetInCU: 0x35, offset: 0x1B31C, size: 0x8, addend: 0x0, symName: __ZL10bootargOff, symObjAddr: 0x2820, symBinAddr: 0x5F50, symSize: 0x0 }
  - { offsetInCU: 0x6D, offset: 0x1B354, size: 0x8, addend: 0x0, symName: __ZL12bootargDebug, symObjAddr: 0x2828, symBinAddr: 0x5F58, symSize: 0x0 }
  - { offsetInCU: 0x8A, offset: 0x1B371, size: 0x8, addend: 0x0, symName: __ZL11bootargBeta, symObjAddr: 0x2830, symBinAddr: 0x5F60, symSize: 0x0 }
  - { offsetInCU: 0x3C, offset: 0x1B432, size: 0x8, addend: 0x0, symName: _AppleALCU_kextList, symObjAddr: 0x2860, symBinAddr: 0x5F90, symSize: 0x0 }
  - { offsetInCU: 0x6F, offset: 0x1B465, size: 0x8, addend: 0x0, symName: _AppleALCU_controllerMod, symObjAddr: 0x3860, symBinAddr: 0x6F90, symSize: 0x0 }
  - { offsetInCU: 0xA1, offset: 0x1B497, size: 0x8, addend: 0x0, symName: __ZL9kextPath0, symObjAddr: 0x2838, symBinAddr: 0x5F68, symSize: 0x0 }
  - { offsetInCU: 0xBE, offset: 0x1B4B4, size: 0x8, addend: 0x0, symName: __ZL9kextPath1, symObjAddr: 0x2840, symBinAddr: 0x5F70, symSize: 0x0 }
  - { offsetInCU: 0xDB, offset: 0x1B4D1, size: 0x8, addend: 0x0, symName: __ZL9kextPath2, symObjAddr: 0x2848, symBinAddr: 0x5F78, symSize: 0x0 }
  - { offsetInCU: 0xF8, offset: 0x1B4EE, size: 0x8, addend: 0x0, symName: __ZL9kextPath3, symObjAddr: 0x2850, symBinAddr: 0x5F80, symSize: 0x0 }
  - { offsetInCU: 0x115, offset: 0x1B50B, size: 0x8, addend: 0x0, symName: __ZL9kextPath4, symObjAddr: 0x2858, symBinAddr: 0x5F88, symSize: 0x0 }
  - { offsetInCU: 0x131, offset: 0x1B527, size: 0x8, addend: 0x0, symName: __ZL10patches110, symObjAddr: 0x2930, symBinAddr: 0x6060, symSize: 0x0 }
  - { offsetInCU: 0x166, offset: 0x1B55C, size: 0x8, addend: 0x0, symName: __ZL11patchBuf146, symObjAddr: 0x4993, symBinAddr: 0x3E83, symSize: 0x0 }
  - { offsetInCU: 0x19B, offset: 0x1B591, size: 0x8, addend: 0x0, symName: __ZL11patchBuf147, symObjAddr: 0x4997, symBinAddr: 0x3E87, symSize: 0x0 }
  - { offsetInCU: 0x1BC, offset: 0x1B5B2, size: 0x8, addend: 0x0, symName: __ZL10patches111, symObjAddr: 0x2960, symBinAddr: 0x6090, symSize: 0x0 }
  - { offsetInCU: 0x1DD, offset: 0x1B5D3, size: 0x8, addend: 0x0, symName: __ZL11patchBuf148, symObjAddr: 0x499B, symBinAddr: 0x3E8B, symSize: 0x0 }
  - { offsetInCU: 0x1FE, offset: 0x1B5F4, size: 0x8, addend: 0x0, symName: __ZL10patches112, symObjAddr: 0x2990, symBinAddr: 0x60C0, symSize: 0x0 }
  - { offsetInCU: 0x21F, offset: 0x1B615, size: 0x8, addend: 0x0, symName: __ZL11patchBuf149, symObjAddr: 0x499F, symBinAddr: 0x3E8F, symSize: 0x0 }
  - { offsetInCU: 0x240, offset: 0x1B636, size: 0x8, addend: 0x0, symName: __ZL10patches113, symObjAddr: 0x29C0, symBinAddr: 0x60F0, symSize: 0x0 }
  - { offsetInCU: 0x261, offset: 0x1B657, size: 0x8, addend: 0x0, symName: __ZL11patchBuf150, symObjAddr: 0x49A3, symBinAddr: 0x3E93, symSize: 0x0 }
  - { offsetInCU: 0x282, offset: 0x1B678, size: 0x8, addend: 0x0, symName: __ZL10patches114, symObjAddr: 0x29F0, symBinAddr: 0x6120, symSize: 0x0 }
  - { offsetInCU: 0x2A3, offset: 0x1B699, size: 0x8, addend: 0x0, symName: __ZL11patchBuf151, symObjAddr: 0x49A7, symBinAddr: 0x3E97, symSize: 0x0 }
  - { offsetInCU: 0x2C4, offset: 0x1B6BA, size: 0x8, addend: 0x0, symName: __ZL10patches115, symObjAddr: 0x2A20, symBinAddr: 0x6150, symSize: 0x0 }
  - { offsetInCU: 0x2E5, offset: 0x1B6DB, size: 0x8, addend: 0x0, symName: __ZL11patchBuf152, symObjAddr: 0x49AB, symBinAddr: 0x3E9B, symSize: 0x0 }
  - { offsetInCU: 0x306, offset: 0x1B6FC, size: 0x8, addend: 0x0, symName: __ZL10patches116, symObjAddr: 0x2A50, symBinAddr: 0x6180, symSize: 0x0 }
  - { offsetInCU: 0x327, offset: 0x1B71D, size: 0x8, addend: 0x0, symName: __ZL11patchBuf153, symObjAddr: 0x49AF, symBinAddr: 0x3E9F, symSize: 0x0 }
  - { offsetInCU: 0x348, offset: 0x1B73E, size: 0x8, addend: 0x0, symName: __ZL10patches117, symObjAddr: 0x2A80, symBinAddr: 0x61B0, symSize: 0x0 }
  - { offsetInCU: 0x369, offset: 0x1B75F, size: 0x8, addend: 0x0, symName: __ZL11patchBuf154, symObjAddr: 0x49B3, symBinAddr: 0x3EA3, symSize: 0x0 }
  - { offsetInCU: 0x38A, offset: 0x1B780, size: 0x8, addend: 0x0, symName: __ZL11patchBuf155, symObjAddr: 0x49B7, symBinAddr: 0x3EA7, symSize: 0x0 }
  - { offsetInCU: 0x3AB, offset: 0x1B7A1, size: 0x8, addend: 0x0, symName: __ZL10patches118, symObjAddr: 0x2AB0, symBinAddr: 0x61E0, symSize: 0x0 }
  - { offsetInCU: 0x3CC, offset: 0x1B7C2, size: 0x8, addend: 0x0, symName: __ZL11patchBuf156, symObjAddr: 0x49BB, symBinAddr: 0x3EAB, symSize: 0x0 }
  - { offsetInCU: 0x3ED, offset: 0x1B7E3, size: 0x8, addend: 0x0, symName: __ZL10patches119, symObjAddr: 0x2AE0, symBinAddr: 0x6210, symSize: 0x0 }
  - { offsetInCU: 0x40E, offset: 0x1B804, size: 0x8, addend: 0x0, symName: __ZL11patchBuf157, symObjAddr: 0x49BF, symBinAddr: 0x3EAF, symSize: 0x0 }
  - { offsetInCU: 0x42F, offset: 0x1B825, size: 0x8, addend: 0x0, symName: __ZL10patches120, symObjAddr: 0x2B10, symBinAddr: 0x6240, symSize: 0x0 }
  - { offsetInCU: 0x450, offset: 0x1B846, size: 0x8, addend: 0x0, symName: __ZL11patchBuf158, symObjAddr: 0x49C3, symBinAddr: 0x3EB3, symSize: 0x0 }
  - { offsetInCU: 0x471, offset: 0x1B867, size: 0x8, addend: 0x0, symName: __ZL10patches121, symObjAddr: 0x2B40, symBinAddr: 0x6270, symSize: 0x0 }
  - { offsetInCU: 0x4A6, offset: 0x1B89C, size: 0x8, addend: 0x0, symName: __ZL11patchBuf159, symObjAddr: 0x49C7, symBinAddr: 0x3EB7, symSize: 0x0 }
  - { offsetInCU: 0x4C7, offset: 0x1B8BD, size: 0x8, addend: 0x0, symName: __ZL11patchBuf160, symObjAddr: 0x49CB, symBinAddr: 0x3EBB, symSize: 0x0 }
  - { offsetInCU: 0x4E8, offset: 0x1B8DE, size: 0x8, addend: 0x0, symName: __ZL11patchBuf161, symObjAddr: 0x49CF, symBinAddr: 0x3EBF, symSize: 0x0 }
  - { offsetInCU: 0x509, offset: 0x1B8FF, size: 0x8, addend: 0x0, symName: __ZL11patchBuf162, symObjAddr: 0x49D3, symBinAddr: 0x3EC3, symSize: 0x0 }
  - { offsetInCU: 0x52A, offset: 0x1B920, size: 0x8, addend: 0x0, symName: __ZL11patchBuf163, symObjAddr: 0x49D7, symBinAddr: 0x3EC7, symSize: 0x0 }
  - { offsetInCU: 0x54B, offset: 0x1B941, size: 0x8, addend: 0x0, symName: __ZL11patchBuf164, symObjAddr: 0x49DB, symBinAddr: 0x3ECB, symSize: 0x0 }
  - { offsetInCU: 0x56C, offset: 0x1B962, size: 0x8, addend: 0x0, symName: __ZL11patchBuf165, symObjAddr: 0x49DF, symBinAddr: 0x3ECF, symSize: 0x0 }
  - { offsetInCU: 0x58D, offset: 0x1B983, size: 0x8, addend: 0x0, symName: __ZL11patchBuf166, symObjAddr: 0x49E3, symBinAddr: 0x3ED3, symSize: 0x0 }
  - { offsetInCU: 0x5AE, offset: 0x1B9A4, size: 0x8, addend: 0x0, symName: __ZL10patches122, symObjAddr: 0x2C00, symBinAddr: 0x6330, symSize: 0x0 }
  - { offsetInCU: 0x5CF, offset: 0x1B9C5, size: 0x8, addend: 0x0, symName: __ZL11patchBuf167, symObjAddr: 0x49E7, symBinAddr: 0x3ED7, symSize: 0x0 }
  - { offsetInCU: 0x5F0, offset: 0x1B9E6, size: 0x8, addend: 0x0, symName: __ZL11patchBuf168, symObjAddr: 0x49EB, symBinAddr: 0x3EDB, symSize: 0x0 }
  - { offsetInCU: 0x611, offset: 0x1BA07, size: 0x8, addend: 0x0, symName: __ZL11patchBuf169, symObjAddr: 0x49EF, symBinAddr: 0x3EDF, symSize: 0x0 }
  - { offsetInCU: 0x632, offset: 0x1BA28, size: 0x8, addend: 0x0, symName: __ZL10patches123, symObjAddr: 0x2CC0, symBinAddr: 0x63F0, symSize: 0x0 }
  - { offsetInCU: 0x667, offset: 0x1BA5D, size: 0x8, addend: 0x0, symName: __ZL11patchBuf170, symObjAddr: 0x49F3, symBinAddr: 0x3EE3, symSize: 0x0 }
  - { offsetInCU: 0x688, offset: 0x1BA7E, size: 0x8, addend: 0x0, symName: __ZL11patchBuf171, symObjAddr: 0x49F7, symBinAddr: 0x3EE7, symSize: 0x0 }
  - { offsetInCU: 0x6A9, offset: 0x1BA9F, size: 0x8, addend: 0x0, symName: __ZL10patches124, symObjAddr: 0x2D20, symBinAddr: 0x6450, symSize: 0x0 }
  - { offsetInCU: 0x6DE, offset: 0x1BAD4, size: 0x8, addend: 0x0, symName: __ZL11patchBuf172, symObjAddr: 0x49FB, symBinAddr: 0x3EEB, symSize: 0x0 }
  - { offsetInCU: 0x6FF, offset: 0x1BAF5, size: 0x8, addend: 0x0, symName: __ZL11patchBuf173, symObjAddr: 0x49FF, symBinAddr: 0x3EEF, symSize: 0x0 }
  - { offsetInCU: 0x720, offset: 0x1BB16, size: 0x8, addend: 0x0, symName: __ZL11patchBuf174, symObjAddr: 0x4A03, symBinAddr: 0x3EF3, symSize: 0x0 }
  - { offsetInCU: 0x755, offset: 0x1BB4B, size: 0x8, addend: 0x0, symName: __ZL11patchBuf175, symObjAddr: 0x4A09, symBinAddr: 0x3EF9, symSize: 0x0 }
  - { offsetInCU: 0x776, offset: 0x1BB6C, size: 0x8, addend: 0x0, symName: __ZL11patchBuf176, symObjAddr: 0x4A0F, symBinAddr: 0x3EFF, symSize: 0x0 }
  - { offsetInCU: 0x7AB, offset: 0x1BBA1, size: 0x8, addend: 0x0, symName: __ZL11patchBuf177, symObjAddr: 0x4A1B, symBinAddr: 0x3F0B, symSize: 0x0 }
  - { offsetInCU: 0x7CC, offset: 0x1BBC2, size: 0x8, addend: 0x0, symName: __ZL11patchBuf178, symObjAddr: 0x4A27, symBinAddr: 0x3F17, symSize: 0x0 }
  - { offsetInCU: 0x801, offset: 0x1BBF7, size: 0x8, addend: 0x0, symName: __ZL11patchBuf179, symObjAddr: 0x4A2C, symBinAddr: 0x3F1C, symSize: 0x0 }
  - { offsetInCU: 0x822, offset: 0x1BC18, size: 0x8, addend: 0x0, symName: __ZL11patchBuf180, symObjAddr: 0x4A31, symBinAddr: 0x3F21, symSize: 0x0 }
  - { offsetInCU: 0x857, offset: 0x1BC4D, size: 0x8, addend: 0x0, symName: __ZL11patchBuf181, symObjAddr: 0x4A38, symBinAddr: 0x3F28, symSize: 0x0 }
  - { offsetInCU: 0x878, offset: 0x1BC6E, size: 0x8, addend: 0x0, symName: __ZL10patches125, symObjAddr: 0x2E10, symBinAddr: 0x6540, symSize: 0x0 }
  - { offsetInCU: 0x899, offset: 0x1BC8F, size: 0x8, addend: 0x0, symName: __ZL11patchBuf182, symObjAddr: 0x4A3F, symBinAddr: 0x3F2F, symSize: 0x0 }
  - { offsetInCU: 0x8BA, offset: 0x1BCB0, size: 0x8, addend: 0x0, symName: __ZL10patches126, symObjAddr: 0x2F00, symBinAddr: 0x6630, symSize: 0x0 }
  - { offsetInCU: 0x8DB, offset: 0x1BCD1, size: 0x8, addend: 0x0, symName: __ZL11patchBuf183, symObjAddr: 0x4A43, symBinAddr: 0x3F33, symSize: 0x0 }
  - { offsetInCU: 0x8FC, offset: 0x1BCF2, size: 0x8, addend: 0x0, symName: __ZL10patches127, symObjAddr: 0x2FF0, symBinAddr: 0x6720, symSize: 0x0 }
  - { offsetInCU: 0x91D, offset: 0x1BD13, size: 0x8, addend: 0x0, symName: __ZL11patchBuf184, symObjAddr: 0x4A47, symBinAddr: 0x3F37, symSize: 0x0 }
  - { offsetInCU: 0x93E, offset: 0x1BD34, size: 0x8, addend: 0x0, symName: __ZL11patchBuf185, symObjAddr: 0x4A4B, symBinAddr: 0x3F3B, symSize: 0x0 }
  - { offsetInCU: 0x95F, offset: 0x1BD55, size: 0x8, addend: 0x0, symName: __ZL11patchBuf186, symObjAddr: 0x4A4F, symBinAddr: 0x3F3F, symSize: 0x0 }
  - { offsetInCU: 0x980, offset: 0x1BD76, size: 0x8, addend: 0x0, symName: __ZL11patchBuf187, symObjAddr: 0x4A53, symBinAddr: 0x3F43, symSize: 0x0 }
  - { offsetInCU: 0x9A1, offset: 0x1BD97, size: 0x8, addend: 0x0, symName: __ZL11patchBuf188, symObjAddr: 0x4A58, symBinAddr: 0x3F48, symSize: 0x0 }
  - { offsetInCU: 0x9C2, offset: 0x1BDB8, size: 0x8, addend: 0x0, symName: __ZL11patchBuf189, symObjAddr: 0x4A5D, symBinAddr: 0x3F4D, symSize: 0x0 }
  - { offsetInCU: 0x9E3, offset: 0x1BDD9, size: 0x8, addend: 0x0, symName: __ZL10patches128, symObjAddr: 0x30E0, symBinAddr: 0x6810, symSize: 0x0 }
  - { offsetInCU: 0xA04, offset: 0x1BDFA, size: 0x8, addend: 0x0, symName: __ZL11patchBuf190, symObjAddr: 0x4A62, symBinAddr: 0x3F52, symSize: 0x0 }
  - { offsetInCU: 0xA25, offset: 0x1BE1B, size: 0x8, addend: 0x0, symName: __ZL11patchBuf191, symObjAddr: 0x4A66, symBinAddr: 0x3F56, symSize: 0x0 }
  - { offsetInCU: 0xA46, offset: 0x1BE3C, size: 0x8, addend: 0x0, symName: __ZL11patchBuf192, symObjAddr: 0x4A6A, symBinAddr: 0x3F5A, symSize: 0x0 }
  - { offsetInCU: 0xA67, offset: 0x1BE5D, size: 0x8, addend: 0x0, symName: __ZL10patches129, symObjAddr: 0x3140, symBinAddr: 0x6870, symSize: 0x0 }
  - { offsetInCU: 0xA88, offset: 0x1BE7E, size: 0x8, addend: 0x0, symName: __ZL11patchBuf193, symObjAddr: 0x4A6E, symBinAddr: 0x3F5E, symSize: 0x0 }
  - { offsetInCU: 0xAA9, offset: 0x1BE9F, size: 0x8, addend: 0x0, symName: __ZL11patchBuf194, symObjAddr: 0x4A72, symBinAddr: 0x3F62, symSize: 0x0 }
  - { offsetInCU: 0xACA, offset: 0x1BEC0, size: 0x8, addend: 0x0, symName: __ZL11patchBuf195, symObjAddr: 0x4A76, symBinAddr: 0x3F66, symSize: 0x0 }
  - { offsetInCU: 0xAEB, offset: 0x1BEE1, size: 0x8, addend: 0x0, symName: __ZL11patchBuf196, symObjAddr: 0x4A7A, symBinAddr: 0x3F6A, symSize: 0x0 }
  - { offsetInCU: 0xB0C, offset: 0x1BF02, size: 0x8, addend: 0x0, symName: __ZL10patches130, symObjAddr: 0x31A0, symBinAddr: 0x68D0, symSize: 0x0 }
  - { offsetInCU: 0xB2D, offset: 0x1BF23, size: 0x8, addend: 0x0, symName: __ZL11patchBuf197, symObjAddr: 0x4A7E, symBinAddr: 0x3F6E, symSize: 0x0 }
  - { offsetInCU: 0xB4E, offset: 0x1BF44, size: 0x8, addend: 0x0, symName: __ZL11patchBuf198, symObjAddr: 0x4A82, symBinAddr: 0x3F72, symSize: 0x0 }
  - { offsetInCU: 0xB6F, offset: 0x1BF65, size: 0x8, addend: 0x0, symName: __ZL10patches131, symObjAddr: 0x3260, symBinAddr: 0x6990, symSize: 0x0 }
  - { offsetInCU: 0xB90, offset: 0x1BF86, size: 0x8, addend: 0x0, symName: __ZL11patchBuf199, symObjAddr: 0x4A86, symBinAddr: 0x3F76, symSize: 0x0 }
  - { offsetInCU: 0xBB1, offset: 0x1BFA7, size: 0x8, addend: 0x0, symName: __ZL11patchBuf200, symObjAddr: 0x4A8A, symBinAddr: 0x3F7A, symSize: 0x0 }
  - { offsetInCU: 0xBD2, offset: 0x1BFC8, size: 0x8, addend: 0x0, symName: __ZL10patches132, symObjAddr: 0x32C0, symBinAddr: 0x69F0, symSize: 0x0 }
  - { offsetInCU: 0xBF3, offset: 0x1BFE9, size: 0x8, addend: 0x0, symName: __ZL11patchBuf201, symObjAddr: 0x4A8E, symBinAddr: 0x3F7E, symSize: 0x0 }
  - { offsetInCU: 0xC28, offset: 0x1C01E, size: 0x8, addend: 0x0, symName: __ZL11patchBuf202, symObjAddr: 0x4A99, symBinAddr: 0x3F89, symSize: 0x0 }
  - { offsetInCU: 0xC49, offset: 0x1C03F, size: 0x8, addend: 0x0, symName: __ZL10patches133, symObjAddr: 0x32F0, symBinAddr: 0x6A20, symSize: 0x0 }
  - { offsetInCU: 0xC6A, offset: 0x1C060, size: 0x8, addend: 0x0, symName: __ZL11patchBuf203, symObjAddr: 0x4AA4, symBinAddr: 0x3F94, symSize: 0x0 }
  - { offsetInCU: 0xC8B, offset: 0x1C081, size: 0x8, addend: 0x0, symName: __ZL11patchBuf204, symObjAddr: 0x4AA8, symBinAddr: 0x3F98, symSize: 0x0 }
  - { offsetInCU: 0xCAC, offset: 0x1C0A2, size: 0x8, addend: 0x0, symName: __ZL11patchBuf205, symObjAddr: 0x4AAC, symBinAddr: 0x3F9C, symSize: 0x0 }
  - { offsetInCU: 0xCCD, offset: 0x1C0C3, size: 0x8, addend: 0x0, symName: __ZL11patchBuf206, symObjAddr: 0x4AB0, symBinAddr: 0x3FA0, symSize: 0x0 }
  - { offsetInCU: 0xCEE, offset: 0x1C0E4, size: 0x8, addend: 0x0, symName: __ZL11patchBuf207, symObjAddr: 0x4AB4, symBinAddr: 0x3FA4, symSize: 0x0 }
  - { offsetInCU: 0xD0F, offset: 0x1C105, size: 0x8, addend: 0x0, symName: __ZL11patchBuf208, symObjAddr: 0x4AB8, symBinAddr: 0x3FA8, symSize: 0x0 }
  - { offsetInCU: 0xD30, offset: 0x1C126, size: 0x8, addend: 0x0, symName: __ZL10patches134, symObjAddr: 0x33B0, symBinAddr: 0x6AE0, symSize: 0x0 }
  - { offsetInCU: 0xD51, offset: 0x1C147, size: 0x8, addend: 0x0, symName: __ZL10patches135, symObjAddr: 0x3410, symBinAddr: 0x6B40, symSize: 0x0 }
  - { offsetInCU: 0xD72, offset: 0x1C168, size: 0x8, addend: 0x0, symName: __ZL10patches136, symObjAddr: 0x3440, symBinAddr: 0x6B70, symSize: 0x0 }
  - { offsetInCU: 0xD93, offset: 0x1C189, size: 0x8, addend: 0x0, symName: __ZL11patchBuf209, symObjAddr: 0x4ABC, symBinAddr: 0x3FAC, symSize: 0x0 }
  - { offsetInCU: 0xDB4, offset: 0x1C1AA, size: 0x8, addend: 0x0, symName: __ZL11patchBuf210, symObjAddr: 0x4AC0, symBinAddr: 0x3FB0, symSize: 0x0 }
  - { offsetInCU: 0xDD5, offset: 0x1C1CB, size: 0x8, addend: 0x0, symName: __ZL11patchBuf211, symObjAddr: 0x4AC4, symBinAddr: 0x3FB4, symSize: 0x0 }
  - { offsetInCU: 0xDF6, offset: 0x1C1EC, size: 0x8, addend: 0x0, symName: __ZL11patchBuf212, symObjAddr: 0x4AC8, symBinAddr: 0x3FB8, symSize: 0x0 }
  - { offsetInCU: 0xE17, offset: 0x1C20D, size: 0x8, addend: 0x0, symName: __ZL10patches137, symObjAddr: 0x34A0, symBinAddr: 0x6BD0, symSize: 0x0 }
  - { offsetInCU: 0xE38, offset: 0x1C22E, size: 0x8, addend: 0x0, symName: __ZL11patchBuf213, symObjAddr: 0x4ACC, symBinAddr: 0x3FBC, symSize: 0x0 }
  - { offsetInCU: 0xE59, offset: 0x1C24F, size: 0x8, addend: 0x0, symName: __ZL10patches138, symObjAddr: 0x34D0, symBinAddr: 0x6C00, symSize: 0x0 }
  - { offsetInCU: 0xE7A, offset: 0x1C270, size: 0x8, addend: 0x0, symName: __ZL10patches139, symObjAddr: 0x3530, symBinAddr: 0x6C60, symSize: 0x0 }
  - { offsetInCU: 0xE9B, offset: 0x1C291, size: 0x8, addend: 0x0, symName: __ZL11patchBuf214, symObjAddr: 0x4AD7, symBinAddr: 0x3FC7, symSize: 0x0 }
  - { offsetInCU: 0xEBC, offset: 0x1C2B2, size: 0x8, addend: 0x0, symName: __ZL11patchBuf215, symObjAddr: 0x4ADB, symBinAddr: 0x3FCB, symSize: 0x0 }
  - { offsetInCU: 0xEDD, offset: 0x1C2D3, size: 0x8, addend: 0x0, symName: __ZL10patches140, symObjAddr: 0x3590, symBinAddr: 0x6CC0, symSize: 0x0 }
  - { offsetInCU: 0xEFE, offset: 0x1C2F4, size: 0x8, addend: 0x0, symName: __ZL11patchBuf216, symObjAddr: 0x4ADF, symBinAddr: 0x3FCF, symSize: 0x0 }
  - { offsetInCU: 0xF1F, offset: 0x1C315, size: 0x8, addend: 0x0, symName: __ZL11patchBuf217, symObjAddr: 0x4AE3, symBinAddr: 0x3FD3, symSize: 0x0 }
  - { offsetInCU: 0xF40, offset: 0x1C336, size: 0x8, addend: 0x0, symName: __ZL10patches141, symObjAddr: 0x35F0, symBinAddr: 0x6D20, symSize: 0x0 }
  - { offsetInCU: 0xF61, offset: 0x1C357, size: 0x8, addend: 0x0, symName: __ZL10patches142, symObjAddr: 0x3620, symBinAddr: 0x6D50, symSize: 0x0 }
  - { offsetInCU: 0xF82, offset: 0x1C378, size: 0x8, addend: 0x0, symName: __ZL11patchBuf218, symObjAddr: 0x4AE7, symBinAddr: 0x3FD7, symSize: 0x0 }
  - { offsetInCU: 0xFA3, offset: 0x1C399, size: 0x8, addend: 0x0, symName: __ZL11patchBuf219, symObjAddr: 0x4AEB, symBinAddr: 0x3FDB, symSize: 0x0 }
  - { offsetInCU: 0xFC4, offset: 0x1C3BA, size: 0x8, addend: 0x0, symName: __ZL10patches143, symObjAddr: 0x3680, symBinAddr: 0x6DB0, symSize: 0x0 }
  - { offsetInCU: 0xFE5, offset: 0x1C3DB, size: 0x8, addend: 0x0, symName: __ZL10patches144, symObjAddr: 0x36B0, symBinAddr: 0x6DE0, symSize: 0x0 }
  - { offsetInCU: 0x1006, offset: 0x1C3FC, size: 0x8, addend: 0x0, symName: __ZL10patches145, symObjAddr: 0x36E0, symBinAddr: 0x6E10, symSize: 0x0 }
  - { offsetInCU: 0x1027, offset: 0x1C41D, size: 0x8, addend: 0x0, symName: __ZL10patches146, symObjAddr: 0x3710, symBinAddr: 0x6E40, symSize: 0x0 }
  - { offsetInCU: 0x1048, offset: 0x1C43E, size: 0x8, addend: 0x0, symName: __ZL11patchBuf220, symObjAddr: 0x4AEF, symBinAddr: 0x3FDF, symSize: 0x0 }
  - { offsetInCU: 0x1069, offset: 0x1C45F, size: 0x8, addend: 0x0, symName: __ZL11patchBuf221, symObjAddr: 0x4AF3, symBinAddr: 0x3FE3, symSize: 0x0 }
  - { offsetInCU: 0x108A, offset: 0x1C480, size: 0x8, addend: 0x0, symName: __ZL10patches147, symObjAddr: 0x3770, symBinAddr: 0x6EA0, symSize: 0x0 }
  - { offsetInCU: 0x10AB, offset: 0x1C4A1, size: 0x8, addend: 0x0, symName: __ZL10patches148, symObjAddr: 0x37A0, symBinAddr: 0x6ED0, symSize: 0x0 }
  - { offsetInCU: 0x10CC, offset: 0x1C4C2, size: 0x8, addend: 0x0, symName: __ZL11patchBuf222, symObjAddr: 0x4AF7, symBinAddr: 0x3FE7, symSize: 0x0 }
  - { offsetInCU: 0x10ED, offset: 0x1C4E3, size: 0x8, addend: 0x0, symName: __ZL10patches149, symObjAddr: 0x37D0, symBinAddr: 0x6F00, symSize: 0x0 }
  - { offsetInCU: 0x110E, offset: 0x1C504, size: 0x8, addend: 0x0, symName: __ZL10patches150, symObjAddr: 0x3800, symBinAddr: 0x6F30, symSize: 0x0 }
  - { offsetInCU: 0x112F, offset: 0x1C525, size: 0x8, addend: 0x0, symName: __ZL10patches151, symObjAddr: 0x3830, symBinAddr: 0x6F60, symSize: 0x0 }
...
