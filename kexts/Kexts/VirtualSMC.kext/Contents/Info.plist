<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>23H626</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleExecutable</key>
	<string>VirtualSMC</string>
	<key>CFBundleIdentifier</key>
	<string>as.vit9696.VirtualSMC</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>VirtualSMC</string>
	<key>CFBundlePackageType</key>
	<string>KEXT</string>
	<key>CFBundleShortVersionString</key>
	<string>1.3.7</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CFBundleVersion</key>
	<string>1.3.7</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string></string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>14.2</string>
	<key>DTSDKBuild</key>
	<string>23C53</string>
	<key>DTSDKName</key>
	<string>macosx14.2</string>
	<key>DTXcode</key>
	<string>1520</string>
	<key>DTXcodeBuild</key>
	<string>15C500b</string>
	<key>IOKitPersonalities</key>
	<dict>
		<key>as.vit9696.VirtualSMC</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>as.vit9696.VirtualSMC</string>
			<key>IOClass</key>
			<string>VirtualSMC</string>
			<key>IODeviceMemory</key>
			<array>
				<array>
					<dict>
						<key>address</key>
						<integer>768</integer>
						<key>length</key>
						<integer>32</integer>
					</dict>
				</array>
				<array>
					<dict>
						<key>address</key>
						<integer>**********</integer>
						<key>length</key>
						<integer>65536</integer>
					</dict>
				</array>
			</array>
			<key>IOInterruptControllers</key>
			<array>
				<string>io-apic-0</string>
			</array>
			<key>IOInterruptSpecifiers</key>
			<array>
				<data>
				BgAAAAAAAAA=
				</data>
			</array>
			<key>IOMatchCategory</key>
			<string>IOACPIPlatformDevice</string>
			<key>IOName</key>
			<string>SMC</string>
			<key>IOProbeScore</key>
			<integer>60000</integer>
			<key>IOProviderClass</key>
			<string>AppleACPIPlatformExpert</string>
			<key>Keystore</key>
			<dict>
				<key>Generic</key>
				<array>
					<dict>
						<key>attr</key>
						<data>
						iA==
						</data>
						<key>comment</key>
						<string>Total fan number, this should be put to a plugin</string>
						<key>name</key>
						<data>
						Rk51bQ==
						</data>
						<key>type</key>
						<data>
						dWk4IA==
						</data>
						<key>value</key>
						<data>
						AA==
						</data>
					</dict>
					<dict>
						<key>attr</key>
						<data>
						gA==
						</data>
						<key>comment</key>
						<string>CPU plimit</string>
						<key>name</key>
						<data>
						TVNUYw==
						</data>
						<key>type</key>
						<data>
						dWk4IA==
						</data>
						<key>value</key>
						<data>
						AA==
						</data>
					</dict>
					<dict>
						<key>attr</key>
						<data>
						gA==
						</data>
						<key>comment</key>
						<string>FAN plimit (supposedly)</string>
						<key>name</key>
						<data>
						TVNUZg==
						</data>
						<key>type</key>
						<data>
						dWk4IA==
						</data>
						<key>value</key>
						<data>
						AA==
						</data>
					</dict>
					<dict>
						<key>attr</key>
						<data>
						gA==
						</data>
						<key>comment</key>
						<string>Memory plimit</string>
						<key>name</key>
						<data>
						TVNUbQ==
						</data>
						<key>type</key>
						<data>
						dWk4IA==
						</data>
						<key>value</key>
						<data>
						AA==
						</data>
					</dict>
					<dict>
						<key>attr</key>
						<data>
						gA==
						</data>
						<key>comment</key>
						<string>This should be 1 on laptops, and is overriden by sensors</string>
						<key>name</key>
						<data>
						QkFUUA==
						</data>
						<key>type</key>
						<data>
						ZmxhZw==
						</data>
						<key>value</key>
						<data>
						AA==
						</data>
					</dict>
					<dict>
						<key>attr</key>
						<data>
						gA==
						</data>
						<key>comment</key>
						<string>Only MacPros have custom illumination controllers</string>
						<key>name</key>
						<data>
						THNOTQ==
						</data>
						<key>type</key>
						<data>
						dWk4IA==
						</data>
						<key>value</key>
						<data>
						AA==
						</data>
					</dict>
				</array>
				<key>GenericDesktopV1</key>
				<array/>
				<key>GenericDesktopV2</key>
				<array/>
				<key>GenericLaptopV1</key>
				<array/>
				<key>GenericLaptopV2</key>
				<array/>
				<key>GenericV1</key>
				<array>
					<dict>
						<key>attr</key>
						<data>
						gA==
						</data>
						<key>comment</key>
						<string>GPU plimit</string>
						<key>name</key>
						<data>
						TVNUZw==
						</data>
						<key>type</key>
						<data>
						dWk4IA==
						</data>
						<key>value</key>
						<data>
						AA==
						</data>
					</dict>
				</array>
				<key>GenericV2</key>
				<array>
					<dict>
						<key>attr</key>
						<data>
						gA==
						</data>
						<key>comment</key>
						<string>E plimit (???)</string>
						<key>name</key>
						<data>
						TVNUZQ==
						</data>
						<key>type</key>
						<data>
						dWk4IA==
						</data>
						<key>value</key>
						<data>
						AA==
						</data>
					</dict>
					<dict>
						<key>attr</key>
						<data>
						gA==
						</data>
						<key>comment</key>
						<string>I plimit (???)</string>
						<key>name</key>
						<data>
						TVNUaQ==
						</data>
						<key>type</key>
						<data>
						dWk4IA==
						</data>
						<key>value</key>
						<data>
						AA==
						</data>
					</dict>
					<dict>
						<key>attr</key>
						<data>
						gA==
						</data>
						<key>comment</key>
						<string>J plimit (???)</string>
						<key>name</key>
						<data>
						TVNUag==
						</data>
						<key>type</key>
						<data>
						dWk4IA==
						</data>
						<key>value</key>
						<data>
						AA==
						</data>
					</dict>
				</array>
			</dict>
			<key>ModelInfo</key>
			<dict>
				<key>GenericV1</key>
				<dict>
					<key>branch</key>
					<data>
					ajUyAAAAAAA=
					</data>
					<key>hwname</key>
					<data>
					c21jLXBpa2V0b24A
					</data>
					<key>platform</key>
					<data>
					ajUyAAAAAAA=
					</data>
					<key>rev</key>
					<data>
					AXQPAAAE
					</data>
					<key>revfb</key>
					<data>
					AXQPAAAE
					</data>
					<key>revfu</key>
					<data>
					AXQPAAAE
					</data>
				</dict>
				<key>GenericV2</key>
				<dict>
					<key>branch</key>
					<data>
					ajUyAAAAAAA=
					</data>
					<key>hwname</key>
					<data>
					c21jLWh1cm9ucml2ZXIA
					</data>
					<key>platform</key>
					<data>
					ajUyAAAAAAA=
					</data>
					<key>rev</key>
					<data>
					AigPAAAH
					</data>
					<key>revfb</key>
					<data>
					AigPAAAH
					</data>
					<key>revfu</key>
					<data>
					AigPAAAH
					</data>
				</dict>
				<key>GenericV3</key>
				<dict>
					<key>hwname</key>
					<data>
					c21jLWh1cm9ucml2ZXIA
					</data>
					<key>platform</key>
					<data>
					ajUyAAAAAAA=
					</data>
				</dict>
			</dict>
			<key>_STA</key>
			<integer>11</integer>
			<key>name</key>
			<data>
			QVBQMDAwMQA=
			</data>
		</dict>
	</dict>
	<key>LSMinimumSystemVersion</key>
	<string>10.6</string>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright © 2017 vit9696. All rights reserved.</string>
	<key>OSBundleCompatibleVersion</key>
	<string>1.0</string>
	<key>OSBundleLibraries</key>
	<dict>
		<key>as.vit9696.Lilu</key>
		<string>1.2.0</string>
		<key>com.apple.iokit.IOACPIFamily</key>
		<string>1.0.0d1</string>
		<key>com.apple.kernel.6.0</key>
		<string>7.9.9</string>
		<key>com.apple.kpi.bsd</key>
		<string>8.0.0</string>
		<key>com.apple.kpi.iokit</key>
		<string>8.0.0</string>
		<key>com.apple.kpi.libkern</key>
		<string>8.0.0</string>
		<key>com.apple.kpi.mach</key>
		<string>8.0.0</string>
		<key>com.apple.kpi.unsupported</key>
		<string>8.0.0</string>
	</dict>
	<key>OSBundleLibraries_x86_64</key>
	<dict>
		<key>as.vit9696.Lilu</key>
		<string>1.2.0</string>
		<key>com.apple.iokit.IOACPIFamily</key>
		<string>1.0.0d1</string>
		<key>com.apple.kpi.bsd</key>
		<string>10.0.0</string>
		<key>com.apple.kpi.iokit</key>
		<string>10.0.0</string>
		<key>com.apple.kpi.libkern</key>
		<string>10.0.0</string>
		<key>com.apple.kpi.mach</key>
		<string>10.0.0</string>
		<key>com.apple.kpi.unsupported</key>
		<string>10.0.0</string>
	</dict>
	<key>OSBundleRequired</key>
	<string>Root</string>
</dict>
</plist>
