<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ACPI</key>
	<dict>
		<key>Add</key>
		<array>
			<dict>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>Path</key>
				<string>SSDT-PLUG-z390.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>Path</key>
				<string>SSDT-EC-USBX-z390.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>Path</key>
				<string>SSDT-AWAC-z390.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>Path</key>
				<string>SSDT-PMC-z390.aml</string>
			</dict>
		</array>
		<key>Delete</key>
		<array/>
		<key>Patch</key>
		<array/>
		<key>Quirks</key>
		<dict>
			<key>FadtEnableReset</key>
			<false/>
			<key>NormalizeHeaders</key>
			<false/>
			<key>RebaseRegions</key>
			<false/>
			<key>ResetHwSig</key>
			<false/>
			<key>ResetLogoStatus</key>
			<false/>
			<key>SyncTableIds</key>
			<false/>
		</dict>
	</dict>
	<key>Booter</key>
	<dict>
		<key>MmioWhitelist</key>
		<array/>
		<key>Patch</key>
		<array/>
		<key>Quirks</key>
		<dict>
			<key>AllowRelocationBlock</key>
			<false/>
			<key>AvoidRuntimeDefrag</key>
			<true/>
			<key>DevirtualiseMmio</key>
			<true/>
			<key>DisableSingleUser</key>
			<false/>
			<key>DisableVariableWrite</key>
			<false/>
			<key>DiscardHibernateMap</key>
			<false/>
			<key>EnableSafeModeSlide</key>
			<true/>
			<key>EnableWriteUnprotector</key>
			<true/>
			<key>FixupAppleEfiImages</key>
			<false/>
			<key>ForceBooterSignature</key>
			<false/>
			<key>ForceExitBootServices</key>
			<false/>
			<key>ProtectMemoryRegions</key>
			<false/>
			<key>ProtectSecureBoot</key>
			<false/>
			<key>ProtectUefiServices</key>
			<true/>
			<key>ProvideCustomSlide</key>
			<true/>
			<key>ProvideMaxSlide</key>
			<integer>0</integer>
			<key>RebuildAppleMemoryMap</key>
			<true/>
			<key>ResizeAppleGpuBars</key>
			<integer>-1</integer>
			<key>SetupVirtualMap</key>
			<true/>
			<key>SignalAppleOS</key>
			<false/>
			<key>SyncRuntimePermissions</key>
			<true/>
		</dict>
	</dict>
	<key>DeviceProperties</key>
	<dict>
		<key>Add</key>
		<dict>
			<key>PciRoot(0x0)/Pci(0x1F,0x3)</key>
			<dict>
				<key>AAPL,slot-name</key>
				<string>Internal@0,31,3</string>
				<key>device_type</key>
				<string>Audio device</string>
				<key>hda-gfx</key>
				<string>onboard-1</string>
				<key>layout-id</key>
				<data>BwAAAA==</data>
				<key>model</key>
				<string>Cannon Lake PCH cAVS</string>
			</dict>
			<key>PciRoot(0x0)/Pci(0x2,0x0)</key>
			<dict>
				<key>AAPL,GfxYTile</key>
				<data>AQAAAA==</data>
				<key>AAPL,ig-platform-id</key>
				<data>AACbPg==</data>
				<key>AAPL,slot-name</key>
				<string>Internal@0,2,0</string>
				<key>device-id</key>
				<data>mz4AAA==</data>
				<key>device_type</key>
				<string>VGA compatible controller</string>
				<key>enable-hdmi-dividers-fix</key>
				<data>AQAAAA==</data>
				<key>enable-hdmi20</key>
				<data>AQAAAA==</data>
				<key>framebuffer-con0-busid</key>
				<data>BAAAAA==</data>
				<key>framebuffer-con0-enable</key>
				<data>AQAAAA==</data>
				<key>framebuffer-con0-flags</key>
				<data>mAAAAA==</data>
				<key>framebuffer-con0-index</key>
				<data>AAAAAA==</data>
				<key>framebuffer-con0-pipe</key>
				<data>EgAAAA==</data>
				<key>framebuffer-con0-type</key>
				<data>AAgAAA==</data>
				<key>framebuffer-con1-busid</key>
				<data>AQAAAA==</data>
				<key>framebuffer-con1-enable</key>
				<data>AQAAAA==</data>
				<key>framebuffer-con1-flags</key>
				<data>hwEAAA==</data>
				<key>framebuffer-con1-index</key>
				<data>AQAAAA==</data>
				<key>framebuffer-con1-pipe</key>
				<data>EgAAAA==</data>
				<key>framebuffer-con1-type</key>
				<data>AAQAAA==</data>
				<key>framebuffer-con2-busid</key>
				<data>AgAAAA==</data>
				<key>framebuffer-con2-enable</key>
				<data>AQAAAA==</data>
				<key>framebuffer-con2-flags</key>
				<data>hwEAAA==</data>
				<key>framebuffer-con2-index</key>
				<data>AgAAAA==</data>
				<key>framebuffer-con2-pipe</key>
				<data>EgAAAA==</data>
				<key>framebuffer-con2-type</key>
				<data>AAgAAA==</data>
				<key>framebuffer-fbmem</key>
				<data>AAAAAA==</data>
				<key>framebuffer-patch-enable</key>
				<data>AQAAAA==</data>
				<key>framebuffer-stolenmem</key>
				<data>AACQAw==</data>
				<key>framebuffer-unifiedmem</key>
				<data>AAAAwA==</data>
				<key>hda-gfx</key>
				<string>onboard-1</string>
				<key>model</key>
				<string>Intel UHD Graphics 630</string>
			</dict>
		</dict>
		<key>Delete</key>
		<dict/>
	</dict>
	<key>Kernel</key>
	<dict>
		<key>Add</key>
		<array>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>Lilu.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/Lilu</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>VirtualSMC.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/VirtualSMC</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>IntelBTPatcher.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/IntelBTPatcher</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>itlwm.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/itlwm</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>SmallTreeIntel8259x.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/SmallTreeIntel8259x</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>AppleALC.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/AppleALC</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>CPUFriend.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/CPUFriend</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>IntelBluetoothFirmware.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/IntelBluetoothFirmware</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>BlueToolFixup.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/BlueToolFixup</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>IntelMausi.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/IntelMausi</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>SMCProcessor.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/SMCProcessor</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>SMCSuperIO.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/SMCSuperIO</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>USBPorts.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string></string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>WhateverGreen.kext</string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/WhateverGreen</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
		</array>
		<key>Block</key>
		<array/>
		<key>Emulate</key>
		<dict>
			<key>Cpuid1Data</key>
			<data></data>
			<key>Cpuid1Mask</key>
			<data></data>
			<key>DummyPowerManagement</key>
			<false/>
			<key>MaxKernel</key>
			<string></string>
			<key>MinKernel</key>
			<string></string>
		</dict>
		<key>Force</key>
		<array/>
		<key>Patch</key>
		<array/>
		<key>Quirks</key>
		<dict>
			<key>AppleCpuPmCfgLock</key>
			<false/>
			<key>AppleXcpmCfgLock</key>
			<false/>
			<key>AppleXcpmExtraMsrs</key>
			<false/>
			<key>AppleXcpmForceBoost</key>
			<false/>
			<key>CustomSMBIOSGuid</key>
			<true/>
			<key>CustomPciSerialDevice</key>
			<false/>
			<key>DisableIoMapper</key>
			<true/>
			<key>DisableIoMapperMapping</key>
			<true/>
			<key>DisableLinkeditJettison</key>
			<true/>
			<key>DisableRtcChecksum</key>
			<true/>
			<key>ExtendBTFeatureFlags</key>
			<false/>
			<key>ExternalDiskIcons</key>
			<false/>
			<key>ForceAquantiaEthernet</key>
			<false/>
			<key>ForceSecureBootScheme</key>
			<false/>
			<key>IncreasePciBarSize</key>
			<false/>
			<key>LapicKernelPanic</key>
			<false/>
			<key>LegacyCommpage</key>
			<false/>
			<key>PanicNoKextDump</key>
			<true/>
			<key>PowerTimeoutKernelPanic</key>
			<true/>
			<key>ProvideCurrentCpuInfo</key>
			<false/>
			<key>SetApfsTrimTimeout</key>
			<integer>0</integer>
			<key>ThirdPartyDrives</key>
			<false/>
			<key>XhciPortLimit</key>
			<false/>
		</dict>
		<key>Scheme</key>
		<dict>
			<key>CustomKernel</key>
			<false/>
			<key>FuzzyMatch</key>
			<false/>
			<key>KernelArch</key>
			<string>x86_64</string>
			<key>KernelCache</key>
			<string>Auto</string>
		</dict>
	</dict>
	<key>Misc</key>
	<dict>
		<key>BlessOverride</key>
		<array/>
		<key>Boot</key>
		<dict>
			<key>ConsoleAttributes</key>
			<integer>0</integer>
			<key>HibernateMode</key>
			<string>None</string>
			<key>HibernateSkipsPicker</key>
			<true/>
			<key>HideAuxiliary</key>
			<true/>
			<key>InstanceIdentifier</key>
			<string></string>
			<key>LauncherOption</key>
			<string>Full</string>
			<key>LauncherPath</key>
			<string>Default</string>
			<key>PickerAttributes</key>
			<integer>17</integer>
			<key>PickerAudioAssist</key>
			<false/>
			<key>PickerMode</key>
			<string>External</string>
			<key>PickerVariant</key>
			<string>Acidanthera\GoldenGate</string>
			<key>PollAppleHotKeys</key>
			<false/>
			<key>ShowPicker</key>
			<true/>
			<key>TakeoffDelay</key>
			<integer>0</integer>
			<key>Timeout</key>
			<integer>-1</integer>
		</dict>
		<key>Debug</key>
		<dict>
			<key>AppleDebug</key>
			<true/>
			<key>ApplePanic</key>
			<true/>
			<key>DisableWatchDog</key>
			<true/>
			<key>DisplayDelay</key>
			<integer>0</integer>
			<key>DisplayLevel</key>
			<integer>2147483650</integer>
			<key>LogModules</key>
			<string>*</string>
			<key>SysReport</key>
			<false/>
			<key>Target</key>
			<integer>3</integer>
		</dict>
		<key>Entries</key>
		<array/>
		<key>Security</key>
		<dict>
			<key>AllowSetDefault</key>
			<true/>
			<key>ApECID</key>
			<integer>0</integer>
			<key>AuthRestart</key>
			<false/>
			<key>BlacklistAppleUpdate</key>
			<false/>
			<key>DmgLoading</key>
			<string>Signed</string>
			<key>EnablePassword</key>
			<false/>
			<key>ExposeSensitiveData</key>
			<integer>6</integer>
			<key>HaltLevel</key>
			<integer>2147483648</integer>
			<key>PasswordHash</key>
			<data></data>
			<key>PasswordSalt</key>
			<data></data>
			<key>ScanPolicy</key>
			<integer>0</integer>
			<key>SecureBootModel</key>
			<string>Default</string>
			<key>Vault</key>
			<string>Optional</string>
		</dict>
		<key>Serial</key>
		<dict>
			<key>Custom</key>
			<dict>
				<key>BaudRate</key>
				<integer>115200</integer>
				<key>ClockRate</key>
				<integer>1843200</integer>
				<key>DetectCable</key>
				<false/>
				<key>ExtendedTxFifoSize</key>
				<integer>64</integer>
				<key>FifoControl</key>
				<integer>7</integer>
				<key>LineControl</key>
				<integer>7</integer>
				<key>PciDeviceInfo</key>
				<data>/w==</data>
				<key>RegisterAccessWidth</key>
				<integer>8</integer>
				<key>RegisterBase</key>
				<integer>1016</integer>
				<key>RegisterStride</key>
				<integer>1</integer>
				<key>UseHardwareFlowControl</key>
				<false/>
				<key>UseMmio</key>
				<false/>
			</dict>
			<key>Init</key>
			<false/>
			<key>Override</key>
			<false/>
		</dict>
		<key>Tools</key>
		<array>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Auxiliary</key>
				<true/>
				<key>Comment</key>
				<string>Not signed for security reasons</string>
				<key>Enabled</key>
				<true/>
				<key>Flavour</key>
				<string>Auto</string>
				<key>FullNvramAccess</key>
				<false/>
				<key>Name</key>
				<string>UEFI Shell</string>
				<key>Path</key>
				<string>OpenShell.efi</string>
				<key>RealPath</key>
				<false/>
				<key>TextMode</key>
				<false/>
			</dict>
			<dict>
				<key>Arguments</key>
				<string>Shutdown</string>
				<key>Auxiliary</key>
				<true/>
				<key>Comment</key>
				<string>Perform shutdown</string>
				<key>Enabled</key>
				<true/>
				<key>Flavour</key>
				<string>Auto</string>
				<key>FullNvramAccess</key>
				<false/>
				<key>Name</key>
				<string>Shutdown</string>
				<key>Path</key>
				<string>ResetSystem.efi</string>
				<key>RealPath</key>
				<false/>
				<key>TextMode</key>
				<false/>
			</dict>
		</array>
	</dict>
	<key>NVRAM</key>
	<dict>
		<key>Add</key>
		<dict>
			<key>4D1EDE05-38C7-4A6A-9CC6-4BCCA8B38C14</key>
			<dict>
				<key>DefaultBackgroundColor</key>
				<data>AAAAAA==</data>
				<key>UIScale</key>
				<data>Ag==</data>
			</dict>
			<key>4D1FDA02-38C7-4A6A-9CC6-4BCCA8B30102</key>
			<dict>
				<key>rtc-blacklist</key>
				<data></data>
			</dict>
			<key>7C436110-AB2A-4BBB-A880-FE41995C9F82</key>
			<dict>
				<key>ForceDisplayRotationInEFI</key>
				<integer>0</integer>
				<key>SystemAudioVolume</key>
				<data>Rg==</data>
				<key>boot-args</key>
				<string>-wegnoegpu</string>
				<key>csr-active-config</key>
				<data>4AAAAA==</data>
				<key>prev-lang:kbd</key>
				<data></data>
				<key>run-efi-updater</key>
				<string>No</string>
				<key>bluetoothExternalDongleFailed</key>
				<data>AA==</data>
				<key>bluetoothInternalControllerInfo</key>
				<data>AAAAAAAAAAAAAAAAAAA=</data>
			</dict>
		</dict>
		<key>Delete</key>
		<dict>
			<key>4D1EDE05-38C7-4A6A-9CC6-4BCCA8B38C14</key>
			<array>
				<string>UIScale</string>
				<string>DefaultBackgroundColor</string>
			</array>
			<key>4D1FDA02-38C7-4A6A-9CC6-4BCCA8B30102</key>
			<array>
				<string>rtc-blacklist</string>
			</array>
			<key>7C436110-AB2A-4BBB-A880-FE41995C9F82</key>
			<array>
				<string>boot-args</string>
				<string>ForceDisplayRotationInEFI</string>
			</array>
		</dict>
		<key>LegacyOverwrite</key>
		<false/>
		<key>WriteFlash</key>
		<true/>
		<key>LegacySchema</key>
		<dict>
			<key>7C436110-AB2A-4BBB-A880-FE41995C9F82</key>
			<array>
				<string>EFILoginHiDPI</string>
				<string>EFIBluetoothDelay</string>
				<string>LocationServicesEnabled</string>
				<string>SystemAudioVolume</string>
				<string>SystemAudioVolumeDB</string>
				<string>SystemAudioVolumeSaved</string>
				<string>bluetoothActiveControllerInfo</string>
				<string>bluetoothInternalControllerInfo</string>
				<string>flagstate</string>
				<string>fmm-computer-name</string>
				<string>fmm-mobileme-token-FMM</string>
				<string>fmm-mobileme-token-FMM-BridgeHasAccount</string>
				<string>nvda_drv</string>
				<string>prev-lang:kbd</string>
			</array>
			<key>8BE4DF61-93CA-11D2-AA0D-00E098032B8C</key>
			<array>
				<string>Boot0080</string>
				<string>Boot0081</string>
				<string>Boot0082</string>
				<string>BootNext</string>
				<string>BootOrder</string>
			</array>
		</dict>
	</dict>
	<key>PlatformInfo</key>
	<dict>
		<key>Automatic</key>
		<true/>
		<key>CustomMemory</key>
		<false/>
		<key>Generic</key>
		<dict>
			<key>AdviseFeatures</key>
			<false/>
			<key>MaxBIOSVersion</key>
			<false/>
			<key>MLB</key>
			<string>C02923207J9LNV9A8</string>
			<key>ProcessorType</key>
			<integer>4105</integer>
			<key>ROM</key>
			<data>qF5F4oQy</data>
			<key>SpoofVendor</key>
			<false/>
			<key>SystemMemoryStatus</key>
			<string>Auto</string>
			<key>SystemProductName</key>
			<string>iMac19,1</string>
			<key>SystemSerialNumber</key>
			<string>C02YTCYAJV3Q</string>
			<key>SystemUUID</key>
			<string>FE28FBDC-AD3D-4AC1-B188-74BD9903DCC9</string>
		</dict>
		<key>UpdateDataHub</key>
		<true/>
		<key>UpdateNVRAM</key>
		<true/>
		<key>UpdateSMBIOS</key>
		<true/>
		<key>UpdateSMBIOSMode</key>
		<string>Custom</string>
		<key>UseRawUuidEncoding</key>
		<false/>
	</dict>
	<key>UEFI</key>
	<dict>
		<key>APFS</key>
		<dict>
			<key>EnableJumpstart</key>
			<true/>
			<key>GlobalConnect</key>
			<false/>
			<key>HideVerbose</key>
			<true/>
			<key>JumpstartHotPlug</key>
			<false/>
			<key>MinDate</key>
			<integer>0</integer>
			<key>MinVersion</key>
			<integer>0</integer>
		</dict>
		<key>AppleInput</key>
		<dict>
			<key>AppleEvent</key>
			<string>Builtin</string>
			<key>CustomDelays</key>
			<false/>
			<key>GraphicsInputMirroring</key>
			<false/>
			<key>KeyInitialDelay</key>
			<integer>50</integer>
			<key>KeySubsequentDelay</key>
			<integer>5</integer>
			<key>PointerDwellClickTimeout</key>
			<integer>0</integer>
			<key>PointerDwellDoubleClickTimeout</key>
			<integer>0</integer>
			<key>PointerDwellRadius</key>
			<integer>0</integer>
			<key>PointerPollMask</key>
			<integer>-1</integer>
			<key>PointerPollMin</key>
			<integer>0</integer>
			<key>PointerPollMax</key>
			<integer>0</integer>
			<key>PointerSpeedDiv</key>
			<integer>7</integer>
			<key>PointerSpeedMul</key>
			<integer>3</integer>
		</dict>
		<key>Audio</key>
		<dict>
			<key>AudioCodec</key>
			<integer>0</integer>
			<key>AudioDevice</key>
			<string>PciRoot(0x0)/Pci(0x1F,0x3)</string>
			<key>AudioOutMask</key>
			<integer>-1</integer>
			<key>AudioSupport</key>
			<false/>
			<key>DisconnectHda</key>
			<false/>
			<key>MaximumGain</key>
			<integer>-15</integer>
			<key>MinimumAssistGain</key>
			<integer>-30</integer>
			<key>MinimumAudibleGain</key>
			<integer>-128</integer>
			<key>PlayChime</key>
			<string>Auto</string>
			<key>ResetTrafficClass</key>
			<false/>
			<key>SetupDelay</key>
			<integer>0</integer>
		</dict>
		<key>ConnectDrivers</key>
		<true/>
		<key>Drivers</key>
		<array>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>HfsPlus.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>ResetNvramEntry.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>ToggleSipEntry.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>OpenRuntime.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>OpenCanopy.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>AudioDxe.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string></string>
				<key>Enabled</key>
				<true/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>CrScreenshotDxe.efi</string>
			</dict>
		</array>
		<key>Input</key>
		<dict>
			<key>KeyFiltering</key>
			<false/>
			<key>KeyForgetThreshold</key>
			<integer>5</integer>
			<key>KeySupport</key>
			<true/>
			<key>KeySupportMode</key>
			<string>Auto</string>
			<key>KeySwap</key>
			<false/>
			<key>PointerSupport</key>
			<false/>
			<key>PointerSupportMode</key>
			<string>ASUS</string>
			<key>TimerResolution</key>
			<integer>50000</integer>
		</dict>
		<key>Output</key>
		<dict>
			<key>ClearScreenOnModeSwitch</key>
			<false/>
			<key>ConsoleFont</key>
			<string></string>
			<key>ConsoleMode</key>
			<string></string>
			<key>DirectGopRendering</key>
			<false/>
			<key>ForceResolution</key>
			<false/>
			<key>GopBurstMode</key>
			<false/>
			<key>GopPassThrough</key>
			<string>Disabled</string>
			<key>IgnoreTextInGraphics</key>
			<false/>
			<key>InitialMode</key>
			<string>Graphics</string>
			<key>ProvideConsoleGop</key>
			<true/>
			<key>ReconnectGraphicsOnConnect</key>
			<false/>
			<key>ReconnectOnResChange</key>
			<false/>
			<key>ReplaceTabWithSpace</key>
			<false/>
			<key>Resolution</key>
			<string>Max</string>
			<key>SanitiseClearScreen</key>
			<false/>
			<key>TextRenderer</key>
			<string>BuiltinGraphics</string>
			<key>UgaPassThrough</key>
			<false/>
			<key>UIScale</key>
			<integer>-1</integer>
		</dict>
		<key>ProtocolOverrides</key>
		<dict>
			<key>AppleAudio</key>
			<false/>
			<key>AppleBootPolicy</key>
			<false/>
			<key>AppleDebugLog</key>
			<false/>
			<key>AppleEg2Info</key>
			<false/>
			<key>AppleFramebufferInfo</key>
			<false/>
			<key>AppleImageConversion</key>
			<false/>
			<key>AppleImg4Verification</key>
			<false/>
			<key>AppleKeyMap</key>
			<false/>
			<key>AppleRtcRam</key>
			<false/>
			<key>AppleSecureBoot</key>
			<false/>
			<key>AppleSmcIo</key>
			<false/>
			<key>AppleUserInterfaceTheme</key>
			<false/>
			<key>DataHub</key>
			<false/>
			<key>DeviceProperties</key>
			<false/>
			<key>FirmwareVolume</key>
			<true/>
			<key>HashServices</key>
			<false/>
			<key>OSInfo</key>
			<false/>
			<key>PciIo</key>
			<false/>
			<key>UnicodeCollation</key>
			<false/>
		</dict>
		<key>Quirks</key>
		<dict>
			<key>ActivateHpetSupport</key>
			<false/>
			<key>DisableSecurityPolicy</key>
			<false/>
			<key>EnableVectorAcceleration</key>
			<true/>
			<key>EnableVmx</key>
			<false/>
			<key>ExitBootServicesDelay</key>
			<integer>0</integer>
			<key>ForceOcWriteFlash</key>
			<false/>
			<key>ForgeUefiSupport</key>
			<false/>
			<key>IgnoreInvalidFlexRatio</key>
			<true/>
			<key>ReleaseUsbOwnership</key>
			<false/>
			<key>ReloadOptionRoms</key>
			<false/>
			<key>RequestBootVarRouting</key>
			<true/>
			<key>ResizeGpuBars</key>
			<integer>-1</integer>
			<key>ResizeUsePciRbIo</key>
			<false/>
			<key>ShimRetainProtocol</key>
			<false/>
			<key>TscSyncTimeout</key>
			<integer>0</integer>
			<key>UnblockFsConnect</key>
			<false/>
		</dict>
		<key>ReservedMemory</key>
		<array/>
		<key>Unload</key>
		<array/>
	</dict>
</dict>
</plist>