<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>23H222</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleExecutable</key>
	<string>AppleALC</string>
	<key>CFBundleIdentifier</key>
	<string>as.vit9696.AppleALC</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>AppleALC</string>
	<key>CFBundlePackageType</key>
	<string>KEXT</string>
	<key>CFBundleShortVersionString</key>
	<string>1.9.3</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CFBundleVersion</key>
	<string>1.9.3</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string></string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>14.2</string>
	<key>DTSDKBuild</key>
	<string>23C53</string>
	<key>DTSDKName</key>
	<string>macosx14.2</string>
	<key>DTXcode</key>
	<string>1520</string>
	<key>DTXcodeBuild</key>
	<string>15C500b</string>
	<key>IOKitPersonalities</key>
	<dict>
		<key>ALCUserClientProvider</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>as.vit9696.AppleALC</string>
			<key>IOClass</key>
			<string>ALCUserClientProvider</string>
			<key>IOMatchCategory</key>
			<string>ALCUserClientProvider</string>
			<key>IOProbeScore</key>
			<integer>1000</integer>
			<key>IOProviderClass</key>
			<string>IOHDACodecDevice</string>
			<key>IOUserClientClass</key>
			<string>ALCUserClient</string>
		</dict>
		<key>as.vit9696.AppleALC</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>as.vit9696.AppleALC</string>
			<key>HDAConfigDefault</key>
			<array>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Baio77 - ALC295 Lenovo_X1_Tablet_3°Gen</string>
					<key>CodecID</key>
					<integer>283902613</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4XAUcfkAFHDAICFxwgAhcd
					EAIXHiECFx8AAhcMAgEnHDABJx0AASceoAEn
					H5ABlxxAAZcdEAGXHoEBlx8A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>69</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>hoaug - ALC295 - Razer Blade 15 2018 Advanced</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQABJx6gAScfkAGHHHABhx0QAYce
					gQGHHwACFxwgAhcdEAIXHiECFx8AAUccEAFH
					HQEBRx4XAUcfkAFHDAICBQAQAgQMIQIFACoC
					BI8Y
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>25</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgGHByU=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906408</integer>
					<key>CodecName</key>
					<string>Realtek ALC S1220A Hoangtu92, 7.1 outputs (MSI X470 Gaming Pro Carbon)</string>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHBABRx1QAUce
					EQFHHwEBRwwCAVccIAFXHRABVx4BAVcfAQFn
					HDABZx1gAWceAQFnHwEBdxzwAXcdAAF3HgAB
					dx9AAYccQAGHHSABhx4BAYcfAQGXHFABlx2Q
					AZcegQGXHwIBpxxgAacdMAGnHoEBpx8BAbcc
					cAG3HUABtx4hAbcfAgG3DAIB1xzwAdcdAAHX
					HgAB1x9AAeccgAHnHRAB5x5FAecfAQIFAAcC
					BAPAAgUAQwIEMAUCBQBdAgQWFgIFAF4CBBAQ
					AgUAXwIEv8UCBQBqAgQCDgIFAG8CBIAz
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Floron - Realtek ALC256 (3246) for Honor MagicBook Pro HBB-WAH9</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAICFxwgAhcd
					EAIXHiECFx8EAhcMAgEnHDABJx0BAScepgEn
					H5A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>95</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC282 Lenovo-IdeaPad-Z510 by hoseinrez</string>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHCABRx0BAUce
					FwFHH5ABRwwCAXcc8AF3HQABdx4AAXcfQAGH
					HPABhx0AAYceAAGHH0ABpxzwAacdAAGnHgAB
					px9AAbcc8AG3HQABtx4AAbcfQAHXHPAB1x0A
					AdceAAHXH0AB5xzwAecdAAHnHgAB5x9AAZcc
					MAGXHRABlx6BAZcfBAIXHEACFx0QAhceIQIX
					HwQ=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>69</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>agasecond - Realtek ALC256 (3246) for Xiaomi Pro Enhanced 2019</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxzwAUcdAAFHHgABRx9AAYcc8AGH
					HQABhx4AAYcfQAGXHEABlx0QAZceqwGXHwQB
					pxzwAacdAAGnHgABpx9AAbccEAG3HQEBtx4X
					AbcfkAG3DAIB1xzwAdcdAAHXHgAB1x9AAecc
					8AHnHQAB5x4AAecfQAIXHDACFx0QAhceKwIX
					HwQCFwwCAgUAEAIEACACBQBGAgQwpA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>69</integer>
					<key>WakeConfigData</key>
					<data>
					AbcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Custom ALC269VD for ThinkPad T430</string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHEABRx0BAUce
					FwFHH5ABVxxQAVcdEAFXHiEBVx8CAXcc8AF3
					HQABdx4AAXcfQAGHHCABhx0QAYcegQGHHwIB
					lxxwAZcdEAGXHqEBlx8CAacccAGnHRABpx6h
					AacfAgG3HHABtx0QAbceoQG3HwIB1xxwAdcd
					EAHXHqEB1x8CAecccAHnHRAB5x6hAecfAgFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>23</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>Toshiba Satellite Pro C50</string>
					<key>ConfigData</key>
					<data>
					AKccQACnHRAApx4hAKcfAQC3HCAAtx0QALce
					gQC3HwEAxxzwAMcdAADHHgAAxx9AANccMADX
					HQEA1x4QANcfkADnHBAA5x0BAOceoADnH5AA
					9xzwAPcdAAD3HgAA9x9AAYcc8AGHHQABhx4A
					AYcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>frankiezdh - Conexant CX20632 for HP ProDesk 480 G4</string>
					<key>ConfigData</key>
					<data>
					AZccUAGXHRABlx4hAZcfAQGnHBABpx0QAace
					oQGnHwEBxxyAAccdQQHHHgEBxx8BAdccIAHX
					HTEB1x6BAdcfAQHXDAIB9xxAAfcdAQH3Hh8B
					9x+R
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>23</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>phucnguyen2411 - CX20632 HP Elitedesk 800 G5 mini</string>
					<key>ConfigData</key>
					<data>
					AXcMAAG3DAAB1xwRAdcdEQHXHoEB1x8CAdcM
					AgHHHCABxx0RAcceAQHHHwIBpwckAaccMAGn
					HRABpx6BAacfAgGXHEABlx0QAZceIQGXHwIB
					9xxRAfcdAQH3Hh8B9x+Q
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>20</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAAGnByQBtwwAAdcMAA==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>Andres ZeroCross - Axioo MyPC One Pro H5</string>
					<key>ConfigData</key>
					<data>
					AYccEAGHHQEBhx4XAYcfkAGHDAIBlxxAAZcd
					EAGXHiEBlx8CAbccMAG3HRABtx6BAbcfAQG3
					DAIBpxxAAacdAQGnHqABpx+Q
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AYcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>CX20632 by Daniel</string>
					<key>ConfigData</key>
					<data>
					AfccEAH3HQEB9x4XAfcfkgH3DAIBpxwgAacd
					EAGnHosBpx8CAZccQAGXHRABlx4rAZcfAgHH
					HNABxx1AAcceIQHHHwIBhxzwAYcdAAGHHgAB
					hx9AAbcc8AG3HQABtx4AAbcfQAHXHPAB1x0A
					AdceAAHXH0AB5xzwAecdAAHnHgAB5x9AAgcc
					8AIHHQACBx4AAgcfQAIXHPACFx0AAhceAAIX
					H0ACZxzwAmcdAAJnHgACZx9A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Creative CA0132, default</string>
					<key>CodecID</key>
					<integer>285343761</integer>
					<key>ConfigData</key>
					<data>
					ABcWHwAXFx4BVwoBAVcXDQFXGCQAtwwAANcc
					8ADXHQAA1x4AANcfQAEXBwQBJx+QATceAAE3
					H0ABhx4AAYcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>0</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Creative CA0132: Alienware 15 R2</string>
					<key>CodecID</key>
					<integer>285343761</integer>
					<key>ConfigData</key>
					<data>
					AVcKAQFXDwEAFxceABcWHwAXFRABVxcNAVcY
					IAC3HhAAtx+QANcc8ADXHQAA1x4AANcfQADn
					HPAA5x0AAOceAADnH0AA9xwgAPcfAQEHHPAB
					Bx0AAQceAAEHH0ABFxxAARcegQEXHwEBJxwA
					AScfkAE3HPABNx0AATceAAE3H0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Creative CA0132: Alienware 17, Desktop 2xIn 3xOut</string>
					<key>CodecID</key>
					<integer>285343761</integer>
					<key>ConfigData</key>
					<data>
					AVcKAQFXDwEAFxceABcWHwAXFRABVxcNAVcY
					JAC3HhAAtx+QANcc8ADXHQAA1x4AANcfQADn
					HPAA5x0AAOceAADnH0AA9xwgAPcfAQEHHDAB
					Bx4hAQcfAQEXHEABFx6BARcfAQEnHAABJx+Q
					ATcc8AE3HQABNx4AATcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>2</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Creative CA0132, 2.0 + rear line-out</string>
					<key>CodecID</key>
					<integer>285343761</integer>
					<key>ConfigData</key>
					<data>
					ABcXHgAXFRQBVwoBAScIgQFnCIABVxcNAVcY
					JADXHPAA1x0AANceAADXH0AA5xzwAOcdAADn
					HgAA5x9AAQceAQEnH5A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Creative CA0132: R3Di default</string>
					<key>CodecID</key>
					<integer>285343761</integer>
					<key>ConfigData</key>
					<data>
					AVcKAQFXDwEAFxceABcWHwAXFRQBVxcNAVcY
					JAC3HUEA1xzwANcdAADXHgAA1x9AAOcc8ADn
					HQAA5x4AAOcfQAD3HCAA9x8BAQccMAEHHgEB
					Bx8BARccQAEXHgEBFx8BASccAAEnH5ABNxzw
					ATcdAAE3HgABNx9AAYcc8AGHHQABhx4AAYcf
					QA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Creative CA0132, 2.0 front HP + Mic </string>
					<key>CodecID</key>
					<integer>285343761</integer>
					<key>ConfigData</key>
					<data>
					ABcXHgAXFRIBVwoBAScIgQFnCIABVxcNAVcY
					IQC3HiEA1xzwANcdAADXHgAA1x9AAQceAQEX
					BwQBJx+Q
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Creative CA0132, 5.1 with front HP</string>
					<key>CodecID</key>
					<integer>285343761</integer>
					<key>ConfigData</key>
					<data>
					ABcXHgAXFRABVwoBAScIgQFnCIABVxcNAVcY
					JADXHPAA1x0AANceAADXH0ABBx4hAScfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>6</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Creative CA0132: ZxRi</string>
					<key>CodecID</key>
					<integer>285343761</integer>
					<key>ConfigData</key>
					<data>
					AVcKAQFXDwEAF5AzABcXDwAXFg8AFxUNAVcX
					GAFXGBQBVxcaAVcYkQFQoCABUQAYAV8BAAFT
					AIMBVAAAAVcXDQFXGCQAtx1BANcc8ADXHQAA
					1x4AANcfQADnHPAA5x0AAOceAADnH0AA9xwg
					APcfAQEHHDABBx4BAQcfAQEXHEABFx4BARcf
					AQEnH5ABNxzwATcdAAE3HgABNx9AAYcc8AGH
					HQABhx4AAYcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Creative CA0132 by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>285343761</integer>
					<key>ConfigData</key>
					<data>
					IVcKAQC3HBAAtx1AALceAQC3HwEAxxwgAMcd
					gADHHkUAxx8BANccIADXHUAA1x4BANcfAQD3
					HDAA9x1AAPceIQD3HwEBBxxAAQcdQAEHHiEB
					Bx8CARccUAEXHUABFx4BARcfAQEnHFABJx2Q
					AScepwEnH5A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>9</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Creative CA0132 by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>285343761</integer>
					<key>ConfigData</key>
					<data>
					AVcKAQAXFRQBJwiBAWcIgAFXFw0BVxgkIQce
					AQEnH5A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>10</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom Creative CA0132 5.1 channel</string>
					<key>CodecID</key>
					<integer>285343761</integer>
					<key>ConfigData</key>
					<data>
					IVcD8CFXFw0hVxgkIVcPgCC3HCAgtx1AILce
					ASC3HwEgxxxgIMcdICDHHkUgxx8BINcc8CDX
					HQAg1x4AINcfQCDnHPAg5x0AIOceACDnH0Ag
					9xwvIPcdQCD3HiEg9x8BIQccMCEHHUAhBx4h
					IQcfASEXHEAhFx0QIRceASEXHwEhJxwQIScd
					kSEnHqEhJx+QITcc8CE3HQAhNx4AITcfQCGH
					HFAhhx1gIYceASGHHwE=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom Creative CA0132</string>
					<key>CodecID</key>
					<integer>285343761</integer>
					<key>ConfigData</key>
					<data>
					AVcKAQAXFx8AFxUQAScIgQFnCIABVxcNAVcY
					JAEnH5A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Creative CA0132 5.1 channel for Alienware-M17X-R4 by DalianSky</string>
					<key>CodecID</key>
					<integer>285343761</integer>
					<key>ConfigData</key>
					<data>
					ALccEAC3HUEAtx4QALcfkADHHCAAxx0gAMce
					RQDHHwEA1xzwANcdAADXHgAA1x9AAOcc8ADn
					HQAA5x4AAOcfQAD3HFAA9x1AAPceIQD3HwEB
					BxzwAQcdAAEHHgABBx9AARcccAEXHRABFx4B
					ARcfAQEnHIABJx2RASceoAEnH5ABNxzwATcd
					AAE3HgABNx9AAYcc8AGHHQABhx4AAYcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Cirrus Logic CS4210</string>
					<key>CodecID</key>
					<integer>269697552</integer>
					<key>ConfigData</key>
					<data>
					AFccEABXHUAAVx4hAFcfAABnHCAAZx0AAGce
					FwBnH5AAdxwwAHcdkAB3HoEAdx8AAJccQACX
					HQAAlx6gAJcfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Cirrus Logic -CS4213</string>
					<key>CodecID</key>
					<integer>269697555</integer>
					<key>ConfigData</key>
					<data>
					AEccEABHHRAARx4hAEcfAABXHCAAVx0AAFce
					FwBXH5AAZxwwAGcdEABnHoEAZx8AAHccQAB3
					HQAAdx6gAHcfkABXDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Goldfish64 - AD1884 - Panasonic Toughbook CF-30</string>
					<key>CodecID</key>
					<integer>299112580</integer>
					<key>ConfigData</key>
					<data>
					ARccIAEXHRABFx4hARcfAQEnHBABJx0BASce
					EAEnH5ABJwwCATcc8AE3HQABNx4AATcfQAFH
					HDABRx0QAUcegQFHHwEBVxzwAVcdAAFXHgAB
					Vx9AAWcc8AFnHQABZx4AAWcfQAGnHPABpx0A
					AaceAAGnH0ABtxzwAbcdAAG3HgABtx9AAccc
					8AHHHQABxx4AAccfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - AD1984 - for_IBM_Lenovo_ThinkPad_T61_T61p</string>
					<key>CodecID</key>
					<integer>299112836</integer>
					<key>ConfigData</key>
					<data>
					ARccEAEXHUABFx4hARcfAAFHHCABRx1QAUce
					gQFHHwABJxwwAScdAAEnHhcBJx+QAScMAgFX
					HEABVx0AAVcepwFXH5ABxxyAAccdUAHHHoEB
					xx8BAbccoAG3HRABtx5EAbcfIQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - AD1984A</string>
					<key>CodecID</key>
					<integer>299112778</integer>
					<key>ConfigData</key>
					<data>
					ISccECEnHUAhJx4BIScfASFHHCAhRx2QIUce
					oSFHHwIhVxwwIVcdMCFXHoEhVx8BIRccQCEX
					HUAhFx4hIRcfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - AD1984A - Version2</string>
					<key>CodecID</key>
					<integer>299112778</integer>
					<key>ConfigData</key>
					<data>
					ISccECEnHUAhJx4RIScfkCFHHCAhRx2QIUce
					oSFHHwIhVxwwIVcdMCFXHoEhVx8BIRccQCEX
					HUAhFx4hIRcfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>AD1984A - giesteira</string>
					<key>CodecID</key>
					<integer>299112778</integer>
					<key>ConfigData</key>
					<data>
					ASccAAEnHUABJx4BAScfAQHHHBABxx0wAcce
					gQHHHwEBdxwgAXcdEAF3HqYBdx+5ARccMAEX
					HUABFx4hARcfAQFnHEABZx1AAWceFwFnH5EB
					pxxQAacdEAGnHvcBpx9RAbccYAG3HWABtx5E
					AbcfAQE3HPABNx0QATceHwE3H1E=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>44</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>AD1988A by chrome</string>
					<key>CodecID</key>
					<integer>299112840</integer>
					<key>ConfigData</key>
					<data>
					ARccQAEXHUABFx4hARcfAQEnHAABJx1AASce
					EQEnHwEBNxzwATcdAAE3HgABNx9AAUccYAFH
					HZABRx6gAUcfkAFXHIABVx0wAVcegQFXHwEB
					ZxwgAWcdEAFnHgEBZx8BAXccUAF3HZABdx6B
					AXcfAQGHHPABhx0AAYceAAGHH0ABtxygAbcd
					8QG3HkUBtx8BAccc8AHHHQABxx4AAccfQAJH
					HBACRx1gAkceAQJHHwECVxwwAlcdIAJXHgEC
					Vx8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - ADI-1988B</string>
					<key>CodecID</key>
					<integer>299112843</integer>
					<key>ConfigData</key>
					<data>
					ARccEAEXHUABFx4hARcfAQEnHCABJx1AASce
					AQEnHwEBRxxAAUcdkAFHHqEBRx8BAVccUAFX
					HTABVx6BAVcfAQF3HHABdx2QAXceoQF3HwEB
					txzwAbcd8QG3HkUBtx8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - ADI-1988B</string>
					<key>CodecID</key>
					<integer>299112843</integer>
					<key>ConfigData</key>
					<data>
					ARccEAEXHUABFx4hARcfAQEnHCABJx1AASce
					EQEnHwEBRxwwAUcdkAFHHqABRx+QAWccQAFn
					HRABZx4BAWcfAQF3HFABdx2QAXcegQF3HwEB
					txxgAbcd8QG3HkUBtx8BAccccAHHHfEBxx7F
					AccfAQHXHIAB1x3xAdceVgHXHxgCRxyQAkcd
					YAJHHgECRx8BAlcckAJXHSACVx4BAlcfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>0x11d4198b</string>
					<key>CodecID</key>
					<integer>299112843</integer>
					<key>Comment</key>
					<string>Custom AD1988B by Rodion</string>
					<key>ConfigData</key>
					<data>
					AXccIAF3HZABdx6gAXcfkQFHHCEBRx2QAUce
					gQFHHwIBJxwQAScdQAEnHhEBJx8BAkccEQJH
					HWACRx4RAkcfAQFnHBIBZx0QAWceEQFnHwEC
					VxwUAlcdIAJXHhECVx8BAccc8AHHHQABxx4A
					AccfQAE3HPABNx0AATceAAE3H0ABpxzwAacd
					AAGnHgABpx9AAYcc8AGHHQABhx4AAYcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - ADI-2000B</string>
					<key>CodecID</key>
					<integer>299145371</integer>
					<key>ConfigData</key>
					<data>
					ARccEAEXHUABFx4hARcfAQEnHCABJx1AASce
					AQEnHwEBRxxAAUcdkAFHHqEBRx8BAVccUAFX
					HTABVx6BAVcfAQF3HHABdx2QAXceoQF3HwEB
					txzwAbcd8QG3HkUBtx8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - ADI-2000B</string>
					<key>CodecID</key>
					<integer>299145371</integer>
					<key>ConfigData</key>
					<data>
					ARccMAEXHUABFx4hARcfAQEnHBABJx1AASce
					EQEnHwEBRxxAAUcdkAFHHqABRx+QAWccUAFn
					HRABZx4BAWcfAQF3HCABdx2QAXcegQF3HwEB
					txygAbcd8QG3HkUBtx8BAcccYAHHHfEBxx7F
					AccfAQHXHLAB1x3xAdceVgHXHxgCRxxwAkcd
					YAJHHgECRx8BAlccgAJXHSACVx4BAlcfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC215 for HP 830 G6 for 965987400abc</string>
					<key>CodecID</key>
					<integer>283902485</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfmQIXHCACFx0QAhce
					IQIXHwECFwwCASccMAEnHRgBJx6BAScfAQGX
					HEABlx0BAZcepgGXH5kBRwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>18</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Goldfish64 - ALC221 for HP Compaq Pro 4300/Pro 6300/Elite 8300 (All Form Factors)</string>
					<key>CodecID</key>
					<integer>283902497</integer>
					<key>ConfigData</key>
					<data>
					AUccIAFHHUABRx4BAUcfAQFHDAIBdxwQAXcd
					AQF3HhcBdx+QAXcMAgGnHEABpx0QAacegQGn
					HwIBpwckAbccMAG3HTABtx6BAbcfAQIXHFAC
					Fx0QAhceIQIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgF3DAIBpwckAhcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - ALC221 for HP ELITE DESK 800 G1</string>
					<key>CodecID</key>
					<integer>283902497</integer>
					<key>ConfigData</key>
					<data>
					AUccIAFHHUABRx4RAUcfkQFHDAIBpxwwAacd
					EAGnHoEBpx8CAXccQAF3HQABdx4XAXcfkAG3
					HFABtx0wAbcegQG3HwECFxxwAhcdEAIXHiEC
					Fx8CAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC221 for HP ProDesk 400 G2 Desktop Mini PC by dragonbbc</string>
					<key>CodecID</key>
					<integer>283902497</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQEBdx4XAXcfkAF3DAICFxwgAhcd
					EAIXHiECFx8CAaccMAGnHRABpx6BAacfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>88</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC222 aka ALC3205-CG for HP EliteDesk 800 G6 Mini</string>
					<key>CodecID</key>
					<integer>283902498</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQEBdx4XAXcfkAF3DAIBhxxAAYcd
					AAGHHqABhx+QAhccUAIXHRACFx4hAhcfAgIX
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC225/ALC3253 on dell 7579 by ChalesYu</string>
					<key>CodecID</key>
					<integer>283902501</integer>
					<key>ConfigData</key>
					<data>
					ASccUAEnHQEBJx6mAScftwE3HAABNx0AATce
					AAE3H0ABRxywAUcdAQFHHhcBRx+QAWcc8AFn
					HREBZx4RAWcfQQF3HPABdx0RAXceEQF3H0EB
					hxzwAYcdEQGHHhEBhx9BAZccQAGXHRABlx6B
					AZcfAQGnHPABpx0RAaceEQGnH0EBtxzwAbcd
					EQG3HhEBtx9BAdccAQHXHQAB1x5gAdcfQAHn
					HPAB5x0RAeceEQHnH0ECFxwgAhcdEAIXHiEC
					Fx8EAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC225/ALC3253 for Dell Inspiron 17-7779 by Constanta</string>
					<key>CodecID</key>
					<integer>283902501</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQABJx6gAScfkAFHHEABRx0AAUce
					FwFHH5ABlxxwAZcdEAGXHoEBlx8AAhccIAIX
					HRACFx4hAhcfAAFHDAICFwwCAZcHJQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>30</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC225/ALC3253 by ChalesYu</string>
					<key>CodecID</key>
					<integer>283902501</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfmQG3HCABtx0AAbce
					FwG3H5kBlxwwAZcdEAGXHoEBlx8CAhccQAIX
					HRACFx4hAhcfAgG3DAIBRwwCAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>33</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC225/ALC3253 for Dell Inspiron 15-5379 by fast900</string>
					<key>CodecID</key>
					<integer>283902501</integer>
					<key>ConfigData</key>
					<data>
					ASccUAEnHQABJx6gAScfkAFHHEABRx0AAUce
					FwFHH5ABlxxwAZcdEAGXHoEBlx8AAhccIAIX
					HRACFx4hAhcfAAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>90</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres Laptop Patch ALC230 Lenovo 310-14ISK</string>
					<key>CodecID</key>
					<integer>283902512</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAFHHCABRx0AAUce
					FwFHH5ABRwwCAZccMAGXHRABlx6BAZcfBAIX
					HEACFx0QAhceKwIXHwQCFwwCATccUAE3HQAB
					Nx4AATcfQAHXHGAB1x2wAdceZgHXH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC230 for Lenovo Ideapad 320 by maiconjs</string>
					<key>CodecID</key>
					<integer>283902512</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAICFxwgAhcd
					EAIXHiECFx8TAhcMAgEnHDABJx0BASceoAEn
					H5ABlxxAAZcdEQGXHoEBlx+T
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>20</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC233</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4XAUcfkAGXHCABlx0QAZce
					qwGXHwMBpxwwAacdAAGnHqABpx+QAhccQAIX
					HRACFx4rAhcfAwFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom Realtek ALC233 (3236)</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4XAUcfkAG3HCABtx0AAbce
					oAG3H5ACFxwwAhcdEAIXHiECFx8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>4</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC233/ALC3236</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4XAUcfkAGXHCABlx2QAZce
					iwGXHwIBtxwwAbcdkAG3HqABtx+QAhccQAIX
					HUACFx4rAhcfAgFHDAIBtwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC233 for Asus X550LC</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4XAUcfmQIXHCACFx0QAhce
					IQIXHwMBpxwwAacdAQGnHqABpx+ZAZccQAGX
					HRABlx6BAZcfAw==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres ZeroCross - Realtek ALC233 for Asus A451LB-WX076D</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAGXHCABlx0QAZce
					gQGXHwQBtxwwAbcdAQG3HqABtx+QAhccQAIX
					HRACFx4hAhcfBAFHDAIBtwwCAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom for Realtek ALC233 for SONY VAIO Fit 14E(SVF14316SCW) by SquallATF</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQEBJx6mAScfkAGnHDABpx1QAace
					gQGnHwMBRxwQAUcdAQFHHhcBRx+QAUcMAgIX
					HCACFx0QAhceIQIXHwMCFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>27</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>JudySL - ALC236 for Lenovo Air 13 IML(S530-13IML)</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6mAScfkAE3HPABNx0AASce
					AAE3H0ABRxwQAUcdAQFHHhcBRx+QAUcMAgGH
					HPABhx0AAYceAAGHH0ABlxwwAZcdEAGXHoEB
					lx8BAacc8AGnHQABpx4AAacfQAG3HPABtx0A
					AbceAAG3H0ABtwwCAdcc8AHXHQAB1x4AAdcf
					QAHnHPAB5x0AAeceAAHnH0ACFxxQAhcdEAIX
					HiECFx8BAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>23</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom for Realtek ALC3236 for Asus TP500LN by Mohamed Khairy</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx4AAScfQAFHHCABRx0AAUce
					EwFHH5ABtxxAAbcdAAG3HqABtx+QAdccUAHX
					HZAB1x5FAdcfQAIXHDACFx0QAhceIQIXHwAB
					RwwCAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom by Mirone - Realtek ALC233 (ALC3236) for Asus X550LDV</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4TAUcfmQGXHEABlx0QAZce
					gQGXHwABpxwwAacdAQGnHqABpx+ZAhccIAIX
					HRACFx4hAhcfAAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>29</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - ALC233 (ALC3236) for ASUS VIVOBOOK S301LA </string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AaccMAGnHQABpx6nAacfkAGXHEABlx0QAZce
					gQGXHwABRxxQAUcdAAFHHhcBRx+QAUcMAgIX
					HGACFx0QAhceIQIXHwACFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>32</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - ALC233 (ALC3236) for ASUS VIVOBOOK S451LA </string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AhccIAIXHRACFx4hAhcfAAIXDAIBtxwwAbcd
					AAG3HqcBtx+QAZccQAGXHRABlx6BAZcfAAFH
					HFABRx0AAUceFwFHH5ABRwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>33</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC235 Intel NUC 8</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6mAScfkAGXHDABlx6nAZcf
					AA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>8</integer>
					<key>WakeConfigData</key>
					<data>
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Deskmini H470 ALC235 by dumk1217</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAHXHPAB1x0AAdce
					AAHXH0ACFxwQAhcdQAIXHiECFx8CAhcMAgG3
					HCABtx0AAbceEwG3H5kBtwwCAZccMAGXHQAB
					lx6jAZcfmQGnHEABpx1QAacegQGnHwIBRxzw
					AUcdAAFHHgABRx9AAUcMAgF3HPABdx0AAXce
					AAF3H0ABhxzwAYcdAAGHHgABhx9AAecc8AHn
					HQAB5x4AAecfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AhcMAgG3DAIBRwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC235</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAFHHCABRx0AAUce
					FwFHH5ABdxwwAXcdAAF3HgABdx9AAZccQAGX
					HRABlx6LAZcfAAHXHFAB1x2QAdce9wHXH0AC
					FxxgAhcdEAIXHisCFx8BAUcMAgIXDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC235 for Ienovo by soto2080</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHAEBJxygAScckAFHHAABRxwBAUcc
					EAFHHJABlxwwAZccEAGXHIEBlxwCAhccIAIX
					HBACFxwhAhccAgF3HPABdx0AAXceAAF3H0AB
					hxzwAYcdAAGHHgABhx9AAacc8AGnHQABpx4A
					AacfQAG3HPABtx0AAbceAAG3H0AB1xzwAdcd
					AAHXHgAB1x9AAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC235 for Lenovo Rescuer 15ISK by Z39</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6mAScfkAFHHBABRx0BAUce
					FwFHH5ABlxxAAZcdEAGXHoEBlx8EAhccMAIX
					HRACFx4hAhcfBAF3HPABdx0AAXceAAF3H0AB
					hxzwAYcdAAGHHgABhx9AAacc8AGnHQABpx4A
					AacfQAG3HPABtx0AAbceAAG3H0AB1xzwAdcd
					AAHXHgAB1x9AAecc8AHnHQAB5x4AAecfQAFH
					DAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>the-braveknight - Realtek ALC235 for Lenovo Legion Y520</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQABJx6gAScfsAFHHBABRx0AAUce
					FwFHH5ABRwwCAZccMAGXHRABlx6BAZcfAAIX
					HGACFx0QAhceIQIXHwA=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>14</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>qiuchenly - Realtek ALC235 for ASUS FX53VD</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					AbccAAG3HQEBtx6gAbcfmQIXHBACFx0QAhce
					KwIXHwEBRxwgAUcdAQFHHhABRx+ZAZccMAGX
					HRABlx6LAZcfAQEnHPABJx0AASceAAEnH0AB
					dxzwAXcdAAF3HgABdx9AAYcc8AGHHQABhx4A
					AYcfQAGnHPABpx0AAaceAAGnH0AB1xzwAdcd
					AAHXHgAB1x9AAecc8AHnHQAB5x4AAecfQAHX
					HPAB1x0AAdceAAHXH0AB1xzwAdcdAAHXHgAB
					1x9A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - Realtek ALC235 for ASUS GL553VD</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					AhccIAIXHRACFx4rAhcfAAG3HDABtx0AAbce
					pwG3H5ABlxwwAZcdEAGXHosBlx8BAUccQAFH
					HQABRx4XAUcfkAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>16</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC235 for Lenovo ThinkCentre Tiny M720q by marian</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAICFxwfAhcd
					EAIXHiECFx8CAhcMAgGXHCABlx0QAZceoAGX
					H5IBpxwwAacdEAGnHoABpx8C
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>17</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwwAAacMAA==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC235 for Asrock_bb_310 by_vio</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					AZccMAGXHQEBlx6gAZcfkAGnHEABpx0QAace
					gQGnHwIBtxwgAbcdQAG3HgEBtx8BAbcMAgIX
					HBACFx0QAhceIQIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>18</integer>
					<key>WakeConfigData</key>
					<data>
					AZcHIAGnByQBtwwCAhcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC235 for Lenovo C530 Desktop PC by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHPABRx0AAUce
					AAFHH0ABdxzwAXcdAAF3HgABdx9AAYcc8AGH
					HQABhx4AAYcfQAGXHDABlx0QAZceiwGXHwIB
					pxwQAacdAQGnHqABpx+QAbccIAG3HQEBtx4X
					AbcfkAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhccQAIXHRACFx4rAhcfAgG3
					DAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AbcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC235 for Asus ROG GL553VD-FY380 by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					AbccEAG3HQABtx6gAbcfkAFHHCABRx0AAUce
					FwFHH5ABlxwwAZcdEAGXHoEBlx8AAhccQAIX
					HRACFx4hAhcfAQFHDAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>22</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC235 for Asus TUF FX705GM by TheRealGudv1n</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					AhccIAIXHRACFx4rAhcfAAEnHDABJx0AASce
					pwEnH5ABlxwwAZcdEAGXHosBlx8BAUccQAFH
					HQABRx4XAUcfkAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>24</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>vusun123 - Realtek ALC235 for Lenovo Legion Y520</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQABJx6gAScfsAFHHBABRx0AAUce
					FwFHH5ABRwwCAZccMAGXHRABlx6BAZcfAAIX
					HGACFx0QAhceIQIXHwA=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>hla63 - Realtek ALC235 for Msi Modern 15 A10M</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6gAScfkAFHHBABRx0AAUce
					FwFHH5ABRwwCAZccQAGXHRABlx6LAZcfAwIX
					HCACFx0QAhceKwIXHwMCFwwCAgUAEAIEACAC
					BQAaAgSACw==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>29</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom by fuzzyrock for ALC235 Lenovo A340-22IWL</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScfkAGXHEABlx0QAZce
					oQGXHwIBtxwQAbcdAQG3HhcBtx+QAbcMAgIX
					HCACFx0QAhceIQIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>33</integer>
					<key>WakeConfigData</key>
					<data>
					AbcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC235 for Lenovo Qitian M420 by Cryse Hillmes</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHBABRx0BAUce
					FwFHH5ABdxzwAXcdAAF3HgABdx9AAYcc8AGH
					HQABhx4AAYcfQAGXHHABlx0QAZceoQGXHwIB
					pxwgAacdEAGnHoEBpx8CAbccgAG3HRABtx4B
					AbcfAQHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhccUAIXHRACFx4hAhcfAgFH
					DAIBtwwCAhcMAgIXB8ACFwiCAZcHJA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>35</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwwCAhcHwAIXCIIBlwck
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC235 for Lenovo Ideacentre Mini 5</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHPABRx0AAUce
					AAFHH0ABdxzwAXcdAAF3HgABdx9AAYcc8AGH
					HQABhx4AAYcfQAGXHDABlx0RAZcegQGXHwIB
					pxzwAacdAAGnHgABpx9AAbcc8AG3HQABtx4A
					AbcfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhccEAIXHRACFx4hAhcfAgGX
					ByQBlwiBAhcMAgIXB8ACFwiC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>37</integer>
					<key>WakeConfigData</key>
					<data>
					AZcHJAGXCIECFwwCAhcHwAIXCII=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC235 for Lenovo TianYi 510s Mini by DalianSky</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAICFxwfAhcd
					EAIXHiECFx8CAhcMAgGXHCABlx0QAZceoAGX
					H5IBpxwwAacdEAGnHoABpx8C
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwwAAacMAA==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC236</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6mAScfkAFHHCABRx0AAUce
					FwFHH5ABlxwwAZcdEAGXHoEBlx8EAhccQAIX
					HRACFx4hAhcfBAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Jake Lo - Realtek ALC236</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAE3HPABNx0AATce
					AAE3H0ABRxwwAUcdAQFHHhABRx+QAUcMAgGH
					HPABhx0AAYceAAGHH0ABlxwgAZcdMAGXHosB
					lx8BAacc8AGnHQABpx4AAacfQAG3HPABtx0A
					AbceAAG3H0AB1xzwAdcdAAHXHgAB1x9AAecc
					8AHnHQAB5x4AAecfQAIXHEACFx1AAhceKwIX
					HwECFwwCABcgAAAXIXIAFyJrABcjEA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC236 for Lenovo Xiaoxin Air 14IKBR by AlexanderLake</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAFHHEABRx0BAUce
					EAFHH5ABRwwCAZccIAGXHRABlx6LAZcfAQIX
					HFACFx0QAhceKwIXHwECFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom - Realtek ALC236 for Lenovi Air 13 Pro by rexx0520</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					ASccAAEnHQEBJx6mAScfkAFHHBABRx0BAUce
					EAFHH5ABdxzwAXcdAAF3HgABdx9AAYcc8AGH
					HQABhx4AAYcfQAGXHCABlx2QAZceqwGXHwAB
					pxzwAacdAAGnHgABpx9AAbcc8AG3HQABtx4A
					AbcfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhccMAIXHUACFx4rAhcfAAFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>erinviegas - ALC236 for Lenovo Ideapad 330S</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					ASccAAEnHQEBJx6gAScfkAFHHBABRx0BAUce
					EAFHH5ABRwwCAZccIAGXHRABlx6LAZcfAAIX
					HDACFx0QAhceKwIXHwACFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>14</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - ALC236 for Lenovo Ideapad 500-15ISK</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					ASccAAEnHQEBJx6mAScfkAFHHBABRx0BAUce
					EAFHH5ABRwwCAZccIAGXHRABlx6LAZcfAAIX
					HDACFx0QAhceKwIXHwACFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC236 for Lenovo IdeaPad 330S-14IKB by Ab2774</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAICFxwgAhcd
					EAIXHiECFx8TAhcMAgEnHDABJx0BASceoAEn
					H5ABlxxAAZcdEQGXHoEBlx+T
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>17</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC236 for Lenovo LEGION Y7000/Y530 by xiaoM</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxwgAUcdkAFHHhcBRx+QAUcMAgGH
					HPABhx0AAYceAAGHH0ABlxwgAZcdEAGXHosB
					lx8BAacc8AGnHQABpx4AAacfQAG3HPABtx0A
					AbceAAG3H0AB1xzwAdcdAAHXHgAB1x9AAecc
					8AHnHQAB5x4AAecfQAIXHBACFx0QAhceKwIX
					HwECFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>18</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>wolf606 - ALC236 for Lenovo Ideapad 500-14ISK</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQABJx6gAScfkAGXHDABlx0QAZce
					gAGXHwABRxxQAUcdAAFHHhcBRx+QAUcMAgIX
					HGACFx0QAhceIQIXHwACFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>19</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>RodionS - ALC236 for Lenovo Ideapad 320s 14ikb</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					ASccAAEnHQEBJx6mAScfkAFHHBABRx0BAUce
					EAFHH5ABRwwCAZccIAGXHRABlx6LAZcfAAIX
					HDACFx0QAhceKwIXHwABRwwCAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>16</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>volcbs - ALC236 for Lenovo Ideapad 510s 14isk (modified from MacPeet's)</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQABJx6gAScfkAFHHFABRx0AAUce
					FwFHH5ABRwwCAZccMAGXHRABlx6AAZcfAAIX
					HGACFx0QAhceIQIXHwACFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>36</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC236 for DELL-5488 by Daggeryu</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxwQAUcdAQFHHhcBRx+QAUcMAgGH
					HPABhx0AAYceAAGHH0ABlxzwAZcdEQGXHhEB
					lx9BAacc8AGnHQABpx4AAacfQAG3HPABtx0A
					AbceAAG3H0AB1xzwAdcdAAHXHgAB1x9AAecc
					8AHnHQAB5x4AAecfQAIXHCACFx0QAhceIQIX
					HwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>54</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC236 for Dell Vostro 5401 for Lorys89</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAICFxwgAhcd
					EAIXHisCFx8EAhcMAgEnHDABJx0BAScepgEn
					H5ABlxxAAZcdEAGXHosBlx8E
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>68</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC236 for Dell ICL for Lorys89 by Vorshim</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6mAScfkAFHHEABRx0BAUce
					FwFHH5ABRwwCAZccQAGXHRABlx6nAZcfAAIF
					AEUCBNaJAhcdEAIXHiECFx8BAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>69</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgGXByQCFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC236 for HP-240G8 by 8DireZ3</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAICFxwgAhcd
					EAIXHiECFx8EAhcMAgEnHDABJx0BAScepgEn
					H5ABlxxAAZcdEAGXHoEBlx8EATcc8AE3HQAB
					Nx4AATcfQAHXHPAB1x0AAdceAAHXH0ABhxzw
					AYcdAAGHHgABhx9AAacc8AGnHQABpx4AAacf
					QAG3HPABtx0AAbceAAG3H0ABtwwCAecc8AHn
					HQAB5x4AAecfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>55</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC236 for Lenovo Air 13 IWL by DalianSky</string>
					<key>CodecID</key>
					<integer>283902518</integer>
					<key>ConfigData</key>
					<data>
					ATcc8AE3HQABNx4AATcfQAFHHBABRx0BAUce
					FwFHH5ABRwwCAYcc8AGHHQABhx4AAYcfQAGX
					HDABlx0QAZcegQGXHwQBpxzwAacdAAGnHgAB
					px9AAbcc8AG3HQABtx4AAbcfQAHXHEUB1x0b
					AdceZgHXH0AB5xzwAecdAAHnHgAB5x9AAhcc
					HwIXHRACFx4hAhcfBA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC245 for Ienovo by soto2080</string>
					<key>CodecID</key>
					<integer>283902533</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAICFxwgAhcd
					EAIXHiECFx8EAhcMAgGXHEABlx0QAZcegQGX
					HwQ=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC245 for Ienovo by soto2080</string>
					<key>CodecID</key>
					<integer>283902533</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQEBdx4XAXcfkAF3DAICFxwgAhcd
					EAIXHiECFx8EAhcMAgGXHEABlx0QAZcegQGX
					HwQ=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>lunjielee - Realtek ALC245 for HP Omen 2020</string>
					<key>CodecID</key>
					<integer>283902533</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQEBdx4XAXcfkAGXHDABlx0QAZce
					gQGXHwMCFxwgAhcdEAIXHiECFx8DAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AhcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC255</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6mAScfkAFHHCABRx0AAUce
					FwFHH5ABlxwwAZcdEAGXHosBlx8AAhccUAIX
					HRACFx4rAhcfAgFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>Codec</key>
					<string>ALC255 for Dell Optiplex7060/7070MT(Separate LineOut)</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAGXHEABlx0QAZce
					iwGXHwIBtxwgAbcdEAG3HgEBtx8BAhccMAIX
					HRACFx4rAhcfAgFHDAIBtwwCAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>66</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>Codec</key>
					<string>ALC255, Dell Optiplex 7040 MT</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					AgUARQIE1IkBJxzwAZccQAGXHRABlx6nAZcf
					kA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
					<key>WakeConfigData</key>
					<data>
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC255 for minisforum U820 by DalianSky</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					AbccMAG3HQEBtx6gAbcfkAGXHCABlx0RAZce
					gQGXHwICFxxAAhcdEQIXHiECFx8CAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>82</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Armênio - Realtek ALC255/ALC3234 - Dell 7348</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAGXHCABlx0wAZce
					gQGXHwEBRxwwAUcdYQFHHhABRx+QAUcMAgIX
					HEACFx1wAhceIQIXHwECFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>86</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>Codec</key>
					<string>Realtek ALC255(3234) for Dell Optiplex series by Heporis</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHBABRx0BAUce
					FwFHH5ABdxzwAXcdAAF3HgABdx9AAYcc8AGH
					HQABhx4AAYcfQAGXHEABlx0QAZceiwGXHwIB
					pxzwAacdAAGnHgABpx9AAbccIAG3HRABtx4B
					AbcfAQHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhccMAIXHRACFx4rAhcfAgFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC255_v1</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					AbccIAG3HQABtx6gAbcfkAFHHDABRx0AAUce
					FwFHH5ACFxxQAhcdEAIXHiECFx8AAUcMAgIX
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC255 Gigabyte Brix BRI5(H) by Najdanovic Ivan</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6gAScfkAGXHCABlx0RAZce
					gQGXHwICFxxAAhcdEQIXHiECFx8CAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC255_v2</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6mAScfkAFHHCABRx0AAUce
					FwFHH5ABdxwwAXcdAAF3HgABdx9AAdccQAHX
					HQAB1x5wAdcfQAIXHFACFx0QAhceIQIXHwIB
					RwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>17</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>DuNe - Realtek ALC255 for Aorus X5V7</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6mAScfkAFHHCABRx0AAUce
					FwFHH5ABtxwhAbcdAAG3HhcBtx+QAXccMAF3
					HQABdx4AAXcfQAHXHEAB1x0AAdcecAHXH0AC
					FxxQAhcdEAIXHiECFx8CAaccYAGnHRABpx6B
					AacfAgHnHHAB5x0QAeceRQHnHwIBRwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>18</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC255 for Dell 7447 by was3912734</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHEABRx0BAUce
					FwFHH5ABRwwCAXccQQF3HQEBdx4XAXcfkAGX
					HCABlx0RAZceiwGXHwIB5xxgAecdEQHnHkUB
					5x8CAhccUAIXHRACFx4rAhcfAgIXDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>20</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC255 for Asus X441UA-WX096D by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					AZcc8AGXHRABlx6BAZcfAgGnHDABpx0BAace
					oAGnH5ABtxwQAbcdAQG3HhcBtx+QAhccIAIX
					HRACFx4hAhcfAgG3DAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AbcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC255 for Acer Aspire A515-54G</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHCABRx0RAUce
					FwFHH5AB1xwxAdcdAAHXHoAB1x8AAhccQAIX
					HRACFx4hAhcfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>23</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC255 for Asus X556UA m-dudarev</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAGXHCABlx0QAZce
					gQGXHwQCFxwgAhcdEAIXHiECFx8EAbccMAG3
					HQEBtx6gAbcfkAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>27</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC255 for Lenovo B470 - vusun123</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccYAEnHQABJx6gAScfkAFHHCABRx0AAUce
					FwFHH5ABRwwCAhccMAIXHRACFx4hAhcfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>dhinakg - Realtek ALC255 for Acer Predator G3-571</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAGXHCABlx0QAZce
					gQGXHwQCFxwgAhcdEAIXHiECFx8EAbccMAG3
					HQEBtx6gAbcfkAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>29</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>HongyuS - Realtek ALC255 for XiaoMiAir 13.3</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScfkAFHHBABRx0BAUce
					FwFHH5ABRwwCAXccAAF3HQABdx4AAXcfQAGH
					HPABhx0AAYceAAGHH0ABlxwgAZcdEAGXHoEB
					lx8EAacc8AGnHQABpx4AAacfQAG3HPABtx0A
					AbceAAG3H0AB1xwtAdcdmgHXHvcB1x9AAecc
					8AHnHQAB5x4AAecfQAIXHCACFx0QAhceIQIX
					HwQCFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>30</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>cowpod - Realtek ALC255 for UX360CA</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4XAUcfkAFHDAICFxwgAhcd
					EAIXHiECFx8AAhcMAgEnHDABJx0AAScepgEn
					H5ABlxxAAZcdAAGXHoEBlx8A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>31</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>DoctorStrange96 - Realtek ALC255 for Acer Aspire A51x</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQEBJx6mAScfkAFHHCABRx0BAUce
					FwFHH5ABRwwCAZcccAGXHRABlx6LAZcfAgIX
					HDACFx0QAhceIQIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>71</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Bhavin dell 5559 alc255</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHCABRx0BAUce
					FwFHH5ABRwwCAZccMAGXHRABlx6LAZcfAgIX
					HEACFx0QAhceIQIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>96</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>DalianSky - Realtek ALC255 (3246) for XiaoMi Air</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScfkAFHHBABRx0BAUce
					FwFHH5ABRwwCAXcc8AF3HQABdx4AAXcfQAGH
					HPABhx0AAYceAAGHH0ABlxxwAZcdEAGXHosB
					lx8CAacc8AGnHQABpx4AAacfQAG3HPABtx0A
					AbceAAG3H0AB1xzwAdcdAAHXHgAB1x9AAecc
					8AHnHQAB5x4AAecfQAIXHCACFx0QAhceKwIX
					HwQCFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>DalianSky - Realtek ALC255 (3246) for alienware alpha r2</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxxAAUcdAQFHHhcBRx+QAUcMAgF3
					HPABdx0AAXceAAF3H0ABhxzwAYcdAAGHHgAB
					hx9AAZccEAGXHRABlx6LAZcfAgGnHPABpx0A
					AaceAAGnH0ABtxzwAbcdAAG3HgABtx9AAdcc
					8AHXHQAB1x4AAdcfQAHnHJAB5x3gAeceRQHn
					HwECFxwwAhcdEAIXHisCFx8CAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>100</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC255(3234) for Dell Inspiron 5548 by CynCYX</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHCABRx0BAUce
					FwFHH5ABdxzwAXcdAAF3HgABdx9AAYcc8AGH
					HQABhx4AAYcfQAGXHDABlx0QAZceiwGXHwIB
					pxzwAacdAAGnHgABpx9AAbcc8AG3HQABtx4A
					AbcfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhccQAIXHRACFx4rAhcfAgFH
					DAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>255</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC255 for Acer Aspire 7 A715-42G AMD by Long</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQEBJx6mAScfkAFHHCABRx0BAUce
					FwFHH5ABRwwCAZcccAGXHRABlx6LAZcfAgIX
					HDACFx0QAhceIQIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>80</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC256</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHCABRx0AAUce
					FwFHH5ABlxwwAZcdEAGXHosBlx8CAhccUAIX
					HRACFx4rAhcfAgE3HPABNx0AATceAAE3H0AB
					hxzwAYcdAAGHHgABhx9AAacc8AGnHQABpx4A
					AacfQAG3HDABtx0AAbceoAG3H5AB1xzwAdcd
					AAHXHgAB1x9AAecc8AHnHQAB5x4AAecfQAFH
					DAIBtwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Rockjesus - Realtek ALC256 (3246) - dell 7559</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccAAEnHQEBJx6mAScfmQFHHBABRx0BAUce
					FwFHH5kCFxwgAhcdEAIXHiECFx8BAbcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Insanelydeepak - Realtek ALC256 (3246) for Dell Series</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAFHHCABRx0AAUce
					FwFHH5ABlxwwAZcdEAGXHosBlx8CAhccUAIX
					HRACFx4rAhcfAgE3HPABNx0AATceAAE3H0AB
					hxzwAYcdAAGHHgABhx9AAacc8AGnHQABpx4A
					AacfQAG3HPABtx0AAbceAAG3H0AB1xzwAdcd
					AAHXHgAB1x9AAecc8AHnHQAB5x4AAecfQAFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Insanelydeepak - Realtek ALC256 (3246) for Dell Series with subwoofer</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAFHHCABRx0AAUce
					FwFHH5ABRwwCAZccMAGXHRABlx6LAZcfAgG3
					HEABtx0AAbceFwG3H5ABtwwCAhccUAIXHRAC
					Fx4rAhcfAgE3HPABNx0AATceAAE3H0ABhxzw
					AYcdAAGHHgABhx9AAacc8AGnHQABpx4AAacf
					QAHXHPAB1x0AAdceAAHXH0AB5xzwAecdAAHn
					HgAB5x9A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>14</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwfAAhcIgwGXByQBpwcg
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>VicQ - Realtek ALC256 (3246) for Dell 7000 Series with 2.1Ch</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccYAEnHAEBJxymAScckAE3HPABNx0AATce
					AAE3H0ABRxxAAUcdAQFHHhABRx+QAUcMAgGH
					HPABhx0AAYceAAGHH0ABlxwgAZcdEAGXHosB
					lx8BAacccAGnHQABpx6hAacfAQG3HEEBtx0B
					AbceEAG3H5ABtwwCAdcc8AHXHQAB1x4AAdcf
					QAHnHPAB5x0AAeceAAHnH0ACFxxQAhcdEAIX
					HisCFx8BAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>16</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>hjmmc - Realtek ALC256 (3246) for Magicbook 2018 with 4CH</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAIBtxwSAbcd
					AQG3HhcBtx+QAbcMAgIXHCACFx0QAhceIQIX
					HwQCFwwCASccMAEnHQEBJx6mAScfkAGXHEAB
					lx0QAZcegQGXHwQBNxzwATcdAAE3HgABNx9A
					AYcc8AGHHQABhx4AAYcfQAGnHPABpx0AAace
					AAGnH0AB1xzwAdcdAAHXHgAB1x9AAecc8AHn
					HQAB5x4AAecfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>17</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres ZeroCross for Asus AIO PC V222UAK-WA541T</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AbccUAG3HQEBtx4XAbcfkAEnHDABJx0BASce
					oAEnH5ACFxwgAhcdEAIXHiECFx8DAbcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>20</integer>
					<key>WakeConfigData</key>
					<data>
					AbcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres ZeroCross for Dell 5570</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAFHHCABRx0BAUce
					FwFHH5ABlxwwAZcdEAGXHosBlx8CAhccUAIX
					HRACFx4rAhcfAgFHDAICFwwCAZcHJQIXCIM=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwclAhcIgw==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>Andres ZeroCross for Asus VivoBook Pro 17  N705UDR</string>
					<key>ConfigData</key>
					<data>
					AbccUAG3HQEBtx4XAbcfkAGnHDABpx0BAace
					oAGnH5ACFxwgAhcdEAIXHiECFx8DAbcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>22</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>Andres ZeroCross for Razer Blade 15 RZ09-02705E75</string>
					<key>ConfigData</key>
					<data>
					AUccUAFHHQABRx4XAUcfkAFHDAIBJxwwAScd
					AAEnHqABJx+QAhccIAIXHRACFx4hAhcfAAGX
					HEABlx0QAZcegQGXHwQ=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>23</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres ZeroCross - Intel NUC NUC10i5FNH</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6gAScfkAGXHCABlx0QAZce
					gQGXHwECFxwQAhcdEAIXHgECFx8BAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>24</integer>
					<key>WakeConfigData</key>
					<data>
					AhcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>vusun123 - ALC256 for Asus X555UJ</string>
					<key>ConfigData</key>
					<data>
					AUccUAFHHQABRx4XAUcfkAFHDAIBpxwwAacd
					AAGnHqABpx+QAhccIAIXHRACFx4hAhcfAA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>insanelyme - ALC256 for Huawei Matebook D15 2018 (MRC-W10)</string>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxwRAUcdAQFHHhcBRx+QAUcMAgGH
					HPABhx0AAYceAAGHH0ABlxxAAZcdEAGXHoEB
					lx8EAacc8AGnHQABpx4AAacfQAG3HBABtx0B
					AbceFwG3H5ABtwwCAdcc8AHXHQAB1x4AAdcf
					QAHnHPAB5x0AAeceAAHnH0ACFxwgAhcdEAIX
					HiECFx8EAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>33</integer>
					<key>WakeConfigData</key>
					<data>
					AbcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>DalianSky - Realtek ALC256 (3246) for Dell 7000 Series</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxwQAUcdAQFHHhcBRx+QAUcMAgGH
					HPABhx0AAYceAAGHH0ABlxxAAZcdEAGXHoEB
					lx8CAacc8AGnHQABpx4AAacfQAG3HPABtx0A
					AbceAAG3H0AB1xzwAdcdAAHXHgAB1x9AAecc
					8AHnHQAB5x4AAecfQAIXHCACFx0QAhceIQIX
					HwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>56</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Kk Realtek ALC256 (3246) for magicbook</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AScccAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxzwAUcdAAFHHgABRx9AAYcc8AGH
					HQABhx4AAYcfQAGXHDABlx0QAZceiwGXHwIB
					pxzwAacdAAGnHgABpx9AAbccQAG3HQEBtx4Q
					AbcfkAG3DAIB1xzwAdcdAAHXHgAB1x9AAecc
					8AHnHQAB5x4AAecfQAIXHFACFx0QAhceKwIX
					HwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>57</integer>
					<key>WakeConfigData</key>
					<data>
					AbcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>lgs3137 - Realtek ALC256 for ASUS Y5000U X507UBR</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAE3HPABNx0AATce
					AAE3H0ABRxwgAUcdAQFHHhcBRx+QAUcMAgGH
					HPABhx0AAYceAAGHH0ABlxwwAZcdEAGXHosB
					lx8CAacc8AGnHQABpx4AAacfQAG3HDABtx0A
					AbceoAG3H5ABtwwCAdcc8AHXHQAB1x4AAdcf
					QAHnHPAB5x0AAeceAAHnH0ACFxxQAhcdEAIX
					HisCFx8CAhcMAgGXByQBtwckAhcIgw==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>66</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwckAbcHJAIXCIM=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC256 for Dell OptiPlex 7080</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAGXHEABlx0QAZce
					iwGXHwIBtxwgAbcdEAG3HgEBtx8BAhccMAIX
					HRACFx4rAhcfAgFHDAIBtwwCAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>67</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Durian - Realtek ALC256 (3246) for MateBook X Pro 2019（4CH）</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AbccEAG3HQEBtx4XAbcfkAG3DAIBRxwRAUcd
					AQFHHhcBRx+QAUcMAgIXHCACFx0QAhceIQIX
					HwICFwwCASccMAEnHQEBJx6mAScfkAGXHPAB
					lx0AAZceAAGXH0AB1xzwAdcdAAHXHgAB1x9A
					AYcc8AGHHQABhx4AAYcfQAE3HPABNx0AATce
					AAE3H0ABpxzwAacdAAGnHgABpx9AAecc8AHn
					HQAB5x4AAecfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>76</integer>
					<key>WakeConfigData</key>
					<data>
					AbcMAgFHDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Wanwu - Realtek ALC256 (3246) for MateBook X Pro 2019</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxzwAUcdAAFHHgABRx9AAYcc8AGH
					HQABhx4AAYcfQAGXHEABlx0QAZceqwGXHwQB
					pxzwAacdAAGnHgABpx9AAbccEAG3HQEBtx4X
					AbcfkAG3DAIB1xzwAdcdAAHXHgAB1x9AAecc
					8AHnHQAB5x4AAecfQAIXHDACFx0QAhceKwIX
					HwQCFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>19</integer>
					<key>WakeConfigData</key>
					<data>
					AbcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>DalianSky - Realtek ALC256 (3246) for MateBook X Pro 2019</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxwRAUcdAQFHHhcBRx+QAUcMAgGH
					HPABhx0AAYceAAGHH0ABlxxAAZcdEAGXHoEB
					lx8EAacc8AGnHQABpx4AAacfQAG3HBABtx0B
					AbceFwG3H5ABtwwCAdcc8AHXHQAB1x4AAdcf
					QAHnHPAB5x0AAeceAAHnH0ACFxwgAhcdEAIX
					HiECFx8EAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>97</integer>
					<key>WakeConfigData</key>
					<data>
					AbcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Hoping - Realtek ALC256 (3246) for XiaoMiPro 2020</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AScccAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxzwAUcdAAFHHgABRx9AAYcc8AGH
					HQABhx4AAYcfQAGXHDABlx0QAZceiwGXHwMB
					pxzwAacdAAGnHgABpx9AAbccQAG3HQEBtx4Q
					AbcfkAG3DAIB1xzwAdcdAAHXHgAB1x9AAecc
					8AHnHQAB5x4AAecfQAIXHFACFx0QAhceKwIX
					HwMCFwwCAgUAEAIEACA=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
					<key>WakeConfigData</key>
					<data>
					AhcMAgG3DAIBtwdAAZcHIAIXCIMBtQA2AbQX
					Nw==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - Realtek ALC257 for Lenovo T480</string>
					<key>CodecID</key>
					<integer>283902551</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQABJx6mAScfkAGXHDABlx0QAZce
					gQGXHwABRxxQAUcdAAFHHhcBRx+QAUcMAgIX
					HGACFx0QAhceIQIXHwA=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Armênio - Realtek ALC257 - Lenovo T480</string>
					<key>CodecID</key>
					<integer>283902551</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAGXHCABlx0wAZce
					gQGXHwEBRxwwAUcdYQFHHhABRx+QAUcMAgIX
					HEACFx1wAhceIQIXHwECFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>86</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC257 for Lenovo Legion Y540 and Y7000-2019</string>
					<key>CodecID</key>
					<integer>283902551</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxwQAUcdAQFHHhcBRx+QAUcMAgGH
					HPABhx0AAYceAAGHH0ABlxwwAZcdEAGXHosB
					lx8EAacc8AGnHQABpx4AAacfQAG3HPABtx0A
					AbceAAG3H0ABtwwCAdcc8AHXHQAB1x4AAdcf
					QAHnHPAB5x0AAeceAAHnH0ACFxwfAhcdEAIX
					HisCFx8EAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>18</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>antoniomcr96 - Realtek ALC257 for Lenovo Thinkpad L390</string>
					<key>CodecID</key>
					<integer>283902551</integer>
					<key>ConfigData</key>
					<data>
					ASccYAEnHQEBJx6gAScfkAFHHBABRx0BAUce
					EAFHH5ABRwwCAZccgAGXHRABlx6BAZcfBAIX
					HCACFx0QAhceIQIXHwQCFwwCATcc8AE3HQAB
					Nx4AATcfQAGHHPABhx0AAYceAAGHH0ABpxzw
					AacdAAGnHgABpx9AAbcc8AG3HQABtx4AAbcf
					QAHXHPAB1x0AAdceAAHXH0AB5xzwAecdAAHn
					HgAB5x9A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>96</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>savvamitrofanov - Realtek ALC257 for Lenovo Thinkpad T490</string>
					<key>CodecID</key>
					<integer>283902551</integer>
					<key>ConfigData</key>
					<data>
					ASccYAEnHQEBJx6gAScfkAFHHBABRx0BAUce
					EAFHH5ABRwwCAZccgAGXHRABlx6BAZcfBAIX
					HEACFx1wAhceIQIXHwECFwwCATcc8AE3HQAB
					Nx4AATcfQAGHHPABhx0AAYceAAGHH0ABpxzw
					AacdAAGnHgABpx9AAbcc8AG3HQABtx4AAbcf
					QAG3DAIB1xzwAdcdAAHXHgAB1x9AAecc8AHn
					HQAB5x4AAecfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>97</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwwCAYcHJQGXByUBpwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC257 for Lenovo XiaoXin Pro 2019(81XB/81XD) by DalianSky</string>
					<key>CodecID</key>
					<integer>283902551</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScfkAIXHCACFx0QAhce
					IQIXHwQBlxxAAZcdEAGXHoEBlx8EAUccEAFH
					HQEBRx4XAUcfkAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC257 for Lenovo XiaoXin Pro 2019(81XB/81XD) by DalianSky</string>
					<key>CodecID</key>
					<integer>283902551</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScfkAIXHCACFx0QAhce
					IQIXHwQBlxxAAZcdEAGXHoEBlx8EAUccEAFH
					HQEBRx4XAUcfkAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>100</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Hoping - Realtek ALC257 for Lenovo XiaoXin Air14ALC</string>
					<key>CodecID</key>
					<integer>283902551</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQABJx6mAScfkAFHHBABRx0AAUce
					EAFHH5ABRwwCAZccMAGXHRABlx6LAZcfAwGX
					ByQCFxxAAhcdEAIXHisCFx8D
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>101</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgGXByQ=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet ALC260 for Fujitsu Celsius M 450</string>
					<key>CodecID</key>
					<integer>283902560</integer>
					<key>ConfigData</key>
					<data>
					IPccECD3HUAg9x4RIPcfASD3DAIhhxwgIYcd
					YCGHHkQhhx8BITccQCE3HZAhNx6hITcfmSFH
					HFAhRx0wIUcegSFHHwEhVxxgIVcdQCFXHiEh
					Vx8C
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC260</string>
					<key>CodecID</key>
					<integer>283902560</integer>
					<key>ConfigData</key>
					<data>
					AQccAAEHHUABBx4hAQcfAQEnHBABJx2QASce
					oQEnH5kBNxwgATcdMAE3HoEBNx8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>DalianSky - ALC262 for MS-7480N1</string>
					<key>CodecID</key>
					<integer>283902562</integer>
					<key>ConfigData</key>
					<data>
					AVccYAFXHUABVx4BAVcfAQFHHFABRx1AAUce
					IQFHHwEBhxwgAYcdMAGHHoEBhx8BAaccEAGn
					HZABpx6iAacfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - ALC262</string>
					<key>CodecID</key>
					<integer>283902562</integer>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4BIUcfASHnHCAh5x1gIece
					RSHnHwAhhxwwIYcdkCGHHqEhhx+RIZccQCGX
					HZAhlx6hIZcfkiGnHFAhpx0wIacegSGnHwEh
					txxgIbcdQCG3HiEhtx8C
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Goldfish64 - ALC262 for HP Compaq dc7700 SFF</string>
					<key>CodecID</key>
					<integer>283902562</integer>
					<key>ConfigData</key>
					<data>
					AbccEAG3HUABtx4BAbcfAQFXHCABVx0QAVce
					IQFXHwIBZxwwAWcdAQFnHhMBZx+QAZccQAGX
					HTABlx6BAZcfAQGnHFABpx0QAacegQGnHwI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - ALC262 for Fujitsu Celsius H270</string>
					<key>CodecID</key>
					<integer>283902562</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4TAUcfkAFXHCABVx0QAVce
					IQFXHwIBhxwwAYcdEAGHHoEBhx8CAZccQAGX
					HQABlx6jAZcfkAGnHFABpx0QAacegQGnHwE=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Goldfish64 - ALC262 for Dell Studio One 19 1909</string>
					<key>CodecID</key>
					<integer>283902562</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQEBJx6gAScfkAFHHDABRx0QAUce
					AQFHHwEBVxwQAVcdAQFXHhABVx+QAYccIAGH
					HZABhx6BAYcfAgGnHFABpx1AAaceIQGnHwI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>14</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - ALC262 for HP Z800-Z600 series</string>
					<key>CodecID</key>
					<integer>283902562</integer>
					<key>ConfigData</key>
					<data>
					AZccAAGXHREBlx6gAZcfkgGnHBABpx0xAace
					gAGnH5EBVxwgAVcdQQFXHhABVx+RAWccMAFn
					HQEBZx4AAWcfKQGHHEABhx2QAYceoAGHH5EB
					txxQAbcdEAG3HisBtx8C
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC262 for MS-7847</string>
					<key>CodecID</key>
					<integer>283902562</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4hAUcfAgFXHB8BVx0QAVce
					AQFXHwEBZxwgAWcdAQFnHhMBZx+ZAYccMAGH
					HTABhx6BAYcfAQGnHD8Bpx2QAaceoQGnHwI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>66</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC268</string>
					<key>CodecID</key>
					<integer>283902568</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHRABRx4hAUcfAQGHHEABhx2QAYce
					gQGHHwEBVxxQAVcdAAFXHhMBVx+QAZccYAGX
					HQABlx6jAZcfkAFXDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Goldfish64 - ALC268 for Dell Inspiron Mini 9</string>
					<key>CodecID</key>
					<integer>283902568</integer>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAE3HPABNx0AATce
					AAE3H0ABRxwQAUcdAQFHHhMBRx+QAUcMAgFX
					HCABVx1AAVceIQFXHwEBVwwCAWcc8AFnHQAB
					Zx4AAWcfQAGHHEABhx2QAYcegQGHHwEBlxww
					AZcdAQGXHqABlx+QAacc8AGnHQABpx4AAacf
					QAHHHPABxx0AAcceAAHHH0AB1xzwAdcdAAHX
					HgAB1x9AAecc8AHnHQAB5x4AAecfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone Laptop patch ALC269 Asus N53J</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AYccIAGHHRABhx6BAYcfBAGXHBABlx0BAZce
					oAGXH5kBtxxAAbcdAQG3HhMBtx+ZAhccUAIX
					HRACFx4hAhcfBAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>1</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC269-VB v1</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AUccQAFHHQEBRx4TAUcfmQGHHCABhx0QAYce
					gQGHHwMBlxwQAZcdAQGXHqABlx+ZAhccUAIX
					HRACFx4hAhcfAwFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>2</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Mirone - Realtek ALC269 for Asus K53SJ, Asus G73s</string>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHBABRx0BAUce
					EwFHH5ABdxxQAXcdAQF3HhMBdx+QAYccIAGH
					HZABhx6BAYcfAwGXHDABlx0BAZceoAGXH5AB
					pxzwAacdAAGnHgABpx9AAbcc8AG3HQABtx4A
					AbcfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhccQAIXHRACFx4hAhcfAwFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC269-VB v2</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4TAUcfkAGHHCABhx2QAYce
					gQGHHwIBtxwwAbcdEAG3HqABtx+QAhccQAIX
					HRACFx4hAhcfAgFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>4</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC269-VB v3</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAFHHCABRx0AAUce
					EwFHH5ABhxwwAYcdEAGHHoEBhx8AAhccUAIX
					HRACFx4hAhcfAAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC269-VC v1</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4TAUcfkAGHHDABhx0QAYce
					gQGHHwABJxxAAScdAAEnHqABJx+QAVccUAFX
					HRABVx4hAVcfAAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>6</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC269-VC v2</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6mAScfkAFXHCABVx0QAVce
					IQFXHwABhxwwAYcdEAGHHoEBhx8CAbccQAG3
					HQABtx4XAbcfkAG3DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC269VC-v3</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6mAScfkAFHHCABRx0AAUce
					FwFHH5ABVxwwAVcdEAFXHiEBVx8AAYccQAGH
					HZABhx6BAYcfAgFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>8</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC269VB v4</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAFHHCABRx0AAUce
					EwFHH5ABhxwwAYcdEAGHHoEBhx8AAhccUAIX
					HRACFx4hAhcfAAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>9</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Toleda ALC269 patch for Brix</string>
					<key>ConfigData</key>
					<data>
					IUcc8CFHHQAhRx4AIUcfQCFXHHAhVx1AIVce
					ISFXHwIhZxzwIWcdACFnHgAhZx9AIXcc8CF3
					HQAhdx4AIXcfQCGHHPAhhx0AIYceACGHH0Ah
					lxzwIZcdACGXHgAhlx9AIacc8CGnHQAhpx4A
					IacfQCG3HPAhtx0AIbceACG3H0Ah5xyQIecd
					YSHnHksh5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>10</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mosser - ALC269VB Dell Precision Workstation T1600</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AbccIAG3HUABtx4BAbcfAQGHHDABhx2YAYce
					gQGHHwIBlxxAAZcdmAGXHoEBlx8BAhccUAIX
					HUACFx4hAhcfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Asus Vivobook S200CE - Realtek ALC269VB</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScfkAFHHBABRx0BAUce
					FwFHH5ABdxzwAXcdAAF3HgABdx9AAYcc8AGH
					HQABhx4AAYcfQAGXHPABlx0AAZceAAGXH0AB
					pxwgAacdEAGnHisBpx8AAbcc8AG3HQABtx4A
					AbcfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhcc8AIXHQACFx4AAhcfQAFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC269VC for Samsung NP350V5C-S08IT</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AZccEAGXHQABlx6nAZcfmQFXHCABVx0QAVce
					IQFXHwIBhxwwAYcdEAGHHoEBhx8CAUccQAFH
					HQABRx4XAUcfmQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Custom ALC269VC for Samsung NT550P7C-S65 with subwoofer 2.1ch by Rockjesus</string>
					<key>ConfigData</key>
					<data>
					AVccEAFXHRABVx4hAVcfAQGHHCABhx0QAYce
					gQGHHwEBlxwwAZcdAQGXHqcBlx+QAbccQAG3
					HQEBtx4XAbcfkAF3HEEBdx0BAXceFwF3H5AB
					JxzwAScdAAEnHgABJx9AAUcc8AFHHQABRx4A
					AUcfQAGnHPABpx0AAaceAAGnH0AB1xzwAdcd
					AAHXHgAB1x9AAecc8AHnHQAB5x4AAecfQAG3
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>14</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - ALC269VB for Dell Optiplex 790</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AbccIAG3HUABtx4BAbcfAQGHHDABhx2QAYce
					gQGHHwIBlxxAAZcdkAGXHoEBlx8BAhccUAIX
					HUACFx4hAhcfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - ALC269VB for Dell Optiplex 790 Version2</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AbccIAG3HUABtx4RAbcfkAGHHDABhx2QAYce
					oQGHH5ABlxxAAZcdkAGXHoEBlx8BAhccUAIX
					HUACFx4hAhcfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>16</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - Latte Panda</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AYccIAGHHVABhx6hAYcfkQFXHDABVx1AAVce
					IQFXHwEBVwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>17</integer>
					<key>WakeConfigData</key>
					<data>
					AVcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Hypereitan - ALC269VC for Thinkpad X230 i7</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAFHHEABRx0BAUce
					EAFHH5ABVxxQAVcdEAFXHiEBVx8BAYcccAGH
					HRABhx6hAYcfAQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>18</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Asus Vivobook S300CA - Realtek ALC269VB</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScfkAFHHBABRx0BAUce
					FwFHH5ABdxzwAXcdAAF3HgABdx9AAYcc8AGH
					HQABhx4AAYcfQAGXHPABlx0AAZceAAGXH0AB
					pxzwAacdAAGnHgABpx9AAbcc8AG3HQABtx4A
					AbcfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhccIAIXHRACFx4rAhcfAAFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>19</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Custom ALC269 Sony Vaio VPCEB3M1R by Rodion</string>
					<key>ConfigData</key>
					<data>
					AVccQAFXHRABVx4hAVcfAwGHHCABhx0QAYce
					gQGHHwMBlxwwAZcdAQGXHqABlx+QAbccEAG3
					HQEBtx4XAbcfkAFXDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>20</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Goldfish64 - ALC269VB for Dell Optiplex 7010</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHBABRx0BAUce
					EwFHH5ABRwwCAXcc8AF3HQABdx4AAXcfQAGH
					HDABhx2QAYcegQGHHwIBlxxAAZcdkAGXHoEB
					lx8BAacc8AGnHQABpx4AAacfQAG3HCABtx1A
					AbceAQG3HwEB1xzwAdcdAAHXHgAB1x9AAecc
					8AHnHQAB5x4AAecfQAIXHFACFx1AAhceIQIX
					HwI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Andres ZeroCross - ALC269VC for Acer Veriton Z4640G</string>
					<key>ConfigData</key>
					<data>
					AaccEAGnHQABpx4XAacfkAGXHDABlx0QAZce
					gQGXHwABJxxAAScdAAEnHqABJx+QAbccUAG3
					HRABtx4hAbcfAA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>22</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>ALC269VC for Hasee U45S1 by zero816</string>
					<key>ConfigData</key>
					<data>
					AXccAAF3HQABdx4AAXcfQAHXHAUB1x2GAdce
					9AHXH0ABRxwQAUcdAQFHHhcBRx+QAUcMAgFX
					HCABVx0QAVceIQFXHwEBVwwCAYccMAGHHRAB
					hx6hAYcfAQEnHEABJx0BAScepgEnH5ABlxzw
					AZcdEQGXHhEBlx9BAbcc8AG3HREBtx4RAbcf
					QQHnHPAB5x0RAeceEQHnH0EBpxzxAacdEQGn
					HhEBpx9B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>24</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>hua0512 - Realtek ALC269 for Medium Akoya P6653</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfmQFXHCABVx0QAVce
					IQFXHwEBVwwCAYccQAGHHRABhx6BAYcfAQEn
					HDABJx0BAScepgEnH5kBRwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>25</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Custom ALC269 for Acer Aspire by Andrey1970</string>
					<key>ConfigData</key>
					<data>
					AUccAAFHHUEBRx4XAUcfmQGHHBABhx2QAYce
					gQGHHwEBtxwgAbcdkQG3HqcBtx+ZAhccMAIX
					HUACFx4hAhcfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>27</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269VC</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>ALC269VC for Lenovo Z580, John</string>
					<key>ConfigData</key>
					<data>
					AbccEAG3HQEBtx4XAbcfkAG3DAIBhxwgAYcd
					EAGHHoEBhx8DAZccMAGXHQEBlx6gAZcfkAFX
					HEABVx0QAVceIQFXHwMBVwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AVcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>ALC269VC for Lenovo V580, ar4er</string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6mAScfkAFHHCABRx0AAUce
					FwFHH5ABVxwwAVcdEAFXHiEBVx8AAYccQAGH
					HZABhx6BAYcfAgFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>29</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269VC for Hasee Z6SL7R3 by HF</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAFHHBABRx0BAUce
					FwFHH5ABVxyAAVcdEAFXHiEBVx8CAYccIAGH
					HRABhx6BAYcfAgHnHGAB5x0AAeceQQHnHwIB
					RwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>30</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Custom ALC269-VC Samsung np540U4E by majonez</string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6mAScfkAG3HCABtx0AAbce
					FwG3H5ABVxwwAVcdEAFXHiEBVx8AAYccQAGH
					HZABhx6BAYcfAgG3DAIBVwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>34</integer>
					<key>WakeConfigData</key>
					<data>
					AVcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Custom ALC269 Samsung np880z5e-x01ru by Constanta</string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6mAScfkAG3HCABtx0AAbce
					FwG3H5ABVxwwAVcdEAFXHiEBVx8AAYccQAGH
					HZABhx6BAYcfAgG3DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>32</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Custom ALC269VC for Samsung NP530U3C-A0F by BblDE3HAP</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAIBVxxAAVcd
					EAFXHiEBVx8DAVcMAgGHHCABhx0QAYcegQGH
					HwMBlxwwAZcdAQGXHqABlx+Q
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>33</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC269VC - Samsung NP350V5C-S0URU</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4XAUcfkAGHHCABhx0QAYce
					gQGHHwIBVxwwAVcdEAFXHiEBVx8CAZccQAGX
					HQABlx6gAZcfkAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>35</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC269 - Samsung R780</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4XAUcfkAFXHCABVx0QAVce
					IQFXHwEBhxwwAYcdEAGHHoEBhx8BAZccQAGX
					HQABlx6gAZcfkAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>36</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>vusun123 - Realtek ALC269VC for Lenovo W530</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQABJx6gAScfkAFHHBABRx0AAUce
					FwFHH5ABRwwCAVccIAFXHRABVx4hAVcfAAGH
					HDABhx0QAYcegQGHHwA=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>40</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269VC</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Laptop patch ALC269VC Samsung _NP350V5C - Giesteira</string>
					<key>ConfigData</key>
					<data>
					AUccAAFHHQABRx4XAUcfmQGHHBABhx0QAYce
					gQGHHwEBVxwgAVcdEAFXHiEBVx8BAZccMAGX
					HQABlx6nAZcfmQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>44</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>maiconjs (Wolfield) - Asus A45A 269VB1</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4XAUcfmQGHHCABhx0QAYce
					gQGHHwEBJxwwAScdAAEnHqYBJx+ZAhccUAIX
					HRACFx4hAhcfAQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>45</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269VC for Thinkpad X230 with Dock4338</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHEABRx0BAUce
					FwFHH5ABRwwCAVccUAFXHRABVx4rAVcfAgFX
					DAIBdxzwAXcdAAF3HgABdx9AAYccIAGHHRAB
					hx6LAYcfAgGXHPABlx0AAZceAAGXH0ABpxzw
					AacdAAGnHgABpx9AAbccYAG3HUABtx4BAbcf
					AQHXHPAB1x0AAdceAAHXH0AB5xzwAecdAAHn
					HgAB5x9A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>55</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>HASEE Z6-i78154S2 ALC269 by lianny  </string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4TAUcfkAGHHDABhx0QAYce
					gQGHHwABJxxAAScdAAEnHqABJx+QAVccIAFX
					HRABVx4hAVcfAAF3HPABdx0AAXceAAF3H0AB
					lxzwAZcdAAGXHgABlx9AAacc8AGnHQABpx4A
					AacfQAG3HPABtx0AAbceAAG3H0AB1xzwAdcd
					AAHXHgAB1x9AAeccYAHnHRAB5x5EAecfAgFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>58</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269VC for Clevo N155RD by DalianSky</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHDABRx0BAUce
					FwFHH5ABVxwgAVcdEAFXHiEBVx8CAXcc8AF3
					HQABdx4AAXcfQAGHHAABhx0QAYcegQGHHwIB
					lxzwAZcdAAGXHgABlx9AAacc8AGnHQABpx4A
					AacfQAG3HPABtx0AAbceAAG3H0AB5xxwAecd
					EQHnHkQB5x8CAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>66</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Vorshim92 - Realtek ALC269 - GF63 Thin 9SEXR</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQABJx6gAScfkAFHHBABRx0AAUce
					FwFHH5ABRwwCAVccUAFXHRABVx4hAVcfAAFX
					DAIBhxwwAYcdEAGHHoEBhx8A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>69</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgFXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC269VB for ENZ C16B by jimmy19990</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4TAUcfkAGHHCABhx0QAYce
					gQGHHwEBlxwgAZcdAQGXHqABlx+QAhccEAIX
					HRACFx4hAhcfAQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>76</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269 for MECHREVO X8Ti Plus by DalianSky</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHDABRx0BAUce
					FwFHH5ABVxwgAVcdEAFXHiEBVx8CAXcc8AF3
					HQABdx4AAXcfQAGHHAABhx0QAYcegQGHHwIB
					lxzwAZcdAAGXHgABlx9AAacc8AGnHQABpx4A
					AacfQAG3HPABtx0AAbceAAG3H0AB5xzwAecd
					AAHnHgAB5x9AAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>88</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Custom ALC269 for Chuwi CoreBox by Luca91</string>
					<key>ConfigData</key>
					<data>
					AVccHwFXHRABVx4hAVcfBAFXDAIBhxwgAYcd
					EAGHHoEBhx8E
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>91</integer>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>ALC269 Asus K53SJ, Asus G73s Mod by Andrey1970 (No input boost - no noise in Siri)</string>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHBABRx0BAUce
					EwFHH5ABdxxQAXcdAQF3HhMBdx+QAYccIAGH
					HZABhx6BAYcfAwGXHDABlx0BAZceoAGXH5AB
					pxzwAacdAAGnHgABpx9AAbcc8AG3HQABtx4A
					AbcfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhccQAIXHRACFx4hAhcfAwFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>93</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269-VB v4 Mod by Andrey1970 (No input boost - no noise in Siri)</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAFHHCABRx0AAUce
					EwFHH5ABhxwwAYcdEAGHHoEBhx8AAhccUAIX
					HRACFx4hAhcfAAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres ZeroCross - ALC269 for Infinix X1 XL11</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAG3HCABtx0AAbce
					FwG3H5ACFxxQAhcdEAIXHiECFx8A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>26</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>ALC269 for GPD P2 Max by DalianSky</string>
					<key>ConfigData</key>
					<data>
					AVccEAFXHRABVx4hAVcfBAGnHCABpx0BAace
					FwGnH5ABpwwCAYccMAGHHRABhx6BAYcfBAEn
					HEABJx0BAScepgEnH7c=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>100</integer>
					<key>WakeConfigData</key>
					<data>
					AacMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>ALC269 for minisforum NAG6 by DalianSky</string>
					<key>ConfigData</key>
					<data>
					AYccEAGHHZABhx6BAYcfAQIXHDACFx1AAhce
					IQIXHwEBRwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>111</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Custom ALC269 Acer Aspire by Andrey1970 (No input boost - no noise in Siri)</string>
					<key>ConfigData</key>
					<data>
					AUccAAFHHUEBRx4XAUcfmQGHHBABhx2QAYce
					gQGHHwEBtxwgAbcdkQG3HqcBtx+ZAhccMAIX
					HUACFx4hAhcfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>127</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>ALC269 for Lenovo Y500 by BaoStorm (No input boost - no noise in Siri)</string>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQkBJx6mAScfkAFHHBABRx0BAUce
					FwFHH5ABRwwCAVccIAFXHSABVx4hAVcfBAFX
					DAIBhxxQAYcdKAGHHqEBhx8EAeccMAHnHSEB
					5x5FAecfBA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>188</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269VC for Hasee K790s</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHPABRx0AAUce
					AAFHH0ABdxzwAXcdAAF3HgABdx9AAYccEAGH
					HRABhx6AAYcfkAGXHBABlx0AAZceoAGXH5AB
					pxyQAacdAQGnHhcBpx+QAbcc8AG3HQABtx4A
					AbcfQAHXHPAB1x0AAdceAAHXH0AB5xxwAecd
					EQHnHkQB5x8EAhccoAIXHRACFx4hAhcfBAFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>47</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Custom ALC269 by ryahpalma for Laptop NS4SL01</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAIBhxwgAYcd
					EAGHHoEBhx8DAVccQAFXHRABVx4hAVcfAwFX
					DAIBJxwwAScdAQEnHqYBJx+Q
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>128</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC270 v1</string>
					<key>CodecID</key>
					<integer>283902576</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4TAUcfkAGXHCABlx0AAZce
					oAGXH5ACFxwwAhcdEAIXHiECFx8AAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC270 v2</string>
					<key>CodecID</key>
					<integer>283902576</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAFHHCABRx0AAUce
					EwFHH5ACFxwwAhcdEAIXHiECFx8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>4</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC270</string>
					<key>CodecID</key>
					<integer>283902576</integer>
					<key>Comment</key>
					<string>ALC270 for Asus A46CB-WX024D Laptop by Andres ZeroCross</string>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHBABRx0BAUce
					FwFHH5ABdxzwAXcdAAF3HgABdx9AAYccIAGH
					HRABhx6BAYcfBAGXHDABlx0BAZceoAGXH5AB
					pxwgAacdEAGnHiEBpx8EAbcc8AG3HQABtx4A
					AbcfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhcc8AIXHQACFx4AAhcfQAFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC270</string>
					<key>CodecID</key>
					<integer>283902576</integer>
					<key>Comment</key>
					<string>ALC270 for Asus Laptop with alternative microphone</string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHCABRx0BAUce
					EwFHH5ABdxzwAXcdAAF3HgABdx9AAYccMAGH
					HRABhx6BAYcfAgGXHPABlx0AAZceAAGXH0AB
					pxzwAacdAAGnHgABpx9AAbcc8AG3HQABtx4A
					AbcfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhccQAIXHRACFx4hAhcfAgFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>27</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC270</string>
					<key>CodecID</key>
					<integer>283902576</integer>
					<key>Comment</key>
					<string>ALC270 for Asus Laptop</string>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHBABRx0BAUce
					FwFHH5ABdxzwAXcdAAF3HgABdx9AAYccIAGH
					HRABhx6BAYcfBAGXHDABlx0BAZceoAGXH5AB
					pxzwAacdAAGnHgABpx9AAbcc8AG3HQABtx4A
					AbcfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhccIAIXHRACFx4hAhcfBAFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Custom ALC271x Acer Aspire s3-951</string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAFHHCABRx0AAUce
					EwFHH5ABhxwwAYcdkAGHHoEBhx8AAdccQAHX
					HZAB1x4XAdcfQAHnHFAB5x0QAeceRQHnHwAC
					FxxgAhcdEAIXHiECFx8AAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>31</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC272</string>
					<key>CodecID</key>
					<integer>283902578</integer>
					<key>ConfigData</key>
					<data>
					AYccMAGHHZABhx6BAYcfAAGXHCABlx0AAZce
					owGXH5ABRxwQAUcdAAFHHhMBRx+QAhccUAIX
					HUACFx4hAhcfAAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC 272 - Lenovo B470 - Sam Chen</string>
					<key>CodecID</key>
					<integer>283902578</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQABJx6jAScfkAFHHBABRx0AAUce
					EwFHH5ABhxwgAYcdEAGHHoEBhx8AAhccUAIX
					HRACFx4hAhcfAAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC 272 for Lenovo Y470 by amu_1680c</string>
					<key>CodecID</key>
					<integer>283902578</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAGHHCABhx0AAYce
					gQGHHwEBRxwwAUcdAQFHHhABRx+QAhccQAIX
					HRACFx4hAhcfAQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Sniki - Realtek ALC 272 for Lenovo B570 and B570e</string>
					<key>CodecID</key>
					<integer>283902578</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAFHHCABRx0BAUce
					EwFHH5ABRwwCAYccMAGHHZABhx6BAYcfAQIX
					HEACFx1AAhceIQIXHwE=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>18</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres ZeroCross - Lenovo All In One PC C440</string>
					<key>CodecID</key>
					<integer>283902578</integer>
					<key>ConfigData</key>
					<data>
					AYccQAGHHZABhx6BAYcfAQEnHDABJx0BASce
					oAEnH5ABpxwQAacdAQGnHhMBpx+ZAhccIAIX
					HRACFx4hAhcfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC274 for Optiplex 7470 AIO</string>
					<key>CodecID</key>
					<integer>283902580</integer>
					<key>ConfigData</key>
					<data>
					AWcMAgIFAEUCBNaJATcdAAE3HgABNx9AAZcc
					QAGXHRABlx6nAZcfAAG3HFABtx0QAbceAQG3
					HwECFx8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AWcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres ZeroCross - Realtek ALC274 for Dell Inspiron 27-7777 AIO Series</string>
					<key>CodecID</key>
					<integer>283902580</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6gAScfkAFnHBABZx0BAWce
					FwFnH5ABlxxAAZcdAAGXHoEBlx8EAhccIAIX
					HRACFx4hAhcfBAFHDAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AWcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres ZeroCross - Realtek ALC274 for Dell Inspiron 27-7777 AIO Series</string>
					<key>CodecID</key>
					<integer>283902580</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAF3HCABdx0BAXce
					FwF3H5ABdwwCAaccMAGnHRABpx6BAacfAgIX
					HEACFx0QAhceIQIXHwICFwwCAgUAbwIEC/M=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>jackjack1-su Realtek ALC274 for Microsoft Surface Pro 7</string>
					<key>CodecID</key>
					<integer>283902580</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxzwAUcdAAFHHgABRx9AAVcc8AFX
					HQABVx4AAVcfQAFnHPABZx0AAWceAAFnH0AB
					dxzwAXcdAAF3HgABdx9AAYcc8AGHHQABhx4A
					AYcfQAGXHCABlx0QAZcegQGXHwIBpxzwAacd
					AAGnHgABpx9AAbccQAG3HQEBtx4QAbcfkAG3
					DAIB5xzwAecdAAHnHgAB5x9AAfcc8AH3HQAB
					9x4AAfcfQAIXHFACFx0QAhceIQIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>35</integer>
					<key>WakeConfigData</key>
					<data>
					AWcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Harahi - Realtek ALC274 for Mechrevo UmiPro3 (Tongfang GM5MG0Y)</string>
					<key>CodecID</key>
					<integer>283902580</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxzwAUcdAAFHHgABRx9AAVcc8AFX
					HQABVx4AAVcfQAFnHPABZx0AAWceAAFnH0AB
					dxwQAXcdAQF3HhcBdx+QAXcMAgGHHPABhx0A
					AYceAAGHH0ABlxxAAZcdEAGXHqEBlx8CAacc
					8AGnHQABpx4AAacfQAG3HPABtx0AAbceAAG3
					H0AB1xzwAdcdAAHXHgAB1x9AAecc8AHnHQAB
					5x4AAecfQAH3HPAB9x0AAfceAAH3H0ACFxwg
					AhcdEAIXHiECFx8CAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>39</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC275</string>
					<key>CodecID</key>
					<integer>283902581</integer>
					<key>ConfigData</key>
					<data>
					ASccAAEnHQABJx6gAScfkAFHHBABRx0BAUce
					FwFHH5ABVxwgAVcdEAFXHiEBVx8DAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC275</string>
					<key>CodecID</key>
					<integer>283902581</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFXHCABVx0QAVce
					IQFXHwMBJxwwAScdAAEnHqABJx+QAYccQAGH
					HVABhx6BAYcfAAHnHFAB5x0QAeceRQHnHwAB
					RwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Piscean - ALC275 for Sony Vaio SVD11225PXB</string>
					<key>CodecID</key>
					<integer>283902581</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQABJx6gAScfkAFXHBABVx0QAVce
					IQFXHwABhxwwAYcdUAGHHoEBhx8AAaccUAGn
					HQABpx4XAacfkAGnDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC275 for Sony Vaio - vusun123</string>
					<key>CodecID</key>
					<integer>283902581</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQABJx6gAScfkAFXHBABVx0QAVce
					IQFXHwABhxwwAYcdUAGHHoEBhx8AAaccUAGn
					HQABpx4XAacfkAGnDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC280</string>
					<key>CodecID</key>
					<integer>283902592</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4TAUcfkAFXHCABVx0QAVce
					IQFXHwEBJxwwAScdAAEnHqABJx+QAaccQAGn
					HRABpx6BAacfAgFHDAIBVwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC280 - ComboJack</string>
					<key>CodecID</key>
					<integer>283902592</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAFHHCABRx0AAUce
					FwFHH5ABVxwwAVcdEAFXHiEBVx8CAaccQAGn
					HRABpx6BAacfAgFHDAIBVwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>4</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Alienware alpha - Realtek ALC280</string>
					<key>CodecID</key>
					<integer>283902592</integer>
					<key>ConfigData</key>
					<data>
					IUcc8CFHHQAhRx4AIUcfQCFXHPAhVx0AIVce
					ACFXH0AhZxzwIWcdACFnHgAhZx9AIXcc8CF3
					HQAhdx4AIXcfQCGHHPAhhx0AIYceACGHH0Ah
					lxzwIZcdACGXHgAhlx9AIacc8CGnHQAhpx4A
					IacfQCG3HPAhtx0AIbceACG3H0Ah5xwQIecd
					4SHnHkUh5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - Realtek ALC280 - Dell T20 - Version1 - ManualMode</string>
					<key>CodecID</key>
					<integer>283902592</integer>
					<key>ConfigData</key>
					<data>
					AbccIAG3HUABtx4BAbcfAQGnHDABpx2QAace
					gQGnHwIBhxxAAYcdMAGHHoEBhx8BAVccYAFX
					HUABVx4hAVcfAgFXDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - Realtek ALC280 - Dell T20 - Version2 - SwitchMode</string>
					<key>CodecID</key>
					<integer>283902592</integer>
					<key>ConfigData</key>
					<data>
					AbccIAG3HUABtx4RAbcfkAGnHDABpx2QAace
					gQGnHwIBhxxAAYcdMAGHHoEBhx8BAVccYAFX
					HUABVx4hAVcfAgFXDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>cowpod - Realtek ALC280 - Optiplex 9020SFF</string>
					<key>CodecID</key>
					<integer>283902592</integer>
					<key>ConfigData</key>
					<data>
					AaccMAGnHZABpx6BAacfAgGHHEABhx0wAYce
					gQGHHwEBRxxQAUcdAAFHHhcBRx+QAUcMAgFX
					HGABVx1AAVceIQFXHwIBVwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>16</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgFXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC280 - Optiplex 9020SFF - ManualMode</string>
					<key>CodecID</key>
					<integer>283902592</integer>
					<key>ConfigData</key>
					<data>
					AUccMAFHHQABRx4XAUcfkAFHDAIBtxwgAbcd
					QAG3HgEBtx8BAVccEAFXHUABVx4hAVcfAgFX
					DAIBhxxAAYcdMAGHHoEBhx8BAaccUAGnHZAB
					px6BAacfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>17</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgFXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Dell Precision T7610 Workstation ALC280 by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>283902592</integer>
					<key>ConfigData</key>
					<data>
					AVccIAFXHUABVx4hAVcfAgGHHDABhx0AAYce
					oAGHH5ABpxxAAacdkAGnHoEBpx8CAbccEAG3
					HQABtx4AAbcfAQFnDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC282_v1</string>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfmQFHHCABRx0AAUce
					EwGXHDABlx0QAZceiwGXHwABRx+ZAhccUAIX
					HRACFx4rAhcfAQFHDAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC282_v2</string>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQABJx6gAScfkAFHHBABRx0AAUce
					FwFHH5ABhxwwAYcdEAGHHoEBhx8AAeccIAHn
					HRAB5x5EAecfAAIXHFACFx0QAhceIQIXHwAB
					RwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>4</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC282</string>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQABJx6gAScfkAFHHBABRx0AAUce
					FwFHH5ABhxwwAYcdEAGHHoEBhx8AAeccIAHn
					HRAB5x5EAecfAAIXHFACFx0QAhceIQIXHwAB
					RwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>Comment</key>
					<string>ALC282 for TinyMonster ECO by DalianSky</string>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHPABRx0AAUce
					AAFHH0ABlxwwAZcdAQGXHqcBlx+QAdccUAHX
					HZAB1x5GAdcfQAIXHCACFx0QAhceIQIXHwAC
					FwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AZcHJA==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>Comment</key>
					<string>Custom ALC282 lenovo y430p by loverto</string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHPABRx0AAUcd
					AAFHH0ABdxzwAXcdAAF3HgABdx9AAYcccAGH
					HRABhx6BAYcfAQGHHAIBlxzwAZcdAAGXHgAB
					lx9AAacc8AGnHQABpx4AAacfQAG3HEABtx0B
					AbceFwG3H5AB1xzwAdcdAAHXHgAB1x9AAecc
					YAHnHRAB5x5EAecfAQIXHFACFx0QAhceIQIX
					HwECFxwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>22</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>Comment</key>
					<string>Skvo ALC282 Acer Aspire on IvyBridge by Andrey1970</string>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQABJx6gAScfkAFHHBABRx0AAUce
					FwFHH5ABdxzwAXcdAAF3HgABdx9AAYcc8AGH
					HQABhx4AAYcfQAGXHPABlx0AAZceAAGXH0AB
					pxzwAacdAAGnHgABpx9AAbcc8AG3HQABtx4A
					AbcfQAHnHPAB5x0AAeceAAHnH0ACFxwgAhcd
					EAIXHiECFx8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>27</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>Comment</key>
					<string>Custom ALC282 Acer Aspire E1-572G</string>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHBABRx0AAUce
					FwFHH5ABdxzwAXcdAAF3HgABdx9AAYcc8AGH
					HQABhx4AAYcfQAGXHPABlx0AAZceAAGXH0AB
					pxzwAacdAAGnHgABpx9AAbccMAG3HQEBtx6g
					AbcfkAHnHPAB5x0AAeceAAHnH0ACFxwgAhcd
					EAIXHiECFx8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>Comment</key>
					<string>Custom ALC282 Dell Inspirion 3521 by Generation88</string>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQEBJx6gAScfkAFHHBABRx0BAUce
					FwFHH5ABlxwwAZcdEAGXHoEBlx8BAhccIAIX
					HRACFx4hAhcfAQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>29</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>Comment</key>
					<string>Custom ALC282 Soarsea S210H by Jokerman1991</string>
					<key>ConfigData</key>
					<data>
					ASccAAEnHQABJx4AAScfQAHXHAUB1x2bAdce
					RgHXH0ABRxwQAUcdAQFHHhcBRx+QAhccIAIX
					HRACFx4hAhcfBAGXHDABlx0BAZcepwGXH5AB
					dxzwAXcdEQF3HhEBdx9BAYcc8AGHHREBhx4R
					AYcfQQGnHPABpx0RAaceEQGnH0EBtxzwAbcd
					EQG3HhEBtx9BAecc8AHnHREB5x4RAecfQQFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>30</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC282 Lenovo Y410P by yunsur</string>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHPABRx0AAUce
					AAFHH0ABdxzwAXcdAAF3HgABdx9AAYccIAGH
					HRABhx6BAYcfAQGXHPABlx0AAZceAAGXH0AB
					pxzwAacdAAGnHgABpx9AAbccQAG3HQEBtx4X
					AbcfkAHXHPAB1x0AAdceAAHXH0AB5xxwAecd
					EAHnHkQB5x8BAhccUAIXHRACFx4hAhcfAQFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>41</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC282 Lenovo Y430P by yunsur</string>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHPABRx0AAUce
					AAFHH0ABdxzwAXcdAAF3HgABdx9AAYccIAGH
					HRABhx6BAYcfAQGXHPABlx0AAZceAAGXH0AB
					pxzwAacdAAGnHgABpx9AAbccQAG3HQEBtx4X
					AbcfkAHXHPAB1x0AAdceAAHXH0AB5xxwAecd
					EAHnHkQB5x8BAhccUAIXHRACFx4hAhcfAQFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>43</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC282 Lenovo Y510P by yunsur</string>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHEABRx0BAUce
					FwFHH5ABdxzwAXcdAAF3HgABdx9AAYccIAGH
					HRABhx6BAYcfAQGXHPABlx0AAZceAAGXH0AB
					pxzwAacdAAGnHgABpx9AAbcc8AG3HQABtx4A
					AbcfQAHXHPAB1x0AAdceAAHXH0AB5xxwAecd
					EAHnHkQB5x8BAhccUAIXHRACFx4hAhcfAQFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>51</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC282 Hasee K580C by YM2008</string>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHCABRx1AAUce
					EQFHHwEBdxzwAXcdAAF3HgABdx9AAYccIAGH
					HRABhx6BAYcfAQGXHPABlx0AAZceAQGXH0AB
					pxzwAacdAAGnHgEBpx9AAdcc8AG3HQABtx4B
					AbcfQAHXHPUB1x0AAdceBQHXH0AB5xzwAecd
					AAHnHgEB5x9AAhccQAIXHXACFx4hAhcfAQFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>76</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>Comment</key>
					<string>Custom ALC282 for Asus x200la</string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6BAScfAAFHHCABRx0BAUce
					EAFHH5kBdxzwAXcdAAF3HgABdx9AAYcc8AGH
					HQABhx4AAYcfQAGXHDABlx0BAZcepgGXH5kB
					pxzwAacdAAGnHgABpx9AAbcc8AG3HQABtx4A
					AbcfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhccUAIXHUACFx4rAhcfAAFH
					DAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>86</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902594</integer>
					<key>Comment</key>
					<string>No input boost ALC282 Acer Aspire on IvyBridge by Andrey1970</string>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQABJx6gAScfkAFHHBABRx0AAUce
					FwFHH5ABdxzwAXcdAAF3HgABdx9AAYcc8AGH
					HQABhx4AAYcfQAGXHPABlx0AAZceAAGXH0AB
					pxzwAacdAAGnHgABpx9AAbcc8AG3HQABtx4A
					AbcfQAHnHPAB5x0AAeceAAHnH0ACFxwgAhcd
					EAIXHiECFx8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>127</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Toleda NUC/BRIX patch ALC283</string>
					<key>CodecID</key>
					<integer>283902595</integer>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHPABRx0AAUce
					AAFHH0ABdxzwAXcdAAF3HgABdx9AAYcc8AGH
					HQABhx4AAYcfQAGXHGABlx0wAZceiwGXHwEB
					pxzwAacdAAGnHgABpx9AAbcc8AG3HQABtx4A
					AbcfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhcccAIXHUACFx4rAhcfAQGX
					DAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC283</string>
					<key>CodecID</key>
					<integer>283902595</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAFHHCABRx0BAUce
					FwFHH5ABlxwwAZcdAAGXHosBlx8AAhccQAIX
					HRACFx4rAhcfAQFHDAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom by Slbomber ALC283 (V3-371)</string>
					<key>CodecID</key>
					<integer>283902595</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAFHHEABRx0BAUce
					FwFHH5ABdxzwAXcdAAF3HgABdx9AAYcc8AGH
					HQABhx4AAYcfQAGXHPABlx0AAZceAAGXH0AB
					pxzwAacdAAGnHgABpx8AAbcc8AG3HQABtx4A
					AbcfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAhccUAIXHRACFx4hAhcfAwFH
					DAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC283 for AlldoCube/Cube Mix Plus by Aldo97</string>
					<key>CodecID</key>
					<integer>283902595</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAFHHCABRx0BAUce
					FwFHH5ABlxwwAZcdAAGXHosBlx8AAhccQAIX
					HRACFx4rAhcfAQFHDAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - alc283 for LENOVO IDEAPAD 14</string>
					<key>CodecID</key>
					<integer>283902595</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4XAUcfkAFHDAIBlxwgAZcd
					EAGXHoEBlx8AASccMAEnHQABJx6mAScfkAIX
					HGACFx0QAhceIQIXHwACFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC283 for ThinkCentre M93z 10AF ALC283 by giesteira </string>
					<key>CodecID</key>
					<integer>283902595</integer>
					<key>ConfigData</key>
					<data>
					AbccQAG3HQABtx4XAbcfmQEnHBABJx0AASce
					pgEnH5kBlxwgAZcdkAGXHoEBlx8BAaccMAGn
					HZABpx6BAacfAQIXHAACFx1AAhceIQIXHwEB
					RwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>44</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC283 for NUC7 by mikes </string>
					<key>CodecID</key>
					<integer>283902595</integer>
					<key>ConfigData</key>
					<data>
					AUccAAFHHQABRx4AAUcfQAEnHCABJx0AASce
					pgEnH5AB1xwwAdcdEAHXHoEB1x8AAhccQAIX
					HRACFx4hAhcfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>45</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ASRock DeskMini 110(H110M-STX) ALC283 by licheedev</string>
					<key>CodecID</key>
					<integer>283902595</integer>
					<key>ConfigData</key>
					<data>
					ASccgAEnHQABJx4AAScfQAFHHEABRx0BAUce
					EwFHH5ABpxwgAacdkAGnHoEBpx8BAdccYAHX
					HZAB1x5VAdcfQAIXHFACFx0QAhceIQIXHwEB
					RwwCAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>66</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC283 for DELL R14 3437 by xiaoleGun(zoran)</string>
					<key>CodecID</key>
					<integer>283902595</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAIBdxzwAXcd
					AAF3HgABdx9AAYcc8AGHHQABhx4AAYcfQAGX
					HEABlx0gAZceiwGXHwIBpxzwAacdAAGnHgAB
					px9AAbcc8AG3HQABtx4AAbcfQAHXHPAB1x0A
					AdceAAHXH0AB5xzwAecdAAHnHgAB5x9AAScc
					MAEnHQEBJx6mAScfkAIXHCACFx0QAhceIQIX
					HwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>88</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ThinkCentre M73(10AX) ALC283 by dumk1217</string>
					<key>CodecID</key>
					<integer>283902595</integer>
					<key>ConfigData</key>
					<data>
					ISccACEnHQAhJx4AIScfQCHXHAEh1x0AIdce
					QCHXH0AiFxwQIhcdQCIXHiEiFx8CIhcMAiFH
					HCAhRx0BIUceFyFHH5AhRwwCIZccMCGXHZAh
					lx6jIZcfmSGnHEAhpx2QIacegSGnHwEhdxzw
					IXcdACF3HgAhdx9AIYcc8CGHHQAhhx4AIYcf
					QCG3HPAhtx0AIbceACG3H0AhtwwCIecc8CHn
					HQAh5x4AIecfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
					<key>WakeConfigData</key>
					<data>
					IhcMAiFHDAIhtwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC284</string>
					<key>CodecID</key>
					<integer>283902596</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAE3HCABNx0AATce
					AAE3H0ABRxwwAUcdAAFHHhcBRx+QAVccQAFX
					HRABVx4hAVcfAAGHHFABhx0QAYcegQGHHwIB
					1xxgAdcdgAHXHmYB1x9AAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Rover Realtek ALC285 for X1C6th</string>
					<key>CodecID</key>
					<integer>283902597</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHDABRx0BAUce
					FwFHH5ABlxwAAZcdEAGXHosBlx8BAhccIAIX
					HRACFx4rAhcfAQHXHGAB1x2AAdceZgHXH0AB
					RwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902597</integer>
					<key>CodecName</key>
					<string>Andres - Realtek ALC285 for  Lenovo X1 Carbon 6th </string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHDABRx0BAUce
					FwFHH5ABlxwAAZcdEAGXHosBlx8EAhccIAIX
					HRACFx4rAhcfBAFHDAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902597</integer>
					<key>CodecName</key>
					<string>Flymin - Realtek ALC285 for  Thinkpad X1E</string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxxAAUcdAQFHHhcBRx+QAUcMAgFn
					HPABZx0AAWceAAFnH0ABdxzwAXcdAAF3HgAB
					dx9AAYcc8AGHHQABhx4AAYcfQAGXHCABlx0Q
					AZceiwGXHwQBpxzwAacdAAGnHgABpx9AAdcc
					8AHXHQAB1x4AAdcfQAHnHPAB5x0AAeceAAHn
					H0ACFxxQAhcdEAIXHisCFx8EAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>31</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902597</integer>
					<key>CodecName</key>
					<string>PIut02 - Realtek ALC285 for ROG-Zephyrus-G14</string>
					<key>ConfigData</key>
					<data>
					AScccAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxxAAUcdAQFHHhcBRx+QAUcMAgFn
					HPABZx0AAWceAAFnH0ABdxxBAXcdAQF3HhcB
					dx+QAYcc8AGHHQABhx4AAYcfQAGXHCABlx0Q
					AZceiwGXHwIBpxzwAacdAAGnHgABpx9AAbcc
					8AG3HQABtx4AAbcfQAG3DAIB1xzwAdcdAAHX
					HgAB1x9AAecc8AHnHQAB5x4AAecfQAIXHFAC
					Fx0QAhceKwIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>33</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC285 for Yoga C740 by fewtarius</string>
					<key>CodecID</key>
					<integer>283902597</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4TAUcfmQFHDAICFxwgAhcd
					EAIXHiECFx8TAhcMAgGXHEABlx0RAZceoQGX
					H5M=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>61</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC285 for Lenovo Legion S740 15-IRH (Y9000X 2020) by R-a-s-c-a-l</string>
					<key>CodecID</key>
					<integer>283902597</integer>
					<key>ConfigData</key>
					<data>
					AZccAAGXHRABlx6LAZcfAQEnHBABJx0BASce
					pgEnH5ACFxwgAhcdEAIXHisCFx8BAhcMAgF3
					HEABdx0BAXceFwF3H5A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>66</integer>
					<key>WakeConfigData</key>
					<data>
					AhcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>jpuxdev - Realtek ALC285 for Spectre x360 13-ap0xxx</string>
					<key>CodecID</key>
					<integer>283902597</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHDABRx0BAUce
					FwFHH5ABRwwCAXccQAF3HQEBdx4XAXcfkAGX
					HAABlx0QAZceiwGXHwEB1xxQAdcdgAHXHmYB
					1x9AAhccIAIXHRACFx4rAhcfAQIXDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>71</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902598</integer>
					<key>CodecName</key>
					<string>Mirone - Realtek ALC286</string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6mAScfsAFHHCABRx0AAUce
					FwFHH5ABhxwwAYcdEAGHHosBhx8EAhccQAIX
					HRACFx4rAhcfBAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902598</integer>
					<key>CodecName</key>
					<string>Lenovo YOGA3 pro ALC286 - gdllzkusi</string>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxxAAUcdAQFHHhcBRx+QAUcMAgF3
					HPABdx0AAXceAAF3H0ABhxwQAYcdEAGHHoEB
					hx8EAZcc8AGXHQABlx4AAZcfQAGnHPABpx0A
					AaceAAGnH0AB1xzwAdcdAAHXHgAB1x9AAecc
					8AHnHQAB5x4AAecfQAIXHDACFx0QAhceIQIX
					HwQCFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902598</integer>
					<key>CodecName</key>
					<string>HP-Pavilion-Wave-600-A058cn</string>
					<key>ConfigData</key>
					<data>
					AYccEAGHHRABhx6BAYcfBAE3HCABNx0BATce
					pgE3H5ACFxwwAhcdEAIXHiECFx8EAhcMAgF3
					HEABdx0BAXceFwF3H5ABdwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>69</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC287</string>
					<key>CodecID</key>
					<integer>283902599</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQEBdx4XAXcfkAIXHCACFx0QAhce
					IQIXHwMCFwwCAZccMAGXHRABlx6BAZcfAw==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AhcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC287 for Lenovo Yoga Slim 7-14IIL05 by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>283902599</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAICFxwgAhcd
					EAIXHiECFx8DAhcMAgGXHDABlx0QAZcegQGX
					HwMBpxxAAacdAQGnHqABpx+QAZcHJAGnByAC
					FwiD
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwckAacHIAIXCIM=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC287 for Legion 5 Pro(R9000p)</string>
					<key>CodecID</key>
					<integer>283902599</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQEBJx6gAScfkAE3HPABNx0AATce
					AAE3H0ABRxwQAUcdAQFHHhcBRx+QAUcMAgF3
					HPABdx0AAXceAAF3H0ABhxzwAYcdAAGHHgAB
					hx9AAZccMAGXHRABlx6BAZcfAwGnHPABpx0A
					AaceAAGnH0ABtxzwAbcdAAG3HgABtx9AAdcc
					8AHXHQAB1x4AAdcfQAHnHPAB5x0AAeceAAHX
					H0ACFxwgAhcdEAIXHiECFx8DAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AhcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC288</string>
					<key>CodecID</key>
					<integer>283902600</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAE3HCABNx0AATce
					AAE3H0ABRxwwAUcdAAFHHhcBRx+QAYccQAGH
					HRABhx6BAYcfAgHXHFAB1x2AAdceZQHXH0AC
					FxxgAhcdEAIXHiECFx8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC288 for Dell XPS 9343</string>
					<key>CodecID</key>
					<integer>283902600</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQABJx6gAScfkAFHHDABRx0AAUce
					FwFHH5ABNxxAATcdEAE3HoEBNx8AAhccUAIX
					HRACFx4hAhcfAAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>yyfn - Realtek ALC288 for Dell XPS 9343</string>
					<key>CodecID</key>
					<integer>283902600</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxwQAUcdAQFHHhcBRx+QAUcMAgF3
					HPABdx0AAXceAAF3H0ABhxzwAYcdAAGHHgAB
					hx9AAZcc8AGXHQABlx4AAZcfQAGnHHABpx0g
					AacYqwGnHwIBpwwCAdcc8AHXHQAB1x4AAdcf
					QAHnHPAB5x0AAeceAAHnH0ACFxw/AhcdEAIX
					HisCFx8D
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>23</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>leeoem - Realtek ALC289 for alienware m17r2</string>
					<key>CodecID</key>
					<integer>283902601</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxxAAUcdAQFHHhcBRx+QAUcMAgFn
					HPABZx0AAWceAAFnH0ABdxzwAXcdAAF3HgAB
					dx9AAYcc8AGHHQABhx4AAYcfQAGXHHABlx0Q
					AZceiwGXHwIBpxzwAacdAAGnHgABpx9AAbcc
					8AG3HQABtx4AAbcfQAHXHPAB1x0AAdceAAHX
					H0AB5xzwAecdAAHnHgAB5x9AAhccMAIXHRAC
					Fx4rAhcfAgIXDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC289 for Dell XPS 13 9300</string>
					<key>CodecID</key>
					<integer>283902601</integer>
					<key>ConfigData</key>
					<data>
					AScfkAGXHEABlx0QAZcepwGXHwACFx8BAhcM
					AgIFAEUCBNaJ
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
					<key>WakeConfigData</key>
					<data>
					AZcHJAIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC289 for Dell XPS 15 9500 4 Speakers</string>
					<key>CodecID</key>
					<integer>283902601</integer>
					<key>ConfigData</key>
					<data>
					AScfkAGXHEABlx0QAZcepwGXHwACFx8BAhcM
					AgIFAEUCBNaJ
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AZcHJAIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - ALC289 for Dell 7730 Precision CM240 </string>
					<key>CodecID</key>
					<integer>283902601</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6mAScfkAFHHCABRx0AAUce
					FwFHH5ABRwwCAZccMAGXHRABlx6BAZcfAAIX
					HEACFx0QAhceIQIXHwACFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC289 for Acer PT515-51 By Bugprogrammer and Rover</string>
					<key>CodecID</key>
					<integer>283902601</integer>
					<key>ConfigData</key>
					<data>
					ATccMAE3HQEBNx6mATcftwFHHBABRx0BAUce
					FwFHH5ABRwwCAbccQAG3HRABtx6BAbcfAwG3
					DAICFxwgAhcdEAIXHiECFx8DAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>23</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>PIut02 - Realtek ALC289 for ROG-Zephyrus-G14</string>
					<key>CodecID</key>
					<integer>283902601</integer>
					<key>ConfigData</key>
					<data>
					AScccAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxxAAUcdAQFHHhcBRx+QAUcMAgFn
					HPABZx0AAWceAAFnH0ABdxxBAXcdAQF3HhcB
					dx+QAYcc8AGHHQABhx4AAYcfQAGXHCABlx0Q
					AZceiwGXHwIBpxzwAacdAAGnHgABpx9AAbcc
					8AG3HQABtx4AAbcfQAG3DAIB1xzwAdcdAAHX
					HgAB1x9AAecc8AHnHQAB5x4AAecfQAIXHFAC
					Fx0QAhceKwIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>33</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC289 for Dell XPS 7390 ICL 2in1 By Lorys89</string>
					<key>CodecID</key>
					<integer>283902601</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAICFxwgAhcd
					EAIXHisCFx8EAhcMAgEnHDABJx0BAScepgEn
					H5ABlxxAAZcdEAGXHosBlx8E
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>68</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC289 for Dell XPS 2in1 7390 Vorshim</string>
					<key>CodecID</key>
					<integer>283902601</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6mAScfkAFHHEABRx0BAUce
					FwFHH5ABRwwCAZccQAGXHRABlx6nAZcfAAIF
					AEUCBNaJAhcdEAIXHiECFx8BAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>69</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgGXByQCFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC289 for Dell XPS 13 9300 by DalianSky</string>
					<key>CodecID</key>
					<integer>283902601</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfmQF3HDABdx0BAXce
					FwF3H5kBlxwAAZcdEAGXHosBlx4CAhccIAIX
					HRACFx4rAhcfAQIXDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>naufalkharits - Realtek ALC289 for Alienware m15</string>
					<key>CodecID</key>
					<integer>283902601</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAICFxwwAhcd
					EAIXHiECFx8EAhcMAgEnHEABJx0BAScepgEn
					H7cBlxxQAZcdEQGXHoEBlx+R
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>87</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwck
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC290</string>
					<key>CodecID</key>
					<integer>283902608</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4XAUcfkAFXHCABVx0QAVce
					KwFXHwIBlxwwAZcdAAGXHqABlx+QAaccQAGn
					HRABpx6LAacfAAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902608</integer>
					<key>Comment</key>
					<string>macpeetALC ALC290 aka ALC3241</string>
					<key>ConfigData</key>
					<data>
					AaccIAGnHRABpx6BAacfAAEnHDABJx0AASce
					owEnH5ABRxxAAUcdAAFHHhcBRx+QAVccUAFX
					HRABVx4hAVcfAAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>4</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902608</integer>
					<key>Comment</key>
					<string>ALC3241 - HP Envy 15t-k200 Beats Audio 2.1</string>
					<key>ConfigData</key>
					<data>
					AaccEAGnHRABpx6BAacfAAEnHCABJx0AASce
					owEnH5ABRxwwAUcdAQFHHhABRx+QAXccMQF3
					HQEBdx4QAXcfkAFXHFABVx0QAVceIQFXHwAB
					RwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>10</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902608</integer>
					<key>Comment</key>
					<string>MacPeet - ALC290 for HP m6 n015dx</string>
					<key>ConfigData</key>
					<data>
					AaccIAGnHRABpx6BAacfAAEnHDABJx0AASce
					owEnH5ABVxxAAVcdEAFXHiEBVx8AAYccUAGH
					HQEBhx4XAYcfkAGHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					AYcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902608</integer>
					<key>Comment</key>
					<string>vusun123 - ALC 290 for Dell Vostro 5480</string>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQABJx6gAScfkAFHHBABRx0AAUce
					FwFHH5ABRwwCAVccIAFXHRABVx4hAVcfAAGn
					HEABpx0QAacegQGnHwA=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC292</string>
					<key>CodecID</key>
					<integer>283902610</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfmQFHHCABRx0AAUce
					FwFHH5kBVxwwAVcdQAFXHiEBVx8BAZccUAGX
					HZABlx6BAZcfAQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - alc292 for LENOVO THINKPAD T450_T450s_X240 - ManualMode</string>
					<key>CodecID</key>
					<integer>283902610</integer>
					<key>ConfigData</key>
					<data>
					AaccIAGnHRABpx6BAacfAAEnHDABJx0AASce
					pgEnH5ABRxxAAUcdAAFHHhcBRx+QAUcMAgFX
					HFABVx0QAVceAQFXHwABVwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgFXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902610</integer>
					<key>Comment</key>
					<string>vanquybn - ALC 292 for Dell M4800</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4XAUcfkAGHHCABhx2QAYce
					gQGHHwEBJxwwAScdAAEnHqYBJx+QAVccQAFX
					HUABVx4hAVcfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>18</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902610</integer>
					<key>Comment</key>
					<string>vusun123 - ALC 292 for Lenovo T440</string>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQABJx6gAScfkAFHHEABRx0AAUce
					FwFHH5ABRwwCAVccUAFXHRABVx4hAVcfAAGn
					HCABpx0QAacegQGnHwA=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902610</integer>
					<key>Comment</key>
					<string>ALC292 for Lenovo T450s By Echo</string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRx0BAUceFwFHH5ABRwwCAVccQAFX
					HRABVx4rAVcfBAFXDAIBZxzwAWcdAAFnHgAB
					Zx9AAYcc8AGHHQABhx4AAYcfQAGXHPABlx0A
					AZceAAGXH0ABpxwgAacdEAGnHosBpx8EAbcc
					8AG3HQABtx4AAbcfQAHXHPAB1x0AAdceAAHX
					H0AB5xzwAecdAAHnHgAB5x9AAUccMA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>32</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902610</integer>
					<key>Comment</key>
					<string>baesar0 -ALC 292 for e6540 with dock</string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxxAAUcdAQFHHhcBRx+QAUcMAgFX
					HFABVx1AAVceKwFXHwIBVwwCAWccgAFnHUAB
					Zx4BAWcfAgFnDAIBhxzwAYcdAAGHHgABhx9A
					AZccIAGXHZABlx6BAZcfAgGnHHABpx0QAace
					qwGnHwIBtxzwAbcdAAG3HgABtx9AAdcc8AHX
					HQAB1x4AAdcfQAHnHPAB5x0AAeceAAHnH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>55</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC293 Dell E7450 by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>283902611</integer>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAE3HBABNx0BATce
					oAE3H5ABRxwwAUcdAQFHHhcBRx+QAVccQAFX
					HUABVx4rAVcfAgFnHFABZx1AAWceAQFnHwIB
					hxzwAYcdAAGHHgABhx9AAZcc8AGXHQABlx4A
					AZcfQAGnHCABpx0QAaceiwGnHwIBtxzwAbcd
					AAG3HgABtx9AAdcc8AHXHQAB1x4AAdcfQAHn
					HPAB5x0AAeceAAHnH0ABRwwCAVcMAgFnDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>tluck - ALC 293 for Lenovo T460/T560 - extra LineOut on Dock</string>
					<key>CodecID</key>
					<integer>283902611</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxwgAUcdAQFHHhcBRx+QAVccMAFX
					HRABVx4hAVcfAwFnHPABZx0AAWceAAFnH0AB
					hxzwAYcdAAGHHgABhx9AAZcc8AGXHQABlx4A
					AZcfQAGnHEABpx0QAacegQGnHwMBtxzwAbcd
					AAG3HgABtx9AAdcc8AHXHQAB1x4AAdcfQAHn
					HPAB5x0AAeceAAHnH0ABRwwCAVcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>tluck - ALC 293 for Lenovo T460/T560</string>
					<key>CodecID</key>
					<integer>283902611</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxwgAUcdAQFHHhcBRx+QAVccMAFX
					HRABVx4hAVcfAwFnHPABZx0AAWceAAFnH0AB
					hxzwAYcdAAGHHgABhx9AAZcc8AGXHQABlx4A
					AZcfQAGnHEABpx0QAacegQGnHwMBtxzwAbcd
					AAG3HgABtx9AAdcc8AHXHQAB1x4AAdcfQAHn
					HPAB5x0AAeceAAHnH0ABRwwCAVcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>29</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC 293 for Hasee ZX8-CT5DA/Clevo N9x0TD_TF by RushiaBoingBoing</string>
					<key>CodecID</key>
					<integer>283902611</integer>
					<key>ConfigData</key>
					<data>
					ASccUAEnHQEBJx6jAScfmQE3HCABNx0AATce
					AAE3HwEBRxwQAUcdAQFHHhMBRx+ZAVccMAFX
					HRABVx4hAVcfAQGHHGABhx0QAYcegQGHHwEB
					1xxwAdcdAAHXHnMB1x9AAeccQAHnHREB5x5F
					AecfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>30</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC 293 for Hasee Z7-CT7NA by lgh07711</string>
					<key>CodecID</key>
					<integer>283902611</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFXHCABVx0QAVce
					IQFXHwIBVwwCASccMAEnHQEBJx6mAScfkAGn
					HEABpx0RAacegQGnHwIBRwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>31</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Rover - Realtek ALC294 for Asus FL8000U</string>
					<key>CodecID</key>
					<integer>283902612</integer>
					<key>ConfigData</key>
					<data>
					AbccEAG3HQEBtx6nAbcfkAFHHCABRx0BAUce
					FwFHH5ACFxwwAhcdEAIXHiECFx8BAUcMAgG3
					DAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - Realtek ALC294 for Lenovo M710Q</string>
					<key>CodecID</key>
					<integer>283902612</integer>
					<key>ConfigData</key>
					<data>
					AhccIAIXHRACFx4hAhcfAAGXHDABlx0QAZce
					gQGXHwABpxxAAacdkAGnHoEBpx8AAUccUAFH
					HQABRx4XAUcfkAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC294</string>
					<key>CodecID</key>
					<integer>283902612</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQABdx4XAXcfkAEnHCABJx0AASce
					oAEnH5ACFxwwAhcdEAIXHiECFx8A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC294, ZenBook UX434</string>
					<key>CodecID</key>
					<integer>283902612</integer>
					<key>ConfigData</key>
					<data>
					AgUAGwIETksCBQBFAgRSiQGXHEABlx0QAZce
					pwGXHwA=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAgGXByQ=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres ZeroCross - ALC294 ASUS ZenBook Flip 14 UX461UA</string>
					<key>CodecID</key>
					<integer>283902612</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6gAScfkAF3HBABdx0BAXce
					FwF3H5ABlxxAAZcdEAGXHoEBlx8EAhccMAIX
					HRACFx4hAhcfBAF3DAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>cowpod - Realtek ALC294 for ASUS ROG GL504GW</string>
					<key>CodecID</key>
					<integer>283902612</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6gAScfkAF3HBABdx0BAXce
					FwF3H5ABlxxAAZcdEAGXHoEBlx8EAhccMAIX
					HRACFx4hAhcfBAF3DAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>22</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Theroadw - Realtek ALC294 for Asus rog strix g512li</string>
					<key>CodecID</key>
					<integer>283902612</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAICFxwgAhcd
					EAIXHiECFx8DAhcMAgEnHDABJx0BAScepgEn
					H5ABlxxAAZcdEAGXHoEBlx8CATcc8AE3HQAB
					Nx4AATcfQAGHHPABhx0AAYceAAGHH0ABpxzw
					AacdAAGnHgABpx9AAbcc8AG3HQABtx4AAbcf
					QAHXHPAB1x0AAdceAAHXH0AB5xzwAecdAAHn
					HgAB5x9AAgUAEAIEASABlwckAhcIgw==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>24</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Ayat Kyo - Realtek ALC294 for Asus ROG G531GD</string>
					<key>CodecID</key>
					<integer>283902612</integer>
					<key>ConfigData</key>
					<data>
					ATccAAE3HQABNx4QATcfQAFHHBABRx0BAUce
					FwFHH5ABRwwCAZccIAGXHRABlx6BAZcfBAIX
					HDACFx0QAhceIQIXHwQCFwwCAdccQAHXHZoB
					1x5nAdcfQAEnHFABJx0BASceoAEnH5A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>narcyzzo - Realtek ALC294 for ASUS UX534FAC</string>
					<key>CodecID</key>
					<integer>283902612</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAF3HCABdx0BAXce
					EwF3H5ABdwwCAZccMAGXHRABlx6BAZcfBAIX
					HEACFx0QAhceIQIXHwQCFwwCAgUADwIEd3QC
					BQBFAgRSiQIFABACBAQgAgUAGwIETks=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>44</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>KKKIIINNN - ALC294 ASUS X542UQR</string>
					<key>CodecID</key>
					<integer>283902612</integer>
					<key>ConfigData</key>
					<data>
					AhccIAIXHRACFx4hAhcfAQG3HDABtx0BAbce
					pwG3H5ABlxwwAZcdEAGXHoEBlx8BAUccEAFH
					HQEBRx4XAUcfkAFHDAIBtwwCAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>66</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>hoping - Realtek ALC294 for ASUS ROG GU502LV</string>
					<key>CodecID</key>
					<integer>283902612</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6gAScfkAF3HBABdx0BAXce
					FwF3H5ABlxxAAZcdEAGXHqEBlx8DAhcc8AIX
					HRACFx4hAhcfAwF3DAICFwwCAgUAEAIECiAB
					VwfAAVOwAAG3ByQ=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAgIXDAM=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Damon - Realtek ALC 295 for HP Envy x360 15-bp107tx</string>
					<key>CodecID</key>
					<integer>283902613</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxzwAUcdAAFHHgABRx9AAUcMAgFn
					HPABZx0AAWceAAFnH0ABdxwQAXcdAQF3HhcB
					dx+QAYcc8AGHHQABhx4BAYcfQAGXHEABlx0Q
					AZcegQGXHwIBpxzwAacdAAGnHgABpx9AAbcc
					8AG3HQABtx4AAbcfQAG3DAIB1xzwAdcdAAHX
					HgAB1x9AAecc8AHnHQAB5x4BAecfQAIXHCAC
					Fx0QAhceIQIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>1</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC295, ZenBook UX581</string>
					<key>CodecID</key>
					<integer>283902613</integer>
					<key>ConfigData</key>
					<data>
					AgUARQIEUIkCBQAbAgROSwIXDAIBhx0AAYce
					AAGHH0ABlxxQAZcdEAGXHqcBlx8A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AhcMAgGXByQ=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC295/ALC3254</string>
					<key>CodecID</key>
					<integer>283902613</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6mAScfmQF3HCABdx0AAXce
					FwF3H5kBlxwwAZcdEAGXHoEBlx8CAhccQAIX
					HRACFx4hAhcfAgF3DAIBRwwCAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>DalianSky - Realtek ALC295/ALC3254 Dell7570</string>
					<key>CodecID</key>
					<integer>283902613</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxxAAUcdAQFHHhcBRx+QAUcMAgFn
					HPABZx0AAWceAAFnH0ABdxzwAXcdAAF3HgAB
					dx9AAYcc8AGHHQABhx4AAYcfQAGXHBABlx0Q
					AZcegQGXHwIBpxzwAacdAAGnHgABpx9AAbcc
					8AG3HQABtx4AAbcfQAHXHPAB1x0AAdceAAHX
					H0AB5xzwAecdAAHnHgAB5x9AAhccMAIXHRAC
					Fx4hAhcfAgIXDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC295 v2 Asus UX430UA</string>
					<key>CodecID</key>
					<integer>283902613</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQABdx4XAXcfkAEnHCABJx0AASce
					oAEnH5ACFxwwAhcdEAIXHiECFx8AAZccQAGX
					HRABlx6BAZcfAAIXDAIBJwcgAXcHQAGXByAB
					1wcgAecHQAIXB8A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>14</integer>
					<key>WakeConfigData</key>
					<data>
					AScHIAF3B0ABlwcgAdcHIAHnB0ACFwfA
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC295/ALC3254 </string>
					<key>CodecID</key>
					<integer>283902613</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4XAUcfkAEnHCABJx0AASce
					oAEnH5ACFxwwAhcdEAIXHiECFx8AAZccQAGX
					HRABlx6BAZcfAA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres - ALC295 Acer Nitro 5 Spin (NP515-51)</string>
					<key>CodecID</key>
					<integer>283902613</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6gAScfkAE3HPABNx0AATce
					AAE3H0ABRxwQAUcdAQFHHhcBRx+QAWcc8AFn
					HQABZx4AAWcfQAF3HPABdx0AAXceAAF3H0AB
					hxzwAYcdAAGHHgABhx9AAZccMAGXHRABlx6B
					AZcfAgGnHPABpx0AAaceAAGnH0ABtxzwAbcd
					AAG3HgABtx9AAdcc8AHXHQAB1x4AAdcfQAHn
					HPAB5x0AAeceAAHnH0ACFxxAAhcdEAIXHiEC
					Fx8CAUcMAgIXDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC295 by aleix</string>
					<key>CodecID</key>
					<integer>283902613</integer>
					<key>ConfigData</key>
					<data>
					ATccAAE3HQABNx4AATcfQAHXHAEB1x0AAdce
					YAHXH0ABRxwQAUcdAQFHHhcBRx+RAUcMAgIX
					HCACFx0QAhceIQIXHwMCFwwCASccMAEnHQEB
					Jx6mAScftwGXHEABlx0QAZcegQGXHwMBZxzw
					AWcdEQFnHhEBZx9BAXcc8AF3HREBdx4RAXcf
					QQGHHPABhx0RAYceEQGHH0EBpxzwAacdEQGn
					HhEBpx9BAbcc8AG3HREBtx4RAbcfQQG3DAIB
					5xzwAecdEQHnHhEB5x9B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>22</integer>
					<key>WakeConfigData</key>
					<data>
					ATcMAAHXDAABRwwCAhcMAgEnDAABlwwAAWcM
					AAF3DAABhwwAAacMAAG3DAIB5wwA
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>vusun123 - ALC 295 for Skylake HP Pavilion</string>
					<key>CodecID</key>
					<integer>283902613</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQABJx6gAScfuQFHHFABRx0AAUce
					FwFHH5ABRwwCAZccQAGXHRABlx6BAZcfAAIX
					HCACFx0QAhceIQIXHwA=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Lorys89 - Realtek ALC295/ALC3254 for Dell Latitude 7210 2-in-1</string>
					<key>CodecID</key>
					<integer>283902613</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAICFxwgAhcd
					EAIXHisCFx8EAhcMAgEnHDABJx0BAScepgEn
					H5ABlxxAAZcdEAGXHosBlx8E
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>33</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Lorys89 - Realtek ALC295/ALC3254 for Dell Inspiron 7590</string>
					<key>CodecID</key>
					<integer>283902613</integer>
					<key>ConfigData</key>
					<data>
					AZccEAGXHREBlx6BAZcfBAFHHBABRx0BAUce
					FwFHH5ABRwwCAhccIAIXHRACFx4hAhcfBAIX
					DAIBJxzwAScdAQEnHqYBJx+QAgUARQIE1ok=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>75</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgGXByQCFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Unbelievable9 - Realtek ALC295/ALC3254 for Dell Latitude 5290</string>
					<key>CodecID</key>
					<integer>283902613</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScftwE3HPABNx0AATce
					AAE3H0ABRxwQAUcdAQFHHhcBRx+QAUcMAgFn
					HPABZx0AAWceAAFnH0ABdxzwAXcdAAF3HgAB
					dx9AAYcc8AGHHQABhx4AAYcfQAGXHHABlx0g
					AZceqwGXHwIBpxzwAacdAAGnHgABpx9AAbcc
					8AG3GwABtx4AAbcfQAHXHPAB1x0AAdceAAHX
					H0AB5xzwAecdAAHnHgAB5x9AAhccMAIXHRAC
					Fx4rAhcfAgIXDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>77</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC298 SP4 - ComboJack</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAFHHCABRx0AAUce
					FwFHH5ABhxwwAYcdEAGHHoEBhx8CAhccQAIX
					HRACFx4hAhcfAgFHDAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Rockjesus.cn - Realtek ALC298 for Alienware 17 R4 2.1ch</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQABJx6gAScfkAF3HEABdx0AAXce
					FwF3H5ABdwwCAYcccAGHHRABhx6BAYcfAAIX
					HCACFx0QAhceIQIXHwA=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC298</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQABdx4XAXcfkAEnHCABJx0AASce
					oAEnH5ACFxwwAhcdEAIXHiECFx8CAYccQAGH
					HRABhx6BAYcfAgFHDAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Piscean - Realtek ALC298 for Dell Precision 5540</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6gAScfkAF3HBABdx0BAXce
					FwF3H5ABhxxwAYcdIAGHHosBhx8CAdcc8AHX
					HREB1x4RAdcfQQIXHCACFx0QAhceKwIXHwMB
					dwwCAYcHIgIXCIICBQBPAgTUAAGnByM=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAgGHByICFwiCAgUATwIE1AABpwwCAacH
					IwEnByA=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Ping - Realtek ALC298 for Dell Precision 5520</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6gAScfkAF3HBABdx0BAXce
					FwF3H5ABhxxwAYcdIAGHHosBhx8CAhccIAIX
					HRACFx4rAhcfAwF3DAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>16</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Lenovo 720S-15IKB ALC298 by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6gAScfkAE3HPABNx0AATce
					AAE3H0ABRxzwAUcdAAFHHgABRx9AAXcc8AF3
					HQABdx4AAXcfQAGHHHABhx0QAYcegQGHHwAB
					lxzwAZcdAAGXHgABlx9AAaccUAGnHQEBpx4X
					AacfkAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAfcc8AH3HQAB9x4AAfcfQAIX
					HCACFx0QAhceIQIXHwABpwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AacMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Razer Blade 14 2017 by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6gAScfkAE3HPABNx0AATce
					AAE3H0ABRxwQAUcdAQFHHhcBRx+QAXcc8AF3
					HQABdx4AAXcfQAGHHEABhx0QAYcegQGHHwMB
					lxzwAZcdAAGXHgABlx9AAacc8AGnHQABpx4A
					AacfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAfcc8AH3HQAB9x4AAfcfQAIX
					HCACFx0QAhceIQIXHwMBRwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>22</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>vusun123 - Realtek ALC298 for Dell XPS 9x50</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQABJx6gAScfkAF3HEABdx0AAXce
					FwF3H5ABdwwCAhccIAIXHRACFx4hAhcfAA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>vusun123 - Realtek ALC298 for Lenovo X270</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					AEcc8ABHHQAARx4AAEcfAABXHPAAVx0AAFce
					AABXHwAAdxzwAHcdAAB3HgAAdx8AAOcc8ADn
					HQAA5x4AAOcfAAD3HPAA9x0AAPceAAD3HwAB
					BxzwAQcdAAEHHgABBx8AASccQAEnHQEBJx6g
					AScfkAE3HPABNx0AATceAAE3HwABRxwQAUcd
					AQFHHhcBRx+QAUcMAgFXHPABVx0AAVceAAFX
					HwABZxzwAWcdAAFnHgABZx8AAXcc8AF3HQAB
					dx4AAXcfAAF3DAIBhxwwAYcdEAGHHoEBhx8D
					AZcc8AGXHQABlx4AAZcfAAGnHPABpx0AAace
					AAGnHwABtxzwAbcdAAG3HgABtx8AAccc8AHH
					HQABxx4AAccfAAHXHPAB1x0AAdceAAHXHwAB
					5xzwAecdAAHnHgAB5x8AAfcc8AH3HQAB9x4A
					AfcfAAIHHPACBx0AAgceAAIHHwA=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>29</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgF3DAIBhwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Constanta - Realtek ALC298 for Xiaomi Mi Notebook Air 13.3 Fingerprint 2018</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQABJx6gAScfkAF3HEABdx0AAXce
					FwF3H5ABdwwCAYcccAGHHRABhx6BAYcfAAIX
					HCACFx0QAhceIQIXHwA=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>30</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>smallssnow xps 9570 - Realtek ALC298</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfmQF3HEABdx0BAXce
					FwF3H5ABdwwCAYcc8AGHHQABhx4AAYcfQAGn
					HPABpx0AAaceAAGnH0ACFxxQAhcdEAIXHiEC
					Fx8BATcc8AE3HQABNx4AATcfQAFHHPABRx0A
					AUceAAFHH0ABlxzwAZcdAAGXHgABlx9AAdcc
					8AHXHQAB1x4AAdcfQAHnHPAB5x0AAeceAAHn
					H0AB9xzwAfcdAAH3HgAB9x9A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>32</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>RockJesus.cn - Realtek ALC298 for surface laptop 1gen</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQEBJx6mAScfkAGHHDABhx0QAYce
					oQGHHwMBpxwQAacdAQGnHhcBpx+QAhccIAIX
					HRACFx4hAhcfAw==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>33</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Daliansky - Realtek ALC298 ThinkPad T470p</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAFHHCABRx0AAUce
					FwFHH5ABhxwwAYcdEAGHHoEBhx8CAhccQAIX
					HRACFx4hAhcfAgFHDAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>47</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>lgs3137 - Realtek ALC298 MECHREVO S1</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxwgAUcdAQFHHhcBRx+QAXcc8AF3
					HQABdx4AAXcfQAGHHDABhx0QAYcegQGHHwQB
					lxzwAZcdAAGXHgABlx9AAacc8AGnHQABpx4A
					AacfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAfcc8AH3HQAB9x4AAfcfQAIX
					HEACFx0QAhceIQIXHwQBRwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>66</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>mbarbierato - Realtek ALC298 for Microsoft Surface GO 2</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAFHHCABRx0AAUce
					FwFHH5ABRwwCAYccMAGHHRABhx6BAYcfAgIX
					HEACFx0QAhceIQIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>69</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom - Realtek ALC298 for Dell XPS 9560 by KNNSpeed</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAF3HCABdx0BAXce
					FwF3H5ABhxwwAYcdEAGHHqsBhx8DAaccQAGn
					HRABpx6LAacfAwIXHFACFx0QAhceKwIXHwMB
					RwwCAXcMAgGnDAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>72</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom - Realtek ALC298 for Lenovo Yoga C940 by idalin</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQEBdx4XAXcfkAF3DAIBRxwRAUcd
					AQFHHhcBRx+QAUcMAgIXHBACFx0QAhceKwIX
					HwQBJxzwAScdAAEnHgABJx9AAYccMAGHHQEB
					hx6mAYcfkAHXHPAB1x0AAdceAAHXH0ABNxzw
					ATcdAAE3HgABNx9AAecc8AHnHQAB5x4AAecf
					QAH3HPAB9x0AAfceAAH3H0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>94</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAgFHDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Daliansky - Realtek ALC298 XiaoMi Pro</string>
					<key>CodecID</key>
					<integer>283902616</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAE3HPABNx0AATce
					AAE3H0ABRxzwAUcdAAFHHgABRx9AAXccIAF3
					HQEBdx4XAXcfkAGHHDABhx0QAYcegQGHHwMB
					lxzwAZcdAAGXHgABlx9AAacc8AGnHQABpx4A
					AacfQAHXHPAB1x0AAdceAAHXH0AB5xzwAecd
					AAHnHgAB5x9AAfcc8AH3HQAB9x4AAfcfQAIX
					HEACFx0QAhceIQIXHwMBdwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres - ALC299 Acer Helios 500</string>
					<key>CodecID</key>
					<integer>283902617</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQEBJx6gAScfkAFHHBABRx0BAUce
					FwFHH5ABlxwwAZcdEAGXHoEBlx8EAhccIAIX
					HRACFx4hAhcfBAFHDAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres - ALC299 Dell XPS13</string>
					<key>CodecID</key>
					<integer>283902617</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQEBJx6gAScfkAF3HBABdx0BAXce
					FwF3H5ABlxwwAZcdEAGXHoEBlx8EAhccIAIX
					HRACFx4hAhcfBA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>22</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres ZeroCross - ALC623 Lenovo M70T</string>
					<key>CodecID</key>
					<integer>283903523</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAGHHCABhx2QAYce
					oAGHH5ABlxxgAZcdkAGXHoEBlx8CAhccUAIX
					HUACFx4hAhcfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Pinokyo-H - Lenovo ThinkCentre SFF M720e</string>
					<key>CodecID</key>
					<integer>283903523</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQF3HCABdx0BAXce
					FwF3H5ABdwwCAYccMAGHHRABhx6gAYcfkAGX
					HEABlx0QAZcegQGXHwICFxxQAhcdEAIXHiEC
					Fx8C
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAg==
					</data>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC662</string>
					<key>CodecID</key>
					<integer>283903586</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAGHHCABhx2QAYce
					oAGHH5AB5xwwAecdYQHnHksB5x8BAaccQAGn
					HTABpx6BAacfAQG3HFABtx1AAbceIQG3HwEB
					lxxgAZcdkAGXHoEBlx8C
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC662</string>
					<key>CodecID</key>
					<integer>283903586</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFXHCABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAXccQAF3
					HSABdx4BAXcfAQGHHFABhx2QAYceoAGHH5AB
					lxxgAZcdkAGXHoEBlx8CAacccAGnHTABpx6B
					AacfAQG3HIABtx1AAbceIQG3HwIB5xyQAecd
					YAHnHkUB5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC662 by Irving23 for Lenovo ThinkCentre M8400t-N000</string>
					<key>CodecID</key>
					<integer>283903586</integer>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfASGHHCAhhx2QIYce
					oCGHH5AhlxxgIZcdkCGXHqEhlx8CIaccQCGn
					HTAhpx6BIacfASG3HFAhtx1AIbceISG3HwIh
					5xwwIecdYSHnHksh5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC662 by stich86 for Lenovo ThinkCentre M800</string>
					<key>CodecID</key>
					<integer>283903586</integer>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfASGHHCAhhx2QIYce
					oCGHH5AhlxxgIZcdkCGXHqEhlx8CIaccQCGn
					HTAhpx6BIacfASG3HFAhtx1AIbceISG3HwIh
					5xwwIecdYSHnHksh5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC662 by Vandroiy for Asus X66Ic</string>
					<key>CodecID</key>
					<integer>283903586</integer>
					<key>ConfigData</key>
					<data>
					AUccMAFHHQEBRx4QAUcfkAG3HEABtx0AAbce
					IQG3HwEBlxwQAZcdAQGXHqABlx+QAYccIAGH
					HQABhx6BAYcfAQFXHPABVx0AAVceAAFXH0AB
					ZxzwAWcdAAFnHgABZx9AAacc8AGnHQABpx4A
					AacfQAHHHPABxx0AAcceAAHHH0AB1xzwAdcd
					AAHXHgAB1x9AAecc8AHnHQAB5x4AAecfQAFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - ALC662 for Acer Aspire A7600U All in One</string>
					<key>CodecID</key>
					<integer>283903586</integer>
					<key>ConfigData</key>
					<data>
					AbccEAG3HUABtx4hAbcfAAG3DAIBRxwgAUcd
					AAFHHhcBRx+QAUcMAgGHHDABhx2QAYcegQGH
					HwABJxxAAScdAAEnHqYBJx+QAeccYAHnHWAB
					5x5LAecfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>phucnguyen.2411 - ALC662v3 for Lenovo ThinkCentre M92P SFF</string>
					<key>CodecID</key>
					<integer>283903586</integer>
					<key>ConfigData</key>
					<data>
					AZccEAGXHZEBlx6gAZcfkQFHHCABRx1AAUce
					IQFHHwEBVxwwAVcdQQFXHhEBVx8BAYccQAGH
					HZABhx6BAYcfAQG3HFABtx1AAbceIQG3HwIB
					5xxgAecdYAHnHksB5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>16</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC662 by aloha_cn for HP Compaq Elite 8000 SFF</string>
					<key>CodecID</key>
					<integer>283903586</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4hAUcfAQFXHCABVx0AAVce
					EwFXH5ABtxwwAbcdEAG3HiEBtx8BAYccQAGH
					HTABhx6hAYcfkAGXHFABlx0QAZcegQGXHwI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>17</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC662 by ryahpalma for MP67-DI/Esprimo Q900</string>
					<key>CodecID</key>
					<integer>283903586</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUEBRx4hAUcfAQGHHCABhx2RAYce
					gQGHHwEBpxw/AacdMQGnHoEBpx8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>18</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC662 for MSI X79A-GD65</string>
					<key>CodecID</key>
					<integer>283903586</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkQGHHCABhx2QAYce
					oAGHH5EBpxwwAacdMAGnHoEBpx+RAbccUAG3
					HUABtx4hAbcfAgGXHEABlx2QAZceoQGXHwI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>19</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC663</string>
					<key>CodecID</key>
					<integer>283903587</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAFHHCABRx0AAUce
					EwFHH5ACFxwwAhcdEAIXHiECFx8CAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC663_V2</string>
					<key>CodecID</key>
					<integer>283903587</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4TAUcfkAGHHCABhx0QAYce
					gQGHHwIBlxwwAZcdAAGXHqABlx+QAdccQAHX
					HYAB1x4FAdcfQAHnHFAB5x0QAeceRQHnHwAC
					FxxgAhcdEAIXHiECFx8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>4</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - ALC663 for Fujitsu Celsius r670</string>
					<key>CodecID</key>
					<integer>283903587</integer>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4BIUcfASF3HCAhdx0AIXce
					EyF3H5AhFxwwIRcdYCEXHkQhFx8BIeccQCHn
					HRAh5x5WIecfECGHHFAhhx0AIYceoCGHH5Ah
					lxxgIZcdACGXHqAhlx+QIacccCGnHTAhpx6B
					IacfASIXHJAiFx1AIhceISIXHwE=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC663</string>
					<key>CodecID</key>
					<integer>283903587</integer>
					<key>Comment</key>
					<string>Custom ALC663 for Asus N56/76 by m-dudarev</string>
					<key>ConfigData</key>
					<data>
					AZccEAGXHQABlx6gAZcfkAGHHCABhx0QAYce
					gQGHHwIBRxwwAUcdAAFHHhABRx+QAUcMAgIX
					HEACFx0QAhceIQIXHwIBFxzwARcdAAEXHgAB
					Fx9AAecc8AHnHQAB5x4AAecfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC663</string>
					<key>CodecID</key>
					<integer>283903587</integer>
					<key>Comment</key>
					<string>Custom by alex1960 for ASUS N71J</string>
					<key>ConfigData</key>
					<data>
					AUccAAFHHQEBRx4TAUcfmQA3HBAANx0AADce
					VgA3HxgCFxwgAhcdQAIXHiECFx8BAbccMAG3
					HUABtx4hAbcfAQHnHEAB5x0BAeceQwHnH5kB
					hxxQAYcdCQGHHqMBhx+ZAZccYAGXHZwBlx6B
					AZcfAQF3HPABdx0BAXceEwF3H5k=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC665</string>
					<key>CodecID</key>
					<integer>283903589</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfmQGnHCABpx0QAace
					gQGnH5MBVxxAAVcdAQFXHhMBVx+ZAZccUAGX
					HRABlx4hAZcfAwG3HGABtx0QAbceIQG3HwMB
					5xxwAecdEAHnHkUB5x8D
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC665</string>
					<key>CodecID</key>
					<integer>283903589</integer>
					<key>ConfigData</key>
					<data>
					ASccUAEnHQABJx6gAScfkAFXHBABVx0AAVce
					EwFXH5ABVwwCAZccIAGXHRABlx4hAZcfAAGn
					HEABpx0QAacegQGnHwABtxxgAbcdEAG3HiEB
					tx8AAdcc8AHXHQAB1x6DAdcfUA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283903592</integer>
					<key>Comment</key>
					<string>ALC668 Mirone Laptop Patch</string>
					<key>ConfigData</key>
					<data>
					ABJxwQAScdAAEnHqABJx+QAUccIAFHHQABRx
					4XAUcfkAFXHDABVx0QAVceIQFXHwEBZxxAAW
					cdAAFnHgABZx9AAbccUAG3HRABtx6BAbcfAg
					HXHGAB1x0AAdcewAHXH0ABRwwA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283903592</integer>
					<key>Comment</key>
					<string>Custom ALC668 by lazzy for laptop ASUS G551JM</string>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQABJx6gAScfkAFHHBABRx0AAUce
					FwFHH5ABVxwgAVcdEAFXHiEBVx8AAbccQAG3
					HRABtx6BAbcfAAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>20</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283903592</integer>
					<key>Comment</key>
					<string>ALC668 syscl Laptop Patch (DELL Precision M3800)</string>
					<key>ConfigData</key>
					<data>
					ASccAQEnHQEBJx6gAScfkAFHHAIBRx0BAUce
					FwFHH5ABRwwCAVccAwFXHRABVx4rAVcfAwFX
					DAIBZxzwAWcdAAFnHgABZx9AAYcc8AGHHQAB
					hx4AAYcfQAGXHPABlx0AAZceAAGXH0ABpxzw
					AacdAAGnHgABpx9AAbccBAG3HRABtx6LAbcf
					AwHXHPAB1x0AAdceAAHXH0AB5xzwAecdAAHn
					HgAB5x9AAfcc8AH3HQAB9x4AAfcfQAIFAAEC
					BL6+AgUAAgIEqqoCBQADAgQAAAIFAAQCBAGA
					AgUABgIEAAACBQAHAgQPgAIFAAgCBAAxAgUA
					CgIEAGACBQALAgQAAAIFAAwCBHz3AgUADQIE
					EIACBQAOAgR/fwIFAA8CBMzMAgUAEAIE3cwC
					BQARAgQAAQIFABMCBAAAAgUAFAIEKqACBQAX
					AgSpQAIFABkCBAAAAgUAGgIEAAACBQAbAgQA
					AAIFABwCBAAAAgUAHQIEAAACBQAeAgR0GAIF
					AB8CBAgEAgUAIAIEQgACBQAhAgQEaAIFACIC
					BIzMAgUAIwIEAlACBQAkAgR0GAIFACcCBAAA
					AgUAKAIEjMwCBQAqAgT/AAIFACsCBIAAAgUA
					pwIE/wACBQCoAgSAAAIFAKoCBC4XAgUAqwIE
					oMACBQCsAgQAAAIFAK0CBAAAAgUArgIEKsYC
					BQCvAgSkgAIFALACBAAAAgUAsQIEAAACBQCy
					AgQAAAIFALMCBAAAAgUAtAIEAAACBQC1AgQQ
					QAIFALYCBNaXAgUAtwIEkCsCBQC4AgTWlwIF
					ALkCBJArAgUAugIEuLoCBQC7AgSqqwIFALwC
					BKqvAgUAvQIEaqoCBQC+AgQcAgIFAMACBAD/
					AgUAwQIED6Y=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>27</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283903592</integer>
					<key>Comment</key>
					<string>ALC668 Mirone Laptop Patch (Asus N750Jk)</string>
					<key>ConfigData</key>
					<data>
					ABJxwQAScdAAEnHqABJx+QAUccIAFHHQABRx
					4XAUcfkAFXHDABVx0QAVceIQFXHwEBZxxAAW
					cdAAFnHgABZx9AAbccUAG3HRABtx6BAbcfAg
					HXHGAB1x0AAdcewAHXH0ABRwwA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283903592</integer>
					<key>Comment</key>
					<string>ALC668 Custom (Asus N750JV)</string>
					<key>ConfigData</key>
					<data>
					ASccAAEnHQEBJx6mAScfkAFHHBABRx0BAUce
					FwFHH5ABVxwfAVcdEAFXHiEBVx8DAWcc8AFn
					HQABZx4AAWcfQAGHHPABhx0AAYceAAGHH0AB
					lxzwAZcdAAGXHgABlx9AAacc8AGnHQABpx4A
					AacfQAG3HDABtx0QAbcegQG3HwMB1xzwAdcd
					AAHXHgAB1x9AAecc8AHnHQAB5x4AAecfQAH3
					HPAB9x0AAfceAAH3H0ABRwwCAVcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>29</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283903600</integer>
					<key>Comment</key>
					<string>Custom ALC670 by Alex Auditore</string>
					<key>ConfigData</key>
					<data>
					AbccQAG3HRABtx4rAbcfAQFXHDABVx0BAVce
					EwFXH5ABJxwQAScdAQEnHqABJx+QAaccUAGn
					HTEBpx6BAacfAQGXHCABlx2QAZcegQGXHwEB
					5xxgAecdEQHnHksB5x8BARcc8AEXHQABFx4A
					ARcfQAE3HPABNx0AATceAAE3H0ABRxzwAUcd
					AAFHHgABRx9AAWcc8AFnHQABZx4AAWcfQAF3
					HPABdx0AAXceAAF3H0ABhxzwAYcdAAGHHgAB
					hx9AAdcc8AHXHQAB1x4AAdcfQAIXHPACFx0A
					AhceAAIXH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>MacPeet - ALC671 for Fujitsu-Siemens D3433-S (Q170 chip)</string>
					<key>ConfigData</key>
					<data>
					AYccIAGHHTABhx6BAYcfAQIXHDACFx1AAhce
					AQIXHwECFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>MacPeet - ALC671 for Fujitsu  Esprimo C720</string>
					<key>ConfigData</key>
					<data>
					IXccECF3HQAhdx4TIXcfkCFHHCAhRx0QIUce
					ISFHHwIhRwwCIhccMCIXHUAiFx4RIhcfkSIX
					DAIhlxxQIZcdECGXHoEhlx8CIYccYCGHHTAh
					hx6BIYcfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					IhcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>Sisumara - ALC671 for Fujitsu Q558</string>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQEBdx4TAXcfmQF3DAIBRxwgAUcd
					EAFHHiEBRx8CAUcMAgIXHDACFx0QAhceAQIX
					HwECFwwCAZccUAGXHRABlx6BAZcfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>16</integer>
					<key>WakeConfigData</key>
					<data>
					IhcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string> alc671 for HP 280 Pro G4  by Lcp</string>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQEBdx4XAXcfmQF3DAIBRxwgAUcd
					QQFHHgEBRx8BAUcMAgIXHDACFx0QAhceIQIX
					HwICFwwCAbccQAG3HTABtx6BAbcfAQGHHFAB
					hx2QAYceoQGHHwE=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>88</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>osy86 - Realtek ALC700</string>
					<key>CodecID</key>
					<integer>283903744</integer>
					<key>ConfigData</key>
					<data>
					AZceYQGXByUBtx4R
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AZcHJQ==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Baio77 - Realtek ALC700</string>
					<key>CodecID</key>
					<integer>283903744</integer>
					<key>ConfigData</key>
					<data>
					AbccEAG3HRABtx4RAbcfAQG3DAICFxwvAhcd
					EAIXHgECFx8CAhcMAgEnHDABJx0BAScepgEn
					H5ABNxxAATcdBQE3HqYBNx+QAZccUAGXHRAB
					lx6BAZcfAgHnHGAB5x0RAeceQQHnHwE=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>22</integer>
					<key>WakeConfigData</key>
					<data>
					AbcMAgGXByACFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC882</string>
					<key>CodecID</key>
					<integer>283904130</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQGHHFABhx2QAYce
					oAGHH5ABlxxgAZcdkAGXHoEBlx8CAacccAGn
					HTABpx6BAacfAQG3HIABtx1AAbceIQG3HwEB
					5xyQAecd4AHnHkUB5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC882</string>
					<key>CodecID</key>
					<integer>283904130</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFXHCABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAXccQAF3
					HSABdx4BAXcfAQGHHFABhx2QAYceoAGHH5AB
					lxxgAZcdkAGXHoEBlx8CAacccAGnHTABpx6B
					AacfAQG3HIABtx1AAbceIQG3HwIB5xyQAecd
					YAHnHkUB5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC883</string>
					<key>CodecID</key>
					<integer>283904131</integer>
					<key>Comment</key>
					<string>Mirone - Realtek ALC883 by Andrey1970</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFXHCABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAXccQAF3
					HSABdx4BAXcfAQGHHFABhx2QAYceoAGHH5AB
					lxxgAZcdkAGXHoEBlx8CAacccAGnHTABpx6B
					AacfAQG3HIABtx1AAbceIQG3HwIB5xyQAecd
					YAHnHkUB5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC883</string>
					<key>CodecID</key>
					<integer>283904131</integer>
					<key>Comment</key>
					<string>Realtek ALC883 for Atermiter X79G by SamCabral</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFXHCABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAXccQAF3
					HSABdx4BAXcfAQGHHFABhx2QAYceoAGHH5AB
					lxxgAZcdkAGXHoEBlx8CAacccAGnHTABpx6B
					AacfAQG3HIABtx1AAbceIQG3HwIB5xyQAecd
					YAHnHkUB5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>20</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904133</integer>
					<key>Comment</key>
					<string>toleda ALC885</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfASFXHCAhVx0QIVce
					ASFXHwEhZxwwIWcdYCFnHgEhZx8BIXcc8CF3
					HQAhdx4AIXcfQCGHHEAhhx2QIYceoCGHH5Ah
					lxxgIZcdkCGXHoEhlx8CIaccUCGnHTAhpx6B
					IacfASG3HHAhtx1AIbceISG3HwIh5xyQIecd
					YSHnHksh5x8BIfccoCH3HQEh9x7LIfcfASEX
					HPAhFx0AIRceACEXH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC885</string>
					<key>CodecID</key>
					<integer>283904133</integer>
					<key>Comment</key>
					<string>Custom ALC885 by alex1960</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfASFXHCAhVx0QIVce
					ASFXHwEhZxwwIWcdYCFnHgEhZx8BIXcc8CF3
					HQAhdx4AIXcfQCGHHEAhhx2QIYceoCGHH5Ah
					lxxgIZcdkCGXHoEhlx8CIaccUCGnHTAhpx6B
					IacfASG3HHAhtx1AIbceISG3HwIh5xyQIecd
					YSHnHksh5x8BIfccoCH3HQEh9x7LIfcfASEX
					HPAhFx0AIRceACEXH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC885</string>
					<key>CodecID</key>
					<integer>283904133</integer>
					<key>Comment</key>
					<string>MacPeet - ALC885 for GA-G33M-DS2R</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkSFXHCAhVx0QIVce
					ASFXHwEhZxwwIWcdYCFnHgEhZx8BIYccQCGH
					HZAhhx6gIYcfkCGnHFAhpx0wIacegSGnHwEh
					lxxgIZcdkCGXHoEhlx8CIbcccCG3HUAhtx4h
					IbcfAiHnHJAh5x1hIeceSyHnHwEh9xygIfcd
					ASH3Hssh9x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>Comment</key>
					<string>Toleda ALC887</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFHDAIhVxwgIVcd
					ECFXHgEhVx8BIWccMCFnHWAhZx4BIWcfASF3
					HPAhdx0AIXceACF3H0AhhxxAIYcdkCGHHqAh
					hx+QIZccYCGXHZAhlx6BIZcfAiGnHFAhpx0w
					IacegSGnHwEhtxxwIbcdQCG3HiEhtx8CIbcM
					AiHnHJAh5x1hIeceSyHnHwEh9xzwIfcdACH3
					HgAh9x9AIRcc8CEXHQAhFx4AIRcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>1</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>Comment</key>
					<string>Toleda ALC887</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFHDAIhVxzwIVcd
					ACFXHgAhVx9AIWcc8CFnHQAhZx4AIWcfQCF3
					HPAhdx0AIXceACF3H0AhhxxAIYcdYCGHHgEh
					hx8BIZccYCGXHZAhlx6gIZcfkCGnHFAhpx0Q
					IaceASGnHwEhtxxwIbcdQCG3HiEhtx8CIbcM
					AiHnHJAh5x1hIeceSyHnHwEh9xzwIfcdACH3
					HgAh9x9AIRcc8CEXHQAhFx4AIRcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>2</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>Comment</key>
					<string>Toleda ALC887</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFHDAIhVxwgIVcd
					ECFXHgEhVx9AIWcc8CFnHQAhZx4AIWcfQCF3
					HPAhdx0AIXceACF3H0AhhxxAIYcdkCGHHqAh
					hx+QIZccYCGXHZAhlx6BIZcfAiGnHFAhpx0w
					IacegSGnHwEhtxxwIbcdQCG3HiEhtx8CIbcM
					AiHnHJAh5x1hIeceSyHnHwEh9xzwIfcdACH3
					HgAh9x9AIRcc8CEXHQAhFx4AIRcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC887-VD</string>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQGHHFABhx2QAYce
					oAGHH5ABlxxgAZcdkAGXHoEBlx8CAacccAGn
					HTABpx6BAacfAQG3HIABtx1AAbceIQG3HwIB
					5xyQAecdYAHnHkUB5x8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC887-VD</string>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFXHCABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAXccQAF3
					HSABdx4BAXcfAQGHHFABhx2QAYceoAGHH5AB
					lxxgAZcdkAGXHoEBlx8CAacccAGnHTABpx6B
					AacfAQG3HIABtx1AAbceIQG3HwIB5xyQAecd
					YAHnHkUB5x8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC887-VD</string>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkQFHDAIBtxwgAbcd
					QAG3HiEBtx8CAbcMAgGHHDABhx2QAYceoQGH
					H5EBlxxAAZcdkQGXHoEBlx+SAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>Comment</key>
					<string>VictorXu - ALC887-VD for ASUS H81M-D</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkQFHDAIBtxwgAbcd
					QAG3HiEBtx8CAbcMAgGHHEABhx2QAYceoQGH
					H5EBpxxPAacdMAGnHoEBpx8BAZccUAGXHZAB
					lx6BAZcfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC887-VD</string>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>ConfigData</key>
					<data>
					AbccAAG3HUABtx4hAbcfAQGHHBABhx2QAYce
					oAGHH5EBlxwgAZcdkAGXHoEBlx8BAUccMAFH
					HUABRx4RAUcfkQGnHEABpx0wAacegQGnHwEB
					5xxQAecdYQHnHksB5x8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC887-VD</string>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFXHCAhVx0QIVce
					ASFXHwEhhxxAIYcdkCGHHqAhhx+QIaccUCGn
					HTAhpx6BIacfASGXHGAhlx2QIZcegSGXHwIh
					txxwIbcdQCG3HiEhtx8CIecckCHnHWEh5x5L
					IecfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>17</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC887-VD</string>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkSFHDAIhhxxAIYcd
					YCGHHgEhhx8BIaccUCGnHRAhpx4BIacfASGX
					HGAhlx2QIZceoSGXH5EhtxxwIbcdQCG3HiEh
					tx8CIecckCHnHWEh5x5LIecfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>18</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC887-VD AD0 for Asus Z97M-PLUS/BR by maiconjs</string>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>ConfigData</key>
					<data>
					AUccAAFHHUABRx4BAUcfAQFnHBABZx1gAWce
					AQFnHwEBVxwgAVcdEAFXHgEBVx8BAXccMAF3
					HSABdx4BAXcfAQG3HEABtx1AAbceIQG3HwIB
					FxxQARcdAQEXHkYBFx+ZAYccYAGHHZABhx6g
					AYcfkAGnHHABpx0wAacegQGnHwEBlxyAAZcd
					kAGXHoEBlx8CAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>20</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>Comment</key>
					<string>Custom by klblk ALC887 for GA-Q87TN</string>
					<key>ConfigData</key>
					<data>
					IRcc8CEXHQAhFx4AIRcfQCEnHPAhJx0AISce
					ACEnH0AhRxzwIUcdACFHHgAhRx9AIVcc8CFX
					HQAhVx4AIVcfQCFnHPAhZx0AIWceACFnH0Ah
					dxzwIXcdACF3HgAhdx9AIYcccCGHHZAhhx6B
					IYcfASGXHPAhlx0AIZceACGXH0AhpxwgIacd
					QCGnHgEhpx8BIbcc8CG3HQAhtx4AIbcfQCHH
					HPAhxx0AIcceACHHH0Ah1xzwIdcdACHXHgAh
					1x9AIecc8CHnHQAh5x4AIecfQCH3HPAh9x0A
					IfceACH3H0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>33</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC887-VD for Asus B85-ME by maiconjs</string>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>ConfigData</key>
					<data>
					AUccAAFHHUABRx4BAUcfAQG3HBABtx1AAbce
					IQG3HwIBhxwgAYcdkAGHHqABhx+QAaccMAGn
					HTABpx6BAacfAQGXHEABlx2QAZcegQGXHwIB
					RwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>40</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>Comment</key>
					<string>0th3r ALC887 for PRIME B250-PLUS</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4BAUcfAQG3HCABtx1BAbce
					IQG3HwIBhxxAAYcdkAGHHoEBhx8BAZccUAGX
					HZEBlx6BAZcfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>50</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>Comment</key>
					<string>ALC887 for Asus PRIME Z270-P (full Rear and Front, non auto-switch) by ctich</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4BAUcfAQFHDAIBtxwgAbcd
					QAG3HiEBtx8CAbcMAgEXHDABFx0BARceRgEX
					H5ABhxxAAYcdkAGHHqABhx+RAaccTwGnHTAB
					px6BAacfAQGXHFABlx2RAZcegQGXHwI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>52</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>Comment</key>
					<string>ALC887 for Asus PRIME Z270-P (Rear LineOut1, Mic - LineOut2, LineIn - LineOut3 - 5.1 and Front, non auto-switch) by ctich</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4BAUcfAQFHDAIBhxwQAYcd
					YAGHHgEBhx8BAaccEAGnHRABpx4BAacfAQG3
					HCABtx1AAbceIQG3HwIBtwwCARccMAEXHQEB
					Fx5GARcfkAGXHFABlx2RAZcegQGXHwI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>53</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC887-VD GA-Z97 HD3 ver2.1 by varrtix</string>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>ConfigData</key>
					<data>
					IRccYCEXHQEhFx5DIRcfmSEnHPAhJx0AISce
					ACEnH0AhRxyAIUcdQCFHHhEhRx8BIUcMAiFX
					HIIhVx0QIVceASFXHwEhZxyBIWcdYCFnHgEh
					Zx8BIXccgiF3HSAhdx4BIXcfASGHHHAhhx2Q
					IYceoSGHHwEhlxxwIZcdkCGXHqEhlx8CIacc
					ICGnHTAhpx6BIacfASG3HFAhtx1AIbceISG3
					HwIhtwwCIccc8CHHHQAhxx4AIccfQCHXHPAh
					1x0AIdceACHXH0Ah5xzwIecdACHnHgAh5x9A
					Ifcc8CH3HQAh9x4AIfcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>87</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom Realtek ALC887-VD by Constanta</string>
					<key>CodecID</key>
					<integer>283904135</integer>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfASGHHFAhhx2QIYce
					oCGHH5AhlxxgIZcdkCGXHoEhlx8CIacccCGn
					HTAhpx6BIacfASG3HIAhtx1AIbceISG3HwIh
					5xyQIecdYCHnHkUh5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904136</integer>
					<key>Comment</key>
					<string>toleda ALC888</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFXHCAhVx0QIVce
					ASFXHwEhZxwwIWcdYCFnHgEhZx8BIXcc8CF3
					HQAhdx4AIXcfQCGHHEAhhx2QIYceoCGHH5Ah
					lxxgIZcdkCGXHoEhlx8CIaccUCGnHTAhpx6B
					IacfASG3HHAhtx1AIbceISG3HwIh5xyQIecd
					YSHnHksh5x8BIfcc8CH3HQAh9x4AIfcfQCEX
					HPAhFx0AIRceACEXH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904136</integer>
					<key>Comment</key>
					<string>toleda ALC888</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFXHPAhVx0AIVce
					ACFXH0AhZxzwIWcdACFnHgAhZx9AIXcc8CF3
					HQAhdx4AIXcfQCGHHEAhhx1gIYceASGHHwEh
					lxxgIZcdkCGXHqAhlx+QIaccUCGnHRAhpx4B
					IacfASG3HHAhtx1AIbceISG3HwIh5xyQIecd
					YSHnHksh5x8BIfcc8CH3HQAh9x4AIfcfQCEX
					HPAhFx0AIRceACEXH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>2</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904136</integer>
					<key>Comment</key>
					<string>toleda ALC888</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFXHCAhVx0QIVce
					ASFXHwEhZxzwIWcdACFnHgAhZx9AIXcc8CF3
					HQAhdx4AIXcfQCGHHEAhhx2QIYceoCGHH5Ah
					lxxgIZcdkCGXHoEhlx8CIaccUCGnHTAhpx6B
					IacfASG3HHAhtx1AIbceISG3HwIh5xyQIecd
					YSHnHksh5x8BIfcc8CH3HQAh9x4AIfcfQCEX
					HPAhFx0AIRceACEXH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC888 for Laptop</string>
					<key>CodecID</key>
					<integer>283904136</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfmQFHHCABRx1AAUce
					IQFHHwEBtxwwAbcdAQG3HhMBtx+ZAXccQAF3
					HSABdx4BAXcfAQGHHFABhx2QAYcegQGHHwEB
					pxxgAacdMAGnHoEBpx8BAecccAHnHUAB5x5F
					AecfAQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>4</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC888 3 ports (Pink, Green, Blue)</string>
					<key>CodecID</key>
					<integer>283904136</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQGHHFABhx2QAYce
					oAGHH5ABlxxgAZcdkAGXHoEBlx8CAacccAGn
					HTABpx6BAacfAQG3HIABtx1AAbceIQG3HwEB
					5xyQAecd4AHnHkUB5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC888 5/6 ports (Gray, Black, Orange, Pink, Green, Blue)</string>
					<key>CodecID</key>
					<integer>283904136</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFXHCABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAXccQAF3
					HSABdx4BAXcfAQGHHFABhx2QAYceoAGHH5AB
					lxxgAZcdkAGXHoEBlx8CAacccAGnHTABpx6B
					AacfAQG3HIABtx1AAbceIQG3HwIB5xyQAecd
					YAHnHkUB5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC888S-VD Version1 for MedionP9614 by MacPeet</string>
					<key>CodecID</key>
					<integer>283904136</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHRABRx4hAUcfAQFHDAIBhxwwAYcd
					EAGHHqEBhx8BASccQAEnHQABJx6jAScfkAF3
					HFABdx0AAXceEwF3H5ABpxxgAacdEAGnHoEB
					px8BAecccAHnHRAB5x5FAecfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC888 for Acer Aspire 7738G by MacPeet</string>
					<key>CodecID</key>
					<integer>283904136</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4TAUcfkAFHDAIBJxxAAScd
					AAEnHqMBJx+QAVccUAFXHRABVx4hAVcfAAFX
					DAIBpxxgAacdMAGnHoEBpx8AAecccAHnHRAB
					5x5FAecfAA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>27</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC888S-VD Version2 for MedionE7216 by MacPeet</string>
					<key>CodecID</key>
					<integer>283904136</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4TAUcfkAFHDAIBdxwgAXcd
					AAF3HhMBdx+QAeccMAHnHRAB5x5EAecfAAGH
					HEABhx0QAYceoQGHHwABJxxQAScdAAEnHqMB
					Jx+QAaccYAGnHRABpx6BAacfAAG3HHABtx0Q
					AbceIQG3HwA=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC888S-VD Version3 for MedionP8610 by MacPeet</string>
					<key>CodecID</key>
					<integer>283904136</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4TAUcfkAFHDAIBdxwgAXcd
					EAF3HhMBdx+QAeccMAHnHRAB5x5FAecfAAGX
					HEABlx0AAZceowGXH5ABhxxQAYcdEAGHHoEB
					hx8AAVccYAFXHRABVx4hAVcfAAFXDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>29</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904137</integer>
					<key>Comment</key>
					<string>ALC889, Toleda</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFXHCAhVx0QIVce
					ASFXHwEhZxwwIWcdYCFnHgEhZx8BIXcc8CF3
					HQAhdx4AIXcfQCGHHEAhhx2QIYceoCGHH5Ah
					lxxgIZcdkCGXHoEhlx8CIaccUCGnHTAhpx6B
					IacfASG3HHAhtx1AIbceISG3HwIh5xyQIecd
					YSHnHksh5x8BIfcc8CH3HQAh9x4AIfcfQCEX
					HPAhFx0AIRceACEXH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904137</integer>
					<key>Comment</key>
					<string>ALC889, Toleda</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFXHCAhVx0QIVce
					ASFXHwEhZxwwIWcdYCFnHgEhZx8BIXcc8CF3
					HQAhdx4AIXcfQCGHHEAhhx2QIYceoCGHH5Ah
					lxxgIZcdkCGXHoEhlx8CIaccUCGnHTAhpx6B
					IacfASG3HHAhtx1AIbceISG3HwIh5xyQIecd
					YSHnHksh5x8BIfcc8CH3HQAh9x4AIfcfQCEX
					HPAhFx0AIRceACEXH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>2</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904137</integer>
					<key>Comment</key>
					<string>ALC889, Toleda</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFXHCAhVx0QIVce
					ASFXHwEhZxwwIWcdYCFnHgEhZx8BIXcc8CF3
					ASFXHwEhZxzwIWcdACFnHgAhZx9AIXcc8CF3
					lxxgIZcdkCGXHoEhlx8CIaccUCGnHTAhpx6B
					IacfASG3HHAhtx1AIbceISG3HwIh5xyQIecd
					YSHnHksh5x8BIfcc8CH3HQAh9x4AIfcfQCEX
					HPAhFx0AIRceACEXH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904137</integer>
					<key>Comment</key>
					<string>MacPeet ALC889 Medion P4020 D</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4hAUcfAQFHDAIBtxwgAbcd
					AAG3HhMBtx+QAeccMAHnHWAB5x5EAecfAQGX
					HFABlx0AAZceowGXH5ABpxxgAacdMAGnHoEB
					px8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904137</integer>
					<key>Comment</key>
					<string>alc889, Custom by Sergey_Galan</string>
					<key>ConfigData</key>
					<data>
					IRcc8CEXHQAhFx4AIRcfQCEnHPAhJx0AISce
					ACEnH0AhRxwwIUcdQSFHHhEhRx8BIVcc8CFX
					HQAhVx4AIVcfQCFnHPAhZx0AIWceACFnH0Ah
					dxzwIXcdACF3HgAhdx9AIYccECGHHZEhhx6g
					IYcfkCGXHCAhlx2QIZcegSGXHwEhpxzwIacd
					ACGnHgAhpx9AIbccgCG3HUAhtx4hIbcfASHH
					HPAhxx0AIcceACHHH0Ah1xzwIdcdACHXHgAh
					1x9AIecckCHnHSEh5x5LIecfASH3HPAh9x0A
					IfceACH3H0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - ALC891 for HP Pavilion Power 580-030ng</string>
					<key>CodecID</key>
					<integer>283904103</integer>
					<key>ConfigData</key>
					<data>
					AXccIAF3HRABdx4hAXcfAgGHHDABhx2QAYce
					gQGHHwEBtxxAAbcdMAG3HoEBtx8BAhccYAIX
					HQACFx4RAhcfAAIXDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AhcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC891</string>
					<key>CodecID</key>
					<integer>283904103</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HUABdx4hAXcfAQFnHDABZx0wAWce
					gQFnHwEBhxxAAYcdkAGHHqEBhx+RAaccYAGn
					HZABpx6BAacfAgHnHHAB5x0AAeceRgHnH5AC
					FxyAAhcdQAIXHhECFx8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>ALC892, Toleda</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFHDAIhVxwgIVcd
					ECFXHgEhVx8BIWccMCFnHWAhZx4BIWcfASF3
					HPAhdx0AIXceACF3H0AhhxxAIYcdkCGHHqAh
					hx+QIZccYCGXHZAhlx6BIZcfAiGnHFAhpx0w
					IacegSGnHwEhtxxwIbcdQCG3HiEhtx8CIbcM
					AiHnHJAh5x1hIeceSyHnHwEh9xzwIfcdACH3
					HgAh9x9AIRcc8CEXHQAhFx4AIRcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>1</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>ALC892, Toleda</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFHDAIhVxzwIVcd
					ACFXHgAhVx9AIWcc8CFnHQAhZx4AIWcfQCF3
					HPAhdx0AIXceACF3H0AhhxxAIYcdYCGHHgEh
					hx8BIZccYCGXHZAhlx6gIZcfkCGnHFAhpx0Q
					IaceASGnHwEhtxxwIbcdQCG3HiEhtx8CIbcM
					AiHnHJAh5x1hIeceSyHnHwEh9xzwIfcdACH3
					HgAh9x9AIRcc8CEXHQAhFx4AIRcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>2</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>ALC892, Toleda</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFHDAIhVxwgIVcd
					ECFXHgEhVx9AIWcc8CFnHQAhZx4AIWcfQCF3
					HPAhdx0AIXceACF3H0AhhxxAIYcdkCGHHqAh
					hx+QIZccYCGXHZAhlx6BIZcfAiGnHFAhpx0w
					IacegSGnHwEhtxxwIbcdQCG3HiEhtx8CIbcM
					AiHnHJAh5x1hIeceSyHnHwEh9xzwIfcdACH3
					HgAh9x9AIRcc8CEXHQAhFx4AIRcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC892 for Laptop</string>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHZABJx6gAScfmQFHHCABRx1AAUce
					IQFHHwEBdxwwAXcdEAF3HgEBdx8BAYccQAGH
					HZABhx6BAYcfAQGnHFABpx0wAacegQGnHwEB
					txxgAbcdQAG3HhMBtx+ZAecccAHnHWAB5x5F
					AecfAQG3DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>4</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>ALC892, Mirone</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQGHHFABhx2QAYce
					oAGHH5ABlxxgAZcdkAGXHoEBlx8CAacccAGn
					HTABpx6BAacfAQG3HIABtx1AAbceIQG3HwEB
					5xyQAecd4AHnHkUB5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>ALC892, Mirone</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFXHCABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAXccQAF3
					HSABdx4BAXcfAQGHHFABhx2QAYceoAGHH5AB
					lxxgAZcdkAGXHoEBlx8CAacccAGnHTABpx6B
					AacfAQG3HIABtx1AAbceIQG3HwIB5xyQAecd
					YAHnHkUB5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AQAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>ALC892 for MSI GF72-8RE</string>
					<key>ConfigData</key>
					<data>
					AYcegQG3DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AbcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>MSI GP70/CR70 by Slava77</string>
					<key>ConfigData</key>
					<data>
					AbceEQGXHqABlx+RAYcegQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>MacPeet - alc892 for MSi Z97S SLI Krait Edition</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkQG3HCABRwwCAbcd
					QAG3HiEBtx8CAbcMAgGHHDABhx2QAYceoQGH
					H5EBVxxQAVcdEAFXHgEBVx8BAWccYAFnHWAB
					Zx4BAWcfAQF3HHABdx0gAXceAQF3HwEBlxyA
					AZcdkAGXHoEBlx8CAacckAGnHTABpx6BAacf
					AQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>MacPeet - alc892 for MSI GL73-8RD</string>
					<key>ConfigData</key>
					<data>
					AUccIAFHHXABRx4hAUcfAAFHDAIBVxwwAVcd
					AAFXHhcBVx+QAeccQAHnHXAB5x5FAecfAAEn
					HFABJx0AAScepgEnH5ABhxxgAYcdcAGHHoEB
					hx8AAXcccAF3HQABdx4XAXcfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>16</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>MacPeet - alc892 for MSI B150M MORTAR - SwitchMode</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFHDAIBtxwgAbcd
					QAG3HiEBtx8CAbcMAgFXHDABVx0QAVceAQFX
					HwEBlxxQAZcdkAGXHoEBlx8CAWcccAFnHWAB
					Zx4BAWcfAQGHHIABhx2QAYceoAGHH5ABpxyQ
					AacdMAGnHoEBpx8BAeccsAHnHRAB5x5FAecf
					AQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>17</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>MacPeet - alc892 for MSI B150M MORTAR - ManualMode</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4BAUcfAQFHDAIBtxwgAbcd
					QAG3HiEBtx8CAbcMAgFXHDABVx0QAVceAQFX
					HwEBlxxQAZcdkAGXHoEBlx8CAWcccAFnHWAB
					Zx4BAWcfAQGHHIABhx2QAYceoAGHH5ABpxyQ
					AacdMAGnHoEBpx8BAeccsAHnHRAB5x5FAecf
					AQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>18</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>Custom ALC892 for GIGABYTE Z390M GAMING - Manual - by Bokey</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFHDAIBtxwgAbcd
					QAG3HiEBtx8CAbcMAgFnHDABZx1gAWceAQFn
					HwEBVxxAAVcdEAFXHgEBVx8BAXccUAF3HSAB
					dx4BAXcfAQGHHHABhx2QAYceoQGHH5ABlxyA
					AZcdkAGXHoEBlx8CARcckAEXHQABFx5DARcf
					kAGnHGABpx0wAacegQGnHwEB1xzwAdcdAAHX
					HgAB1x9AAScc8AEnHQABJx4AAScfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>20</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>ASRock Z390m-ITX/ac by imEgo</string>
					<key>ConfigData</key>
					<data>
					ARcc8AEXHQABFx4AARcfQAEnHPABJx0AASce
					AAEnH0ABRxwQAUcdQAFHHhABRx+QAUcMAgFX
					HPABVx0AAVceAAFXH0ABZxzwAWcdAAFnHgAB
					Zx9AAXcc8AF3HQABdx4AAXcfQAGHHDABhx2Q
					AYceoAGHH5ABlxxAAZcdkAGXHoEBlx8CAacc
					UAGnHTABpx6BAacfAQG3HCABtx1AAbceIQG3
					HwIBtwwCAccc8AHHHQABxx4AAccfQAHXHPAB
					1x0AAdceAAHXH0AB5xzwAecdAAHnHgAB5x9A
					Afcc8AH3HQAB9x4AAfcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>22</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>ALC892 for ASRock B365 Pro4 By TheHackGuy</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4BAUcfAQFHDAIBhxwgAYcd
					kAGHHqABhx+BAaccMAGnHTABpx6BAacfAQGX
					HEABlx2QAZcegQGXHwIBtxxQAbcdQAG3HiEB
					tx8CAbcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>23</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>ALC892 for Clevo P751DMG by Cryse Hillmes</string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHEABRx0BAUce
					FwFHH5ABdxxgAXcdEAF3HgEBdx8BAYccgAGH
					HRABhx6BAYcfAQGnHCABpx0QAacegQGnHwEB
					txxQAbcdEAG3HiEBtx8BAecccAHnHRAB5x5F
					AecfAQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>ALC892 for Clevo P65xSE/SA by Derek Zhu</string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHZEBJx6mAScfkAGHHCABhx1gAYce
					gQGHHwEBRxwwAUcdAQFHHhcBRx+QAbccQAG3
					HTABtx4hAbcfAQF3HFABdx1AAXceAQF3HwEB
					5xxgAecdYQHnHkUB5x8BALcccAC3HREAtx4W
					ALcfkAFXHPABVx0AAVceAAFXHwQBZxzwAWcd
					AAFnHgABZx8EAZcc8AGXHQABlx4AAZcfBAGn
					HPABpx0AAaceAAGnHwQBxxzwAccdAAHHHgAB
					xx8EAdcc8AHXHQAB1x4AAdcfBAH3HPAB9x0A
					AfceAAH3HwQBRwwCAbcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>31</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MSI GE60 2OC/2OE/2OD</string>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>ConfigData</key>
					<data>
					ARcc8AEXHQABFx4AARcfQAEnHPABJx0AASce
					AAEnH0ABRxwgAUcdEAFHHiEBRx8BAVcc8AFX
					HQABVx4AAVcfQAFnHPABZx0AAWceAAFnH0AB
					dxzwAXcdAAF3HgABdx9AAYccUAGHHRABhx6B
					AYcfAQGXHEABlx0BAZceowGXH5kBpxzwAacd
					AAGnHgABpx9AAbccEAG3HQEBtx4TAbcfmQHH
					HPABxx0AAcceAAHHH0AB1xzwAdcdAAHXHgAB
					1x9AAeccMAHnHRAB5x5FAecfAQH3HPAB9x0A
					AfceAAH3H0ABRwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>66</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>Custom ALC892 for GIGABYTE B360 M AORUS PRO</string>
					<key>ConfigData</key>
					<data>
					ARccMAEXHQEBFx5DARcfmQEnHPABJx0AASce
					AAEnH0ABRxxAAUcdQQFHHhEBRx+RAUcMAgFX
					HPABVx0AAVceAAFXH0ABZxzwAWcdAAFnHgAB
					Zx9AAXccgAF3HSABdx4BAXcfAQGHHBABhx2R
					AYceoQGHH5EBlxxyAZcdEAGXHqEBlx8CAacc
					8AGnHQABpx4AAacfQAG3HFIBtx0QAbceIQG3
					HwIBtwwCAccc8AHHHQABxx4AAccfQAHXHPAB
					1x0AAdceAAHXH0AB5xzwAecdAAHnHgAB5x9A
					Afcc8AH3HQAB9x4AAfcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>90</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>Custom ALC892 for GA-Z87-HD3 by BIM167</string>
					<key>ConfigData</key>
					<data>
					IRccUCEXHXEhFx5EIRcfASEnHPAhJx0AISce
					ACEnH0AhRxwQIUcdQCFHHhEhRx+QIVccICFX
					HRAhVx4BIVcfASFnHDAhZx1gIWceASFnHwEh
					dxzwIXcdACF3HgAhdx9AIYccYCGHHZAhhx6g
					IYcfkCGXHIAhlx2QIZcegSGXHwIhpxxwIacd
					MCGnHoEhpx8BIbccQCG3HUAhtx4hIbcfAiHH
					HPAhxx0AIcceACHHH0Ah5xzwIecdACHnHgAh
					5x9AIfcckCH3HXEh9x7EIfcfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>92</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>Custom ALC892 for HASEE K770e i7 D1 by gitawake</string>
					<key>ConfigData</key>
					<data>
					ARcc8AEXHQABFx4AARcfQAEnHFABJx0BASce
					pgEnH5ABRxwQAUcdAQFHHhcBRx+QAUcMAgFX
					HPABVx0AAVceAAFXH0ABZxzwAWcdAAFnHgAB
					Zx9AAXccIAF3HRABdx4BAXcfAQGHHGABhx0Q
					AYcegQGHHwEBlxzwAZcdAAGXHgABlx9AAacc
					UAGnHRABpx6BAacfAQG3HDABtx0QAbceIQG3
					HwEBtwwCAccc8AHHHQABxx4AAccfQAHXHPAB
					1x0AAdceAAHXH0AB5xzwAecdAAHnHgAB5x9A
					Afcc8AH3HQAB9x4AAfcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>97</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>ALC892 with working SPDIF</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQGHHFABhx2QAYce
					oAGHH5ABlxxgAZcdkAGXHoEBlx8CAacccAGn
					HTABpx6BAacfAQG3HIABtx1AAbceIQG3HwEB
					5xyQAecd4AHnHkUB5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>98</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>Custom ALC892 DNS P150EM by Constanta</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQGHHHABhx2QAYce
					gQGHHwEBlxxgAZcdAQGXHqABlx+QAaccgAGn
					HTABpx6BAacfAQG3HCABtx1AAbceIQG3HwEB
					5xyQAecd4AHnHkUB5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>GeorgeWan - ALC892 for MSI-Z370-A PRO</string>
					<key>ConfigData</key>
					<data>
					ARcc8AEXHQABFx4AARcfQAEnHPABJx0AASce
					AAEnH0ABRxwQAUcdQAFHHhEBRx+QAUcMAgFX
					HCABVx0QAVceAQFXHwEBZxwwAWcdYAFnHgEB
					Zx8BAXccQAF3HSABdx4BAXcfAQGHHFABhx2Q
					AYceoAGHH5ABlxxwAZcdkAGXHoEBlx8CAacc
					YAGnHTABpx6BAacfAQG3HIABtx1AAbceIQG3
					HwIBtwwCAccc8AHHHQABxx4AAccfQAHXHPAB
					1x0AAdceAAHXH0AB5xzwAecdAAHnHgAB5x9A
					Afcc8AH3HQAB9x4AAfcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>100</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>Custom ALC892 for G4/G5mod by ATL</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4BAUcfAQFHDAIBtxwgAbcd
					EAG3HhABtx+QAbcMAgGHHDABhx2QAYceoQGH
					HwEBVxxQAVcdEAFXHgEBVx8BAWccYAFnHQEB
					Zx7wAWcfcAF3HHABdx0gAXceAQF3HwEBlxyA
					AZcdkAGXHiEBlx8CAZcMAgGnHJABpx0wAace
					gQGnHwEB5xygAecdYQHnHkUB5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>32</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904146</integer>
					<key>Comment</key>
					<string>Custom ALC892 for GIGABYTE B365M AORUS ELITE</string>
					<key>ConfigData</key>
					<data>
					AUccQAFHHUEBRx4RAUcfkQFnHIEBZx1gAWce
					AQFnHwEBVxyCAVcdEAFXHgEBVx8BAXccgAF3
					HSABdx4BAXcfAQG3HDABtx1AAbceIQG3HwIB
					FxxgARcdAAEXHkMBFx+ZAYccEAGHHZEBhx6h
					AYcfkQGnHCABpx0wAacegQGnHwEBlxxwAZcd
					kAGXHoEBlx8CAUcMAgG3DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC897 for Chuwi-CoreBookX14 by weachy</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfmQG3HCABtx0AAbce
					IQG3HwEBtwwCASccMAEnHQEBJx6mAScfmQFH
					DAIBlxxAAZcdAQGXHoABlx+Q
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>23</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAIBlwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>OPS Computer ALC897 by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4hAUcfAgGHHCABhx2QAYce
					oAGHHwI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Asus VivoBook 15 OLED M513UA by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAIBtxwgAbcd
					EAG3HiEBtx8DAbcMAgGnHDABpx0BAaceoAGn
					H5A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>22</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Asus_PRIME_B460M-K_ALC897</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQGHHFABhx2QAYce
					oAGHH5ABlxxgAZcdkAGXHoEBlx8CAacccAGn
					HTABpx6BAacfAQG3HIABtx1AAbceIQG3HwEB
					twwCARcckAEXHeABFx5FARcfAQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>66</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904153</integer>
					<key>Comment</key>
					<string>ALC898, Toleda</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFHDAIhVxwgIVcd
					ECFXHgEhVx8BIWccMCFnHWAhZx4BIWcfASF3
					HPAhdx0AIXceACF3H0AhhxxAIYcdkCGHHqAh
					hx+QIZccYCGXHZAhlx6BIZcfAiGnHFAhpx0w
					IacegSGnHwEhtxxwIbcdQCG3HiEhtx8CIbcM
					AiHnHJAh5x1hIeceSyHnHwEh9xzwIfcdACH3
					HgAh9x9AIRcc8CEXHQAhFx4AIRcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>1</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904153</integer>
					<key>Comment</key>
					<string>ALC898, Toleda</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFHDAIhVxzwIVcd
					ACFXHgAhVx9AIWcc8CFnHQAhZx4AIWcfQCF3
					HPAhdx0AIXceACF3H0AhhxxAIYcdYCGHHgEh
					hx8BIZccYCGXHZAhlx6gIZcfkCGnHFAhpx0Q
					IaceASGnHwEhtxxwIbcdQCG3HiEhtx8CIbcM
					AiHnHJAh5x1hIeceSyHnHwEh9xzwIfcdACH3
					HgAh9x9AIRcc8CEXHQAhFx4AIRcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>2</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904153</integer>
					<key>Comment</key>
					<string>ALC898, Toleda</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFXHCAhVx0QIVce
					ASFXHwEhZxzwIWcdACFnHgAhZx9AIXcc8CF3
					HQAhdx4AIXcfQCGHHEAhhx2QIYceoCGHH5Ah
					lxxgIZcdkCGXHoEhlx8CIaccUCGnHTAhpx6B
					IacfASG3HHAhtx1AIbceISG3HwIh5xyQIecd
					YSHnHksh5x8BIfcc8CH3HQAh9x4AIfcfQCEX
					HPAhFx0AIRceACEXH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC898</string>
					<key>CodecID</key>
					<integer>283904153</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQGHHFABhx2QAYce
					oAGHH5ABlxxgAZcdkAGXHoEBlx8CAacccAGn
					HTABpx6BAacfAQG3HIABtx1AAbceIQG3HwEB
					5xyQAecd4AHnHkUB5x8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC898</string>
					<key>CodecID</key>
					<integer>283904153</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFXHCABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAXccQAF3
					HSABdx4BAXcfAQGHHFABhx2QAYceoAGHH5AB
					lxxgAZcdkAGXHoEBlx8CAacccAGnHTABpx6B
					AacfAQG3HIABtx1AAbceIQG3HwIB5xyQAecd
					YAHnHkUB5x8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904153</integer>
					<key>Comment</key>
					<string>Custom ALC898 by Irving23 for MSI GT72S 6QF-065CN</string>
					<key>ConfigData</key>
					<data>
					ARcc8AEXHQABFx4AARcfQAEnHBABJx0BASce
					oAEnH5ABRxzwAUcdAAFHHgABRx9AAVcc8AFX
					HQABVx4AAVcfQAFnHPABZx0AAWceAAFnH0AB
					dxxgAXcdEAF3HgEBdx8BAYccEAGHHRABhx6h
					AYcfAQGXHEABlx0BAZceFwGXH5ABpxwgAacd
					EAGnHoEBpx8BAbccQAG3HQEBtx4XAbcfkAHH
					HPABxx0AAcceAAHHH0AB1xzwAdcdAAHXHgAB
					1x9AAecccAHnHREB5x5FAecfAQH3HPAB9x0A
					AfceAAH3H0ABRwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Realtek ALC898 for MSI GS40</string>
					<key>CodecID</key>
					<integer>283904153</integer>
					<key>ConfigData</key>
					<data>
					AaccEAGnHQABpx4XAacfkAHnHCAB5x0QAece
					RgHnHwEBhxwwAYcdEAGHHoEBhx8BASccQAEn
					HQABJx6gAScfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904153</integer>
					<key>Comment</key>
					<string>ALC898, Toleda</string>
					<key>ConfigData</key>
					<data>
					IUccECFHHUAhRx4RIUcfkCFXHCAhVx0QIVce
					ASFXHwEhZxzwIWcdACFnHgAhZx9AIXcc8CF3
					HQAhdx4AIXcfQCGHHEAhhx2QIYceoCGHH5Ah
					lxxgIZcdkCGXHoEhlx8CIaccUCGnHTAhpx6B
					IacfASG3HHAhtx1AIbceISG3HwIh5xyQIecd
					YSHnHksh5x8BIfcc8CH3HQAh9x4AIfcfQCEX
					HPAhFx0AIRceACEXH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC898 for CLEVO P65xRS(-G) by datasone</string>
					<key>CodecID</key>
					<integer>283904153</integer>
					<key>ConfigData</key>
					<data>
					ARcc8AEXHQABFx4AARcfQAEnHFABJx0BASce
					pgEnH5ABRxwQAUcdAQFHHhcBRx+QAXccIAF3
					HRABdx4BAXcfAQGHHEABhx0QAYcegQGHHwEB
					1xzwAdcdAAHXHgAB1x9AAeccMAHnHREB5x5E
					AecfAQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>65</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC898 for Clevo P750DM2-G</string>
					<key>CodecID</key>
					<integer>283904153</integer>
					<key>ConfigData</key>
					<data>
					ARcc8AEXHQABFx4AARcfQAEnHFABJx0BASce
					pgEnH5ABRxwQAUcdAQFHHhcBRx+QAXccIAF3
					HRABdx4BAXcfAQGHHEABhx0QAYcegQGHHwEB
					pxxgAacdEAGnHoEBpx8BAdcc8AHXHQAB1x4A
					AdcfQAHnHDAB5x0RAeceRAHnHwEBRwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>66</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC898 for MSI GE62 7RE Apache Pro by spectra</string>
					<key>CodecID</key>
					<integer>283904153</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfmQGHHCABhx0QAYce
					gQGHHwIBVxwwAVcdAQFXHhMBVx+ZAaccMQGn
					HQEBpx4TAacfmQG3HDIBtx0BAbceEwG3H5kB
					twwCAUccQAFHHRABRx4hAUcfAgFHDAIB5xxQ
					AecdEQHnHkUB5x8C
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>98</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC898 for MSI GP62-6QG Leopard Pro</string>
					<key>CodecID</key>
					<integer>283904153</integer>
					<key>ConfigData</key>
					<data>
					ARcc8AEXHQABFx4AARcfQAEnHBABJx0BASce
					oAEnH5ABRxxQAUcdQAFHHiEBRx8BAUcMAgFX
					HEABVx0BAVceEAFXH5ABZxzwAWcdAAFnHgAB
					Zx9AAXcc8AF3HQABdx4AAXcfQAGHHCABhx2Q
					AYcegQGHHwEBlxzwAZcdAAGXHgABlx9AAacc
					8AGnHQABpx4AAacfQAG3HPABtx0AAbceAAG3
					H0ABxxzwAccdAAHHHgABxx9AAdcc8AHXHQAB
					1x4AAdcfQAHnHHAB5x1BAeceRQHnHwEB9xzw
					AfcdAAH3HgAB9x9A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904153</integer>
					<key>Comment</key>
					<string>ALC898, 4 Line Out by Andrey1970</string>
					<key>ConfigData</key>
					<data>
					AUccAAFHHUABRx4RAUcfkAFXHBABVx0QAVce
					AQFXHwEBZxwgAWcdYAFnHgEBZx8BAYccMAGH
					HZABhx6gAYcfkAGnHEABpx0wAacegQGnHwEB
					lxxQAZcdkAGXHoEBlx8CAbccYAG3HUABtx4h
					AbcfAgHnHHAB5x1hAeceSwHnHwEBdxyAAXcd
					IAF3HgEBdx8BAfcc8AH3HQAB9x4AAfcfSQEX
					HPABFx0AARceAAEXH0k=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>101</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904256</integer>
					<key>Comment</key>
					<string>toleda - ALC1150 </string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFHDAIBVxwgAVcd
					EAFXHgEBVx8BAWccMAFnHWABZx4BAWcfAQF3
					HPABdx0AAXceAAF3H0ABhxxAAYcdkAGHHqAB
					hx+QAZccYAGXHZABlx6BAZcfAgGnHFABpx0w
					AacegQGnHwEBtxxwAbcdQAG3HiEBtx8CAbcM
					AgHnHJAB5x1hAeceSwHnHwEB9xzwAfcdAAH3
					HgAB9x9AARcc8AEXHQABFx4AARcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>1</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904256</integer>
					<key>Comment</key>
					<string>toleda - ALC1150 </string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFHDAIBVxzwAVcd
					AAFXHgABVx9AAWcc8AFnHQABZx4AAWcfQAF3
					HPABdx0AAXceAAF3H0ABhxxAAYcdYAGHHgEB
					hx8BAZccYAGXHZABlx6gAZcfkAGnHFABpx0Q
					AaceAQGnHwEBtxxwAbcdQAG3HiEBtx8CAbcM
					AgHnHJAB5x1hAeceSwHnHwEB9xzwAfcdAAH3
					HgAB9x9AARcc8AEXHQABFx4AARcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>2</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904256</integer>
					<key>Comment</key>
					<string>toleda - ALC1150 </string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFHDAIBVxzwAVcd
					AAFXHgABVx9AAWcc8AFnHQABZx4AAWcfQAF3
					HPABdx0AAXceAAF3H0ABhxxAAYcdkAGHHqAB
					hx+QAZccYAGXHZABlx6BAZcfAgGnHFABpx0w
					AacegQGnHwEBtxxwAbcdQAG3HiEBtx8CAbcM
					AgHnHJAB5x1hAeceSwHnHwEB9xzwAfcdAAH3
					HgAB9x9AARcc8AEXHQABFx4AARcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC1150</string>
					<key>CodecID</key>
					<integer>283904256</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQGHHFABhx2QAYce
					oAGHH5ABlxxgAZcdkAGXHoEBlx8CAacccAGn
					HTABpx6BAacfAQG3HIABtx1AAbceIQG3HwEB
					5xyQAecd4AHnHkUB5x8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC1150</string>
					<key>CodecID</key>
					<integer>283904256</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFXHCABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAXccQAF3
					HSABdx4BAXcfAQGHHFABhx2QAYceoAGHH5AB
					lxxgAZcdkAGXHoEBlx8CAacccAGnHTABpx6B
					AacfAQG3HIABtx1AAbceIQG3HwIB5xyQAecd
					YAHnHkUB5x8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Realtek ALC1150 (mic boost)</string>
					<key>CodecID</key>
					<integer>283904256</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFXHCABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAXccQAF3
					HSABdx4BAXcfAQGHHFABhx2QAYceoAGHH5AB
					lxxgAZcdkAGXHoEBlx8CAacccAGnHTABpx6B
					AacfAQG3HIABtx1AAbceIQG3HwIB5xyQAecd
					YAHnHkUB5x8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904256</integer>
					<key>Comment</key>
					<string>ALC1150 for Gigabyte GA-Z97X-UD5H by DalianSky</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFHDAIBVxwgAVcd
					EAFXHgEBVx8BAWccMAFnHWABZx4BAWcfAQF3
					HPABdx0AAXceAAF3H0ABhxxAAYcdkAGHHqAB
					hx+QAZccYAGXHZABlx6BAZcfAgGnHFABpx0w
					AacegQGnHwEBtxxwAbcdQAG3HiEBtx8CAbcM
					AgHnHJAB5x1hAeceSwHnHwEB9xzwAfcdAAH3
					HgAB9x9AARcc8AEXHQABFx4AARcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>Toleda -  Realtek ALC1220</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFHDAIBVxwgAVcd
					EAFXHgEBVx8BAWccMAFnHWABZx4BAWcfAQF3
					HPABdx0AAXceAAF3H0ABhxxAAYcdkAGHHqAB
					hx+QAZccYAGXHZABlx6BAZcfAgGnHFABpx0w
					AacegQGnHwEBtxxwAbcdQAG3HiEBtx8CAbcM
					AgHnHJAB5x1hAeceSwHnHwE=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>1</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>Toleda -  Realtek ALC1220</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFHDAIBVxzwAVcd
					AAFXHgABVx9AAWcc8AFnHQABZx4AAWcfQAF3
					HPABdx0AAXceAAF3H0ABhxxAAYcdYAGHHgEB
					hx8BAZccYAGXHZABlx6gAZcfkAGnHFABpx0Q
					AaceAQGnHwEBtxxwAbcdQAG3HiEBtx8CAbcM
					AgHnHJAB5x1hAeceSwHnHwE=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>2</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>Toleda -  Realtek ALC1220</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFHDAIBVxzwAVcd
					AAFXHgABVx9AAWcc8AFnHQABZx4AAWcfQAF3
					HPABdx0AAXceAAF3H0ABhxxAAYcdkAGHHqAB
					hx+QAZccYAGXHZABlx6BAZcfAgGnHFABpx0w
					AacegQGnHwEBtxxwAbcdQAG3HiEBtx8CAbcM
					AgHnHJAB5x1hAeceSwHnHwEB9xzwAfcdAAH3
					HgAB9x9AARcc8AEXHQABFx4AARcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>Mirone - Realtek ALC1220</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQGHHFABhx2QAYce
					oAGHH5ABlxxgAZcdkAGXHoEBlx8CAacccAGn
					HTABpx6BAacfAQG3HIABtx1AAbceIQG3HwEB
					5xyQAecd4AHnHkUB5x8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>Mirone - Realtek ALC1220</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFXHCABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAXccQAF3
					HSABdx4BAXcfAQGHHFABhx2QAYceoAGHH5AB
					lxxgAZcdkAGXHoEBlx8CAacccAGnHTABpx6B
					AacfAQG3HIABtx1AAbceIQG3HwIB5xyQAecd
					YAHnHkUB5x8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>Custom Realtek ALC1220 by truesoldier</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAG3HCABtx1AAbce
					IQG3HwIB5xwwAecdIAHnHksB5x8BAYccQAGH
					HZABhx6gAYcfkAGXHFABlx2QAZcegQGXHwIB
					VxxwAVcdEAFXHgEBVx8BAWccgAFnHWABZx4B
					AWcfAQGnHKABpx0wAacegQGnHwE=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>MacPeet - ALC1220 for Clevo P950HR</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHRABRx4hAUcfAQFHDAIBJxwwAScd
					AAEnHqYBJx+ZAYccQAGHHRABhx6BAYcfAQG3
					HGABtx0AAbceFwG3H5kBtwwCAecccAHnHRAB
					5x5EAecfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>fleaplus - ALC1220 for MSI WT75</string>
					<key>ConfigData</key>
					<data>
					AbccIAG3HQEBtx4XAbcfkAG3DAIBhxxAAYcd
					EAGHHqEBhx8BAZccQQGXHRQBlx6BAZcfAQGn
					HE8Bpx0QAacegQGnHwEBJxxQAScdAQEnHmAB
					Jx+Q
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>MacPeet - ALC1220 for Gigabyte Z390</string>
					<key>ConfigData</key>
					<data>
					AeccMAHnHSAB5x5FAecfAQGHHEABhx2QAYce
					oQGHH5EBlxxQAZcdkAGXHoEBlx8CAUccYAFH
					HUABRx4hAUcfAgFHDAIBVxxwAVcdEAFXHgEB
					Vx8BAWccgAFnHWABZx4BAWcfAQGnHJABpx0w
					AacegQGnHwEBtxygAbcdQAG3HhEBtx+RAbcM
					AgIFAAcCBAPAAgUAGgIEAcE=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>16</integer>
					<key>WakeConfigData</key>
					<data>
					AbcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>NIBLIZE - ALC1220 for Gigabyte Z490 Vision G manual SP/HP</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFXHCABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAXccQAF3
					HSABdx4BAXcfAQGHHFABhx2QAYceoAGHH5AB
					lxxgAZcdkAGXHoEBlx8CAacccAGnHTABpx6B
					AacfAQG3HIABtx1AAbceIQG3HwIB5xyQAecd
					YAHnHkUB5x8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>17</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>CaseySJ - ALC1220 for Gigabyte B550 Vision D</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4QAUcfkAFHDAIBVxwgAVcd
					EAFXHgEBVx8BAWccMAFnHWABZx4BAWcfAQGH
					HEABhx2QAYceoAGHH5ABlxxQAZcdkAGXHqEB
					lx8CAaccYAGnHTABpx6BAacfAQG3HHABtx1A
					AbceIQG3HwIBtwwCAeccgAHnHSEB5x5FAecf
					AQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>20</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>ALC1220 for MSI GE63 Raider RGB 8RF</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQEnHCABJx0BASce
					oAEnH5ABlxwwAZcdEAGXHoEBlx8CAbccgAG3
					HUABtx4hAbcfAQHnHJAB5x3gAeceRQHnHwEB
					RwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>lostwolf - ALC1220 for Gigabyte Z370-HD3P</string>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHBABRx1AAUce
					EQFHHwEBRwwCAVccIAFXHRABVx4BAVcfAQFn
					HDABZx1gAWceAQFnHwEBdxxAAXcdIAF3HgEB
					dx8BAYccUAGHHZABhx6gAYcfkQGXHGABlx2Q
					AZcegAGXHwIBpxxwAacdMAGnHoEBpx8BAbcc
					gAG3HUABtx4hAbcfAgG3DAIB1xzwAdcdAAHX
					HgAB1x9AAecckAHnHQAB5x5DAecfmQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>27</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>MacPeet- ALC1220 for Z390 Aorus Ultra - Output SP/HP Manualmode </string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFHDAIBVxwgAVcd
					EAFXHgEBVx8BAWccMAFnHWABZx4BAWcfAQF3
					HPABdx0AAXceAAF3H0ABhxxAAYcdkAGHHqAB
					hx+QAZccYAGXHZABlx6BAZcfAgGnHFABpx0w
					AacegQGnHwEBtxxwAbcdQAG3HiEBtx8CAbcM
					AgHnHJAB5x1hAeceSwHnHwE=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>MacPeet- ALC1220 for Z390 Aorus Ultra - Output SP/HP SwitchMode</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFHDAIBVxwgAVcd
					EAFXHgEBVx8BAWccMAFnHWABZx4BAWcfAQF3
					HPABdx0AAXceAAF3H0ABhxxAAYcdkAGHHqAB
					hx+QAZccYAGXHZABlx6BAZcfAgGnHFABpx0w
					AacegQGnHwEBtxxwAbcdQAG3HiEBtx8CAbcM
					AgHnHJAB5x1hAeceSwHnHwE=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>29</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>MacPeet- ALC1220 for Z370 AORUS Gaming 7 - Output SP/HP SwitchMode</string>
					<key>ConfigData</key>
					<data>
					AeccMAHnHSAB5x5FAecfAQGHHEABhx2QAYce
					oQGHH5ABlxxQAZcdkAGXHoEBlx8CAUccYAFH
					HUABRx4hAUcfAgFHDAIBVxxwAVcdEAFXHgEB
					Vx8BAWccgAFnHWABZx4BAWcfAQGnHJABpx0w
					AacegQGnHwEBtxygAbcdQAG3HhEBtx+QAbcM
					Ag==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>30</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>Custom ALC1220 for MSI P65 Creator by CleverCoder</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHRABRx4RAUcfAAFHDAIBtxwgAbcd
					AAG3HhcBtx+QAbcMAgGXHDABlx0QAZcegQGX
					HwABJxxAAScdAAEnHqYBJx+Q
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>34</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>Custom ALC1220 for MSI GP75 9SD by Win7GM</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHRABRx4hAUcfAQFHDAIBtxwgAbcd
					AQG3HhcBtx+QAbcMAgGXHDABlx0QAZcegQGX
					HwEBJxxAAScdAQEnHqYBJx+Q
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>35</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>Lorys89 ALC1220 for AMD B450/B550 - SwitchMode</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFHDAIBtxwgAbcd
					QAG3HiEBtx8CAbcMAgGHHEABhx2QAYceoAGH
					H5ABlxxgAZcdkAGXHoEBlx8C
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>69</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>Custom ALC1220 for Mi Gaming Notebook Creator by Xsixu</string>
					<key>ConfigData</key>
					<data>
					AaccEAGnHQEBpx4XAacfkAFHHCABRx0QAUce
					IQFHHwABRwwCASccMAEnHREBJx6mAScfkAGX
					HGABlx0AAZcegQGXHwABlwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>98</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgGXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>MiBook 2019 by Dynamix1997</string>
					<key>ConfigData</key>
					<data>
					AaccEAGnHQEBpx4QAacfkAFHHCABRx0QAUce
					IQFHHwMBRwwCASccMAEnHQEBJx6mAScfkAGH
					HEABhx0QAYcegQGHHwM=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>Hasee_G8-CU7PK</string>
					<key>ConfigData</key>
					<data>
					AbccEAG3HQEBtx4XAbcfkAG3DAIBRxwfAUcd
					EAFHHiEBRx8EAUcMAgEnHDABJx0BAScepgEn
					H5ABhxxAAYcdEAGHHoEBhx8EAeccUAHnHREB
					5x5FAecfBA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>100</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>CodecID</key>
					<integer>283904768</integer>
					<key>CodecName</key>
					<string>toleda -  Realtek ALCS1200A</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFXHCABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAXcc8AF3
					HQABdx4AAXcfQAGHHEABhx2QAYceoAGHH5AB
					lxxgAZcdkAGXHoEBlx8CAaccUAGnHTABpx6B
					AacfAQG3HHABtx1AAbceIQG3HwIB5xyQAecd
					YQHnHksB5x8BARcc8AEXHQABFx4AARcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>1</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>CodecID</key>
					<integer>283904768</integer>
					<key>CodecName</key>
					<string>toleda -  Realtek ALCS1200A</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFXHPABVx0AAVce
					AAFXH0ABZxzwAWcdAAFnHgABZx9AAXcc8AF3
					HQABdx4AAXcfQAGHHEABhx1gAYceAQGHHwEB
					lxxgAZcdkAGXHqABlx+QAaccUAGnHRABpx4B
					AacfAQG3HHABtx1AAbceIQG3HwIB5xyQAecd
					YQHnHksB5x8BARcc8AEXHQABFx4AARcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>2</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>CodecID</key>
					<integer>283904768</integer>
					<key>CodecName</key>
					<string>toleda -  Realtek ALCS1200A</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFXHPABVx0AAVce
					AAFXH0ABZxzwAWcdAAFnHgABZx9AAXcc8AF3
					HQABdx4AAXcfQAGHHEABhx2QAYceoAGHH5AB
					lxxgAZcdkAGXHoEBlx8CAaccUAGnHTABpx6B
					AacfAQG3HHABtx1AAbceIQG3HwIB5xyQAecd
					YQHnHksB5x8BARcc8AEXHQABFx4AARcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>CodecID</key>
					<integer>283904768</integer>
					<key>CodecName</key>
					<string>ALCS1200A for B550M Gaming Carbon WIFI by Kila2</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHVABRx4RAUcfkQFHDAIBZxwgAWcd
					EAFnHgEBZx8BAVccMAFXHRABVx4BAVcfAQGH
					HEABhx0QAYceoQGHH5EBpxxQAacdEAGnHoEB
					px8BAZccYAGXHZABlx6BAZcfAgG3HHABtx1A
					AbceIQG3HwIBtwwCAecckAHnHREB5x5FAecf
					AQEXHPABFx0AARceAAEXH0ABdxzwAXcdAAF3
					HgABdx9AAgUABwIEfKM=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>CodecID</key>
					<integer>283904768</integer>
					<key>CodecName</key>
					<string>owen0o0 -  Realtek ALCS1200A</string>
					<key>ConfigData</key>
					<data>
					AUccQAFHHVABRx4RAUcfkAFHDAIBZxxhAWcd
					EAFnHgEBZx8BAVccYgFXHRABVx4BAVcfAQG3
					HFABtx1AAbceIQG3HwIBtwwCAecccAHnHREB
					5x5FAecfAQGHHBABhx0QAYceoQGHH5ABpxwg
					AacdEAGnHoEBpx8BAZccgAGXHZABlx6hAZcf
					AgF3HPABdx0AAXceAAF3H0ABFxzwARcdAAEX
					HgABFx9AAgUABwIEfKM=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>CodecID</key>
					<integer>283904768</integer>
					<key>CodecName</key>
					<string>VictorXu -  Realtek ALCS1200A for MSI B460I GAMING EDGE WIFI</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHVABRx4RAUcfkQFHDAIBtxwgAbcd
					QAG3HiEBtx8CAbcMAgHnHDAB5x0QAeceRQHn
					HwEBlxxQAZcdkAGXHoEBlx8CAVccYAFXHRAB
					Vx4BAVcfAQFnHHABZx0QAWceAQFnHwEBhxyA
					AYcdEAGHHqEBhx+RAacckAGnHRABpx6BAacf
					AQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>23</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>CodecID</key>
					<integer>283904768</integer>
					<key>CodecName</key>
					<string>VictorXu -  Realtek ALCS1200A for Asrock Z490M-ITX</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkQFHDAIBtxwgAbcd
					QAG3HiEBtx8CAbcMAgGHHDABhx2QAYceoQGH
					H5EBpxw/AacdMAGnHoEBpx8BAZccQAGXHZAB
					lx6BAZcfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>49</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>CodecID</key>
					<integer>283904768</integer>
					<key>CodecName</key>
					<string>VictorXu -  Realtek ALCS1200A for Gigabyte B460M Aorus Pro</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkQFHDAIBZxwRAWcd
					YAFnHgEBZx8BAVccEgFXHRABVx4BAVcfAQF3
					HBQBdx0gAXceAQF3HwEBtxwgAbcdQAG3HiEB
					tx8CAbcMAgGHHEABhx2QAYceoQGHH5EBpxxP
					AacdMAGnHoEBpx8BAZccUAGXHZABlx6BAZcf
					Ag==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>50</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904768</integer>
					<key>CodecName</key>
					<string>GeorgeWan - ALCS1200A for ASROCK-Z490-Steel-Legend</string>
					<key>ConfigData</key>
					<data>
					ARcc8AEXHQABFx4AARcfQAFHHBABRx1AAUce
					EQFHH5ABRwwCAVccIAFXHRABVx4BAVcfAQFn
					HDABZx1gAWceAQFnHwEBdxzwAXcdAAF3HgAB
					dx9AAYccQAGHHZABhx6gAYcfkAGXHGABlx2Q
					AZcegQGXHwIBpxxQAacdMAGnHoEBpx8BAbcc
					cAG3HUABtx4hAbcfAgG3DAIB5xyAAecdYQHn
					HksB5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>51</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>CodecID</key>
					<integer>283904768</integer>
					<key>CodecName</key>
					<string>GeorgeWan - ALCS1200A for MSI-Mortar-B460M</string>
					<key>ConfigData</key>
					<data>
					ARcc8AEXHQABFx4AARcfQAFHHBABRx1AAUce
					EQFHH5ABRwwCAVccIAFXHRABVx4BAVcfAQFn
					HDABZx1gAWceAQFnHwEBdxzwAXcdAAF3HgAB
					dx9AAYccQAGHHZABhx6gAYcfkAGXHGABlx2Q
					AZcegQGXHwIBpxxQAacdMAGnHoEBpx8BAbcc
					cAG3HUABtx4hAbcfAgG3DAIB5xyQAecdYQHn
					HksB5x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>52</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283904768</integer>
					<key>CodecName</key>
					<string>Lorys89 and Vorshim92 - ALCS1200A for ASROCK Z490M ITX AC</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFHDAIBhxwgAYcd
					kAGHHqABhx+QAZccMAGXHZABlx6BAZcfAgGn
					HEABpx0wAacegQGnHwEBtxxQAbcdQAG3HiEB
					tx8CAbcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>69</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906408</integer>
					<key>CodecName</key>
					<string>Toleda -  Realtek ALC S1220A</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFHDAIBVxwgAVcd
					EAFXHgEBVx8BAWccMAFnHWABZx4BAWcfAQF3
					HPABdx0AAXceAAF3H0ABhxxAAYcdkAGHHqAB
					hx+QAZccYAGXHZABlx6BAZcfAgGnHFABpx0w
					AacegQGnHwEBtxxwAbcdQAG3HiEBtx8CAbcM
					AgHnHJAB5x1hAeceSwHnHwEB9xzwAfcdAAH3
					HgAB9x9AARcc8AEXHQABFx4AARcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>1</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906408</integer>
					<key>CodecName</key>
					<string>Toleda -  Realtek ALC S1220A</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFHDAIBVxzwAVcd
					AAFXHgABVx9AAWcc8AFnHQABZx4AAWcfQAF3
					HPABdx0AAXceAAF3H0ABhxxAAYcdYAGHHgEB
					hx8BAZccYAGXHZABlx6gAZcfkAGnHFABpx0Q
					AaceAQGnHwEBtxxwAbcdQAG3HiEBtx8CAbcM
					AgHnHJAB5x1hAeceSwHnHwEB9xzwAfcdAAH3
					HgAB9x9AARcc8AEXHQABFx4AARcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>2</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906408</integer>
					<key>CodecName</key>
					<string>Toleda -  Realtek ALC S1220A</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfkAFHDAIBVxzwAVcd
					AAFXHgABVx9AAWcc8AFnHQABZx4AAWcfQAF3
					HPABdx0AAXceAAF3H0ABhxxAAYcdkAGHHqAB
					hx+QAZccYAGXHZABlx6BAZcfAgGnHFABpx0w
					AacegQGnHwEBtxxwAbcdQAG3HiEBtx8CAbcM
					AgHnHJAB5x1hAeceSwHnHwEB9xzwAfcdAAH3
					HgAB9x9AARcc8AEXHQABFx4AARcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906408</integer>
					<key>CodecName</key>
					<string>Mirone - Realtek ALC S1220A</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQGHHFABhx2QAYce
					oAGHH5ABlxxgAZcdkAGXHoEBlx8CAacccAGn
					HTABpx6BAacfAQG3HIABtx1AAbceIQG3HwEB
					5xyQAecd4AHnHkUB5x8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906408</integer>
					<key>CodecName</key>
					<string>Mirone - Realtek ALC S1220A</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFXHCABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAXccQAF3
					HSABdx4BAXcfAQGHHFABhx2QAYceoAGHH5AB
					lxxgAZcdkAGXHoEBlx8CAacccAGnHTABpx6B
					AacfAQG3HIABtx1AAbceIQG3HwIB5xyQAecd
					YAHnHkUB5x8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906408</integer>
					<key>CodecName</key>
					<string>Realtek ALC S1220P_MSI_Z490i_UNIFY_ by_vio</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHVABRx4BAUcfAQFHDAIBVxwgAVcd
					EAFXHgEBVx8BAWccMAFnHRABZx4BAWcfAQGH
					HEABhx0QAYceoAGHH5EBlxxQAZcdkAGXHosB
					lx8CAaccYAGnHRABpx6BAacfAQG3HHABtx1A
					AbceIQG3HwIBtwwCAeccgAHnHREB5x5FAecf
					AQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>8</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906408</integer>
					<key>CodecName</key>
					<string>Realtek ALC S1220A Kushamot for Asus Z270G mb (based on Mirone's layout 7)</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFXHFABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAYccYAGH
					HZABhx6gAYcfkAGXHHABlx2QAZcegQGXHwEB
					txwgAbcdQAG3HiEBtx8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906408</integer>
					<key>CodecName</key>
					<string>Realtek ALC S1220A for Asus ProArt Z690-Creator WiFi (CaseySJ)</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4BAUcfAAFHDAIBVxwgAVcd
					EAFXHgEBVx8BAWccMAFnHWABZx4BAWcfAQGH
					HEABhx2QAYceoAGHH5ABlxxQAZcdkAGXHoEB
					hx8CAaccYAGnHTABpx6BAacfAQG3HHABtx1A
					AbceIQG3HwIBtwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906408</integer>
					<key>CodecName</key>
					<string>Realtek ALC S1220A for Asus ROG Strix X570-F Gaming (based on Mirone's layout 7)</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFXHFABVx0QAVce
					AQFXHwEBZxwwAWcdYAFnHgEBZx8BAYccYAGH
					HZABhx6gAYcfkAGXHHABlx2QAZcegQGXHwEB
					txwgAbcdQAG3HiEBtx8BAUcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906408</integer>
					<key>CodecName</key>
					<string>Realtek ALC S1220A RodionS, Nacho 2.0 outputs(green), 2 inputs (blue)+front panel (mic fr.panel), mic (pink), headphones(lime), SPDIF/Optical </string>
					<key>ConfigData</key>
					<data>
					AUceEAGHHqABhx+RAZcegQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>20</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906408</integer>
					<key>CodecName</key>
					<string>Realtek ALC S1220A RodionS, Nacho 5.1 outputs(green, black, orange), 2 inputs (blue)+front panel (mic fr.panel), mic (pink), headphones(lime), SPDIF/Optical </string>
					<key>ConfigData</key>
					<data>
					AUceEAGHHqABhx+RAZcegQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Conexant CX8050</string>
					<key>CodecID</key>
					<integer>351346546</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQABdx4XAXcfkAGnHCABpx0AAace
					oAGnH5ABlxwwAZcdEAGXHosBlx8BAWccQAFn
					HRABZx4rAWcfAQF3DAIBZwwCAZcHJA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Conexant CX8050 for ASUS S410U/X411U by cowpod</string>
					<key>CodecID</key>
					<integer>351346546</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQABdx4XAXcfkAGnHCABpx0AAace
					oAGnH5ABlxwwAZcdEAGXHosBlx8BAWccQAFn
					HRABZx4rAWcfAQF3DAIBZwwCAZcHJAGnByQ=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAgFnDAIBlwckAacHJA==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - Conexant CX8070 (CX11880) for Lenovo ThinkPad E590</string>
					<key>CodecID</key>
					<integer>351346566</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQABdx4XAXcfkQF3DAIBpxwgAacd
					AAGnHqYBpx+QAZccMAGXHRABlx6BAZcfAAFn
					HEABZx0QAWceIQFnHwABZwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAgFnDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres ZeroCross - Conexant CX8070 for Lenovo ThinkPad E14</string>
					<key>CodecID</key>
					<integer>351346566</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQABdx4XAXcfkQGnHCABpx0AAace
					oAGnH5ABlxwwAZcdEAGXHoEBlx8AAWccQAFn
					HRABZx4hAWcfAAF3DAIBZwwCAZcHJAGnByQ=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAgFnDAIBlwckAacHJA==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Asus VivoBook Pro 15 CX8150 by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>351346646</integer>
					<key>ConfigData</key>
					<data>
					AWccQAFnHRABZx4hAWcfBAGXHDABlx0QAZce
					gQGXHwQBpxwgAacdAQGnHqABpx+QAdccEAHX
					HQEB1x4XAdcfkAFnDAIB1wwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ASUS VivoBook S405UA-EB906T - CX8150 by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>351346646</integer>
					<key>ConfigData</key>
					<data>
					AWccQAFnHRABZx4hAWcfBAGXHDABlx0QAZce
					gQGXHwQBpxwgAacdAQGnHqABpx+QAXccEAF3
					HQEBdx4XAXcfkAFnDAIBdwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>22</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Conexant CX8200</string>
					<key>CodecID</key>
					<integer>351346696</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQABdx4XAXcfkAGnHCABpx0AAace
					oAGnH5ABlxwwAZcdEAGXHosBlx8BAdccQAHX
					HRAB1x4rAdcfAQF3DAIB1wwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - Conexant CX8200 for HP ZbooK 15UG4</string>
					<key>CodecID</key>
					<integer>351346696</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQABdx4XAXcfkAF3DAIBpxwgAacd
					AAGnHqYBpx+QAZccMAGXHRABlx6BAZcfAAHX
					HEAB1x0QAdceIQHXHwAB1wwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres ZeroCross - HP Spectre 13-V130NG</string>
					<key>CodecID</key>
					<integer>351346696</integer>
					<key>ConfigData</key>
					<data>
					AXccIAF3HQEBdx4XAXcfkAGXHDABlx0QAZce
					gQGXHwEBpxxAAacdAQGnHqABpx+QAWccEAFn
					HRABZx4hAWcfAQFnDAIBdwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AWcMAgF3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>frankiezdh - Conexant CX8200 for HP Probook 440 G5</string>
					<key>CodecID</key>
					<integer>351346696</integer>
					<key>ConfigData</key>
					<data>
					AWccUAFnHRABZx4hAWcfAQFnDAIBdxxAAXcd
					AQF3HhcBdx+RAXcMAgGXHHABlx0QAZcegQGX
					HwEBpxwQAacdAQGnHqYBpx+R
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>23</integer>
					<key>WakeConfigData</key>
					<data>
					AWcMAgF3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Conexant CX8200 for LG Gram Z990/Z90N</string>
					<key>CodecID</key>
					<integer>351346696</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQEBdx4XAXcfkQF3DAIBpxwgAacd
					AQGnHqABpx+VAZccMAGXHRABlx6LAZcfBAGX
					ByQBZxxAAWcdEAFnHisBZx8EAWcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>80</integer>
					<key>WakeConfigData</key>
					<data>
					AWcMAgF3DAIBlwck
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Conexant CX8400</string>
					<key>CodecID</key>
					<integer>351346896</integer>
					<key>ConfigData</key>
					<data>
					AWccEAFnHRABZx4hAWcfBAF3HPABdx0AAXce
					AAF3H0ABhxzwAYcdAAGHHgABhx9AAZccIAGX
					HRABlx6BAZcfBAGnHDABpx0BAacepgGnH5AB
					1xxAAdcdAQHXHhcB1x+ZAecc8AHnHQAB5x4A
					AecfQAH3HPAB9x0AAfceAAH3H0ACFxzwAhcd
					AAIXHgACFx9AAmcc8AJnHQACZx4AAmcfQAJ3
					HPACdx0AAnceAAJ3H0ABZwwCAdcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Conexant CX20561</string>
					<key>CodecID</key>
					<integer>351359057</integer>
					<key>ConfigData</key>
					<data>
					AWccQAFnHUABZx4hAWcfAQF3HPABdx0AAXce
					AAF3H0ABhxwwAYcdMAGHHoEBhx8BAZcc8AGX
					HQABlx4AAZcfQAGnHBABpx0BAaceFwGnH5AB
					txzwAbcdAAG3HgABtx9AAccc8AHHHQABxx4A
					AccfQAHXHCAB1x0BAdceoAHXH5A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Conexant CX20583</string>
					<key>CodecID</key>
					<integer>351359079</integer>
					<key>ConfigData</key>
					<data>
					AZcc8AGXHUABlx4hAZcfBAGnHPABpx2QAace
					oQGnHwQBtxzwAbcdAQG3HgABtx9AAccc8AHH
					HQEBxx4AAccfQAHXHPAB1x0BAdceAAHXH0AB
					5xzwAecdAQHnHqcB5x+VAfcc8AH3HQEB9x4X
					AfcfkgIHHPACBx0RAgceRQIHHwQCJxzwAicd
					AQInHgACJx9AAjcc8AI3HQECNx4AAjcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Conexant CX20585</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AZccEAGXHRABlx4gAZcfAAGnHCABpx0AAace
					AAGnH0ABtxwwAbcdEAG3HoABtx8AAcccUAHH
					HQABxx4AAccfQAHXHGAB1x0AAdceAAHXH0AB
					5xxgAecdAAHnHgAB5x9AAfcccAH3HQAB9x4Q
					AfcfkAIHHIACBx0AAgceAAIHH0ACJxyAAicd
					AAInHgACJx9AAjcckAI3HQACNx6gAjcfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Constanta custom for Toshiba L755-16R - Conexant CX20585</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AZccEAGXHRABlx4gAZcfAAGnHCABpx0wAace
					gQGnHwEBtxwwAbcdAAG3HgABtx9AAcccUAHH
					HQABxx4AAccfQAHXHGAB1x0AAdceAAHXH0AB
					5xxgAecdAAHnHgAB5x9AAfcccAH3HQAB9x4Q
					AfcfkAIHHIACBx0AAgceAAIHH0ACJxyAAicd
					AAInHgACJx9AAjcckAI3HQECNx6gAjcfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Conexant CX20588</string>
					<key>CodecID</key>
					<integer>351359084</integer>
					<key>ConfigData</key>
					<data>
					AZccQAGXHRABlx4hAZcfAgG3HDABtx0QAbce
					owG3H5kCNxxQAjcdAQI3HqECNx+SAfccEAH3
					HQEB9x4TAfcfmQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Conexant CX20590</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AZccQAGXHRABlx4hAZcfAAGnHDABpx0QAace
					gQGnHwABtxwgAbcdAAG3HqcBtx+QAfccEAH3
					HQAB9x4XAfcfkQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>CX20590 Custom for Lenovo Yoga 13 by usr-sse2</string>
					<key>ConfigData</key>
					<data>
					AZccMAGXHUABlx4rAZcfDgH3HCAB9x0BAfce
					EAH3H5ACNxwQAjcdAQI3HqACNx+QAaccQAGn
					HRABpx6BAacfAQG3HPABtx0AAbceAAG3H0AB
					xxzwAccdAAHHHgABxx9AAdcc8AHXHQAB1x4A
					AdcfQAHnHPAB5x0AAeceAAHnH0ACBxzwAgcd
					AAIHHgACBx9AAicc8AInHQACJx4AAicfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>CX20590 for Lenovo T420 by tluck (Additional ports for use with a Docking Station)</string>
					<key>ConfigData</key>
					<data>
					AFccAABXHQAAVx5WAFcfGABnHBAAZx0AAGce
					VgBnHxgAdxwgAHcdAAB3HlYAdx8YAZccMAGX
					HRABlx4hAZcfBAGnHEABpx2QAaceoQGnH2EB
					txxQAbcdEAG3HoEBtx8BAcccYAHHHUABxx4h
					AccfYQH3HHAB9x0BAfceFwH3H5kCNxyAAjcd
					AQI3HqYCNx+ZAbcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>CX20590 for Lenovo T420 by tluck (Standard Laptop)</string>
					<key>ConfigData</key>
					<data>
					AFccAABXHQAAVx5WAFcfGABnHBAAZx0AAGce
					VgBnHxgAdxwgAHcdAAB3HlYAdx8YAZccMAGX
					HRABlx4hAZcfBAGnHEABpx2QAaceoQGnH2EB
					txxQAbcdEAG3HoEBtx8BAcccYAHHHUABxx4h
					AccfYQH3HHAB9x0BAfceFwH3H5kCNxyAAjcd
					AQI3HqYCNx+ZAbcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>14</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>351359137</integer>
					<key>Comment</key>
					<string>CX20641 - MacPeet - Dell OptiPlex 3010 - ManualMode</string>
					<key>ConfigData</key>
					<data>
					IcccECHHHUAhxx4BIccfASGnHCAhpx2QIace
					gSGnHwIhtxwwIbcdMCG3HoEhtx8BIZccQCGX
					HUAhlx4hIZcfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>351359137</integer>
					<key>Comment</key>
					<string>CX20641 - MacPeet - Dell OptiPlex 3010 - SwitchMode</string>
					<key>ConfigData</key>
					<data>
					IcccECHHHUAhxx4RIccfkCGnHCAhpx2QIace
					gSGnHwIhtxwwIbcdMCG3HoEhtx8BIZccQCGX
					HUAhlx4hIZcfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>351359138</integer>
					<key>Comment</key>
					<string>CX20642 - MacPeet - Fujitsu ESPRIMO E910 E90+ Desktop - ManualMode</string>
					<key>ConfigData</key>
					<data>
					IcccECHHHUAhxx4BIccfASGnHCAhpx0QIace
					gSGnHwIhlxxAIZcdECGXHiEhlx8CIdccUCHX
					HTAh1x6BIdcfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>351359138</integer>
					<key>Comment</key>
					<string>CX20642 - MacPeet - Fujitsu ESPRIMO E910 E90+ Desktop - SwitchMode</string>
					<key>ConfigData</key>
					<data>
					IcccECHHHUAhxx4RIccfkCGnHCAhpx0QIace
					oSGnH5IhlxxAIZcdECGXHiEhlx8CIdccUCHX
					HTAh1x6BIdcfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>Comment</key>
					<string>Custom for Dell Vostro 3x60 by vusun123</string>
					<key>ConfigData</key>
					<data>
					AfccEAH3HQAB9x4XAfcfkQGnHDABpx0QAace
					gQGnHwkBlxxAAZcdEAGXHiEBlx8AAjccIAI3
					HQECNx6nAjcfkAG3DAIB1wwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Conexant CX20722</string>
					<key>CodecID</key>
					<integer>351359218</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQABdx4WAXcfkQGnHCABpx0AAace
					pgGnH5ABlxwwAZcdEAGXHoEBlx8CAWccQAFn
					HRABZx4hAWcfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Conexant CX20724</string>
					<key>CodecID</key>
					<integer>351359220</integer>
					<key>ConfigData</key>
					<data>
					AWccEAFnHRABZx4hAWcfAgF3HCABdx0AAXce
					FwF3H5EBlxwwAZcdEAGXHoEBlx8CAaccQAGn
					HQABpx6mAacfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Conexant CX20724</string>
					<key>CodecID</key>
					<integer>351359220</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQEBdx4XAXcfkQGnHCABpx0BAace
					oAGnH5UBlxwwAZcdEAGXHosBlx8EAdccQAHX
					HRAB1x4rAdcfBA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Conexant CX20752</string>
					<key>CodecID</key>
					<integer>351359247</integer>
					<key>ConfigData</key>
					<data>
					AWccEAFnHUABZx4hAWcfAQF3HCABdx0AAXce
					FwF3H5ABhxwwAYcdkAGHHoEBhx8BAaccQAGn
					HQABpx6gAacfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>Codec</key>
					<string>Andres ZeroCross - Asus A455LF - WX039D</string>
					<key>CodecID</key>
					<integer>351359247</integer>
					<key>ConfigData</key>
					<data>
					AZcHJAGnByQBZxxAAWcdEAFnHiEBZx8EAXcc
					EAF3HQEBdx4XAXcfkAGXHDABlx0QAZcegQGX
					HwQBpxwgAacdAQGnHqABpx+Q
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
					<key>WakeConfigData</key>
					<data>
					AZcHJAGnByQ=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>Codec</key>
					<string>Conexant - CX20751/2 by RehabMan</string>
					<key>CodecID</key>
					<integer>351359247</integer>
					<key>ConfigData</key>
					<data>
					AWccQAFnHRABZx4hAWcfBAF3HBABdx0BAXce
					FwF3H5ABlxwwAZcdEAGXHoEBlx8EAZcHJAGn
					HCABpx0BAaceoAGnH5ABpwck
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
					<key>WakeConfigData</key>
					<data>
					AZcHJAGnByQ=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Conexant CX20753/4</string>
					<key>CodecID</key>
					<integer>351359249</integer>
					<key>ConfigData</key>
					<data>
					AWccEAFnHUABZx4hAWcfAgF3HCABdx0AAXce
					FwF3H5ABlxwwAZcdkAGXHoEBlx8CAaccQAGn
					HQABpx6gAacfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Conexant CX20753/4</string>
					<key>CodecID</key>
					<integer>351359249</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQEBdx4XAXcfkAFnHBMBZx0QAWce
					IQFnHwMBlxwwAZcdEAGXHoEBlx8DAaccQAGn
					HQEBpx6gAacfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>14</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - CX20753/4 for Lenovo Thinkpad E580</string>
					<key>CodecID</key>
					<integer>351359249</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQABdx4XAXcfkAGnHCABpx0AAace
					pgGnH5ABlxwwAZcdEAGXHoEBlx8AAWccQAFn
					HRABZx4hAWcfAA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres ZeroCross - LG gram 15ZD960-GX5BK</string>
					<key>CodecID</key>
					<integer>351359249</integer>
					<key>ConfigData</key>
					<data>
					AXccIAF3HQEBdx4XAXcfkAGXHDABlx0QAZce
					gQGXHwMBpxxAAacdAQGnHqABpx+QAdccEAHX
					HRAB1x4hAdcfAw==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Conexant CX20755</string>
					<key>CodecID</key>
					<integer>351359251</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQABdx4XAXcfkAGnHCABpx0AAace
					pgGnH5UBhxwwAYcdkAGHHosBhx8CAWccQAFn
					HUABZx4rAWcfAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AQAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Conexant CX20756</string>
					<key>CodecID</key>
					<integer>351359252</integer>
					<key>ConfigData</key>
					<data>
					AWccEAFnHUABZx4hAWcfAQF3HCABdx0AAXce
					EwF3H5ABhxwwAYcdkAGHHqEBhx8CAaccQAGn
					HQABpx6mAacfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - Conexant CX20756</string>
					<key>CodecID</key>
					<integer>351359252</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQEBdx4XAXcfkAGnHCABpx0BAace
					oAGnH5ABlxwwAZcdEAGXHosBlx8CAWccQAFn
					HRABZx4rAWcfAgGHHPABhx0AAYceAAGHH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AQAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - Conexant CX20757</string>
					<key>CodecID</key>
					<integer>351359253</integer>
					<key>ConfigData</key>
					<data>
					AWccEAFnHQABZx4hAWcfAQF3HCABdx0AAXce
					EwF3H5ABhxwwAYcdAAGHHoEBhx8CAaccUAGn
					HQABpx6gAacfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom IDT 92HD87B1/3 by RehabMan</string>
					<key>CodecID</key>
					<integer>287143633</integer>
					<key>ConfigData</key>
					<data>
					AMcegQDHHwM=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>InsanelyDeepak - IDT92HD87B1/3</string>
					<key>CodecID</key>
					<integer>287143633</integer>
					<key>ConfigData</key>
					<data>
					ANccAADXHQAA1x4XANcfmQEXHCABFx0AARce
					oAEXH5kAtxwwALcdQAC3HiEAtx8BAMccQADH
					HRAAxx6AAMcfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom IDT92HD87B2/4 by RehabMan</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AMcegQDHHwMBFx6gARcfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom IDT92HD95 by RehabMan</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccEACnHRAApx4hAKcfAgCnDAIAtxwgALcd
					EAC3HqEAtx8CALcMAgDXHDAA1x0BANceFwDX
					H5AA1wwCAOccQADnHQEA5x6gAOcfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - IDT 92HD66C3/65</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccEACnHUAApx4hAKcfAgC3HCAAtx1AALce
					EwC3H5AAxxwwAMcdkADHHoEAxx8CAOccQADn
					HZAA5x6gAOcfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - IDT 92HD71B7X</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccEACnHRAApx4hAKcfAAC3HCAAtx0QALce
					gQC3HwIAxxwwAMcdAADHHvAAxx9AANccQADX
					HQAA1x4XANcfkADnHFAA5x0QAOceoQDnHyAB
					RxxgAUcdAAFHHvABRx9AAYcccAGHHQABhx6g
					AYcfkAGXHIABlx0AAZce8AGXH0AB5xyQAecd
					EAHnHkYB5x8BAfccoAH3HQAB9x7wAfcfQAIH
					HLACBx0AAgce8AIHH0ACdxzAAncdAAJ3HvAC
					dx9A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Dell Studio 1535 - IDT 92HD73C1X5 by chunnann</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccEACnHRAApx4hAKcfAwDXHCAA1x0BANce
					FwDXH5AA5xwwAOcdEADnHoEA5x8DAPccQAD3
					HRAA9x4BAPcfAwE3HFABNx0BATceoAE3H5A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>19</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Andres ZeroCross - IDT 92HD73C1X5 for Alienware M17X R2</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccEACnHRAApx4hAKcfBADHHCAAxx0QAMce
					gQDHHwQA5xwwAOcdAQDnHhcA5x+QATccQAE3
					HQEBNx6gATcfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - IDT92HD73E1X5 for HP Envy h8 1425eg</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AMccEADHHUAAxx4RAMcfkQCnHCAApx1AAKce
					IQCnHwIA5xwwAOcdkADnHqEA5x+RALccQAC3
					HZAAtx6BALcfAgDXHFAA1x0wANcegQDXHwEA
					9xxgAPcdEAD3HgEA9x8BAQcccAEHHWABBx4B
					AQcfAQEXHIABFx0gARceAQEXHwECJxygAicd
					EAInHkUCJx8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>15</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - IDT 92HD81B1C5</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccIACnHRAApx6BAKcfAgC3HDAAtx0QALce
					IQC3HwIA1xxAANcdAADXHhcA1x+QARccUAEX
					HQABFx6gARcfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Goldfish64 - IDT 92HD81B1C5 for Dell Latitude E6410</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccIACnHRAApx6BAKcfBACnDAIAtxwwALcd
					EAC3HiEAtx8EALcMAgDHHPAAxx0AAMceAADH
					H0AA1xxAANcdAQDXHhcA1x+QANcMAgDnHPAA
					5x0AAOceAADnH0AA9xzwAPcdAAD3HgAA9x9A
					AQcc8AEHHQABBx4AAQcfQAEXHFABFx0BARce
					oAEXH5AB9xzwAfcdAAH3HgAB9x9AAgcc8AIH
					HQACBx4AAgcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - IDT 92HD81B1X5</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccIACnHRAApx6hAKcfAQC3HBAAtx0QALce
					IQC3HwEA1xwwANcdAADXHhcA1x+QAOcc8ADn
					HQAA5x4AAOcfQAD3HEAA9x0AAPceAAD3H0AB
					BxxQAQcdAAEHHgABBx9AARccYAEXHQABFx6j
					ARcf0AH3HHAB9x0AAfceAAH3H0ACBxyAAgcd
					AAIHHgACBx9A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom IDT 92HD81B1X5 by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHRABJx6BAScfBAFHHCABRx0BAUce
					FwFHH5ABRwwCAXcc8AF3HQABdx4AAXcfQAGH
					HPABhx0AAYceAAGHH0ABlxzwAZcdAAGXHgAB
					lx9AAacc8AGnHQABpx4AAacfQAG3HDABtx0B
					AbceoAG3H5AB1xzwAdcdAAHXHgAB1x9AAecc
					8AHnHQAB5x4AAecfQAIXHEACFx0QAhceIQIX
					HwMCFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>RehabMan - IDT 92HD81B1X5</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AMcegQDHHwMBFx6gARcfkA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom IDT 92HD81B1X5 by Sergey_Galan for HP ProBook 4520s</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccIACnHRAApx6BAKcfAQC3HFAAtx0QALce
					IQC3HwEA1xwwANcdAQDXHhAA1x+QAOcc8ADn
					HQAA5x4AAOcfQAD3HPAA9x0AAPceAAD3H0AB
					BxzwAQcdAAEHHgABBx9AARccEAEXHQEBFx6g
					ARcfkAH3HPAB9x0AAfceAAH3H0ACBxzwAgcd
					AAIHHgACBx9AAMcc8ADHHQAAxx4AAMcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>20</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom IDT 92HD81B1X5 by Sergey_Galan for HP DV6-6169er</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AMccIADHHRAAxx6BAMcfAQC3HFAAtx0QALce
					IQC3HwEA9xwwAPcdAQD3HhAA9x+QAOcc8ADn
					HQAA5x4AAOcfQADXHPAA1x0AANceAADXH0AB
					BxzwAQcdAAEHHgABBx9AARccEAEXHQEBFx6g
					ARcfkAH3HPAB9x0AAfceAAH3H0ACBxzwAgcd
					AAIHHgACBx9AAKcc8ACnHQAApx4AAKcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom IDT 92HD81B1X5 by Gujiangjiang for HP Pavilion g4 1000 series</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ARccAAEXHQEBFx6jARcfmQDHHBAAxx0QAMce
					gQDHHwEA1xwgANcdAQDXHhMA1x+ZALccMAC3
					HRAAtx4hALcfAQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - IDT 92HD75B2X5</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccEACnHRAApx4hAKcfAQC3HCAAtx0QALce
					gQC3HwEAxxwwAMcdEADHHqAAxx+QANccQADX
					HQAA1x4RANcfkADnHFAA5x0AAOce8ADnH0AB
					RxxgAUcdAAFHHvABRx9AAYcccAGHHQABhx7w
					AYcfQAHnHIAB5x0AAece8AHnH0AB9xyQAfcd
					AAH3HvAB9x9AAgccoAIHHQACBx7wAgcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - IDT 92HD75B3X5</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ALccEAC3HRAAtx6gALcfkADXHCAA1x0AANce
					FwDXH5AA9xwwAPcdQAD3HiEA9x8BAYccQAGH
					HZABhx6BAYcfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - IDT 92HD75B3X5</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ALccAAC3HQAAtx6nALcfmQDXHBAA1x0AANce
					FwDXH5kA9xwgAPcdQAD3HiEA9x8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - IDT 92HD90BXX</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ALccEAC3HRAAtx4hALcfAACnHCAApx0QAKce
					gQCnHwABFxwwARcdkAEXHqABFx+QANccQADX
					HQAA1x4XANcfkADnHFAA5x0QAOceAQDnHyAA
					9xxgAPcdEAD3HqEA9x8gAQcc8AEHHQABBx4A
					AQcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>vusun123 - IDT 92HD90BXX</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccIACnHRAApx6BAKcfAAC3HBAAtx0QALce
					IQC3HwAA1xxAANcdAADXHhcA1x+QARccMAEX
					HQABFx6gARcf0A==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - IDT 92HD91BXX </string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccAACnHRAApx6BAKcfAQC3HBAAtx0QALce
					IQC3HwMAxxwgAMcdAADHHgAAxx9JARccMAEX
					HQABFx6gARcfmQDXHEAA1x0BANceFwDXH5kA
					5xxQAOcdEADnHgEA5x8jAQccYAEHHQABBx4A
					AQcfSQH3HHAB9x0AAfceAAH3H0kCBxyAAgcd
					AAIHHgACBx9J
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>RehabMan - IDT 92HD91BXX for HP Envy</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccAACnHRAApx6BAKcfAQC3HBAAtx0QALce
					IQC3HwMAxxwgAMcdAADHHgAAxx9JARccMAEX
					HQABFx6gARcfmQD3HEAA9x0BAPceFwD3H5kA
					5xxQAOcdEADnHgEA5x8jAQccYAEHHQABBx4A
					AQcfSQH3HHAB9x0AAfceAAH3H0kCBxyAAgcd
					AAIHHgACBx9J
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>MacPeet - IDT92HD91BXX for HP Envy 6 1171-SG</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ALccEAC3HRAAtx4hALcfAAC3DAIAxxwgAMcd
					EADHHoEAxx8AARccMAEXHQABFx6jARcfmQDX
					HEAA1x0AANceEADXH5AA1wwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>jl4c - IDT 92HD91BXX for HP Envy</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ALccIAC3HRAAtx4hALcfAwD3HDIA9x0BAPce
					FwD3H5ABFxwQARcdAQEXHqYBFx+XANcc8ADX
					HQAA1x4AANcfQAEHHPABBx0AAQceAAEHH0AA
					pxzwAKcdAACnHgAApx9AAMcc8ADHHQAAxx4A
					AMcfQADnHPAA5x0AAOceAADnH0AB9xzwAfcd
					AAH3HgAB9x9AAgcc8AIHHQACBx4AAgcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>33</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>macish - IDT 92HD91BXX for HP Elitebook G1</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccAACnHRAApx6BAKcfAQC3HBAAtx0QALce
					IQC3HwMAxxwgAMcdAADHHgAAxx9JARccMAEX
					HQABFx6gARcfmQD3HEAA9x0BAPceFwD3H5kA
					5xxQAOcdEADnHgEA5x8jAQccYAEHHQABBx4A
					AQcfSQH3HHAB9x0AAfceAAH3H0kCBxyAAgcd
					AAIHHgACBx9J
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>84</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom - IDT 92HD93BXX Dell Latitude E6430</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccIACnHRAApx6BAKcfAACnDAIAtxwQALcd
					EAC3HiEAtx8AALcMAgDXHEAA1x0BANceFwDX
					H5AA1wwCAOccUADnHRAA5x4BAOcfIADnDAIA
					9xxgAPcdEAD3HoEA9x8gAQcc8AEHHQABBx4A
					AQcfQAEXHDABFx0BARceoAEXH5A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - IDT 92HD99BXX </string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccEACnHZAApx6BAKcfAgC3HCAAtx1AALce
					IQC3HwIAxxwwAMcdAADHHvAAxx9AANccQADX
					HQAA1x4TANcf0AD3HFAA9x0AAPce8AD3H0AB
					FxxgARcdAAEXHqABFx+QANcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - IDT 92HD87B1</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccIACnHRAApx6hAKcfAQC3HBAAtx0QALce
					IQC3HwEA1xwwANcdAADXHhcA1x+QAOcc8ADn
					HQAA5x4AAOcfQAD3HEAA9x0AAPceAAD3H0AB
					BxxQAQcdAAEHHgABBx9AARccYAEXHQABFx6j
					ARcf0AH3HHAB9x0AAfceAAH3H0ACBxyAAgcd
					AAIHHgACBx9A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Goldfish64 - STAC9200 for Dell Precision 390, Latitude D520</string>
					<key>CodecID</key>
					<integer>2206496400</integer>
					<key>ConfigData</key>
					<data>
					AIcc8ACHHQAAhx4AAIcfQACXHPAAlx0AAJce
					AACXH0AA1xwQANcdEADXHiEA1x8CAOccIADn
					HQEA5x4QAOcfkAD3HDAA9x0BAPceoAD3H5AB
					BxxAAQcdEAEHHoEBBx8CARcc8AEXHQABFx4A
					ARcfQAEnHPABJx0AASceAAEnH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Goldfish64 - STAC9205 for Dell Inspiron 1520, Latitude D630</string>
					<key>CodecID</key>
					<integer>2206496416</integer>
					<key>ConfigData</key>
					<data>
					AKccIACnHRAApx4hAKcfAwC3HEAAtx0QALce
					gQC3HwMAxxzwAMcdAADHHgAAxx9AANccEADX
					HQEA1x4QANcfkADnHPAA5x0AAOceAADnH0AA
					9xzwAPcdAAD3HgAA9x9AAUcc8AFHHQABRx4A
					AUcfQAFnHPABZx0AAWceAAFnH0ABdxwwAXcd
					AQF3HqABdx+QAYcc8AGHHQABhx4AAYcfQAIX
					HPACFx0AAhceAAIXH0ACJxzwAicdAAInHgAC
					Jx9A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>STAC9872AK for Sony VGN-FZ11MR by ctich</string>
					<key>CodecID</key>
					<integer>2206496354</integer>
					<key>ConfigData</key>
					<data>
					APccEAD3HQEA9x4XAPcfkACnHCAApx1AAKce
					IQCnHwQBRxw+AUcdkAFHHqABRx+QARccQAEX
					HREBFx5WARcfGA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>VIA VT1705 ECS H81H3-M4 (1.0A) by Andres ZeroCross</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AkccEAJHHQACRx4AAkcfAQKHHCAChx1AAoce
					IQKHHwICtxwwArcdkAK3HoECtx8BAwccQAMH
					HQADBx6gAwcfkAJHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>21</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - VIA VT1802</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AkccEAJHHQACRx4TAkcfkAJXHCACVx1AAlce
					IQJXHwEClxxAApcdAAKXHqAClx+QArccYAK3
					HZACtx6BArcfAgLXHHAC1x0QAtceRALXHwAC
					RwwCAlcMAw==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>3</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>VIA VT1802 for hasee k650d</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AkccEAJHHQACRx4TAkcfkAJXHCACVx1AAlce
					IQJXHwEClxxAApcdAAKXHqAClx+QArccYAK3
					HZACtx6BArcfAgLXHHAC1x0QAtceRALXHwAC
					RwwCAlcMAw==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>65</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ChalesYu - VIA VT1802</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AkccQAJHHQACRx4XAkcfkAJHDAICVxxQAlcd
					EAJXHiECVx8CAlcMAgMHHBADBx0AAwceoAMH
					H5A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>33</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - VIA VT2021</string>
					<key>CodecID</key>
					<integer>285606977</integer>
					<key>ConfigData</key>
					<data>
					IkccECJHHUAiRx4BIkcfASKHHCAihx1AIoce
					ISKHHwEilxwwIpcdkCKXHqEilx8CIqccQCKn
					HTAipx6BIqcfASK3HFAitx2QIrcegSK3HwEi
					5xxgIucdECLnHkUi5x8A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>5</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Mirone - VIA VT2021</string>
					<key>CodecID</key>
					<integer>285606977</integer>
					<key>ConfigData</key>
					<data>
					IkccECJHHUAiRx4RIkcfASJXHCAiVx0QIlce
					ASJXHwEiZxwwImcdYCJnHgEiZx8BInccQCJ3
					HSAidx4BIncfASKHHFAihx1AIoceISKHHwEi
					lxxgIpcdkCKXHqEilx8CIqcccCKnHTAipx6B
					IqcfASK3HIAitx2QIrcegSK3HwEi5xygIucd
					ECLnHkUi5x8A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>7</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>SonicBSV - VIA VT2020/2021</string>
					<key>CodecID</key>
					<integer>285606977</integer>
					<key>ConfigData</key>
					<data>
					Ihcc8CIXHQAiFx4AIhcfQCJHHBAiRx1AIkce
					ESJHHwEiRwwCIlcc8CJXHQAiVx4AIlcfQCJn
					HPAiZx0AImceACJnH0AidxzwIncdACJ3HgAi
					dx9AIoccICKHHUAihx4hIocfASKXHEAilx2Q
					IpceoCKXH5AilwchIqccgCKnHTAipx6BIqcf
					ASK3HPAitx0AIrceACK3H0AixxzwIscdACLH
					HgAixx9AItcc8CLXHQAi1x4AItcfQCLnHJAi
					5x1hIuceSyLnHwEi9xzwIvcdACL3HgAi9x9A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>9</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>HafidzRadhival - DELL Vostro 5468 ALC256 (3246)</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQEBJx6mAScfkAFHHDABRx0BAUce
					FwFHH5ABRwwCAZccIAGXHRABlx6BAZcfAgIX
					HBACFx0QAhceIQIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Enrico - GA-Z77X-D3Hrev1.0 - VIA VT2020/2021</string>
					<key>CodecID</key>
					<integer>285606977</integer>
					<key>ConfigData</key>
					<data>
					IkccECJHHUAiRx4RIkcfASJXHCAiVx0QIlce
					ASJXHwEiZxwwImcdYCJnHgEiZx8BInccQCJ3
					HSAidx4BIncfASKHHFAihx1AIoceISKHHwEi
					lxxgIpcdkCKXHqEilx8CIqcccCKnHTAipx6B
					IqcfASK3HIAitx2QIrcegSK3HwEi5xygIucd
					ECLnHkUi5x8A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AQAAAA==
					</data>
					<key>Codec</key>
					<string>Custom CX20757 Lenovo G510 by Z39</string>
					<key>CodecID</key>
					<integer>351359253</integer>
					<key>ConfigData</key>
					<data>
					AWccQAFnHRABZx4hAWcfAQF3HBABdx0BAXce
					FwF3H5ABhxzwAYcdAAGHHgABhx9AAZccMAGX
					HRABlx6BAZcfAQGnHCABpx0BAacepwGnH5A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>28</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Z  Realtek ALC285 for thinkpad p52</string>
					<key>CodecID</key>
					<integer>283902597</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfmQFHDAICFxwgAhcd
					EAIXHiECFx8EAhcMAgEnHDABJx0BAScepgEn
					H5kBlxxAAZcdEAGXHoEBlx8B
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>52</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Lancet-X—Realtek ALC295/ALC3254 for HP OMEN 15-AX000</string>
					<key>CodecID</key>
					<integer>283902613</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfmQFHHEABRx0BAUce
					FwFHH5kBlxwgAZcdEAGXHoEBlx8AAhccUAIX
					HRACFx4hAhcfAAFHDAICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>23</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>ALC269 for Thunderobot-G7000S-9300H by Phoenix-L</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUEBRx4XAUcfmQFHDAIBVxwgAVcd
					QAFXHiEBVx8CAVcMAgGHHDABhx2QAYcegQGH
					HwIBlxw/AZcdkAGXHoEBlx8CASccQAEnHZEB
					Jx6mAScfmQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>77</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>zty199 - ALC295 for HP Pavilion / OMEN-2</string>
					<key>CodecID</key>
					<integer>283902613</integer>
					<key>ConfigData</key>
					<data>
					ASccAAEnHQEBJx6mAScfmQFHHEABRx0BAUce
					FwFHH5kBRwwCAZccgAGXHRABlx6LAZcfAAIX
					HMACFx0QAhceKwIXHwACFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>24</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC662v3 for Lenovo M415-D339 by Eric</string>
					<key>CodecID</key>
					<integer>283903586</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFHDAIBtxwgAbcd
					EAG3HiEBtx8CAbcMAgGHHDABhx2QAYcehgGH
					HwEBlxxAAZcdEAGXHoYBlx8CAaccPwGnHTAB
					px6BAacfAQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>66</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Asus x430_s4300FN by fangf2018</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccMAEnHQEBJx6mAScfkAFHHBABRx0BAUce
					FwFHH5ABRwwCAZccQAGXHRABlx6BAZcfAgIX
					HCACFx0QAhceIQIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>77</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Asus x430_s4300FN by fangf2018 (mic in and line in  mic in separated)</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ASccQAEnHQEBJx6mAScfkAFHHDABRx0BAUce
					FwFHH5ABRwwCAZccIAGXHRABlx6BAZcfAgIX
					HBACFx0QAhceIQIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>88</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom IDT92HD95 - LenovoG710 by Svilen Ivanov layout14</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccEACnHRAApx4hAKcfAQCnDAIAtxwgALcd
					EAC3HoEAtx8BALcMAgDHHDAAxx0BAMceoADH
					H5AAxwwCANccQADXHQEA1x4QANcfkADXDAIA
					5xzwAOcdAADnHgAA5x9AAPcc8AD3HQAA9x4A
					APcfQAGHHPABhx0AAYceAAGHH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>14</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC297 for MSI Z490-A Pro by MathCampbell</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFHDAIBZxwRAWcd
					YAFnHhEBZx8BAVccEgFXHRABVx4RAVcfAQF3
					HBQBdx0gAXceEQF3HwEBtxwgAbcdQAG3HiEB
					tx8CAbcMAgGHHDABhx2QAYceoAGHH5EBpxw/
					AacdMAGnHoEBpx8BARccQAEXHdUBFx43ARcf
					QAGXHEABlx2QAZcegAGXHwIB1xxrAdcd9gHX
					HioB1x9A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>69</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgFnDAABVwwAAXcMAAG3DAIBhwwAAacM
					AAEXDAABlwwAAdcMAA==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>b0ltun/agasecond - Realtek ALC256 (3246) for Hasee KingBook X57S1</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AbccEAG3HQABtx4XAbcfkAG3DAICFwwCAUcM
					AgGXByUCBQBFAgTQiQIFABsCBAxLAgUAEAIE
					ACA=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>70</integer>
					<key>WakeConfigData</key>
					<data>
					AbcMAgFHDAICFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>sweet3c - ALC289 for XPS 9500 4k </string>
					<key>CodecID</key>
					<integer>283902601</integer>
					<key>ConfigData</key>
					<data>
					ASccIAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxxAAUcdAQFHHhcBRx+QAUcMAgFn
					HPABZx0AAWceAAFnH0ABdxxgAXcdIQF3HhEB
					dx+QAXcMAgGHHPABhx0AAYceAAGHH0ABlxxw
					AZcdEAGXHosBlx8CAacc8AGnHQABpx4AAacf
					QAG3HPABtx0AAbceAAG3H0AB1xzwAdcdAAHX
					HgAB1x9AAecc8AHnHQAB5x4AAecfQAIXHDAC
					Fx0QAhceKwIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>93</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>GeorgeWan - ALC897 for MSI-Z590-A-PRO</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ARcc8AEXHQABFx4AARcfQAEnHPABJx0AASce
					AAEnH0ABRxwQAUcdQAFHHhEBRx+QAUcMAgFX
					HCABVx0QAVceAQFXHwEBZxwwAWcdYAFnHgEB
					Zx8BAXccQAF3HSABdx4BAXcfAQGHHFABhx2Q
					AYceoAGHH5ABlxxwAZcdkAGXHoEBlx8CAacc
					YAGnHTABpx6BAacfAQG3HIABtx1AAbceIQG3
					HwIBtwwCAccc8AHHHQABxx4AAccfQAHnHPAB
					5x0AAeceAAHnH0AB9xzwAfcdAAH3HgAB9x9A
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC897 by Sergey_Galan  for GIGABYTE Z590 Gaming X</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFHDAIBVxwgAVcd
					EAFXHgEBVx8BAWccMAFnHWABZx4BAWcfAQF3
					HPABdx0AAXceAAF3H0ABhxxAAYcdkAGHHqAB
					hx+QAZccYAGXHZABlx6BAZcfAgGnHFABpx0w
					AacegQGnHwEBtxxwAbcdQAG3HiEBtx8CAbcM
					AgEXHJABFx1hARceSwEXHwEB9xzwAfcdAAH3
					HgAB9x9AAecc8AHnHQAB5x4AAecfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC897 by Sergey_Galan  for GIGABYTE Z590M</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4RAUcfAQFHDAIBVxzwAVcd
					AAFXHgABVx9AAWcc8AFnHQABZx4AAWcfQAF3
					HPABdx0AAXceAAF3H0ABhxxAAYcdkAGHHqAB
					hx+QAZccYAGXHZABlx6BAZcfAgGnHFABpx0w
					AacegQGnHwEBtxxwAbcdQAG3HiEBtx8CAbcM
					AgEXHJABFx1hARceSwEXHwEB9xzwAfcdAAH3
					HgAB9x9AAecc8AHnHQAB5x4AAecfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>11</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Conexant CX11970 (CX8400) for Acer Swift 3 SF313 (Ice Lake) by b0ltun</string>
					<key>CodecID</key>
					<integer>351346896</integer>
					<key>ConfigData</key>
					<data>
					AXccEAF3HQEBdx4XAXcfkQF3DAIBlxwgAZcd
					kAGXHoEBlx8EAWccEAFnHUABZx4hAWcfBAFn
					DAIBlwckAaccIAGnHQEBpx6gAacfkAGnByQ=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>13</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAgFnDAIBlwckAacHJA==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC285 for Yoga S740 14IIL by frozenzero123</string>
					<key>CodecID</key>
					<integer>283902597</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4TAUcfkgFHDAICFxwgAhcd
					EAIXHgECFx8DAhcMAgGXHEABlx0QAZcegQGX
					HwM=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>88</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC235 for Lenovo M920x by meloay</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAF3HPABdx0AAXce
					AAF3H0ABhxzwAYcdAAGHHgABhx9AAZcccAGX
					HRABlx6hAZcfAgGnHCABpx0QAacegQGnHwIB
					txyAAbcdEAG3HgEBtx8BAdcc8AHXHQAB1x4A
					AdcfQAHnHPAB5x0AAeceAAHnH0ACFxxQAhcd
					EAIXHiECFx8CAUcMAgG3DAICFwwCAhcHwAIX
					CIIBlwck
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>72</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwwCAhcHwAIXCIIBlwck
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>NUC8I5BEH JUST MIC</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					ASccUAEnHQEBJx6jAScfkg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>88</integer>
					<key>WakeConfigData</key>
					<data>
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC 292 for Dell M4800 with Dock</string>
					<key>CodecID</key>
					<integer>283902610</integer>
					<key>Comment</key>
					<string>ALC 292 for Dell M4800 with Dock</string>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAE3HPABNx0AATce
					AAE3H0ABRxxAAUcdAQFHHhcBRx+QAUcMAgFX
					HFABVx1AAVceKwFXHwIBVwwCAWccgAFnHUAB
					Zx4BAWcfAQFnDAIBhxwgAYcdIAGHHoEBhx8C
					AZccIAGXHZABlx6BAZcfAQGnHCABpx0QAace
					+wGnHwIBtxzwAbcdAAG3HgABtx9AAdcc8AHX
					HQAB1x4AAdcfQAHnHPAB5x0AAeceAAHnH0A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>59</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgFXDAIBZwwCAacHJA==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ONDA H510D4 IPC ALC897</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AUccQAFHHUABRx4RAUcfAQFHDAIBhxwgAYcd
					UAGHHoEBhx8BAZcccAGXHSABlx6hAZcfAgG3
					HFABtx0wAbceIQG3HwIBtwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>77</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>james090500 - Dell OptiPlex 9020 AIO</string>
					<key>CodecID</key>
					<integer>283902592</integer>
					<key>ConfigData</key>
					<data>
					AVccYAFXHRABVx4hAVcfAQFXDAIBtxxQAbcd
					AAG3HhcBtx+QASccMAEnHQABJx6gAScfkAGn
					HEABpx0QAacegQGnHwI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>18</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>5T33Z0 - Lenoco T530 with Dock 4337/4338</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQEBRx4XAUcfkAFHDAIBVxwgAVcd
					EAFXHiEBVx8DAVcMAgEnHDABJx0BASceoAEn
					H5ABhxxAAYcdEAGHHoEBhx8DAZccYAGXHZAB
					lx6BAZcfJAG3HHABtx1AAbceAQG3HyQ=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>39</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgFXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC1220 for MSI GE73 Raider RGB 8RF by Ardhi96</string>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQABJx6gAScfkAFHHCABRx0AAUce
					FwFHH5ABRwwCAVcc8AFXHQABVx4AAVcfQAFn
					HPABZx0AAWceAAFnH0ABdxzwAXcdAAF3HgAB
					dx9AAYcc8AGHHQABhx4AAYcfQAGXHDABlx0Q
					AZcegQGXHwEBpxzwAacdAAGnHgABpx9AAbcc
					QAG3HRABtx4hAbcfAQG3DAIB1xzwAdcdAAHX
					HgAB1x9AAecc8AHnHQAB5x4AAecfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>25</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC235 for Lenovo Tianyi 510 pro-18ICB Desktop PC by hgs v1</string>
					<key>CodecID</key>
					<integer>283902517</integer>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHEABRx0BAUce
					FwFHH5ABRwwCAXcc8AF3HQABdx4AAXcfQAGH
					HPABhx0AAYceAAGHH0ABlxxwAZcdEAGXHqEB
					lx8CAaccEAGnHQEBpx6jAacfkAG3HIABtx1A
					AbceAQG3HwEBtwwCAdcc8AHXHQAB1x4AAdcf
					QAHnHPAB5x0AAeceAAHnH0ACFxxQAhcdEAIX
					HiECFx8CAhcMAg==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>36</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAICFwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC222 for Lenovo Tianyi 510s-07IMB Desktop PC by hgs v1</string>
					<key>CodecID</key>
					<integer>283902498</integer>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHIABRx1AAUce
					AQFHHwEBdxxAAXcdAQF3HhcBdx+QAXcMAgGH
					HBABhx0BAYceowGHH5ABlxxwAZcdEAGXHqEB
					lx8gAacc8AGnHQABpx4AAacfQAG3HPABtx0A
					AbceAAG3H0AB1xzwAdcdAAHXHgAB1x9AAecc
					8AHnHQAB5x4AAecfQAIXHFACFx0QAhceIQIX
					HwI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
					<key>WakeConfigData</key>
					<data>
					AXcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Realtek ALC255(3234) for Asus N752VX by Feartech</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					AZccEAGXHZABlx6AAZcfAQGXByQBRxwgAUcd
					EAFHHhcBRx+QAUcMAgIXHDACFx0QAhceIQIX
					HwECFwwCAhcIgwEnHMABJx0AASceoAEnH5A=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>22</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAIBlwcl
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Littlesum - Realtek ALC256 (3246)  for Intel NUC9 </string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AZccMAGXHRABlx6gAZcfkgIXHBACFx0QAhce
					IQIXHwICFwwCAbccQAG3HRABtx4RAbcfAQG3
					DAIB5xwgAecdEQHnHkUB5x8BAUcc8AFHHREB
					Rx4RAUcfQQFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>68</integer>
					<key>WakeConfigData</key>
					<data>
					AhcMAgG3DAIBRwwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>juniorcaesar - Acer Aspire A315-56-327T ALC255</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6gAScfkAFHHCABRx0BAUce
					FwFHH5ABRwwCAhccMAIXHRACFx4hAhcfAgIX
					DAICFwiD
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>69</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAICFwiD
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Imoize - Realtek ALC255 for Acer Nitro 5 AN515-52-73Y8</string>
					<key>CodecID</key>
					<integer>283902549</integer>
					<key>ConfigData</key>
					<data>
					ASccEAEnHQEBJx6mAScfkAFHHCABRx0BAUce
					FwFHH5ABRwwCAZccMAGXHRABlx6LAZcfAgIX
					HEACFx0QAhceKwIXHwICFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>37</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgIXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283906592</integer>
					<key>CodecName</key>
					<string>hgsshaanxi- ALC1220 for Gigabyte Z490 Aorus Master</string>
					<key>ConfigData</key>
					<data>
					AScc8AEnHQABJx4AAScfQAFHHBABRx1AAUce
					IQFHHwIBRwwCAVccIAFXHRABVx4BAVcfAQFn
					HDABZx1gAWceAQFnHwEBdxzwAXcdAAF3HgAB
					dx9AAYccQAGHHZABhx6gAYcfkQGXHGABlx2Q
					AZcegQGXHwIBpxxQAacdMAGnHoEBpx8BAbcc
					cAG3HUABtx4RAbcfkAG3DAIB1xzwAdcdAAHX
					HgAB1x9AAecckAHnHSEB5x5FAecfkQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>18</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>UHDbits - Realtek ALC283/ALC3239 for the Lenovo ThinkCentre M73 Tiny</string>
					<key>CodecID</key>
					<integer>283902595</integer>
					<key>ConfigData</key>
					<data>
					IUccACFHHRAhRx4XIUcfkCFHDAIhlxwQIZcd
					ECGXHqAhlx+CIaccICGnHRAhpx6AIacfAiIX
					HDAiFx0QIhceISIXHwIiFwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>73</integer>
					<key>WakeConfigData</key>
					<data>
					IhcMAiFHDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>jayveeballesteros - ALC269 for Fujitsu Esprimo D552</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>ConfigData</key>
					<data>
					IaccECGnHUAhpx4BIacfASIXHCAiFx0QIhce
					ISIXHwIhhxxAIYcdkCGHHqEhhx8CIZccUCGX
					HZAhlx6hIZcfkQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>38</integer>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>liangyi - ALC897 for MSI PRO B760M-P DDR4</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					ARcc8AEXHQABFx4AARcfQAEnHPABJx0AASce
					AAEnH0ABRxwQAUcdQAFHHhEBRx8BAUcMAgFX
					HPABVx0AAVceAAFXH0ABZxzwAWcdAAFnHgAB
					Zx9AAXcc8AF3HQABdx4AAXcfQAGHHDABhx2Q
					AYceoAGHH5EBlxxwAZcdkAGXHoABlx8BAacc
					UAGnHTABpx6BAacfAQG3HCABtx1AAbceIQG3
					HwEBtwwCAccc8AHHHQABxx4AAccfQAHXHPAB
					1x0AAdceAAHXH0AB5xzwAecdAAHnHgAB5x9A
					Afcc8AH3HQAB9x4AAfcfQA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>98</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Custom ALC897 by Marcos_Vinicios  for HUANANZHI QD4</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AdccEAHXHcAB1x4kAdcfQAG3HCABtx1AAbce
					IQG3HwIBtwwCAYccMAGHHZABhx6gAYcfkQEX
					HEABFx3AARceFwEXH0ABRxxQAUcdQAFHHhEB
					Rx8BAUcMAgGnHGABpx0wAacegQGnHwEBlxxw
					AZcdkAGXHoEBlx8C
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>99</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>aa820t - Realtek ALC269VC for Lenovo G480</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4XAUcfmQFHDAIBhxwgAYcd
					EAGHHoEBhx8EAZccMAGXHQABlx6nAZcfmQFX
					HEABVx0QAVceIQFXHwQBVwwC
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>138</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgFXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>IDT 92HD81B1X5 by SkyrilHD for HP Elitebook 8x70 series</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AKccMACnHRAApx4BAKcfIQCnDAIAtxwhALcd
					EAC3HiEAtx8EALcMAgDHHBEAxx0QAMceoQDH
					HwQAxwwCANccIADXHQEA1x4TANcfmQDXDAIA
					5xzwAOcdAADnHvAA5x9AAOcMAgD3HBAA9x0Q
					APcegQD3HyEA9wwCARccAAEXHQEBFx6jARcf
					mQ==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>76</integer>
					<key>WakeConfigData</key>
					<data>
					AKcMAgC3DAIAxwwCANcMAgDnDAIA9wwC
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>lshbluesky - Realtek ALC256 for Samsung Galaxy Book NT750XDA-KF59U</string>
					<key>CodecID</key>
					<integer>*********</integer>
					<key>ConfigData</key>
					<data>
					AhccIAIXHRACFx4hAhcfAAIXDAIBlxwwAZcd
					EAGXHqABlx+QAUccUAFHHQABRx4XAUcfkAFH
					DAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>38</integer>
					<key>WakeConfigData</key>
					<data>
					AhcMAgFHDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Ar4eR07 - ALC269VB for Tecno T1 IceLake</string>
					<key>ConfigData</key>
					<data>
					AhccIAIXHRACFx4hAhcfBAEnHDABJx0AASce
					pgEnH5ABhxxAAYcdkAGHHoEBhx8CAUccYAFH
					HQABRx4XAUcfkAFHDAI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>37</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAg==
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>Conexant CX8400 for Zbook G5 - theroadw</string>
					<key>CodecID</key>
					<integer>351346896</integer>
					<key>ConfigData</key>
					<data>
					AWccEAFnHRABZx4hAWcfBAFnDAIBlxwgAZcd
					EAGXHoEBlx8EAaccMAGnHQEBpx6mAacfkAHX
					HEAB1x0BAdceFwHXH5kB1wwCAZcHJA==
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>14</integer>
					<key>WakeConfigData</key>
					<data>
					AWcMAgHXDAIBlwck
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>AFGLowPowerState</key>
					<data>
					AwAAAA==
					</data>
					<key>Codec</key>
					<string>ALC269</string>
					<key>CodecID</key>
					<integer>283902569</integer>
					<key>Comment</key>
					<string>Talha - ALC269VB for Acer Aspire Z3-715</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHQABRx4QAUcfQAFHDAIBVxwgAVcd
					EAFXHiEBVx8BAVcMAgGnHDABpx0AAaceFwGn
					H5ABJxxAAScdAAEnHqABJx+Q
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>89</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgFXDAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
				<dict>
					<key>CodecID</key>
					<integer>283904768</integer>
					<key>CodecName</key>
					<string>mobilestebu - Realtek ALCS1200A for ASUS TUF-Z390M-Gaming (based on owen0o0 layout 11)</string>
					<key>ConfigData</key>
					<data>
					AUccEAFHHUABRx4BAUcfAQFHDAIBtxwgAbcd
					QAG3HiEBtx8CAbcMAgEXHDABFx0BARceRgEX
					H5ABhxxQAYcdkAGHHqEBhx8BAaccXwGnHTAB
					px6BAacfAQGXHGABlx2QAZceoQGXHwI=
					</data>
					<key>FuncGroup</key>
					<integer>1</integer>
					<key>LayoutID</key>
					<integer>12</integer>
					<key>WakeConfigData</key>
					<data>
					AUcMAgG3DAI=
					</data>
					<key>WakeVerbReinit</key>
					<true/>
				</dict>
			</array>
			<key>IOClass</key>
			<string>AppleALC</string>
			<key>IOMatchCategory</key>
			<string>AppleALC</string>
			<key>IOProviderClass</key>
			<string>IOResources</string>
			<key>IOResourceMatch</key>
			<string>IOKit</string>
		</dict>
	</dict>
	<key>LSMinimumSystemVersion</key>
	<string>10.6</string>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright © 2017 vit9696. All rights reserved.</string>
	<key>OSBundleCompatibleVersion</key>
	<string>1.0</string>
	<key>OSBundleLibraries</key>
	<dict>
		<key>as.vit9696.Lilu</key>
		<string>1.2.0</string>
		<key>com.apple.iokit.IOPCIFamily</key>
		<string>1.0.0b1</string>
		<key>com.apple.kpi.bsd</key>
		<string>8.0.0</string>
		<key>com.apple.kpi.iokit</key>
		<string>8.0.0</string>
		<key>com.apple.kpi.libkern</key>
		<string>8.0.0</string>
		<key>com.apple.kpi.mach</key>
		<string>8.0.0</string>
		<key>com.apple.kpi.unsupported</key>
		<string>8.0.0</string>
	</dict>
	<key>OSBundleRequired</key>
	<string>Root</string>
</dict>
</plist>
