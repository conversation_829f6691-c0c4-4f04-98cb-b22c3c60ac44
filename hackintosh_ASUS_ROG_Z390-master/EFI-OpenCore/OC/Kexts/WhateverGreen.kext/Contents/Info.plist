<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>23H222</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleExecutable</key>
	<string>WhateverGreen</string>
	<key>CFBundleIdentifier</key>
	<string>as.vit9696.WhateverGreen</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>WhateverGreen</string>
	<key>CFBundlePackageType</key>
	<string>KEXT</string>
	<key>CFBundleShortVersionString</key>
	<string>1.6.9</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CFBundleVersion</key>
	<string>1.6.9</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string></string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>14.2</string>
	<key>DTSDKBuild</key>
	<string>23C53</string>
	<key>DTSDKName</key>
	<string>macosx14.2</string>
	<key>DTXcode</key>
	<string>1520</string>
	<key>DTXcodeBuild</key>
	<string>15C500b</string>
	<key>IOKitPersonalities</key>
	<dict>
		<key>NVHDAEnabler</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>as.vit9696.WhateverGreen</string>
			<key>IOClass</key>
			<string>NVHDAEnabler</string>
			<key>IOMatchCategory</key>
			<string>IOFramebuffer</string>
			<key>IOPCIClassMatch</key>
			<string>0x03000000&amp;0xff000000</string>
			<key>IOPCIMatch</key>
			<string>0x000010de&amp;0x0000ffff</string>
			<key>IOProbeScore</key>
			<integer>300000</integer>
			<key>IOProviderClass</key>
			<string>IOPCIDevice</string>
		</dict>
		<key>as.vit9696.WhateverGreen</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>as.vit9696.WhateverGreen</string>
			<key>IOClass</key>
			<string>WhateverGreen</string>
			<key>IOMatchCategory</key>
			<string>WhateverGreen</string>
			<key>IOProviderClass</key>
			<string>IOResources</string>
			<key>IOResourceMatch</key>
			<string>IOKit</string>
		</dict>
	</dict>
	<key>LSMinimumSystemVersion</key>
	<string>10.6</string>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright © 2018 vit9696. All rights reserved.</string>
	<key>OSBundleCompatibleVersion</key>
	<string>1.0</string>
	<key>OSBundleLibraries</key>
	<dict>
		<key>as.vit9696.Lilu</key>
		<string>1.2.0</string>
		<key>com.apple.iokit.IOPCIFamily</key>
		<string>1.0.0b1</string>
		<key>com.apple.kpi.bsd</key>
		<string>10.0.0</string>
		<key>com.apple.kpi.dsep</key>
		<string>10.0.0</string>
		<key>com.apple.kpi.iokit</key>
		<string>10.0.0</string>
		<key>com.apple.kpi.libkern</key>
		<string>10.0.0</string>
		<key>com.apple.kpi.mach</key>
		<string>10.0.0</string>
		<key>com.apple.kpi.unsupported</key>
		<string>10.0.0</string>
	</dict>
	<key>OSBundleRequired</key>
	<string>Root</string>
</dict>
</plist>
