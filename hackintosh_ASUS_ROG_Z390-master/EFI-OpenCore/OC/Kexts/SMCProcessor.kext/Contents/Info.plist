<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>21H1320</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleExecutable</key>
	<string>SMCProcessor</string>
	<key>CFBundleIdentifier</key>
	<string>as.vit9696.SMCProcessor</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>SMCProcessor</string>
	<key>CFBundlePackageType</key>
	<string>KEXT</string>
	<key>CFBundleShortVersionString</key>
	<string>1.3.4</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CFBundleVersion</key>
	<string>1.3.4</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string>13F100</string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>12.3</string>
	<key>DTSDKBuild</key>
	<string>21E226</string>
	<key>DTSDKName</key>
	<string>macosx12.3</string>
	<key>DTXcode</key>
	<string>1341</string>
	<key>DTXcodeBuild</key>
	<string>13F100</string>
	<key>IOKitPersonalities</key>
	<dict>
		<key>as.vit9696.SMCProcessor</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>as.vit9696.SMCProcessor</string>
			<key>IOClass</key>
			<string>SMCProcessor</string>
			<key>IOMatchCategory</key>
			<string>SMCProcessor</string>
			<key>IONameMatch</key>
			<string>processor</string>
			<key>IOPropertyMatch</key>
			<dict>
				<key>processor-index</key>
				<integer>0</integer>
			</dict>
			<key>IOProviderClass</key>
			<string>IOACPIPlatformDevice</string>
			<key>IOResourceMatch</key>
			<string>ACPI</string>
		</dict>
	</dict>
	<key>LSMinimumSystemVersion</key>
	<string>10.7</string>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright © 2018 vit9696. All rights reserved.</string>
	<key>OSBundleCompatibleVersion</key>
	<string>1.0</string>
	<key>OSBundleLibraries</key>
	<dict>
		<key>as.vit9696.Lilu</key>
		<string>1.2.0</string>
		<key>as.vit9696.VirtualSMC</key>
		<string>1.0.0</string>
		<key>com.apple.iokit.IOACPIFamily</key>
		<string>1.0.0d1</string>
		<key>com.apple.kpi.bsd</key>
		<string>11.0.0</string>
		<key>com.apple.kpi.dsep</key>
		<string>11.0.0</string>
		<key>com.apple.kpi.iokit</key>
		<string>11.0.0</string>
		<key>com.apple.kpi.libkern</key>
		<string>11.0.0</string>
		<key>com.apple.kpi.mach</key>
		<string>11.0.0</string>
		<key>com.apple.kpi.unsupported</key>
		<string>11.0.0</string>
	</dict>
	<key>OSBundleRequired</key>
	<string>Root</string>
</dict>
</plist>
