<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>23F79</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleExecutable</key>
	<string>BlueToolFixup</string>
	<key>CFBundleIdentifier</key>
	<string>as.acidanthera.BlueToolFixup</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>BlueToolFixup</string>
	<key>CFBundlePackageType</key>
	<string>KEXT</string>
	<key>CFBundleShortVersionString</key>
	<string>2.6.9</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CFBundleVersion</key>
	<string>2.6.9</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string></string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>14.5</string>
	<key>DTSDKBuild</key>
	<string>23F73</string>
	<key>DTSDKName</key>
	<string>macosx14.5</string>
	<key>DTXcode</key>
	<string>1540</string>
	<key>DTXcodeBuild</key>
	<string>15F31d</string>
	<key>IOKitPersonalities</key>
	<dict>
		<key>BlueToolFixup</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>as.acidanthera.BlueToolFixup</string>
			<key>IOClass</key>
			<string>BlueToolFixup</string>
			<key>IOMatchCategory</key>
			<string>BlueToolFixup</string>
			<key>IOProviderClass</key>
			<string>IOResources</string>
			<key>IOResourceMatch</key>
			<string>IOKit</string>
		</dict>
	</dict>
	<key>LSMinimumSystemVersion</key>
	<string>10.8</string>
	<key>OSBundleLibraries</key>
	<dict>
		<key>as.vit9696.Lilu</key>
		<string>1.4.7</string>
		<key>com.apple.kpi.bsd</key>
		<string>12.0.0</string>
		<key>com.apple.kpi.dsep</key>
		<string>12.0.0</string>
		<key>com.apple.kpi.iokit</key>
		<string>12.0.0</string>
		<key>com.apple.kpi.libkern</key>
		<string>12.0.0</string>
		<key>com.apple.kpi.mach</key>
		<string>12.0.0</string>
		<key>com.apple.kpi.unsupported</key>
		<string>12.0.0</string>
	</dict>
	<key>OSBundleRequired</key>
	<string>Root</string>
</dict>
</plist>
