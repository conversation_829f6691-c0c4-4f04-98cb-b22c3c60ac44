<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>English</string>
	<key>CFBundleGetInfoString</key>
	<string>1.0 Copyright © 2018-2020 Headsoft. All rights reserved.</string>
	<key>CFBundleIdentifier</key>
	<string>com.Headsoft.USBPorts</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>USBPorts</string>
	<key>CFBundlePackageType</key>
	<string>KEXT</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>1.0</string>
	<key>IOKitPersonalities</key>
	<dict>
		<key>iMac19,1-XHC</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>com.apple.driver.AppleUSBMergeNub</string>
			<key>IOClass</key>
			<string>AppleUSBMergeNub</string>
			<key>IONameMatch</key>
			<string>XHC</string>
			<key>IOPCIPrimaryMatch</key>
			<string>0xa36d8086</string>
			<key>IOProbeScore</key>
			<integer>5000</integer>
			<key>IOProviderClass</key>
			<string>AppleIntelCNLUSBXHCI</string>
			<key>IOProviderMergeProperties</key>
			<dict>
				<key>kUSBSleepPortCurrentLimit</key>
				<integer>2100</integer>
				<key>kUSBSleepPowerSupply</key>
				<integer>5100</integer>
				<key>kUSBWakePortCurrentLimit</key>
				<integer>2100</integer>
				<key>kUSBWakePowerSupply</key>
				<integer>5100</integer>
				<key>port-count</key>
				<data>
				FwAAAA==
				</data>
				<key>ports</key>
				<dict>
					<key>HS01</key>
					<dict>
						<key>Comment</key>
						<string>后中1</string>
						<key>UsbConnector</key>
						<integer>0</integer>
						<key>name</key>
						<string>HS01</string>
						<key>port</key>
						<data>
						AQAAAA==
						</data>
					</dict>
					<key>HS02</key>
					<dict>
						<key>Comment</key>
						<string>后中2</string>
						<key>UsbConnector</key>
						<integer>0</integer>
						<key>name</key>
						<string>HS02</string>
						<key>port</key>
						<data>
						AgAAAA==
						</data>
					</dict>
					<key>HS03</key>
					<dict>
						<key>Comment</key>
						<string>type c 左侧</string>
						<key>UsbConnector</key>
						<integer>0</integer>
						<key>name</key>
						<string>HS03</string>
						<key>port</key>
						<data>
						AwAAAA==
						</data>
					</dict>
					<key>HS04</key>
					<dict>
						<key>Comment</key>
						<string>type c</string>
						<key>UsbConnector</key>
						<integer>9</integer>
						<key>name</key>
						<string>HS04</string>
						<key>port</key>
						<data>
						BAAAAA==
						</data>
					</dict>
					<key>HS06</key>
					<dict>
						<key>Comment</key>
						<string>后上2 前2 前3</string>
						<key>UsbConnector</key>
						<integer>0</integer>
						<key>name</key>
						<string>HS06</string>
						<key>port</key>
						<data>
						BgAAAA==
						</data>
					</dict>
					<key>HS07</key>
					<dict>
						<key>Comment</key>
						<string>前1</string>
						<key>UsbConnector</key>
						<integer>0</integer>
						<key>name</key>
						<string>HS07</string>
						<key>port</key>
						<data>
						BwAAAA==
						</data>
					</dict>
					<key>HS09</key>
					<dict>
						<key>Comment</key>
						<string>后下1</string>
						<key>UsbConnector</key>
						<integer>255</integer>
						<key>name</key>
						<string>HS09</string>
						<key>port</key>
						<data>
						CQAAAA==
						</data>
					</dict>
					<key>HS10</key>
					<dict>
						<key>Comment</key>
						<string>后下2</string>
						<key>UsbConnector</key>
						<integer>255</integer>
						<key>name</key>
						<string>HS10</string>
						<key>port</key>
						<data>
						CgAAAA==
						</data>
					</dict>
					<key>HS13</key>
					<dict>
						<key>Comment</key>
						<string>后上1</string>
						<key>UsbConnector</key>
						<integer>0</integer>
						<key>name</key>
						<string>HS13</string>
						<key>port</key>
						<data>
						DQAAAA==
						</data>
					</dict>
					<key>HS14</key>
					<dict>
						<key>Comment</key>
						<string>Bluetooth</string>
						<key>UsbConnector</key>
						<integer>255</integer>
						<key>name</key>
						<string>HS14</string>
						<key>port</key>
						<data>
						DgAAAA==
						</data>
					</dict>
					<key>SS01</key>
					<dict>
						<key>Comment</key>
						<string>后中1</string>
						<key>UsbConnector</key>
						<integer>3</integer>
						<key>name</key>
						<string>SS01</string>
						<key>port</key>
						<data>
						EQAAAA==
						</data>
					</dict>
					<key>SS02</key>
					<dict>
						<key>Comment</key>
						<string>后中2</string>
						<key>UsbConnector</key>
						<integer>3</integer>
						<key>name</key>
						<string>SS02</string>
						<key>port</key>
						<data>
						EgAAAA==
						</data>
					</dict>
					<key>SS03</key>
					<dict>
						<key>Comment</key>
						<string>type c左侧</string>
						<key>UsbConnector</key>
						<integer>3</integer>
						<key>name</key>
						<string>SS03</string>
						<key>port</key>
						<data>
						EwAAAA==
						</data>
					</dict>
					<key>SS04</key>
					<dict>
						<key>Comment</key>
						<string>后 type c</string>
						<key>UsbConnector</key>
						<integer>9</integer>
						<key>name</key>
						<string>SS04</string>
						<key>port</key>
						<data>
						FAAAAA==
						</data>
					</dict>
					<key>SS07</key>
					<dict>
						<key>Comment</key>
						<string>前1</string>
						<key>UsbConnector</key>
						<integer>3</integer>
						<key>name</key>
						<string>SS07</string>
						<key>port</key>
						<data>
						FwAAAA==
						</data>
					</dict>
				</dict>
			</dict>
			<key>model</key>
			<string>iMac19,1</string>
		</dict>
	</dict>
	<key>OSBundleRequired</key>
	<string>Root</string>
</dict>
</plist>
