<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>23F79</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleExecutable</key>
	<string>IntelBluetoothFirmware</string>
	<key>CFBundleIdentifier</key>
	<string>com.zxystd.IntelBluetoothFirmware</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>IntelBluetoothFirmware</string>
	<key>CFBundlePackageType</key>
	<string>KEXT</string>
	<key>CFBundleShortVersionString</key>
	<string>2.5.0</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CFBundleVersion</key>
	<string>2.5.0</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string></string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>14.5</string>
	<key>DTSDKBuild</key>
	<string>23F73</string>
	<key>DTSDKName</key>
	<string>macosx14.5</string>
	<key>DTXcode</key>
	<string>1540</string>
	<key>DTXcodeBuild</key>
	<string>15F31d</string>
	<key>IOKitPersonalities</key>
	<dict>
		<key>IntelBluetoothFirmware_0026</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>com.zxystd.IntelBluetoothFirmware</string>
			<key>IOClass</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOMatchCategory</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOProbeScore</key>
			<integer>4000</integer>
			<key>IOProviderClass</key>
			<string>IOUSBHostDevice</string>
			<key>idProduct</key>
			<integer>38</integer>
			<key>idVendor</key>
			<integer>32903</integer>
		</dict>
		<key>IntelBluetoothFirmware_0032</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>com.zxystd.IntelBluetoothFirmware</string>
			<key>IOClass</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOMatchCategory</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOProbeScore</key>
			<integer>4000</integer>
			<key>IOProviderClass</key>
			<string>IOUSBHostDevice</string>
			<key>idProduct</key>
			<integer>50</integer>
			<key>idVendor</key>
			<integer>32903</integer>
		</dict>
		<key>IntelBluetoothFirmware_0035</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>com.zxystd.IntelBluetoothFirmware</string>
			<key>IOClass</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOMatchCategory</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOProbeScore</key>
			<integer>4000</integer>
			<key>IOProviderClass</key>
			<string>IOUSBHostDevice</string>
			<key>idProduct</key>
			<integer>53</integer>
			<key>idVendor</key>
			<integer>32903</integer>
		</dict>
		<key>IntelBluetoothFirmware_0036</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>com.zxystd.IntelBluetoothFirmware</string>
			<key>IOClass</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOMatchCategory</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOProbeScore</key>
			<integer>4000</integer>
			<key>IOProviderClass</key>
			<string>IOUSBHostDevice</string>
			<key>idProduct</key>
			<integer>54</integer>
			<key>idVendor</key>
			<integer>32903</integer>
		</dict>
		<key>IntelBluetoothFirmware_0038</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>com.zxystd.IntelBluetoothFirmware</string>
			<key>IOClass</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOMatchCategory</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOProbeScore</key>
			<integer>4000</integer>
			<key>IOProviderClass</key>
			<string>IOUSBHostDevice</string>
			<key>idProduct</key>
			<integer>56</integer>
			<key>idVendor</key>
			<integer>32903</integer>
		</dict>
		<key>IntelBluetoothFirmware_3165</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>com.zxystd.IntelBluetoothFirmware</string>
			<key>IOClass</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOMatchCategory</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOProbeScore</key>
			<integer>4000</integer>
			<key>IOProviderClass</key>
			<string>IOUSBHostDevice</string>
			<key>idProduct</key>
			<integer>2602</integer>
			<key>idVendor</key>
			<integer>32903</integer>
		</dict>
		<key>IntelBluetoothFirmware_3168</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>com.zxystd.IntelBluetoothFirmware</string>
			<key>IOClass</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOMatchCategory</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOProbeScore</key>
			<integer>4000</integer>
			<key>IOProviderClass</key>
			<string>IOUSBHostDevice</string>
			<key>idProduct</key>
			<integer>2727</integer>
			<key>idVendor</key>
			<integer>32903</integer>
		</dict>
		<key>IntelBluetoothFirmware_726x</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>com.zxystd.IntelBluetoothFirmware</string>
			<key>IOClass</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOMatchCategory</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOProbeScore</key>
			<integer>4000</integer>
			<key>IOProviderClass</key>
			<string>IOUSBHostDevice</string>
			<key>idProduct</key>
			<integer>2012</integer>
			<key>idVendor</key>
			<integer>32903</integer>
		</dict>
		<key>IntelBluetoothFirmware_8265</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>com.zxystd.IntelBluetoothFirmware</string>
			<key>IOClass</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOMatchCategory</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOProbeScore</key>
			<integer>4000</integer>
			<key>IOProviderClass</key>
			<string>IOUSBHostDevice</string>
			<key>idProduct</key>
			<integer>2603</integer>
			<key>idVendor</key>
			<integer>32903</integer>
		</dict>
		<key>IntelBluetoothFirmware_926x</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>com.zxystd.IntelBluetoothFirmware</string>
			<key>IOClass</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOMatchCategory</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOProbeScore</key>
			<integer>4000</integer>
			<key>IOProviderClass</key>
			<string>IOUSBHostDevice</string>
			<key>idProduct</key>
			<integer>37</integer>
			<key>idVendor</key>
			<integer>32903</integer>
		</dict>
		<key>IntelBluetoothFirmware_9560</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>com.zxystd.IntelBluetoothFirmware</string>
			<key>IOClass</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOMatchCategory</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOProbeScore</key>
			<integer>4000</integer>
			<key>IOProviderClass</key>
			<string>IOUSBHostDevice</string>
			<key>idProduct</key>
			<integer>2730</integer>
			<key>idVendor</key>
			<integer>32903</integer>
		</dict>
		<key>IntelBluetoothFirmware_ax200</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>com.zxystd.IntelBluetoothFirmware</string>
			<key>IOClass</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOMatchCategory</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOProbeScore</key>
			<integer>4000</integer>
			<key>IOProviderClass</key>
			<string>IOUSBHostDevice</string>
			<key>idProduct</key>
			<integer>41</integer>
			<key>idVendor</key>
			<integer>32903</integer>
		</dict>
		<key>IntelBluetoothFirmware_ax210</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>com.zxystd.IntelBluetoothFirmware</string>
			<key>IOClass</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOMatchCategory</key>
			<string>IntelBluetoothFirmware</string>
			<key>IOProbeScore</key>
			<integer>4000</integer>
			<key>IOProviderClass</key>
			<string>IOUSBHostDevice</string>
			<key>idProduct</key>
			<integer>51</integer>
			<key>idVendor</key>
			<integer>32903</integer>
		</dict>
	</dict>
	<key>LSMinimumSystemVersion</key>
	<string>10.12</string>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright © 2019 钟先耀. All rights reserved.</string>
	<key>OSBundleLibraries</key>
	<dict>
		<key>com.apple.iokit.IOUSBHostFamily</key>
		<string>1.2</string>
		<key>com.apple.kpi.iokit</key>
		<string>16.7</string>
		<key>com.apple.kpi.libkern</key>
		<string>16.7</string>
		<key>com.apple.kpi.mach</key>
		<string>16.7</string>
	</dict>
</dict>
</plist>
