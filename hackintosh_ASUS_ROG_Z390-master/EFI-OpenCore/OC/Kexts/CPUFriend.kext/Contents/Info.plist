<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>23G93</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleExecutable</key>
	<string>CPUFriend</string>
	<key>CFBundleIdentifier</key>
	<string>org.acidanthera.driver.CPUFriend</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>CPUFriend</string>
	<key>CFBundlePackageType</key>
	<string>KEXT</string>
	<key>CFBundleShortVersionString</key>
	<string>1.2.9</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CFBundleVersion</key>
	<string>1.2.9</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string></string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>13.3</string>
	<key>DTSDKBuild</key>
	<string>22E245</string>
	<key>DTSDKName</key>
	<string>macosx13.3</string>
	<key>DTXcode</key>
	<string>1431</string>
	<key>DTXcodeBuild</key>
	<string>14E300c</string>
	<key>IOKitPersonalities</key>
	<dict>
		<key>CPUFriend</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>org.acidanthera.driver.CPUFriend</string>
			<key>IOClass</key>
			<string>CPUFriend</string>
			<key>IOMatchCategory</key>
			<string>CPUFriend</string>
			<key>IOProviderClass</key>
			<string>IOResources</string>
			<key>IOResourceMatch</key>
			<string>IOKit</string>
		</dict>
		<key>CPUFriendPlatform</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>org.acidanthera.driver.CPUFriend</string>
			<key>IOClass</key>
			<string>CPUFriendData</string>
			<key>IOProbeScore</key>
			<integer>6000</integer>
			<key>IOPropertyMatch</key>
			<dict>
				<key>IOCPUNumber</key>
				<integer>0</integer>
			</dict>
			<key>IOProviderClass</key>
			<string>AppleACPICPU</string>
			<key>IOResourceMatch</key>
			<string>ACPI</string>
		</dict>
	</dict>
	<key>LSMinimumSystemVersion</key>
	<string>10.8</string>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright © 2017-2022 PMheart. All rights reserved.</string>
	<key>OSBundleCompatibleVersion</key>
	<string>1.0</string>
	<key>OSBundleLibraries</key>
	<dict>
		<key>as.vit9696.Lilu</key>
		<string>1.2.0</string>
		<key>com.apple.iokit.IOACPIFamily</key>
		<string>1.0.0d1</string>
		<key>com.apple.kpi.bsd</key>
		<string>10.0.0</string>
		<key>com.apple.kpi.dsep</key>
		<string>10.0.0</string>
		<key>com.apple.kpi.iokit</key>
		<string>10.0.0</string>
		<key>com.apple.kpi.libkern</key>
		<string>10.0.0</string>
		<key>com.apple.kpi.mach</key>
		<string>10.0.0</string>
		<key>com.apple.kpi.unsupported</key>
		<string>10.0.0</string>
	</dict>
	<key>OSBundleRequired</key>
	<string>Root</string>
</dict>
</plist>
