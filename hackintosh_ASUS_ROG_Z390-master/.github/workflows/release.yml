name: release

on:
  push:
    tags:
      - 'v*.*.*'

permissions:
  contents: write

jobs:
  release:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Get Version
        id: get_version
        run: echo "VERSION=${GITHUB_REF/refs\/tags\//}" >> $GITHUB_OUTPUT

      - name: Package files
        run: |
          mkdir hackintosh-EFI-ASUS-ROG-Z390-${{ steps.get_version.outputs.VERSION }}
          tar -zcvf hackintosh-EFI-ASUS-ROG-Z390-${{ steps.get_version.outputs.VERSION }}.tar.gz ./EFI-OpenCore ./usbMap ./frameBufferPatch.md ./README.md ./smalltree.png ./sequoia.png
          zip -r hackintosh-EFI-ASUS-ROG-Z390-${{ steps.get_version.outputs.VERSION }}.zip ./EFI-OpenCore ./usbMap ./frameBufferPatch.md ./README.md ./smalltree.png ./sequoia.png

      - name: Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: ./hackintosh-EFI-ASUS-ROG-Z390-*
          draft: false
