# GenSMBIOS
Py script that uses acidant<PERSON><PERSON>'s macserial to generate SMBIOS and optionally saves them to a plist.

***

## To install:

Do the following one line at a time in Terminal:

    git clone https://github.com/corpnewt/GenSMBIOS
    cd GenSMBIOS
    chmod +x GenSMBIOS.command
    
Then run with either `./GenSMBIOS.command` or by double-clicking *GenSMBIOS.command*

***

## Thanks to:

* acidant<PERSON><PERSON> and crew for the [macserial](https://github.com/acidanthera/macserial) application
