# How to create SSDTs

Creating SSDTs mainly fall into 3 camps:

* [Prebuilt SSDTs](../ssdt-methods/ssdt-prebuilt.md)
  * They're universal but don't teach much
  * For most users, we recommend this as it does not require any compiling
* [Automated tools](../ssdt-methods/ssdt-easy.md)
  * Mainly seen with SSDTTime, work much better than prebuilts as there's less bloat however doesn't teach you much
* [Manually creating them](../ssdt-methods/ssdt-long.md)
  * Will always work, be much cleaner and get to learn about the process
  * However requires you to deal with code

If you're not comfortable with editing and compiling code, we recommend the [prebuilt option](../ssdt-methods/ssdt-prebuilt.md)
