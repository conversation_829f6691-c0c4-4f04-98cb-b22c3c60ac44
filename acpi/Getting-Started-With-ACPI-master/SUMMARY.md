# Table of contents

* [Getting Started With ACPI](README.md)
* [Choosing the SSDTs](ssdt-platform.md)

## Methods

* [How to create SSDTs](/ssdt-methods/ssdt-methods.md)
  * [SSDTs: Prebuilt](/ssdt-methods/ssdt-prebuilt.md)
  * [SSDTs: Easy Way](/ssdt-methods/ssdt-easy.md)
  * [SSDTs: Long Way](/ssdt-methods/ssdt-long.md)

## Manual

* [Dumping the DSDT](/Manual/dump.md)
* [Decompiling and Compiling](/Manual/compile.md)

## Desktop

* [Disabling desktop dGPUs](/Desktops/desktop-disable.md)

## Laptop

* [Backlight PNLF](/Laptops/backlight.md)
  * [Prebuilt](/Laptops/backlight-methods/prebuilt.md)
  * [Manual](/Laptops/backlight-methods/manual.md)
* [Trackpad GPI0](/Laptops/trackpad.md)
  * [Manual](/Laptops/trackpad-methods/manual.md)
* [Disabling laptop dGPUs](/Laptops/laptop-disable.md)

## Universal

* [Embedded Controller](/Universal/ec-fix.md)
  * [Prebuilt](/Universal/ec-methods/prebuilt.md)
  * [SSDTTime](/Universal/ec-methods/ssdttime.md)
  * [Manual](/Universal/ec-methods/manual.md)
* [DMAR Table (VT-d)](/Universal/dmar.md)
  * [Manual](/Universal/dmar-methods/manual.md)
* [CPU Power Management](/Universal/plug.md)
  * [Prebuilt](/Universal/plug-methods/prebuilt.md)
  * [SSDTTime](/Universal/plug-methods/ssdttime.md)
  * [Manual](/Universal/plug-methods/manual.md)
* [AWAC vs RTC](/Universal/awac.md)
  * [Prebuilt](/Universal/awac-methods/prebuilt.md)
  * [Manual](/Universal/awac-methods/manual.md)
* [NVRAM PMC](/Universal/nvram.md)
  * [Prebuilt](/Universal/nvram-methods/prebuilt.md)
  * [Manual](/Universal/nvram-methods/manual.md)
* [USB RHUB](/Universal/rhub.md)
  * [Prebuilt](/Universal/rhub-methods/prebuilt.md)
  * [Manual](/Universal/rhub-methods/manual.md)
* [IRQ Fix](/Universal/irq.md)
* [GPU Spoof](/Universal/spoof.md)
* [Fixing SMBus Support](/Universal/smbus.md)
  * [Manual](/Universal/smbus-methods/manual.md)

## Cleanup

* [Cleanup](cleanup.md)

## Misc

* [Troubleshooting](troubleshooting.md)
* [Contributing](CONTRIBUTING.md)
