4D1EDE05-38C7-4A6A-9CC6-4BCCA8B38C14
4D1FDA02-38C7-4A6A-9CC6-4BCCA8B30102
7C436110-AB2A-4BBB-A880-FE41995C9F82
ACPI
APFS
ARTFrequency
AdviseWindows
AllowNvramReset
AllowSetDefault
AppleAudio
AppleBootPolicy
AppleCpuPmCfgLock
AppleDebug
AppleDebugLog
AppleEvent
AppleImageConversion
AppleKeyMap
ApplePanic
AppleRtcRam
AppleSmcIo
AppleUserInterfaceTheme
AppleXcpmCfgLock
AppleXcpmExtraMsrs
AppleXcpmForceBoost
AudioCodec
AudioDevice
AudioOut
AudioSupport
AuthRestart
AvoidRuntimeDefrag
BID
BIOSReleaseDate
BIOSVendor
BIOSVersion
BlessOverride
BoardAssetTag
BoardLocationInChassis
BoardManufacturer
BoardProduct
BoardRevision
BoardSerialNumber
BoardType
BoardVersion
Boot1f32
BootProtect
Booter
BundlePath
ChassisAssetTag
ChassisManufacturer
ChassisSerialNumber
ChassisType
ChassisVersion
ClearScreenOnModeSwitch
ConnectDrivers
ConsoleAttributes
ConsoleMode
Cpuid1Data
Cpuid1Mask
CustomSMBIOSGuid
DataHub
DevicePathsSupported
DeviceProperties
DevirtualiseMmio
DirectGopCacheMode
DirectGopRendering
DisableIoMapper
DisableRtcChecksum
DisableSingleUser
DisableVariableWrite
DisableWatchDog
DiscardHibernateMap
DisplayDelay
DisplayLevel
DummyPowerManagement
EnableJumpstart
EnableSafeModeSlide
EnableWriteUnprotector
ExecutablePath
ExistBootServicesDelay
ExposeSensitiveData
ExternalDiskIcons
FSBFrequency
FadtEnableReset
FindMask
FirmwareFeatures
FirmwareFeaturesMask
FirmwareVolume
ForceExitBootServices
HaltLevel
HashServices
HibernateMode
HideAuxiliary
HideSelf
HideVerbose
IgnoreInvalidFlexRatio
IgnoreTextInGraphics
IncreasePciBarSize
InitialTSC
JumpstartHotPlug
JumpstartHotplug
KeyFiltering
KeyForgetThreshold
KeyMergeThreshold
KeySupport
KeySupportMode
KeySwap
LapicKernelPanic
LegacyEnable
LegacyOverwrite
LegacySchema
Lifewire
MLB
MaxKernel
MemoryFormFactor
MinDate
MinKernel
MinVersion
MinimumVolume
MmioWhitelist
NormalizeHeaders
OSInfo
OemTableId
PickerAttributes
PickerAudioAssist
PickerMode
PlatformFeature
PlatformInfo
PlatformNVRAM
PlatformName
PlayChime
PlistPath
PointerSupport
PointerSupportMode
PollAppleHotKeys
PowerTimeoutKernelPanic
ProcessorType
ProtectMemoryRegions
ProtectSecureBoot
ProtectUefiServices
ProtocolOverrides
ProvideConsoleGop
ProvideCustomSlide
Quirks
ROM
RebaseRegions
RebuildAppleMemoryMap
ReconnectOnResChange
ReleaseUsbOwnership
ReplaceMask
ReplaceTabWithSpace
RequestBootVarFallback
RequestBootVarRouting
ReservedMemory
ResetHwSig
ResetLogoStatus
Resolution
SMBIOS
SanitiseClearScreen
ScanPolicy
SerialInit
SetupVirtualMap
ShowPicker
SignalAppleOS
SmcBranch
SmcPlatform
SmcRevision
SmcVersion
SpoofVendor
StartupPowerEvents
SyncRuntimePermissions
SysReport
SystemFamily
SystemManufacturer
SystemMemoryStatus
SystemProductName
SystemSKUNumber
SystemSerialNumber
SystemUUID
SystemVersion
TableLength
TableSignature
TakeoffDelay
Target
TextRenderer
ThirdPartyDrives
TimerResolution
UEFI
UIScale
UnblockFsConnect
UnicodeCollation
UpdateDataHub
UpdateNVRAM
UpdateSMBIOS
UpdateSMBIOSMode
Vault
VolumeAmplifier
WriteFlash
XhciPortLimit
boot0
bootia32
bootx64
snapshotted