(15|16)h
(No)?TouchID
.aml
.chunklist
.dat
.dmg
.dsl
.kext
.plist
.txt
00010D13
01xx0900
02000A00
02xx0A00
0x[0-9A-Fa-f]+
100MBe
100MHz
1024x768
120Hz
144Hz
14F1
14e4
16XX
17h
18G103
18G95
19H
19h
1Revenger1
20XX
2200S
2B-i
2B-iii
32MB
3700X
390X
3990X
3D
43a0
4G
4K
4k
50F4
5700XT
64MB
64bit
6970M
6XX
7920x
7980xe
7XX
7zip
970EvoPlus
A320
A520
AAPL
ACPIBatteryManager
AGPM
AHCI
AHCIPortInjector
AIDA64
AIOs
ALC
ALC1220
ALC3601
ALZA
ANS1
ANS2
APIC
APTIO
APUs
ARPT
ASR
ASUS
ASentientBot
ATA
ATAPortInjector
ATH9KFixup
AWAC
AZAL
Above4G
Above4GDecoding
Acidanthera
Acidanthera's
A<PERSON><PERSON>
AcpiSamples
AirPortBrcm4360
AirportAtheros40
AirportBrcmFixup
AirportItlwm
AlGrey
Aleksander
Aleksander's
AlpsT4USB
AmazonBasics
Andrey's
Andrey1970AppleLife
Aorus
ApfsDriverLoader
Apianti
Apianti's
AppStore
AppeALC's
AppleACPICPU
AppleACPIEC
AppleAHCIPort
AppleALC
AppleALC's
AppleBusPowerController
AppleGVA
AppleHDA
AppleHDAController
AppleHDAInput
AppleHV
AppleHV's
AppleID
AppleIntelCPUPM
AppleIntelCPUPowerManagement
AppleIntelCPUPowerManagment
AppleIntelI210Ethernet
AppleIntelPchSeriesAHCI
AppleIntelPowerManagement
AppleIntele1000e
AppleLPC
AppleLife
AppleMCEReporter
AppleMCEReporterDisabler
AppleRTC
AppleSMBus
AppleSMBusController
AppleSMBusPCI
AppleSupport
AppleSupportPkg
AppleTV
Aptio
AptioMemoryFix
Aquantia
Architected
Arg
Arg0
Arrandale
Arrendale
AsMedia
AsRock
AssetCache
Asus
Asus's
Ath3kBT
Ath3kBTInjector
AthBluetoothFirmware
Atheros
AtherosE2200Ethernet
Atmel
Attribution-NonCommercial-ShareAlike
AudioDxe
Auth
B0D3
B360
B365
B450
B460
B550
B75
B8
BCM43224
BCM4331
BCM5722
BCM5722D
BCM94331
BCM94331CD
BCM94352Z
BCM94360
BGRT
BOOTICE
BT
Backlight
BaseSystem
BeyondCompare
Big
BlueScreen
BootCamp
BootInstall
Bootable
Bootcamp
BrcmBluetoothInjector
BrcmFirmwareData
BrcmPatchRAM[0-3]?
Bridge-E5
Broadcom
Broadwell
Broadwell-E
Bugtracker
BusID
C3
C602
C612
C7030000
CAVS
CFG
CFG-Lock
CLKT
CMD
CMOS
CONFIGURATORS
CPU0
CPUFriend
CPUFriendDataProvider
CPUFriendFriend
CPUID
CPUID's
CPUs
CR0
CSM
Capitan
Carcraftz
Celeron
Celerons
Changelog
Chipset
Chipsets
Clarkdale
Clarksfield
CloverV2
Cmd
Codec
Codecs
CoffeeLake
Config
Configurator
ConsoleControl
ConsoleGOP
Core2
CorpNewt
CorpNewt's
Cpu
Cpu0Ist
CpuPm
CpuTscSync
Crohn
Crohn's
CsrDecode
CtlnaAHCIPort
Ctrl
Cupertino
CustomSMBIOSMode
D3
DAC
DACs
DBG
DC7900
DMAR
DMGs
DRM
DSDT
DSDTs
DSMOS
DVI
DVMT
DVMT-prealloc
DYTC
DaVinci
Decluttering
Decompile
Decompiling
Dev
DeviceManager
DevicePath
DeviceProperty
DhinakG
DiskArbitrationFixup
DiskUtility
DisplayPort
Dont
Dortania
Dortania's
DriveDroid
DuetPkg
E2
E2500
EB
EC0
ECDV
EDID
EDK
EDKII
EFI
EFI's
EFI32
EFI64
EH01
EH02
EHC1
EHC2
EHCI
ELAN1200
ELANs
EPP
ETH0
EUSB
EVGA
EVO
EfiPkg
EfiReservedMemoryType
El
El Capitan
EmuVariableUEFI
En0
EnableWriteUnprotected
Enclos
EndRandomSeed
Endian
Endianness
Esc
EveryMac
Evo
ExitBootServices
Explainer
ExtendBTFeatureFlags
F10
F12
F3A
F4
FAT32
FPU
FSB
FTE1001
FW
FX
Factor-Aut
Factor-Auth
FairPlay
FakeSMC
FakeSMC-32
FakeSMC-32's
FakeSMC3_with_plugins
Fenvi
FileVault
FireWire
Firmwares
FixHPET
FixIPIC
FixShutdown-USB-SSDT
Fn
ForceKextsToLoad
Framebuffer
FredWst
FwRuntimeServices
G6
GA-X299-UD4
GBE1
GCN
GFX
GFX0
GMA
GMA950
GPEN
GPHD
GPI0
GPIO
GPT
GPT.
GPUs
GSync
GT1
GT2
GTX
GUID
GenSMBIOS
GenSMBIOS's
GibMacOS
GitBook
Github
Goldfish64
Goldfish64's
GraphicsEnabler
GraphicsEnabler=Yes
Greenwhich
GuC
Gui
H310
H370
H61
HD
HDA
HDAS
HDAU
HDCP
HDDs
HDEF
HD[0-9]*
HEC1
HECI
HEDT
HFS
HM370
HM[0-9]{2}
HPET
HWPEnable
Hackintool
Hackintosh
Hackintosher
Hackintoshes
Hackintoshing
Handoff
Haswell
Haswell-E
Hfs
HfsPlus
HfsPlus's
HfsPlus32
HfsPlusLegacy
HiDPI
HiiDatabase
HoRNDIS
Hx6x
I211
I211-AT
I2C
I2C1
I7,
IA32
IDER
IGPEnabler
IGPEnabler=Yes
IGPU
IM
IMEI
IMEI's
INI
IO80211
IO80211Family
IOHDACodecDevice
IOHIDFamily
IOIIIO
IOKit
IONVMe
IONetworkingFamily
IOPCIFamily
IOReg
IORegistryExplorer
IOService
IPIC
IQSV
IRQ
IRQs
IceLake
Icelake
Icelake's
InjectAMD
InjectAti
InjectIntel
InjectNvidia
InsanelyMac
Inspiron
InstallAssistant
InstallMacOSX
Insyde
Intel
IntelBluetoothFirmware
IntelMausi
IntelMausiEthernet
IntelSnowMausi
InyextcionES
InyextcionES'
IvyBridge
JDK
JRE
JackFix
Journaled
KASLR
KBL-R
KDK
KVM
KVMs
Kaby
KabyLake
Kabylake
KernelAndKextPatches
KernelCollections
KernelPM
Kernelspace
Kext
KextBeast
Kexts
Keychain
Khronokernel
L305
L8200A
LAPIC
LD:OFS
LPC
LPC0
LPCB
LPM
LSTA
LVDS
LegacyBoot
LegacyCommpage
Lenovo
Lenovo's
Lexa
Lilu
Lilu-independent
LoadImage
LoadVBIOS
LowPinCount
LucyRTL8125Ethernet
Lynnfield
M1
MADT
MBR
MBR-based
MEI
MEID
MKext
MMIO
MSI
MSR
MSR_FLEX_RATIO
MT2
MacBook
MacBook7
MacBook8
MacBookAir[0-9]+
MacBookPro13
MacBookPro14
MacBookPro[0-9]+
MacInfoPkg
MacMini
MacMini6
MacMini7
MacOS
MacPro5
MacPro6
MacPro7
MacProMemoryNotificationDisabler
MacRecovery's
MacRumors
MacSerial
MaciASL
Mackie
Mackie's
Macmini6
Macmini7
Maemo
MakeInstall
MasterBootRecord
MediaKit
Mellanox
Memfast
MemoryFix
Merom
MinMultiplier
Misconfigured
MonitorControl
MountEFI
MultiBeast
Multiboot
Multitouch
NIC
NICs
NOOPT
NPM
NTFS
NUC
NUCs
NVCAP
NVCAP-settings
NVMe
NVMeFix
NVRAM
NVRAM's
Navi
Nehalem
Niresh
NoHybGfx
NoOne
Notiflux
NullCPUPowerManagement
NullCPUPowerManagment
Nvidia
Nvidia's
OC
OC's
OC/
OCABC
OCB
OCConfigCompare
OCSCAN_ALLOW_DEVICE
OCSCAN_ALLOW_FS
OC_SCAN_ALLOW_DEVICE_NVME
OC_SCAN_ALLOW_DEVICE_SASE
OC_SCAN_ALLOW_DEVICE_SASEX
OC_SCAN_ALLOW_DEVICE_SATA
OC_SCAN_ALLOW_DEVICE_SCSI
OC_SCAN_ALLOW_DEVICE_US
OC_SCAN_ALLOW_DEVICE_USB
OC_SCAN_ALLOW_FS_APFS
OC_SCAN_DEVICE_LOCK
OC_SCAN_FILE_SYSTEM_LOCK
OEM
OEM's
OEMs
OPENCORE
OSI
OSX
OSX's
OSYS
OcAppleKernelLib
OcQuirks
OpenCanopy
OpenCore
OpenCore's
OpenCorePkg
OpenCorePkg's
OpenRuntime
OpenShell
OpenUsbKbDxe
Optane
Optimus
OsxAptioFixDrvX
OtherOS
P50
P530
PARSEOP_ONE
PBR
PC00
PCI
PCIO
PCIRoot
PCIRootUID
PCIRootUID=Value
PCIe
PEG0
PEGP
PGEC
PLIST
PListPath
PM981
PM991
PMC
PMHeart
PNLF
PNP0C09
PNP0C09's
PR00
PRs
PS2
PXSX
PartitionDxe
PartitionDxe32
PartitionDxeLegacy
Patch1
Pathing
Pathname
Pci
PciRoot
PciRoot's
Penryn
Penyrn
Plist
Plists
PowerBook
PowerMac
PowerProperties
Pre-Allocated
Preboot
Prebuilt
Prelinked
ProperTree
ProperTree's
Psystar
Q8300
QE/CI
QEMU
Qlogic
Quicklook
R5/R7
R9
RDRAND
RHUB
ROMs
RSA
RSA-2048
RST
RTC
RTC0
RTCMemoryFixup
RTVariables
R[5|7]
Radeon
Radeon-Denit-SSDT
Ramus
Realtek
Realtek's
RealtekRTL8100
RealtekRTL8111
RecoveryImage
ReddestDream's
RehabMan's
Rehabman
Rehabman's
Reinstalls
Rename-SSDT
Repo
Ryzen
S3
SAS
SAT0
SAT1
SATA
SATA-Unsupported
SATA-unsupported
SBRG
SGX
SHA-1
SL01
SLOTID
SMBUS
SMBus
SMBus-based
SMC
SMCAMDProcessor
SMCBatteryManager
SMM
SSD
SSDT
SSDT-.*
SSDTTime
SSDTTime's
SSDTs
SSDs
SSE3
SSE4
SSSE3
STAS
Safemode
SandyBridge
SecureBootModel
SetVirtualAddresses
Shannee
SharedSupport
Shiki
ShowPackageContents
SidecarEnabler
Siri
Skylake
Skylake-SP
Skylake-X
SmUUID
SmallTree
SmallTreeIntel82576
Speccy
Stompy
StopSign-fixv
StopSign-fixv5
Subreddit
Sur
Sur's
Synaptics
Syrah
SystemAudioVolume
SystemParameters
SystemProfilerMemoryFixup
T2
TDP
TIMR
TMR
TR4
TRX40
TRx40
TSC
TSCAdjustReset
Takedowns
Technopat
Telemetrap
TetherMe
TextEdit
TextMate
TextMode
TgtBridge
ThinkPad
ThinkPads
Thinkpad
ThrRip
ThreadRipper
Threadripper
TimeMachine
Touchbase
Trackpad
Trackpads
TransMac
Typora
UEFI-based
UEFIExtrac
UEFITool
UHD
UI
UID
USBE
USBInjectAll
USBX
USBmap
UUID
UUIDs
Ubuntu
Uncore
UniBeast
Universal-IFR-Extractor
UsbInjectAll
UselessBanana
Userspace
V1
VBIOS
VDADecoderChecker
VDI
VIAO
VM
VMDK
VMWare
VMs
VMware
VPN
VRAM
VSCode
VerifyMsrE2
VirtualBox
VirtualSMC
VirtualSMC's
Vit
Vit's
VmAssetCacheEnable
VoiceOver
VoodooGPIO
VoodooHDA
VoodooHDA-FAT
VoodooI2C.*
VoodooInput
VoodooPS2
VoodooPS2's
VoodooPS2Controller
VoodooRMI
VoodooSMBus
VoodooTSCSync
VoodooTsc
VuePress
WHFR
WHQL
WIFI
WIP
WLAN
Welp
Westmere
WhateverGreen
WhateverGreen's
Wifi
X299
X3100
X470
X520
X540
X570
X64
X79
X86
X86-based
X86PlatformPlugin
X86PlatformShim
X99
XCPM
XHC
XHC0
XHC1
XHCI
XHCI-unsupported
XLNCUSBFIX
XLNCUSBFix
XNU
XNU's
XOSI
XPoint
XXX-XXXXX
Xcode
Xeon
Xeons
Xserve
Yonah
Z270
Z370
Z390
Z490
Z67
Z68
Z77
Z87
Z87-Pro
Z97
[0-9]+GB
[0-9]+MB
[0-9]+XXX
\.efi
acidanthera
acpica
actionLink
actionText
al3xtjames
alcid
algrey
architected
args
arse
auth
automagically
ba10b5d
backlight
backports
base64
bitmask
boot-arg
boot-args
boot-args.
bootable
bootcamp
bootloader
bootloaders
borked
breakless
bugtracker
build-repo
busID
busId
busid
busid=00
busids
cacheless
cd
cdfon
checksums
chipset
chipset's
chipsets
chunklist
codec
codecs
con0
config
config's
configs
configurator
configurators
createinstallmedia
csr-active-config
csrstat
dGPU
dGPUs
debug=0x100
decompile
decompiled
decompiling
decrypt
dev
devirt
diskXsY
diskpart
diskutil
distro
distro's
distros
dmg
dmg's
dortania
eDP
eMMC
eMac
endian
erroring
ethernet
executables
explainer
fakeID
fassl's
filesystem
firmwares
framebuffer
framebuffer-con0-alldata
framebuffer-con0-enable
framebuffer-con1-alldata
framebuffer-con1-enable
framebuffer-con2-alldata
framebuffer-con2-enable
framebuffers
fs
gIOLockState
gdisk
gfxutil
gibMacOS
gibMacOS's
gitbook
gitbook-cli
google-fu
grey
hackintosh
hackintoshes
hackintoshing
hacky
heroImage
heroText
hotplug
i210
i211
i218
i219
i225
i225-V
i225LM
i3
i3-3110M
i7
i7-10700K
i7-9700T
i9
i9-10850K
i9-10900K
i9-10910
i9-9900K
iASL
iBook
iCloud
iGPU
iGPU's
iGPU-less
iGPU-only
iGPU.
iGPUs
iMac
iMac10
iMac11
iMac12
iMac13
iMac14
iMac15
iMac16
iMac17
iMac18
iMac19
iMac20
iMac6
iMacPro1
iMessage
iPad8
iPadOS
iServices
iStat
iasl
iasl-stable
ie
ie\.
ig-platform-id
ig-platform-id's
igfxcdc
igfxfw
img
ix-3xxx
jailbroken
keepsyms=1
kernelcache
kernelspace
kext
kext(\.)?
kextd
kexts
laggy
lapic
lappie
licensor
linter
lsblk
macOS
macOS's
macOS(\.)?
macOS-compatible
macOS-limited
macOS-only
macOS86
macrecovery
macrecovery's
macserial
markdownlint-cli
max_cpus_from_firmware
midi1996
mini-explainer
msi
multiboot
multibooting
natively
nms42
non-Fenvi
non-UEFI
non-latin
npci
nvda_drv=1
osxaptiofix2drv.efi
passthrough
patcher
pathing
pathing.
pci
pentiums
plist
plist-only
plists
pre-
pre-2012
pre-Skylake
pre-built
pre-builts
pre-compiled
pre-existing
pre-hex
pre-made
preboot
prebuilt
prebuilts
precompiled
prelinked
prelinkedkernel
prelinker
probs
r10
r4
rEFInd
raw2vmdk
re-architecting
readme
rebranded
reimplements
renderer
repo
repos
resolutuion
rottenpants466
savvamitrofanov
shiki
shikigva
slavs
slowgeek
smcread
snapshotting
snb-platform-id
socketed
soooooo
spacebar
spellchecker-cli
ssdtPRGen
ssdtPRgen
stolenmem
subreddit
sudo
sysctl
takedowns
tbh
threadripper
touchpad
touchpads
trackpad
trackpads
trackpoints
trashOS
ubuntu
unallocated
unbootable
uncomment
uncore
unenroll
unmount
unoptimized
untrusted
userspace
v1
v3006
v5210
vSMC
ver
vit9696
vit9696's
vmdk
vmx
vmxf
webserver
wifi
x10
x64
x86
x86-based
xcpm
Как
завести
сервисы
OSID
XSID
5f4f5349
584f5349
4F534944
