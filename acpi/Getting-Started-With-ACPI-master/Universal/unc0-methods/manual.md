# Fixing Uncore Bridges: Manual

* [Download the SSDT](#download-the-ssdt)
* [Wrapping up](#wrapping-up)

## Download the SSDT

Super simple, just grab the SSDT and compile:

* [SSDT-UNC.dsl](https://github.com/acidanthera/OpenCorePkg/tree/master/Docs/AcpiSamples/Source/SSDT-UNC.dsl)

See here how to compile: [Compiling ACPI](/Manual/compile.md)

## Wrapping up

Once you're done making your SSDT, either head to the next page to finish the rest of the SSDTs or head here if you're ready to wrap up:

* [**Cleanup**](/cleanup.md)
