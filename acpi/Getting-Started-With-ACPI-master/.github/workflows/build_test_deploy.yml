name: Build/Test/Deploy
on:
  push:
  workflow_dispatch:
  pull_request:

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: 'recursive'
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'yarn'
          cache-dependency-path: ./yarn.lock
      - name: Install Dependencies
        run: yarn install --frozen-lockfile
      - name: Build
        run: yarn run build
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: .vuepress/dist/
  deploy:
    if: ${{ github.event_name == 'push' && github.ref == 'refs/heads/master' }}
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4

  lint:
    name: <PERSON>t
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'yarn'
          cache-dependency-path: ./yarn.lock
      - name: Install
        run: yarn install --frozen-lockfile
      - name: Lint
        run: yarn run lint-ci
