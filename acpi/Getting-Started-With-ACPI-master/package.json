{"name": "Getting-Started-With-AC<PERSON>", "version": "0.0.1", "description": "", "main": "", "author": {"name": "Dortania", "url": "https://github.com/dortania"}, "repository": {"type": "git", "url": "git+https://github.com/dortania/Getting-Started-With-ACPI.git"}, "scripts": {"dev": "vuepress dev", "build": "vuepress build", "fix-lint": "run-script-os", "fix-lint:default": "(echo Attempting to fix lint... && markdownlint -c .markdownlint.json -p .markdownlintignore '**/*.md' -f && echo Fixed successfully, please commit.) || (echo Fix failed! && exit 1)", "fix-lint:win32": "(echo Attempting to fix lint... && markdownlint -c .markdownlint.json -p .markdownlintignore **/*.md -f && echo Fixed successfully, please commit.) || (echo Fix failed! && exit 1)", "lint": "run-script-os", "lint:default": "(echo <PERSON>ting... && markdownlint -c .markdownlint.json -p .markdownlint<PERSON>ore '**/*.md' && echo <PERSON> passed.) || (echo <PERSON> failed! Please review and fix errors. && exit 1)", "lint:win32": "(echo <PERSON>ting... && markdownlint -c .markdownlint.json -p .markdownlint<PERSON>ore **/*.md && echo <PERSON> passed.) || (echo <PERSON> failed! Please review and fix errors. && exit 1)", "lint-ci": "run-script-os", "lint-ci:default": "(echo Linting... && markdownlint -c .markdownlint.json -p .markdownlint<PERSON>ore '**/*.md' && echo <PERSON> passed.) || ((echo <PERSON> failed, attempting fix... && markdownlint -c .markdownlint.json -p .markdownlintignore '**/*.md' -f && echo Fix generated successfully. Please apply the following diff using git apply && git diff) || echo Fix failed! && exit 1)", "lint-ci:win32": "(echo Linting... && markdownlint -c .markdownlint.json -p .markdownlintignore **/*.md && echo <PERSON> passed.) || ((echo <PERSON> failed, attempting fix... && markdownlint -c .markdownlint.json -p .markdownlintignore **/*.md -f && echo Fix generated successfully. Please apply the following diff using git apply && git diff) || echo Fix failed! && exit 1)", "sort-dict": "node ./scripts/sortDict.js", "spellcheck": "run-script-os", "spellcheck:default": "(spellchecker --plugins spell indefinite-article repeated-words syntax-urls --dictionaries dictionary/dictionary.txt dictionary/opencorekeys.txt --files '**/*.md'  && echo Spellcheck passed.) || (echo Spellcheck failed! Please review and fix errors/add words to dictionary as needed. && exit 1)", "spellcheck:win32": "(spellchecker --plugins spell indefinite-article repeated-words syntax-urls --dictionaries dictionary/dictionary.txt dictionary/opencorekeys.txt --files **/*.md  && echo Spellcheck passed.) || (echo Spellcheck failed! Please review and fix errors/add words to dictionary as needed. && exit 1)", "test": "run-script-os", "test:default": "npm run lint --silent; npm run spellcheck --silent", "test:win32": "npm run lint --silent & npm run spellcheck --silent"}, "license": "CC-BY-NC-SA-4.0", "devDependencies": {"@vuepress/plugin-back-to-top": "^1.9.8", "@vuepress/plugin-medium-zoom": "^1.9.8", "markdown-it-multimd-table": "^4.2.0", "markdown-link-check": "^3.10.3", "markdownlint-cli": "^0.33.0", "run-script-os": "^1.1.6", "spellchecker-cli": "^6.1.1", "vuepress": "^1.9.8", "vuepress-plugin-fulltext-search": "^2.2.1", "vuepress-theme-succinct": "^1.7.2"}, "homepage": "https://dortania.github.io/Getting-Started-With-ACPI/"}