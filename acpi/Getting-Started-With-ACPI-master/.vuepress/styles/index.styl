/**
 * Custom Styles here.
 *
 * ref：https://v1.vuepress.vuejs.org/config/#index-styl
 */

[data-theme='light']
 --sideBgColor $sideBgColor

[data-theme='dark']
 --sideBgColor $sideBgColorDark
 --dropShadowColor $dropShadowColor

.home .hero img
  max-width 450px!important
  
.navbar
  filter drop-shadow(0px 1px 1px var(--dropShadowColor))

.sidebar
  background-color var(--sideBgColor)

div[class*="language-"]
  pre, pre[class*="language-"]
    margin-top 0

html {
  overflow-y: scroll;
  overflow-x: hidden;
  background-color: unset;
}
::-webkit-scrollbar {
    width 0px
    background transparent /* make scrollbar transparent */
}

tr
   &:nth-child(1n)
     background-color var(--bodyBgColor)
   &:nth-child(2n)
     background-color var(--sideBgColor)

	 

.dropdown-wrapper .nav-dropdown
  border 1px solid var(--sideBgColor)!important
  background-color var(--sideBgColor)!important
  box-shadow 0px 0px 2px var(--bodyBgColor)
  right -12%!important


@media (min-width: 1000px)
  .theme-succinct-content:not(.custom)
    max-width 70%

.search-box .suggestion a .suggestion-row .page-title
  border-color var(--borderColor) !important
  background-color: var(--sideBgColor) !important

.search-box .suggestion a .suggestion-row .suggestion-content
  border-color var(--borderColor) !important

.search-box .suggestion a .parent-page-title
  background-color: var(--bodyBgColor) !important
  color: var(--accentColor) !important

.search-box .suggestion.focused a
  color: var(--navItemHoverColor) !important