<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>23H222</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>English</string>
	<key>CFBundleExecutable</key>
	<string>IntelMausi</string>
	<key>CFBundleIdentifier</key>
	<string>as.acidanthera.mieze.IntelMausi</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>IntelMausi</string>
	<key>CFBundlePackageType</key>
	<string>KEXT</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.8</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CFBundleVersion</key>
	<string>1.0.8</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string></string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>14.2</string>
	<key>DTSDKBuild</key>
	<string>23C53</string>
	<key>DTSDKName</key>
	<string>macosx14.2</string>
	<key>DTXcode</key>
	<string>1520</string>
	<key>DTXcodeBuild</key>
	<string>15C500b</string>
	<key>IOKitPersonalities</key>
	<dict>
		<key>IntelMausi</key>
		<dict>
			<key>CFBundleIdentifier</key>
			<string>as.acidanthera.mieze.IntelMausi</string>
			<key>Driver Parameters</key>
			<dict>
				<key>enableCSO6</key>
				<true/>
				<key>enableWakeOnAddrMatch</key>
				<false/>
				<key>maxIntrRate10</key>
				<integer>3000</integer>
				<key>maxIntrRate100</key>
				<integer>5000</integer>
				<key>maxIntrRate1000</key>
				<integer>8000</integer>
				<key>rxAbsTime10</key>
				<integer>0</integer>
				<key>rxAbsTime100</key>
				<integer>0</integer>
				<key>rxAbsTime1000</key>
				<integer>10</integer>
				<key>rxDelayTime10</key>
				<integer>0</integer>
				<key>rxDelayTime100</key>
				<integer>0</integer>
				<key>rxDelayTime1000</key>
				<integer>0</integer>
			</dict>
			<key>Driver_Version</key>
			<string>1.0.8</string>
			<key>IOClass</key>
			<string>IntelMausi</string>
			<key>IOPCIMatch</key>
			<string>0x10EA8086 0x10EB8086 0x10EF8086 0x10F08086 0x15028086 0x15038086 0x153A8086 0x153B8086 0x155A8086 0x15598086 0x15A08086 0x15A18086 0x15A28086 0x15A38086 0x156F8086 0x15708086 0x15B78086 0x15B88086 0x15D78086 0x15D88086 0x15E38086 0x15D68086 0x15BD8086 0x15BE8086 0x15BB8086 0x15BC8086 0x15DF8086 0x15E08086 0x15E18086 0x15E28086 0x15B98086 0x0D4E8086 0x0D4F8086 0x0D4C8086 0x0D4D8086 0x0D538086 0x0D558086 0x15FB8086 0x15FC8086 0x15F98086 0x15FA8086 0x15F48086 0x15F58086 0x1A1E8086 0x1A1F8086 0x1A1C8086 0x1A1D8086 0x550A8086 0x550B8086 0x550C8086 0x550D8086</string>
			<key>IOProbeScore</key>
			<integer>1000</integer>
			<key>IOProviderClass</key>
			<string>IOPCIDevice</string>
		</dict>
	</dict>
	<key>LSMinimumSystemVersion</key>
	<string>10.9</string>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright © 2014 Laura Müller. All rights reserved.</string>
	<key>OSBundleLibraries</key>
	<dict>
		<key>com.apple.iokit.IONetworkingFamily</key>
		<string>1.5.0</string>
		<key>com.apple.iokit.IOPCIFamily</key>
		<string>1.7</string>
		<key>com.apple.kpi.bsd</key>
		<string>8.10.0</string>
		<key>com.apple.kpi.iokit</key>
		<string>8.10.0</string>
		<key>com.apple.kpi.libkern</key>
		<string>8.10.0</string>
		<key>com.apple.kpi.mach</key>
		<string>8.10.0</string>
	</dict>
	<key>OSBundleRequired</key>
	<string>Network-Root</string>
</dict>
</plist>
