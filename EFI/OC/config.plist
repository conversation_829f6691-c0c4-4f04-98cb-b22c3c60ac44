<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ACPI</key>
	<dict>
		<key>Add</key>
		<array>
			<dict>
				<key>Comment</key>
				<string>CPU power management for Coffee Lake</string>
				<key>Enabled</key>
				<true/>
				<key>Path</key>
				<string>SSDT-PLUG.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>Embedded controller and USB power for desktop</string>
				<key>Enabled</key>
				<true/>
				<key>Path</key>
				<string>SSDT-EC-USBX.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>AWAC disable for 300 series</string>
				<key>Enabled</key>
				<true/>
				<key>Path</key>
				<string>SSDT-AWAC.aml</string>
			</dict>
			<dict>
				<key>Comment</key>
				<string>PMC support for 300 series NVRAM</string>
				<key>Enabled</key>
				<true/>
				<key>Path</key>
				<string>SSDT-PMC.aml</string>
			</dict>
		</array>
		<key>Delete</key>
		<array/>
		<key>Patch</key>
		<array/>
		<key>Quirks</key>
		<dict>
			<key>FadtEnableReset</key>
			<false/>
			<key>NormalizeHeaders</key>
			<false/>
			<key>RebaseRegions</key>
			<false/>
			<key>ResetHwSig</key>
			<false/>
			<key>ResetLogoStatus</key>
			<false/>
			<key>SyncTableIds</key>
			<false/>
		</dict>
	</dict>
	<key>Booter</key>
	<dict>
		<key>MmioWhitelist</key>
		<array/>
		<key>Patch</key>
		<array/>
		<key>Quirks</key>
		<dict>
			<key>AllowRelocationBlock</key>
			<false/>
			<key>AvoidRuntimeDefrag</key>
			<true/>
			<key>ClearTaskSwitchBit</key>
			<false/>
			<key>DevirtualiseMmio</key>
			<true/>
			<key>DisableVariableWrite</key>
			<false/>
			<key>DiscardHibernateMap</key>
			<false/>
			<key>DisableSingleUser</key>
			<false/>
			<key>EnableSafeModeSlide</key>
			<true/>
			<key>EnableWriteUnprotector</key>
			<false/>
			<key>FixupAppleEfiImages</key>
			<false/>
			<key>ForceBooterSignature</key>
			<false/>
			<key>ForceExitBootServices</key>
			<false/>
			<key>ProtectMemoryRegions</key>
			<false/>
			<key>ProtectSecureBoot</key>
			<false/>
			<key>ProtectUefiServices</key>
			<true/>
			<key>ProvideCustomSlide</key>
			<true/>
			<key>ProvideMaxSlide</key>
			<integer>0</integer>
			<key>RebuildAppleMemoryMap</key>
			<true/>
			<key>ResizeAppleGpuBars</key>
			<integer>-1</integer>
			<key>SetupVirtualMap</key>
			<true/>
			<key>SignalAppleOS</key>
			<false/>
			<key>SyncRuntimePermissions</key>
			<true/>
		</dict>
	</dict>
	<key>DeviceProperties</key>
	<dict>
		<key>Add</key>
		<dict>
			<key>PciRoot(0x0)/Pci(0x2,0x0)</key>
			<dict>
				<key>AAPL,ig-platform-id</key>
				<data>
				AwCRPg==
				</data>
			</dict>
		</dict>
		<key>Delete</key>
		<dict/>
	</dict>
	<key>Kernel</key>
	<dict>
		<key>Add</key>
		<array>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>Lilu.kext</string>
				<key>Comment</key>
				<string>Patch engine</string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/Lilu</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>VirtualSMC.kext</string>
				<key>Comment</key>
				<string>SMC emulator</string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/VirtualSMC</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>SMCProcessor.kext</string>
				<key>Comment</key>
				<string>CPU temperature monitoring</string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/SMCProcessor</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>SMCSuperIO.kext</string>
				<key>Comment</key>
				<string>Fan monitoring</string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/SMCSuperIO</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>WhateverGreen.kext</string>
				<key>Comment</key>
				<string>Graphics patching</string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/WhateverGreen</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>AppleALC.kext</string>
				<key>Comment</key>
				<string>Audio codec support</string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/AppleALC</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>IntelMausi.kext</string>
				<key>Comment</key>
				<string>Intel I219-V Ethernet</string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/IntelMausi</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>NVMeFix.kext</string>
				<key>Comment</key>
				<string>NVMe power management</string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string>Contents/MacOS/NVMeFix</string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
			<dict>
				<key>Arch</key>
				<string>Any</string>
				<key>BundlePath</key>
				<string>USBPorts.kext</string>
				<key>Comment</key>
				<string>USB port mapping for Z390-E</string>
				<key>Enabled</key>
				<true/>
				<key>ExecutablePath</key>
				<string></string>
				<key>MaxKernel</key>
				<string></string>
				<key>MinKernel</key>
				<string></string>
				<key>PlistPath</key>
				<string>Contents/Info.plist</string>
			</dict>
		</array>
		<key>Block</key>
		<array/>
		<key>Emulate</key>
		<dict>
			<key>Cpuid1Data</key>
			<data>
			</data>
			<key>Cpuid1Mask</key>
			<data>
			</data>
			<key>DummyPowerManagement</key>
			<false/>
			<key>MaxKernel</key>
			<string></string>
			<key>MinKernel</key>
			<string></string>
		</dict>
		<key>Force</key>
		<array/>
		<key>Patch</key>
		<array/>
		<key>Quirks</key>
		<dict>
			<key>AppleCpuPmCfgLock</key>
			<false/>
			<key>AppleXcpmCfgLock</key>
			<true/>
			<key>AppleXcpmExtraMsrs</key>
			<false/>
			<key>AppleXcpmForceBoost</key>
			<false/>
			<key>CustomPciSerialDevice</key>
			<false/>
			<key>CustomSMBIOSGuid</key>
			<false/>
			<key>DisableIoMapper</key>
			<true/>
			<key>DisableIoMapperMapping</key>
			<false/>
			<key>DisableLinkeditJettison</key>
			<true/>
			<key>DisableRtcChecksum</key>
			<false/>
			<key>ExtendBTFeatureFlags</key>
			<false/>
			<key>ExternalDiskIcons</key>
			<false/>
			<key>ForceAquantiaEthernet</key>
			<false/>
			<key>ForceSecureBootScheme</key>
			<false/>
			<key>IncreasePciBarSize</key>
			<false/>
			<key>LapicKernelPanic</key>
			<false/>
			<key>LegacyCommpage</key>
			<false/>
			<key>PanicNoKextDump</key>
			<true/>
			<key>PowerTimeoutKernelPanic</key>
			<true/>
			<key>ProvideCurrentCpuInfo</key>
			<false/>
			<key>SetApfsTrimTimeout</key>
			<integer>-1</integer>
			<key>ThirdPartyDrives</key>
			<false/>
			<key>XhciPortLimit</key>
			<true/>
		</dict>
		<key>Scheme</key>
		<dict>
			<key>CustomKernel</key>
			<false/>
			<key>FuzzyMatch</key>
			<true/>
			<key>KernelArch</key>
			<string>x86_64</string>
			<key>KernelCache</key>
			<string>Auto</string>
		</dict>
	</dict>
	<key>Misc</key>
	<dict>
		<key>BlessOverride</key>
		<array/>
		<key>Boot</key>
		<dict>
			<key>ConsoleAttributes</key>
			<integer>0</integer>
			<key>HibernateMode</key>
			<string>None</string>
			<key>HibernateSkipsPicker</key>
			<false/>
			<key>HideAuxiliary</key>
			<false/>
			<key>InstanceIdentifier</key>
			<string></string>
			<key>LauncherOption</key>
			<string>Disabled</string>
			<key>LauncherPath</key>
			<string>Default</string>
			<key>PickerAttributes</key>
			<integer>1</integer>
			<key>PickerAudioAssist</key>
			<false/>
			<key>PickerMode</key>
			<string>External</string>
			<key>PickerVariant</key>
			<string>Default</string>
			<key>PollAppleHotKeys</key>
			<false/>
			<key>ShowPicker</key>
			<true/>
			<key>TakeoffDelay</key>
			<integer>0</integer>
			<key>Timeout</key>
			<integer>5</integer>
		</dict>
		<key>Debug</key>
		<dict>
			<key>AppleDebug</key>
			<false/>
			<key>ApplePanic</key>
			<false/>
			<key>DisableWatchDog</key>
			<false/>
			<key>DisplayDelay</key>
			<integer>0</integer>
			<key>DisplayLevel</key>
			<integer>2147483650</integer>
			<key>LogModules</key>
			<string>*</string>
			<key>SerialInit</key>
			<false/>
			<key>SysReport</key>
			<false/>
			<key>Target</key>
			<integer>3</integer>
		</dict>
		<key>Entries</key>
		<array/>
		<key>Security</key>
		<dict>
			<key>AllowNvramReset</key>
			<true/>
			<key>AllowSetDefault</key>
			<true/>
			<key>AllowToggleSip</key>
			<false/>
			<key>ApECID</key>
			<integer>0</integer>
			<key>AuthRestart</key>
			<false/>
			<key>BlacklistAppleUpdate</key>
			<true/>
			<key>DmgLoading</key>
			<string>Signed</string>
			<key>EnablePassword</key>
			<false/>
			<key>ExposeSensitiveData</key>
			<integer>6</integer>
			<key>HaltLevel</key>
			<integer>2147483648</integer>
			<key>Password</key>
			<data>
			</data>
			<key>PasswordHash</key>
			<data>
			</data>
			<key>PasswordSalt</key>
			<data>
			</data>
			<key>ScanPolicy</key>
			<integer>0</integer>
			<key>SecureBootModel</key>
			<string>Default</string>
			<key>Vault</key>
			<string>Optional</string>
		</dict>
		<key>Serial</key>
		<dict>
			<key>Custom</key>
			<dict>
				<key>BaudRate</key>
				<integer>115200</integer>
				<key>ClockRate</key>
				<integer>1843200</integer>
				<key>DetectCable</key>
				<false/>
				<key>ExtendedTxFifoSize</key>
				<integer>64</integer>
				<key>FifoControl</key>
				<integer>0</integer>
				<key>LineControl</key>
				<integer>0</integer>
				<key>PciDeviceInfo</key>
				<data>/w==</data>
				<key>RegisterAccessWidth</key>
				<integer>8</integer>
				<key>RegisterBase</key>
				<integer>0</integer>
				<key>RegisterStride</key>
				<integer>1</integer>
				<key>UseHardwareFlowControl</key>
				<false/>
				<key>UseMmio</key>
				<false/>
			</dict>
			<key>Init</key>
			<false/>
			<key>Override</key>
			<false/>
		</dict>
		<key>Tools</key>
		<array/>
	</dict>
	<key>NVRAM</key>
	<dict>
		<key>Add</key>
		<dict>
			<key>4D1EDE05-38C7-4A6A-9CC6-4BCCA8B38C14</key>
			<dict>
				<key>DefaultBackgroundColor</key>
				<data>
				AAAAAA==
				</data>
				<key>UIScale</key>
				<data>
				AQ==
				</data>
			</dict>
			<key>4D1FDA02-38C7-4A6A-9CC6-4BCCA8B30102</key>
			<dict>
				<key>rtc-blacklist</key>
				<data>
				</data>
			</dict>
			<key>7C436110-AB2A-4BBB-A880-FE41995C9F82</key>
			<dict>
				<key>SystemAudioVolume</key>
				<data>
				Rg==
				</data>
				<key>boot-args</key>
				<string>alcid=1</string>
				<key>csr-active-config</key>
				<data>
				AAAAAA==
				</data>
				<key>prev-lang:kbd</key>
				<data>
				ZW4tVVM6MA==
				</data>
				<key>run-efi-updater</key>
				<string>No</string>
			</dict>
		</dict>
		<key>Delete</key>
		<dict>
			<key>4D1EDE05-38C7-4A6A-9CC6-4BCCA8B38C14</key>
			<array>
				<string>DefaultBackgroundColor</string>
				<string>UIScale</string>
			</array>
			<key>4D1FDA02-38C7-4A6A-9CC6-4BCCA8B30102</key>
			<array>
				<string>rtc-blacklist</string>
			</array>
			<key>7C436110-AB2A-4BBB-A880-FE41995C9F82</key>
			<array>
				<string>boot-args</string>
			</array>
		</dict>
		<key>LegacyEnable</key>
		<false/>
		<key>LegacyOverwrite</key>
		<false/>
		<key>LegacySchema</key>
		<dict>
			<key>7C436110-AB2A-4BBB-A880-FE41995C9F82</key>
			<array>
				<string>EFILoginHiDPI</string>
				<string>EFIBluetoothDelay</string>
				<string>LocationServicesEnabled</string>
				<string>SystemAudioVolume</string>
				<string>SystemAudioVolumeDB</string>
				<string>SystemAudioVolumeSaved</string>
				<string>bluetoothActiveControllerInfo</string>
				<string>bluetoothInternalControllerInfo</string>
				<string>flagstate</string>
				<string>fmm-computer-name</string>
				<string>nvda_drv</string>
				<string>prev-lang:kbd</string>
			</array>
			<key>8BE4DF61-93CA-11D2-AA0D-00E098032B8C</key>
			<array>
				<string>Boot0080</string>
				<string>Boot0081</string>
				<string>Boot0082</string>
				<string>BootNext</string>
				<string>BootOrder</string>
			</array>
		</dict>
		<key>WriteFlash</key>
		<true/>
	</dict>
	<key>PlatformInfo</key>
	<dict>
		<key>Automatic</key>
		<true/>
		<key>CustomMemory</key>
		<false/>
		<key>DataHub</key>
		<dict>
			<key>ARTFrequency</key>
			<integer>24000000</integer>
			<key>BoardProduct</key>
			<string>iMac19,1</string>
			<key>BoardRevision</key>
			<data>AwAAAA==</data>
			<key>DevicePathsSupported</key>
			<integer>1</integer>
			<key>FSBFrequency</key>
			<integer>100000000</integer>
			<key>InitialTSC</key>
			<integer>0</integer>
			<key>PlatformName</key>
			<string>platform-uuid</string>
			<key>SmcBranch</key>
			<data>ajMDAA==</data>
			<key>SmcPlatform</key>
			<data>ajMDAA==</data>
			<key>SmcRevision</key>
			<data>
			AgMDAA==
			</data>
			<key>StartupPowerEvents</key>
			<integer>0</integer>
			<key>SystemProductName</key>
			<string>iMac19,1</string>
			<key>SystemSerialNumber</key>
			<string>PLACEHOLDER_SERIAL</string>
			<key>SystemUUID</key>
			<string>PLACEHOLDER_UUID</string>
		</dict>
		<key>Generic</key>
		<dict>
			<key>AdviseFeatures</key>
			<false/>
			<key>MaxBIOSVersion</key>
			<false/>
			<key>MLB</key>
			<string>C02041902GULNV91H</string>
			<key>ProcessorType</key>
			<integer>0</integer>
			<key>ROM</key>
			<data>
			hLFTGHvX
			</data>
			<key>SpoofVendor</key>
			<true/>
			<key>SystemMemoryStatus</key>
			<string>Auto</string>
			<key>SystemProductName</key>
			<string>iMac19,1</string>
			<key>SystemSerialNumber</key>
			<string>C02DJRZ2JV3Q</string>
			<key>SystemUUID</key>
			<string>DFB93A65-BC3C-4853-92A1-1F5A22841079</string>
		</dict>
		<key>Memory</key>
		<dict>
			<key>DataWidth</key>
			<integer>64</integer>
			<key>Devices</key>
			<array/>
			<key>ErrorCorrection</key>
			<integer>0</integer>
			<key>FormFactor</key>
			<integer>13</integer>
			<key>MaxCapacity</key>
			<integer>0</integer>
			<key>TotalWidth</key>
			<integer>64</integer>
			<key>Type</key>
			<integer>0</integer>
			<key>TypeDetail</key>
			<integer>0</integer>
		</dict>
		<key>PlatformNVRAM</key>
		<dict>
			<key>BID</key>
			<string>Mac-60F81C85190FB824</string>
			<key>FirmwareFeatures</key>
			<data>
			AAAAAAAAAA==
			</data>
			<key>FirmwareFeaturesMask</key>
			<data>
			AAAAAAAAAA==
			</data>
			<key>MLB</key>
			<string>PLACEHOLDER_MLB</string>
			<key>ROM</key>
			<data>
			ESIzRFVm
			</data>
			<key>SystemSerialNumber</key>
			<string>C02DJRZ2JV3Q</string>
			<key>SystemUUID</key>
			<string>DFB93A65-BC3C-4853-92A1-1F5A22841079</string>
		</dict>
		<key>SMBIOS</key>
		<dict>
			<key>BIOSReleaseDate</key>
			<string>10/02/2018</string>
			<key>BIOSVendor</key>
			<string>Apple Inc.</string>
			<key>BIOSVersion</key>
			<string>1037.147.1.0.0</string>
			<key>BoardAssetTag</key>
			<string></string>
			<key>BoardLocationInChassis</key>
			<string>Part Component</string>
			<key>BoardManufacturer</key>
			<string>Apple Inc.</string>
			<key>BoardProduct</key>
			<string>Mac-60F81C85190FB824</string>
			<key>BoardSerialNumber</key>
			<string>PLACEHOLDER_MLB</string>
			<key>BoardType</key>
			<integer>10</integer>
			<key>BoardVersion</key>
			<string>1.0</string>
			<key>ChassisAssetTag</key>
			<string></string>
			<key>ChassisManufacturer</key>
			<string>Apple Inc.</string>
			<key>ChassisSerialNumber</key>
			<string>PLACEHOLDER_SERIAL</string>
			<key>ChassisType</key>
			<integer>13</integer>
			<key>ChassisVersion</key>
			<string>Mac-60F81C85190FB824</string>
			<key>FirmwareFeatures</key>
			<data>
			AAAAAAAAAA==
			</data>
			<key>FirmwareFeaturesMask</key>
			<data>
			AAAAAAAAAA==
			</data>
			<key>PlatformFeature</key>
			<integer>0</integer>
			<key>ProcessorType</key>
			<integer>0</integer>
			<key>SmcVersion</key>
			<data>
			AgMDAA==
			</data>
			<key>SystemFamily</key>
			<string>iMac</string>
			<key>SystemManufacturer</key>
			<string>Apple Inc.</string>
			<key>SystemProductName</key>
			<string>iMac19,1</string>
			<key>SystemSKUNumber</key>
			<string>System SKU#</string>
			<key>SystemSerialNumber</key>
			<string>PLACEHOLDER_SERIAL</string>
			<key>SystemUUID</key>
			<string>PLACEHOLDER_UUID</string>
			<key>SystemVersion</key>
			<string>1.0</string>
		</dict>
		<key>UpdateDataHub</key>
		<true/>
		<key>UpdateNVRAM</key>
		<true/>
		<key>UpdateSMBIOS</key>
		<true/>
		<key>UpdateSMBIOSMode</key>
		<string>Create</string>
		<key>UseRawUuidEncoding</key>
		<false/>
	</dict>
	<key>UEFI</key>
	<dict>
		<key>APFS</key>
		<dict>
			<key>EnableJumpstart</key>
			<true/>
			<key>GlobalConnect</key>
			<false/>
			<key>HideVerbose</key>
			<true/>
			<key>JumpstartHotPlug</key>
			<false/>
			<key>MinDate</key>
			<integer>0</integer>
			<key>MinVersion</key>
			<integer>0</integer>
		</dict>
		<key>AppleInput</key>
		<dict>
			<key>AppleEvent</key>
			<string>Builtin</string>
			<key>CustomDelays</key>
			<false/>
			<key>GraphicsInputMirroring</key>
			<false/>
			<key>KeyInitialDelay</key>
			<integer>0</integer>
			<key>KeySubsequentDelay</key>
			<integer>5</integer>
			<key>PointerDwellClickTimeout</key>
			<integer>0</integer>
			<key>PointerDwellDoubleClickTimeout</key>
			<integer>0</integer>
			<key>PointerDwellRadius</key>
			<integer>0</integer>
			<key>PointerPollMask</key>
			<integer>-1</integer>
			<key>PointerPollMax</key>
			<integer>80</integer>
			<key>PointerPollMin</key>
			<integer>10</integer>
			<key>PointerSpeedDiv</key>
			<integer>1</integer>
			<key>PointerSpeedMul</key>
			<integer>1</integer>
		</dict>
		<key>Audio</key>
		<dict>
			<key>AudioCodec</key>
			<integer>0</integer>
			<key>AudioDevice</key>
			<string>PciRoot(0x0)/Pci(0x1f,0x3)</string>
			<key>AudioOut</key>
			<integer>0</integer>
			<key>AudioOutMask</key>
			<integer>-1</integer>
			<key>AudioSupport</key>
			<false/>
			<key>DisconnectHda</key>
			<false/>
			<key>MaximumGain</key>
			<integer>-15</integer>
			<key>MinimumAssistGain</key>
			<integer>-30</integer>
			<key>MinimumAudibleGain</key>
			<integer>-55</integer>
			<key>PlayChime</key>
			<string>Auto</string>
			<key>ResetTrafficClass</key>
			<false/>
			<key>SetupDelay</key>
			<integer>0</integer>
		</dict>
		<key>ConnectDrivers</key>
		<true/>
		<key>Drivers</key>
		<array>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string>Memory fix for UEFI runtime services</string>
				<key>Enabled</key>
				<true/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>OpenRuntime.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string>HFS+ filesystem support</string>
				<key>Enabled</key>
				<true/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>OpenHfsPlus.efi</string>
			</dict>
			<dict>
				<key>Arguments</key>
				<string></string>
				<key>Comment</key>
				<string>Graphical boot picker</string>
				<key>Enabled</key>
				<true/>
				<key>LoadEarly</key>
				<false/>
				<key>Path</key>
				<string>OpenCanopy.efi</string>
			</dict>
		</array>
		<key>Input</key>
		<dict>
			<key>KeyFiltering</key>
			<false/>
			<key>KeyForgetThreshold</key>
			<integer>5</integer>
			<key>KeySupport</key>
			<true/>
			<key>KeySupportMode</key>
			<string>Auto</string>
			<key>KeySwap</key>
			<false/>
			<key>PointerSupport</key>
			<false/>
			<key>PointerSupportMode</key>
			<string>ASUS</string>
			<key>TimerResolution</key>
			<integer>50000</integer>
		</dict>
		<key>Output</key>
		<dict>
			<key>ClearScreenOnModeSwitch</key>
			<false/>
			<key>ConsoleFont</key>
			<string></string>
			<key>ConsoleMode</key>
			<string></string>
			<key>DirectGopRendering</key>
			<false/>
			<key>ForceResolution</key>
			<false/>
			<key>GopBurstMode</key>
			<false/>
			<key>GopPassThrough</key>
			<string>Disabled</string>
			<key>IgnoreTextInGraphics</key>
			<false/>
			<key>InitialMode</key>
			<string>Auto</string>
			<key>ReconnectGraphicsOnConnect</key>
			<false/>
			<key>ReconnectOnResChange</key>
			<false/>
			<key>ReplaceTabWithSpace</key>
			<false/>
			<key>Resolution</key>
			<string>Max</string>
			<key>ProvideConsoleGop</key>
			<true/>
			<key>SanitiseClearScreen</key>
			<false/>
			<key>TextRenderer</key>
			<string>BuiltinGraphics</string>
			<key>UgaPassThrough</key>
			<false/>
			<key>UIScale</key>
			<integer>-1</integer>
		</dict>
		<key>ProtocolOverrides</key>
		<dict>
			<key>AppleAudio</key>
			<false/>
			<key>AppleBootPolicy</key>
			<false/>
			<key>AppleDebugLog</key>
			<false/>
			<key>AppleEg2Info</key>
			<false/>
			<key>AppleFramebufferInfo</key>
			<false/>
			<key>AppleImageConversion</key>
			<false/>
			<key>AppleImg4Verification</key>
			<false/>
			<key>AppleKeyMap</key>
			<false/>
			<key>AppleRtcRam</key>
			<false/>
			<key>AppleSecureBoot</key>
			<false/>
			<key>AppleSmcIo</key>
			<false/>
			<key>AppleUserInterfaceTheme</key>
			<false/>
			<key>DataHub</key>
			<false/>
			<key>DeviceProperties</key>
			<false/>
			<key>FirmwareVolume</key>
			<false/>
			<key>HashServices</key>
			<false/>
			<key>OSInfo</key>
			<false/>
			<key>PciIo</key>
			<false/>
			<key>UnicodeCollation</key>
			<false/>
		</dict>
		<key>Quirks</key>
		<dict>
			<key>ActivateHpetSupport</key>
			<false/>
			<key>DisableSecurityPolicy</key>
			<false/>
			<key>EnableVectorAcceleration</key>
			<true/>
			<key>EnableVmx</key>
			<false/>
			<key>ExitBootServicesDelay</key>
			<integer>0</integer>
			<key>ForceOcWriteFlash</key>
			<false/>
			<key>ForgeUefiSupport</key>
			<false/>
			<key>IgnoreInvalidFlexRatio</key>
			<false/>
			<key>ReleaseUsbOwnership</key>
			<false/>
			<key>ReloadOptionRoms</key>
			<false/>
			<key>RequestBootVarRouting</key>
			<true/>
			<key>ResizeGpuBars</key>
			<integer>-1</integer>
			<key>ResizeUsePciRbIo</key>
			<false/>
			<key>ShimRetainProtocol</key>
			<false/>
			<key>TscSyncTimeout</key>
			<integer>0</integer>
			<key>UnblockFsConnect</key>
			<false/>
		</dict>
		<key>ReservedMemory</key>
		<array/>
		<key>Unload</key>
		<array/>
	</dict>
</dict>
</plist>
